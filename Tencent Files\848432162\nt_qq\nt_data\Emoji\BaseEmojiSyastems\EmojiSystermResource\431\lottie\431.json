{"v": "5.12.1", "fr": 60, "ip": 167, "op": 168, "w": 512, "h": 245, "nm": "01_snake_grey", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Path", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [251.75, 129.45, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.6, 3.2], [9.2, -1.6], [1.9, -13.5], [7.3, 0], [0, 3.7], [25.056, -5.764], [-1.227, -12.569], [-1.8, -0.013], [-0.4, -1.9], [0, -3.7], [3.6, -0.2], [-0.8, 3.7], [-1.143, 0.857], [0.386, 12.689], [0.147, -0.036], [0.2, -19.9], [0.2, -14], [1.5, -0.8], [1.2, 1.1], [0, 1.7], [0.2, 19.3], [27.6, -8.8], [0.1, -19.1], [0.1, -20], [1.1, -0.4], [1.5, 1], [0, 1.8], [0.6, 14.3], [17.3, 4.8], [3.9, 0.8], [5.7, 0.7], [1.912, -9.725], [0.3, 0.5], [5.3, -0.2], [0.8, -6.7], [1, -0.6], [0.3, -8.1], [2.7, -8.3], [-0.7, -1.1], [-3, -3.8], [-0.7, 1], [-3.4, 1.2], [-1.1, -1.7], [-7.7, 0], [-9.7, 0], [0, -6.8], [-0.1, -13], [-1, -3.6], [-17.9, -2.5], [0.3, 22.6], [0, 17], [-0.1, 4], [-3.4, 0.2], [-0.3, -3.3], [0, -1.3], [-0.2, -18.8], [-1.4, -4.2], [-22.3, 21.2], [-0.9, -0.5], [-7.7, 0.1], [-4.3, 14.2], [-2.7, 0], [-9.5, 0.1], [-2.8, 12], [0.3, 5.3]], "o": [[-7, -7.2], [-14, 2.4], [-7.4, 0], [0, -4.1], [-0.298, -25.749], [0.386, 12.689], [0.896, -0.671], [1.703, 0.012], [0.7, 3.3], [-3.4, 0.3], [0, -4], [0.168, -0.953], [-1.227, -12.569], [-0.147, 0.034], [-19.2, 4.7], [-0.2, 14], [0, 1.7], [-1.2, 0.6], [-1.1, -1], [-0.1, -19.3], [-0.3, -29.8], [-17.2, 5.5], [-0.1, 20], [0, 1.5], [-1.8, 0.6], [-1.2, -0.8], [-0.1, -14.3], [-0.7, -17.9], [-4, -1.1], [-1.1, -11.7], [-5.3, -0.7], [-0.3, -0.5], [-0.963, -7.8], [-5.3, 0.1], [-0.1, 1.3], [-6.9, 4.3], [-6.9, 1.8], [1.2, 2.4], [1, 1.7], [0.8, -2.7], [3.5, -5.4], [0.9, -0.4], [4.3, 6.2], [9.7, 0], [6.8, 0.1], [0.1, 13], [0, 3.8], [5.1, 18.2], [23.9, 3.4], [-0.2, -17], [0, -4], [0, -3.2], [3.8, -0.2], [0.1, 1.3], [0, 18.8], [0.1, 4.4], [9.8, 28.8], [1.3, -1.2], [7.2, 3.7], [14.7, -0.1], [0.8, -3], [9.5, 0.1], [12.4, -0.1], [1.1, -5.1], [-0.4, -8.1]], "v": [[184.15, -18.35], [159.95, -27.05], [136.75, -1.85], [114.95, -1.85], [114.95, -13.25], [64.292, -52.943], [70.729, -15.915], [73.55, -16.75], [78.15, -12.15], [78.35, -1.65], [68.45, -0.95], [68.65, -12.65], [70.729, -15.915], [64.292, -52.943], [63.85, -52.85], [31.35, -11.95], [31.15, 30.05], [28.35, 35.05], [23.15, 33.75], [21.95, 28.75], [21.85, -29.25], [-32.75, -70.25], [-61.65, -29.55], [-61.75, 30.45], [-63.75, 34.85], [-69.75, 34.25], [-71.45, 29.05], [-71.75, -13.95], [-101.85, -51.35], [-113.85, -53.75], [-122.35, -69.25], [-133.475, -58.35], [-134.538, -58.463], [-144.55, -69.45], [-153.75, -59.05], [-155.45, -55.35], [-166.15, -36.75], [-184.15, -28.15], [-180.55, -25.75], [-178.75, -19.35], [-177.05, -24.75], [-167.65, -30.25], [-163.15, -26.35], [-145.65, -16.65], [-116.65, -16.65], [-108.45, -8.45], [-108.35, 30.55], [-107.25, 41.95], [-71.25, 71.95], [-25.05, 33.45], [-25.05, -17.55], [-25.05, -29.55], [-20.35, -35.55], [-15.15, -29.55], [-15.15, -25.55], [-15.05, 30.95], [-13.05, 44.15], [55.95, 60.35], [60.65, 58.05], [83.15, 61.35], [113.65, 38.75], [118.45, 35.25], [146.95, 35.25], [172.85, 15.05], [173.75, -0.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058823529, 0.854901960784, 0.890196078431, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 502, "st": 0, "ct": 1, "bm": 0}], "markers": [], "props": {}}