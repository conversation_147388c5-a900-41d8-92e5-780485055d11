[BASE_ALL_ATTACKS]
IK_None=(Action=AttackWithAlternateHeavy)
IK_None=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackWithAlternateHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)
IK_LShift=(Action=PCAlternate)
IK_Pad_Y_TRIANGLE=(Action=AttackHeavy)
IK_Pad_Y_TRIANGLE=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=SpecialAttackWithAlternateLight,State=Duration,IdleTime=0.2)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_Pad_X_SQUARE=(Action=AttackLight)
IK_Pad_X_SQUARE=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)

[BASE_ATTACKS_NO_LIGHT]
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=SpecialAttackWithAlternateLight,State=Duration,IdleTime=0.2)
IK_None=(Action=AttackWithAlternateHeavy)
IK_None=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackWithAlternateHeavy,State=Duration,IdleTime=0.2)
IK_LShift=(Action=PCAlternate)
IK_Pad_Y_TRIANGLE=(Action=AttackHeavy)
IK_Pad_Y_TRIANGLE=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)

[BASE_ATTACK_HEAVY]
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_None=(Action=AttackWithAlternateHeavy)
IK_LShift=(Action=PCAlternate)
IK_Pad_Y_TRIANGLE=(Action=AttackHeavy)

[BASE_ATTACK_LIGHT]
IK_Pad_X_SQUARE=(Action=AttackLight)
IK_LeftMouse=(Action=AttackWithAlternateLight)

[BASE_CameraMovement]
IK_U=(Action=EnablePhotoMode)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_MouseX=(Action=GI_MouseDampX)
IK_MouseY=(Action=GI_MouseDampY)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)

[BASE_CharacterMovement]
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)

[BASE_CharacterMovementWithSprint]
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_LShift=(Action=Sprint)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_None=(Action=SprintToggle)
IK_Pad_A_CROSS=(Action=Sprint)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)

[BASE_DEBUG]
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_NumPad2=(Action=Debug_KillTarget)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_O=(Action=PanelFakeHud)
IK_P=(Action=DebugInput)

[BASE_DRAW_SWORDS]
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SteelSword)
IK_Pad_DigitRight=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SilverSword)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_1=(Action=SteelSword)
IK_1=(Action=SwordSheatheSteel,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=SilverSword)
IK_2=(Action=SwordSheatheSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_C=(Action=SwordSheathe)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)

[BASE_DRAW_SWORDS_KEYBOARD]
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_1=(Action=SteelSword)
IK_1=(Action=SwordSheatheSteel,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=SilverSword)
IK_2=(Action=SwordSheatheSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_C=(Action=SwordSheathe)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)

[BASE_DRINK_POTIONS]
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)

[BASE_FocusMode]
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_RightMouse=(Action=Focus,Reprocess)

[BASE_INTERACTIONS_HORSE]
IK_Pad_A_CROSS=(Action=Follow)
IK_LShift=(Action=Follow)

[BASE_INTERACTIONS_KEYBOARD]
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)

[BASE_INTERACTIONS_PAD]
IK_Pad_RightTrigger=(Action=Spare)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)

[BASE_Interactions]
IK_PrintScrn=(Action=ScreenshotRequested)
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)
IK_Pad_RightTrigger=(Action=Spare)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)

[BASE_Items]
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_Pad_RightShoulder=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_MiddleMouse=(Action=ThrowItem)
IK_MiddleMouse=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)

[BASE_JOURNAL_AND_QUEST_MANAGE]
IK_Escape=(Action=ShowEntryInPanel)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_Pad_Start=(Action=ShowEntryInPanel)

[BASE_PanelsShortcuts]
IK_Enter=(Action=HubMenu)
IK_G=(Action=PanelGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_I=(Action=PanelInv)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_L=(Action=PanelAlch)
IK_M=(Action=PanelMapPC)
IK_Home=(Action=ToggleHud)
IK_N=(Action=PanelMeditation)
IK_O=(Action=PanelCrafting)
IK_B=(Action=PanelBestiary)

[BASE_SHOW_RADIAL_MENU]
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Tab=(Action=RadialMenu)

[BASE_SPECIAL_ATTACK_HEAVY]
IK_LeftMouse=(Action=SpecialAttackWithAlternateLight,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackWithAlternateHeavy,State=Duration,IdleTime=0.2)
IK_Pad_Y_TRIANGLE=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)

[BASE_SPECIAL_ATTACK_LIGHT]
IK_Pad_X_SQUARE=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)

[BASE_Signs]
IK_Pad_RightTrigger=(Action=CastSign)
IK_Pad_RightTrigger=(Action=CastSignHold,State=Duration,IdleTime=0.2)
IK_3=(Action=SelectAard)
IK_4=(Action=SelectYrden)
IK_5=(Action=SelectIgni)
IK_6=(Action=SelectQuen)
IK_Q=(Action=CastSign)
IK_Q=(Action=CastSignHold,State=Duration,IdleTime=0.2)
IK_7=(Action=SelectAxii)

[Boat]
IK_Tab=(Action=RadialMenu)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_MiddleMouse=(Action=VehicleItemAction)
IK_MiddleMouse=(Action=VehicleItemActionHold,State=Duration,IdleTime=0.2)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Home=(Action=ToggleHud)
IK_Pad_RightShoulder=(Action=VehicleItemAction)
IK_Pad_RightShoulder=(Action=VehicleItemActionHold,State=Duration,IdleTime=0.2)
IK_LShift=(Action=GI_Accelerate)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_Pad_X_SQUARE=(Action=GI_Decelerate)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=BoatDismount)
IK_Pad_B_CIRCLE=(Action=VehicleItemActionAbort)
IK_Space=(Action=VehicleItemActionAbort)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_B=(Action=PanelBestiary)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_RightThumb=(Action=CameraLock)
IK_RightMouse=(Action=Focus,Reprocess)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=BoatDismount)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_I=(Action=PanelInv)
IK_Pad_A_CROSS=(Action=GI_Accelerate)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_L=(Action=PanelAlch)
IK_M=(Action=PanelMapPC)
IK_N=(Action=PanelMeditation)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_P=(Action=DebugInput)
IK_NumPad2=(Action=Debug_KillTarget)
IK_Enter=(Action=HubMenu)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_S=(Action=GI_Decelerate)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_MouseY=(Action=GI_MouseDampY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Z=(Action=CameraLock)

[BoatPassenger]
IK_Tab=(Action=RadialMenu)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Home=(Action=ToggleHud)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=BoatDismount)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_B=(Action=PanelBestiary)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_RightMouse=(Action=Focus,Reprocess)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=BoatDismount)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_I=(Action=PanelInv)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_L=(Action=PanelAlch)
IK_M=(Action=PanelMapPC)
IK_N=(Action=PanelMeditation)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_P=(Action=DebugInput)
IK_NumPad2=(Action=Debug_KillTarget)
IK_Enter=(Action=HubMenu)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_MouseY=(Action=GI_MouseDampY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)

[Combat]
IK_Alt=(Action=Dodge)
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)
IK_Pad_A_CROSS=(Action=CbtRoll)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_None=(Action=SprintToggle)
IK_None=(Action=AttackWithAlternateHeavy)
IK_None=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackWithAlternateHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_1=(Action=SteelSword)
IK_1=(Action=SwordSheatheSteel,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=SilverSword)
IK_2=(Action=SwordSheatheSilver,State=Duration,IdleTime=0.3)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_3=(Action=SelectAard)
IK_4=(Action=SelectYrden)
IK_5=(Action=SelectIgni)
IK_Home=(Action=ToggleHud)
IK_6=(Action=SelectQuen)
IK_Pad_LeftThumb=(Action=SpawnHorse)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_7=(Action=SelectAxii)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_RightTrigger=(Action=Spare)
IK_Pad_RightTrigger=(Action=CastSign)
IK_Pad_RightTrigger=(Action=CastSignHold,State=Duration,IdleTime=0.2)
IK_Enter=(Action=HubMenu)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_A=(Action=MovementDoubleTapA)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_B=(Action=PanelBestiary)
IK_C=(Action=SwordSheathe)
IK_D=(Action=MovementDoubleTapD)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_LShift=(Action=Sprint)
IK_LShift=(Action=PCAlternate)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_I=(Action=PanelInv)
IK_Tab=(Action=RadialMenu)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_L=(Action=PanelAlch)
IK_NumPad2=(Action=Debug_KillTarget)
IK_M=(Action=PanelMapPC)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=Dodge)
IK_Pad_B_CIRCLE=(Action=AltQuenCasting)
IK_N=(Action=PanelMeditation)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_P=(Action=DebugInput)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_Q=(Action=CastSign)
IK_Q=(Action=CastSignHold,State=Duration,IdleTime=0.2)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_S=(Action=MovementDoubleTapS)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_Pad_Y_TRIANGLE=(Action=AttackHeavy)
IK_Pad_Y_TRIANGLE=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_U=(Action=EnablePhotoMode)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SilverSword)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=ComboDigitRight)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_W=(Action=MovementDoubleTapW)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_X=(Action=SpawnHorse)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Z=(Action=CameraLock)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=SpecialAttackWithAlternateLight,State=Duration,IdleTime=0.2)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)
IK_RightMouse=(Action=LockAndGuard)
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_Pad_RightShoulder=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_RightThumb=(Action=CameraLock)
IK_Pad_LeftTrigger=(Action=Alternate)
IK_Pad_LeftTrigger=(Action=LockAndGuard)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_X_SQUARE=(Action=AttackLight)
IK_Pad_X_SQUARE=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_Space=(Action=CbtRoll)
IK_MouseX=(Action=GI_MouseDampX)
IK_MouseY=(Action=GI_MouseDampY)
IK_MouseZ=(Action=ToggleSigns)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SteelSword)
IK_Pad_DigitLeft=(Action=ComboDigitLeft)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_MiddleMouse=(Action=ThrowItem)
IK_MiddleMouse=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)

[Combat_Replacer_Ciri]
IK_Tab=(Action=RadialMenu)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_MiddleMouse=(Action=ThrowItem)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Home=(Action=ToggleHud)
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_LShift=(Action=Sprint)
IK_LShift=(Action=PCAlternate)
IK_Pad_RightTrigger=(Action=Spare)
IK_Pad_RightTrigger=(Action=CiriSpecialAttack)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_Pad_X_SQUARE=(Action=AttackLight)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_Pad_DigitRight=(Action=CiriDrawWeapon)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=CiriDodge)
IK_Space=(Action=CiriDash)
IK_F1=(Action=OnShowControlsHelp)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_Pad_LeftTrigger=(Action=LockAndGuard)
IK_B=(Action=PanelBestiary)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_RightThumb=(Action=CameraLock)
IK_RightMouse=(Action=LockAndGuard)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_Pad_DigitLeft=(Action=CiriDrawWeapon)
IK_Pad_Y_TRIANGLE=(Action=CiriAttackHeavy)
IK_Pad_Y_TRIANGLE=(Action=CiriSpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_I=(Action=PanelInv)
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)
IK_Pad_A_CROSS=(Action=CiriDash)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_L=(Action=PanelAlch)
IK_M=(Action=PanelMapPC)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=SpecialAttackWithAlternateLight,State=Duration,IdleTime=0.2)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)
IK_N=(Action=PanelMeditation)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_P=(Action=DebugInput)
IK_NumPad2=(Action=Debug_KillTarget)
IK_Q=(Action=CiriSpecialAttack)
IK_Enter=(Action=HubMenu)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_MouseX=(Action=GI_MouseDampX)
IK_Alt=(Action=CiriDodge)
IK_U=(Action=EnablePhotoMode)
IK_None=(Action=SprintToggle)
IK_None=(Action=SpecialAttackWithAlternateHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=AttackWithAlternateHeavy)
IK_MouseY=(Action=GI_MouseDampY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Z=(Action=CameraLock)

[Death]

[Diving]
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_None=(Action=SprintToggle)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Home=(Action=ToggleHud)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Enter=(Action=HubMenu)
IK_Pad_RightTrigger=(Action=Spare)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_B=(Action=PanelBestiary)
IK_C=(Action=DiveDown)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_LShift=(Action=Sprint)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_I=(Action=PanelInv)
IK_Tab=(Action=RadialMenu)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_L=(Action=PanelAlch)
IK_NumPad2=(Action=Debug_KillTarget)
IK_M=(Action=PanelMapPC)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=DiveUp)
IK_Pad_B_CIRCLE=(Action=ExplorationInteraction)
IK_N=(Action=PanelMeditation)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_P=(Action=DebugInput)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_U=(Action=EnablePhotoMode)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Z=(Action=CameraLock)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)
IK_RightMouse=(Action=Focus,Reprocess)
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_Pad_RightShoulder=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_Pad_RightThumb=(Action=CameraLock)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_X_SQUARE=(Action=DiveDown)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_Space=(Action=DiveUp)
IK_Space=(Action=ExplorationInteraction)
IK_MouseX=(Action=GI_MouseDampX)
IK_MouseY=(Action=GI_MouseDampY)
IK_MouseZ=(Action=ToggleSigns)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_MiddleMouse=(Action=ThrowItem)
IK_MiddleMouse=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)

[EMPTY_CONTEXT]

[Exploration]
IK_Alt=(Action=ShowDeveloperModeAlt)
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_None=(Action=SprintToggle)
IK_None=(Action=AttackWithAlternateHeavy)
IK_None=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackWithAlternateHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_1=(Action=SteelSword)
IK_1=(Action=SwordSheatheSteel,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=SilverSword)
IK_2=(Action=SwordSheatheSilver,State=Duration,IdleTime=0.3)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_3=(Action=SelectAard)
IK_4=(Action=SelectYrden)
IK_5=(Action=SelectIgni)
IK_Home=(Action=ToggleHud)
IK_6=(Action=SelectQuen)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_Pad_LeftThumb=(Action=SpawnHorse)
IK_7=(Action=SelectAxii)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_RightTrigger=(Action=Spare)
IK_Pad_RightTrigger=(Action=CastSign)
IK_Pad_RightTrigger=(Action=CastSignHold,State=Duration,IdleTime=0.2)
IK_Enter=(Action=HubMenu)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_B=(Action=PanelBestiary)
IK_C=(Action=SwordSheathe)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_LShift=(Action=Sprint)
IK_LShift=(Action=PCAlternate)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_LControl=(Action=WalkToggle)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_I=(Action=PanelInv)
IK_Tab=(Action=RadialMenu)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_L=(Action=PanelAlch)
IK_NumPad2=(Action=Debug_KillTarget)
IK_M=(Action=PanelMapPC)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=Jump)
IK_Pad_B_CIRCLE=(Action=ExplorationInteraction)
IK_Pad_B_CIRCLE=(Action=Roll)
IK_Pad_B_CIRCLE=(Action=AltQuenCasting)
IK_N=(Action=PanelMeditation)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_P=(Action=DebugInput)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_Q=(Action=CastSign)
IK_Q=(Action=CastSignHold,State=Duration,IdleTime=0.2)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_Pad_Y_TRIANGLE=(Action=AttackHeavy)
IK_Pad_Y_TRIANGLE=(Action=SpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_U=(Action=EnablePhotoMode)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SilverSword)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_X=(Action=SpawnHorse)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Z=(Action=CameraLock)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=SpecialAttackWithAlternateLight,State=Duration,IdleTime=0.2)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_RightMouse=(Action=Focus,Reprocess)
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_Pad_RightShoulder=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_F1=(Action=OnShowControlsHelp)
IK_F5=(Action=ShowDeveloperMode)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_Pad_X_SQUARE=(Action=AttackLight)
IK_Pad_X_SQUARE=(Action=SpecialAttackLight,State=Duration,IdleTime=0.2)
IK_Space=(Action=Jump)
IK_Space=(Action=ExplorationInteraction)
IK_Space=(Action=Roll)
IK_MouseX=(Action=GI_MouseDampX)
IK_MouseY=(Action=GI_MouseDampY)
IK_MouseZ=(Action=ToggleSigns)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SteelSword)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_MiddleMouse=(Action=ThrowItem)
IK_MiddleMouse=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)

[Exploration_Replacer_Ciri]
IK_Tab=(Action=RadialMenu)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_MiddleMouse=(Action=ThrowItem)
IK_MiddleMouse=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Home=(Action=ToggleHud)
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_Pad_RightShoulder=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_LShift=(Action=Sprint)
IK_LShift=(Action=PCAlternate)
IK_Pad_RightTrigger=(Action=Spare)
IK_Pad_RightTrigger=(Action=CiriSpecialAttack)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_1=(Action=CiriDrawWeapon)
IK_2=(Action=CiriDrawWeaponAlternative)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_Pad_X_SQUARE=(Action=AttackLight)
IK_Pad_DigitRight=(Action=CiriHolsterWeapon,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=CiriDrawWeapon)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=Jump)
IK_Pad_B_CIRCLE=(Action=ExplorationInteraction)
IK_Pad_B_CIRCLE=(Action=Roll)
IK_Space=(Action=Jump)
IK_Space=(Action=ExplorationInteraction)
IK_Space=(Action=Roll)
IK_F1=(Action=OnShowControlsHelp)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_B=(Action=PanelBestiary)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_RightMouse=(Action=Focus,Reprocess)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_Pad_DigitLeft=(Action=CiriHolsterWeapon,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=CiriDrawWeapon)
IK_Pad_Y_TRIANGLE=(Action=CiriAttackHeavy)
IK_Pad_Y_TRIANGLE=(Action=CiriSpecialAttackHeavy,State=Duration,IdleTime=0.2)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_I=(Action=PanelInv)
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_L=(Action=PanelAlch)
IK_M=(Action=PanelMapPC)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_LeftMouse=(Action=SpecialAttackWithAlternateLight,State=Duration,IdleTime=0.2)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)
IK_LeftMouse=(Action=AttackWithAlternateLight)
IK_N=(Action=PanelMeditation)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_P=(Action=DebugInput)
IK_NumPad2=(Action=Debug_KillTarget)
IK_Q=(Action=CiriSpecialAttack)
IK_Enter=(Action=HubMenu)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_None=(Action=SprintToggle)
IK_None=(Action=SpecialAttackWithAlternateHeavy,State=Duration,IdleTime=0.2)
IK_None=(Action=AttackWithAlternateHeavy)
IK_MouseY=(Action=GI_MouseDampY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_LControl=(Action=WalkToggle)

[FakeAxisInput]
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_A=(Action=MoveLeft,State=Axis,Value=-1)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_W=(Action=MoveForward,State=Axis,Value=1)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_S=(Action=MoveBackward,State=Axis,Value=-1)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_D=(Action=MoveRight,State=Axis,Value=1)

[FastMenu]
IK_Escape=(Action=IngameMenu)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_G=(Action=GotoGlossary)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)

[Horse]
IK_Alt=(Action=HorseKick,State=Duration,IdleTime=0.3)
IK_Pad_A_CROSS=(Action=Follow)
IK_Pad_A_CROSS=(Action=Gallop,State=Duration,IdleTime=0.3)
IK_Pad_A_CROSS=(Action=Canter)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_1=(Action=SteelSword)
IK_1=(Action=SwordSheatheSteel,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=SilverSword)
IK_2=(Action=SwordSheatheSilver,State=Duration,IdleTime=0.3)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Home=(Action=ToggleHud)
IK_Pad_LeftThumb=(Action=HorseKick)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Enter=(Action=HubMenu)
IK_Pad_RightTrigger=(Action=VehicleCastSign)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_B=(Action=PanelBestiary)
IK_C=(Action=SwordSheathe)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=HorseDismount)
IK_LShift=(Action=Follow)
IK_LShift=(Action=Gallop,State=Duration,IdleTime=0.3)
IK_LShift=(Action=Canter)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_LControl=(Action=WalkToggle)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_I=(Action=PanelInv)
IK_Tab=(Action=RadialMenu)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_L=(Action=PanelAlch)
IK_NumPad2=(Action=Debug_KillTarget)
IK_M=(Action=PanelMapPC)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=VehicleItemActionAbort)
IK_Pad_B_CIRCLE=(Action=HorseDismount,State=Duration,IdleTime=10000)
IK_Pad_B_CIRCLE=(Action=HorseJump)
IK_N=(Action=PanelMeditation)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_P=(Action=DebugInput)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_Q=(Action=VehicleCastSign)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_Pad_Y_TRIANGLE=(Action=VehicleAttack)
IK_U=(Action=EnablePhotoMode)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SilverSword)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_X=(Action=Stop)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Z=(Action=CameraLock)
IK_LeftMouse=(Action=VehicleAttack)
IK_RightMouse=(Action=Focus,Reprocess)
IK_Pad_RightShoulder=(Action=VehicleItemAction)
IK_Pad_RightShoulder=(Action=VehicleItemActionHold,State=Duration,IdleTime=0.2)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_RightThumb=(Action=CameraLock)
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_X_SQUARE=(Action=VehicleAttack)
IK_Space=(Action=VehicleItemActionAbort)
IK_Space=(Action=HorseJump)
IK_MouseX=(Action=GI_MouseDampX)
IK_MouseY=(Action=GI_MouseDampY)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SteelSword)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_MiddleMouse=(Action=VehicleItemAction)
IK_MiddleMouse=(Action=VehicleItemActionHold,State=Duration,IdleTime=0.2)

[Horse_Replacer_Ciri]
IK_Tab=(Action=RadialMenu)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Home=(Action=ToggleHud)
IK_LShift=(Action=Follow)
IK_LShift=(Action=Gallop,State=Duration,IdleTime=0.3)
IK_LShift=(Action=Canter)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_1=(Action=CiriDrawWeapon)
IK_2=(Action=CiriDrawWeaponAlternative)
IK_Pad_X_SQUARE=(Action=VehicleAttack)
IK_Pad_DigitRight=(Action=CiriHolsterWeapon,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=CiriDrawWeapon)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=HorseJump)
IK_Space=(Action=HorseJump)
IK_F1=(Action=OnShowControlsHelp)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_B=(Action=PanelBestiary)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_RightThumb=(Action=CameraLock)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=HorseDismount)
IK_Pad_DigitLeft=(Action=CiriHolsterWeapon,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=CiriDrawWeapon)
IK_Pad_Y_TRIANGLE=(Action=VehicleAttack)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_I=(Action=PanelInv)
IK_Pad_A_CROSS=(Action=Follow)
IK_Pad_A_CROSS=(Action=Gallop,State=Duration,IdleTime=0.3)
IK_Pad_A_CROSS=(Action=Canter)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Pad_LeftThumb=(Action=HorseKick)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_L=(Action=PanelAlch)
IK_M=(Action=PanelMapPC)
IK_LeftMouse=(Action=VehicleAttack)
IK_N=(Action=PanelMeditation)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_P=(Action=DebugInput)
IK_NumPad2=(Action=Debug_KillTarget)
IK_Enter=(Action=HubMenu)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_Alt=(Action=HorseKick,State=Duration,IdleTime=0.3)
IK_MouseY=(Action=GI_MouseDampY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_LControl=(Action=WalkToggle)
IK_X=(Action=Stop)
IK_Z=(Action=CameraLock)

[InputSettings]
Version=55

[JumpClimb]
IK_Tab=(Action=RadialMenu)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_LShift=(Action=Sprint)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_1=(Action=SteelSword)
IK_1=(Action=SwordSheatheSteel,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=SilverSword)
IK_2=(Action=SwordSheatheSilver,State=Duration,IdleTime=0.3)
IK_Pad_X_SQUARE=(Action=DiveDown)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SilverSword)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=Jump)
IK_Pad_B_CIRCLE=(Action=ExplorationInteraction)
IK_Pad_B_CIRCLE=(Action=Roll)
IK_Space=(Action=Jump)
IK_Space=(Action=ExplorationInteraction)
IK_Space=(Action=Roll)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_C=(Action=DiveDown)
IK_C=(Action=SwordSheathe)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SteelSword)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_G=(Action=GotoGlossary)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_Pad_A_CROSS=(Action=Sprint)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_O=(Action=PanelFakeHud)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_P=(Action=DebugInput)
IK_NumPad2=(Action=Debug_KillTarget)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_None=(Action=SprintToggle)
IK_MouseY=(Action=GI_MouseDampY)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_MouseZ=(Action=ToggleSigns)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)

[LootPopup]

[Meditation]
IK_Space=(Action=MeditationAbort)
IK_Pad_B_CIRCLE=(Action=MeditationAbort)

[Photomode]
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_Pad_RightAxisX=(Action=PMC_RotX)
IK_A=(Action=PMC_MoveLeft)
IK_Pad_RightAxisY=(Action=PMC_RotY)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_D=(Action=PMC_MoveRight)
IK_LeftMouse=(Action=PMC_MouseMovement)
IK_Pad_LeftTrigger=(Action=PMC_MoveUp)
IK_Pad_B_CIRCLE=(Action=DisablePhotoMode)
IK_Escape=(Action=DisablePhotoMode)
IK_RightMouse=(Action=PMC_MouseRotation)
IK_Pad_LeftAxisX=(Action=PMC_MoveRight)
IK_Pad_LeftAxisY=(Action=PMC_MoveForward)
IK_S=(Action=PMC_MoveBackward)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_MouseY=(Action=GI_MouseDampY)
IK_MouseZ=(Action=PMC_MoveForward)
IK_W=(Action=PMC_MoveForward)
IK_Pad_RightTrigger=(Action=PMC_MoveDown)

[RadialMenu]
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_MiddleMouse=(Action=OpenMeditation)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_Space=(Action=OpenMeditation)
IK_RightMouse=(Action=LockAndGuard)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_Tab=(Action=RadialMenu)
IK_Pad_LeftTrigger=(Action=Alternate)
IK_Pad_LeftTrigger=(Action=LockAndGuard)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_X_SQUARE=(Action=OpenMeditation)
IK_Pad_A_CROSS=(Action=ConfirmRadialMenuSelection)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_Pad_B_CIRCLE=(Action=CloseRadialMenu)

[SCENE_IS_STARTING_HACK]
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_U=(Action=EnablePhotoMode)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_MouseX=(Action=GI_MouseDampX)
IK_MouseY=(Action=GI_MouseDampY)
IK_LShift=(Action=Sprint)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_None=(Action=SprintToggle)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)

[Scene]
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_U=(Action=EnablePhotoMode)
IK_Pad_DigitDown=(Action=ChangeChoiceUp)
IK_W=(Action=ChangeChoiceUp)
IK_MouseX=(Action=GI_MouseDampX)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_MouseY=(Action=GI_MouseDampY)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_Home=(Action=ToggleHud)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Escape=(Action=IngameMenu)
IK_Pad_LeftAxisY=(Action=ChangeChoiceAxis)
IK_Pad_DigitUp=(Action=ChangeChoiceDown)
IK_Pad_LeftShoulder=(Action=SCN_DBG_RestartSection)
IK_Pad_LeftShoulder=(Action=SCN_DBG_RestartScene,State=Duration,IdleTime=0.3)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_S=(Action=ChangeChoiceDown)

[ScriptedAction]
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_A_CROSS=(Action=Gallop,State=Duration,IdleTime=0.3)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_F1=(Action=OnShowControlsHelp)
IK_Enter=(Action=HubMenu)
IK_LShift=(Action=Sprint)
IK_LShift=(Action=Gallop,State=Duration,IdleTime=0.2)
IK_None=(Action=SprintToggle)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_B=(Action=PanelBestiary)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_G=(Action=GotoGlossary)
IK_G=(Action=PanelGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_I=(Action=PanelInv)
IK_J=(Action=PanelJour)
IK_Escape=(Action=IngameMenu)
IK_K=(Action=PanelChar)
IK_L=(Action=PanelAlch)
IK_M=(Action=PanelMapPC)
IK_N=(Action=PanelMeditation)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_O=(Action=PanelCrafting)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_Home=(Action=ToggleHud)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_MouseY=(Action=GI_MouseDampY)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)

[Swimming]
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_A_CROSS=(Action=PickOilLamp)
IK_Pad_A_CROSS=(Action=PlaceOilLamp)
IK_Pad_A_CROSS=(Action=PlaceCrystal)
IK_Pad_A_CROSS=(Action=SitDown)
IK_Pad_A_CROSS=(Action=Knock)
IK_Pad_A_CROSS=(Action=Unequip)
IK_Pad_A_CROSS=(Action=FastTravel)
IK_Pad_A_CROSS=(Action=GatherHerbs)
IK_Pad_A_CROSS=(Action=Examine)
IK_Pad_A_CROSS=(Action=EnterBoat)
IK_Pad_A_CROSS=(Action=EnterBoatFromSwimming)
IK_Pad_A_CROSS=(Action=MountHorse)
IK_Pad_A_CROSS=(Action=Talk)
IK_Pad_A_CROSS=(Action=Stash)
IK_Pad_A_CROSS=(Action=Container)
IK_Pad_A_CROSS=(Action=Interact)
IK_Pad_A_CROSS=(Action=InteractHold,State=Duration,IdleTime=0.2)
IK_Pad_A_CROSS=(Action=Use)
IK_Pad_A_CROSS=(Action=UseDevice)
IK_Pad_A_CROSS=(Action=Open)
IK_Pad_A_CROSS=(Action=Close)
IK_Pad_A_CROSS=(Action=Lock)
IK_Pad_A_CROSS=(Action=Unlock)
IK_Pad_A_CROSS=(Action=Take)
IK_Pad_A_CROSS=(Action=Push)
IK_Pad_A_CROSS=(Action=Pull)
IK_Pad_A_CROSS=(Action=Locked)
IK_Pad_A_CROSS=(Action=Destroy)
IK_Pad_A_CROSS=(Action=PrayForSun)
IK_Pad_A_CROSS=(Action=PrayForStorm)
IK_Pad_A_CROSS=(Action=Arm)
IK_Pad_A_CROSS=(Action=Disarm)
IK_Pad_A_CROSS=(Action=GatherBrushwood)
IK_Pad_A_CROSS=(Action=PlaceHerbs)
IK_Pad_A_CROSS=(Action=Drink)
IK_Pad_A_CROSS=(Action=PlaceOffering)
IK_Pad_A_CROSS=(Action=Grab)
IK_Pad_A_CROSS=(Action=Free)
IK_Pad_A_CROSS=(Action=Ignite)
IK_Pad_A_CROSS=(Action=UnblockGate)
IK_Pad_A_CROSS=(Action=UseItem)
IK_Pad_A_CROSS=(Action=Read)
IK_Pad_A_CROSS=(Action=PullAxe)
IK_Pad_A_CROSS=(Action=Interaction)
IK_Pad_A_CROSS=(Action=Extinguish)
IK_Pad_A_CROSS=(Action=CallJohnny)
IK_Pad_A_CROSS=(Action=HideBible)
IK_Pad_A_CROSS=(Action=SitAndWait)
IK_Pad_A_CROSS=(Action=PlaceBottle)
IK_Pad_A_CROSS=(Action=WineSlot)
IK_Pad_A_CROSS=(Action=BurnBody)
IK_Pad_A_CROSS=(Action=PutBack)
IK_Pad_A_CROSS=(Action=Debung)
IK_Pad_A_CROSS=(Action=Touch)
IK_Pad_A_CROSS=(Action=CutRope)
IK_Pad_A_CROSS=(Action=KneelDown)
IK_Pad_A_CROSS=(Action=PlaceTribute)
IK_Pad_A_CROSS=(Action=HangPainting)
IK_Pad_A_CROSS=(Action=PlaceArmor)
IK_Pad_A_CROSS=(Action=PlaceSword)
IK_Pad_A_CROSS=(Action=GiveAlms)
IK_Pad_A_CROSS=(Action=PlaceLure)
IK_Pad_A_CROSS=(Action=PlaceBeans)
IK_Pad_A_CROSS=(Action=Look)
IK_Pad_A_CROSS=(Action=TakePaintGreen)
IK_Pad_A_CROSS=(Action=TakePaintBlue)
IK_Pad_A_CROSS=(Action=TakePaintRed)
IK_Pad_A_CROSS=(Action=TakePaintYellow)
IK_Pad_A_CROSS=(Action=TakePaintPurple)
IK_Pad_A_CROSS=(Action=DisposePaint)
IK_Pad_A_CROSS=(Action=HideIn)
IK_Pad_A_CROSS=(Action=PlaceTrophy)
IK_Pad_A_CROSS=(Action=BuryBody)
IK_Pad_LeftShoulder=(Action=RadialMenu)
IK_None=(Action=SprintToggle)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_PS4_OPTIONS=(Action=IngameMenu)
IK_PS4_OPTIONS=(Action=ShowEntryInPanel)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_Escape=(Action=IngameMenu)
IK_Escape=(Action=ShowEntryInPanel)
IK_Home=(Action=ToggleHud)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_Enter=(Action=HubMenu)
IK_Pad_RightTrigger=(Action=Spare)
IK_PS4_TOUCH_PRESS=(Action=FastMenu)
IK_PS4_TOUCH_PRESS=(Action=HoldFastMenu,State=Duration,IdleTime=0.3)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_B=(Action=PanelBestiary)
IK_C=(Action=DiveDown)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_E=(Action=PickOilLamp)
IK_E=(Action=PlaceOilLamp)
IK_E=(Action=PlaceCrystal)
IK_E=(Action=SitDown)
IK_E=(Action=Knock)
IK_E=(Action=Spare)
IK_E=(Action=Unequip)
IK_E=(Action=FastTravel)
IK_E=(Action=GatherHerbs)
IK_E=(Action=Examine)
IK_E=(Action=EnterBoat)
IK_E=(Action=EnterBoatFromSwimming)
IK_E=(Action=MountHorse)
IK_E=(Action=Talk)
IK_E=(Action=Stash)
IK_E=(Action=Container)
IK_E=(Action=Interact)
IK_E=(Action=InteractHold,State=Duration,IdleTime=0.1)
IK_E=(Action=Use)
IK_E=(Action=UseDevice)
IK_E=(Action=Finish)
IK_E=(Action=Open)
IK_E=(Action=Close)
IK_E=(Action=Lock)
IK_E=(Action=Unlock)
IK_E=(Action=Take)
IK_E=(Action=Push)
IK_E=(Action=Pull)
IK_E=(Action=Locked)
IK_E=(Action=Destroy)
IK_E=(Action=PrayForSun)
IK_E=(Action=PrayForStorm)
IK_E=(Action=Arm)
IK_E=(Action=Disarm)
IK_E=(Action=GatherBrushwood)
IK_E=(Action=PlaceHerbs)
IK_E=(Action=Drink)
IK_E=(Action=PlaceOffering)
IK_E=(Action=Grab)
IK_E=(Action=Free)
IK_E=(Action=Ignite)
IK_E=(Action=UnblockGate)
IK_E=(Action=UseItem)
IK_E=(Action=Read)
IK_E=(Action=PullAxe)
IK_E=(Action=Interaction)
IK_E=(Action=Extinguish)
IK_E=(Action=CallJohnny)
IK_E=(Action=HideBible)
IK_E=(Action=SitAndWait)
IK_E=(Action=PlaceBottle)
IK_E=(Action=WineSlot)
IK_E=(Action=BurnBody)
IK_E=(Action=PutBack)
IK_E=(Action=Debung)
IK_E=(Action=Touch)
IK_E=(Action=CutRope)
IK_E=(Action=KneelDown)
IK_E=(Action=PlaceTribute)
IK_E=(Action=HangPainting)
IK_E=(Action=PlaceArmor)
IK_E=(Action=PlaceSword)
IK_E=(Action=GiveAlms)
IK_E=(Action=PlaceLure)
IK_E=(Action=PlaceBeans)
IK_E=(Action=Look)
IK_E=(Action=TakePaintGreen)
IK_E=(Action=TakePaintBlue)
IK_E=(Action=TakePaintRed)
IK_E=(Action=TakePaintYellow)
IK_E=(Action=TakePaintPurple)
IK_E=(Action=DisposePaint)
IK_E=(Action=HideIn)
IK_E=(Action=PlaceTrophy)
IK_E=(Action=BuryBody)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_LShift=(Action=Sprint)
IK_G=(Action=PanelGlossary)
IK_G=(Action=GotoGlossary)
IK_H=(Action=PanelGwintDeckEditor)
IK_I=(Action=PanelInv)
IK_Tab=(Action=RadialMenu)
IK_J=(Action=PanelJour)
IK_K=(Action=PanelChar)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_L=(Action=PanelAlch)
IK_NumPad2=(Action=Debug_KillTarget)
IK_M=(Action=PanelMapPC)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_Pad_B_CIRCLE=(Action=DiveUp)
IK_Pad_B_CIRCLE=(Action=ExplorationInteraction)
IK_N=(Action=PanelMeditation)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_O=(Action=PanelCrafting)
IK_O=(Action=PanelFakeHud)
IK_Backspace=(Action=FastMenu)
IK_Backspace=(Action=HoldFastMenu,State=Duration,IdleTime=0.2)
IK_P=(Action=DebugInput)
IK_Pad_Back_Select=(Action=GotoGlossary,State=Duration,IdleTime=1.2)
IK_Pad_Back_Select=(Action=IngameMenu)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_U=(Action=EnablePhotoMode)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_V=(Action=HighlightObjective)
IK_V=(Action=TrackQuest)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Pad_Start=(Action=PanelMap,State=Duration,IdleTime=0.5)
IK_Pad_Start=(Action=FastMenu)
IK_Pad_Start=(Action=ShowEntryInPanel)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_Z=(Action=CameraLock)
IK_LeftMouse=(Action=Finish)
IK_LeftMouse=(Action=Finisher)
IK_RightMouse=(Action=Focus,Reprocess)
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_Pad_RightShoulder=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_Pad_RightThumb=(Action=CameraLock)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_RightThumb=(Action=HighlightObjective)
IK_Pad_RightThumb=(Action=TrackQuest)
IK_Pad_LeftTrigger=(Action=Focus,Reprocess)
IK_F1=(Action=OnShowControlsHelp)
IK_Pad_X_SQUARE=(Action=DiveDown)
IK_Pad_X_SQUARE=(Action=Finish)
IK_Pad_X_SQUARE=(Action=Finisher)
IK_Space=(Action=DiveUp)
IK_Space=(Action=ExplorationInteraction)
IK_MouseX=(Action=GI_MouseDampX)
IK_MouseY=(Action=GI_MouseDampY)
IK_MouseZ=(Action=ToggleSigns)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_PrintScrn=(Action=ScreenshotRequested)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_MiddleMouse=(Action=ThrowItem)
IK_MiddleMouse=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)

[ThrowHold]
IK_Pad_A_CROSS=(Action=Sprint)
IK_Pad_LeftThumb=(Action=EnablePhotoMode_Step1)
IK_Pad_LeftThumb=(Action=SprintToggle)
IK_NumPad1=(Action=Debug_KillAllEnemies)
IK_Pad_DigitDown=(Action=DrinkPotion2)
IK_Pad_DigitDown=(Action=DrinkPotionLowerHold,State=Duration,IdleTime=0.3)
IK_NumPad2=(Action=Debug_KillTarget)
IK_NumPad4=(Action=Debug_TeleportToPin)
IK_LShift=(Action=Sprint)
IK_None=(Action=SprintToggle)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_A=(Action=GI_AxisLeftX,State=Axis,Value=-1)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisX=(Action=GI_AxisLeftX)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_Pad_LeftAxisY=(Action=GI_AxisLeftY)
IK_C=(Action=SwordSheathe)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_D=(Action=GI_AxisLeftX,State=Axis,Value=1)
IK_Pad_RightThumb=(Action=EnablePhotoMode_Step2)
IK_Pad_DigitLeft=(Action=OilSteel,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitLeft=(Action=SteelSword)
IK_F=(Action=DrinkPotion2)
IK_F=(Action=DrinkPotion2Hold,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SwordSheathe,State=Duration,IdleTime=0.3)
IK_Pad_DigitRight=(Action=SilverSword)
IK_Pad_DigitRight=(Action=OilSilver,State=Duration,IdleTime=0.3)
IK_Pad_B_CIRCLE=(Action=ThrowCastAbort)
IK_Pad_B_CIRCLE=(Action=DebugInput)
IK_MiddleMouse=(Action=ThrowItem)
IK_MiddleMouse=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_Space=(Action=ThrowCastAbort)
IK_Pad_RightAxisX=(Action=GI_AxisRightX)
IK_O=(Action=PanelFakeHud)
IK_Pad_RightAxisY=(Action=GI_AxisRightY)
IK_P=(Action=DebugInput)
IK_Pad_RightShoulder=(Action=ThrowItem)
IK_Pad_RightShoulder=(Action=ThrowItemHold,State=Duration,IdleTime=0.2)
IK_R=(Action=DrinkPotion1)
IK_R=(Action=DrinkPotion1Hold,State=Duration,IdleTime=0.3)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_S=(Action=GI_AxisLeftY,State=Axis,Value=-1)
IK_T=(Action=DrinkPotion3)
IK_T=(Action=DrinkPotion3Hold,State=Duration,IdleTime=0.3)
IK_MouseX=(Action=GI_MouseDampX)
IK_U=(Action=EnablePhotoMode)
IK_MouseY=(Action=GI_MouseDampY)
IK_Pad_DigitUp=(Action=DrinkPotion1)
IK_Pad_DigitUp=(Action=DrinkPotionUpperHold,State=Duration,IdleTime=0.3)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_W=(Action=GI_AxisLeftY,State=Axis,Value=1)
IK_Y=(Action=DrinkPotion4)
IK_Y=(Action=DrinkPotion4Hold,State=Duration,IdleTime=0.3)
IK_1=(Action=SteelSword)
IK_1=(Action=SwordSheatheSteel,State=Duration,IdleTime=0.3)
IK_1=(Action=OilSteelKB,State=Duration,IdleTime=0.3)
IK_2=(Action=OilSilverKB,State=Duration,IdleTime=0.3)
IK_2=(Action=SilverSword)
IK_2=(Action=SwordSheatheSilver,State=Duration,IdleTime=0.3)

[TutorialPopup]

