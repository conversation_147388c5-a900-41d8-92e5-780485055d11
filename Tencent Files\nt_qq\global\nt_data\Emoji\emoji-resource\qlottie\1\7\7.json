{"v": "5.7.8", "fr": 60, "ip": 0, "op": 300, "w": 512, "h": 512, "nm": "新狗头", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 3, "ty": 4, "nm": "耳洞左", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [100]}, {"t": 225, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [210.035, 123.4, 0], "to": [0, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [210.035, 121.4, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [210.035, 123.4, 0], "to": [0, 0, 0], "ti": [0, 0.333, 0]}, {"t": 224, "s": [210.035, 121.4, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-45.965, -134.181, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 102, "s": [{"i": [[-2.332, 4.508], [-8.967, 2.907], [1.101, -3.605]], "o": [[1.987, -3.842], [3.957, -1.283], [-0.987, 3.231]], "v": [[-64.987, -109.739], [-45.033, -120.488], [-29.763, -122.063]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 8.179], [-16.128, -2.282], [5.559, -4.878]], "o": [[0, -8.179], [16.128, 2.282], [-5.559, 4.878]], "v": [[-65.987, -120.989], [-44.283, -153.863], [-28.013, -117.688]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 121, "s": [{"i": [[0, 8.179], [-14.467, -2.055], [5.559, -4.878]], "o": [[0, -8.179], [16.127, 2.291], [-5.559, 4.878]], "v": [[-65.987, -120.989], [-43.783, -145.113], [-28.013, -117.688]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 129, "s": [{"i": [[0, 8.179], [-16.128, -2.282], [5.559, -4.878]], "o": [[0, -8.179], [16.128, 2.282], [-5.559, 4.878]], "v": [[-65.987, -120.989], [-44.283, -153.863], [-28.013, -117.688]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 135, "s": [{"i": [[0, 8.179], [-16.128, -2.282], [5.559, -4.878]], "o": [[0, -8.179], [16.128, 2.282], [-5.559, 4.878]], "v": [[-65.987, -120.989], [-46.283, -151.613], [-28.013, -117.688]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 141, "s": [{"i": [[0, 8.179], [-16.128, -2.282], [5.559, -4.878]], "o": [[0, -8.179], [16.128, 2.282], [-5.559, 4.878]], "v": [[-65.987, -120.989], [-44.283, -153.863], [-28.013, -117.688]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 213, "s": [{"i": [[0, 8.179], [-16.128, -2.282], [5.559, -4.878]], "o": [[0, -8.179], [16.128, 2.282], [-5.559, 4.878]], "v": [[-65.987, -120.989], [-44.283, -153.863], [-28.013, -117.688]], "c": true}]}, {"t": 224, "s": [{"i": [[-2.332, 4.508], [-8.967, 2.907], [1.101, -3.605]], "o": [[1.987, -3.842], [3.957, -1.283], [-0.987, 3.231]], "v": [[-64.987, -109.739], [-45.033, -120.488], [-29.763, -122.063]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.658823549747, 0.243137255311, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "耳洞左", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "耳洞右", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [100]}, {"t": 225, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [306.66, 131.373, 0], "to": [0, -0.167, 0], "ti": [0, 0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [306.66, 130.373, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [306.66, 130.373, 0], "to": [0, 0.167, 0], "ti": [0, -0.167, 0]}, {"t": 224, "s": [306.66, 131.373, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50.66, -125.208, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 102, "s": [{"i": [[-0.55, 3.207], [-7.38, -3], [-3.002, -3.32]], "o": [[0.643, -3.75], [13.788, 5.605], [-10.252, -0.07]], "v": [[30.175, -118.832], [48.88, -114.332], [67.752, -103.305]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 10.302], [-16.128, -2.282], [8.485, -4.878]], "o": [[0, -10.302], [16.128, 2.282], [-8.485, 4.878]], "v": [[30.175, -118.832], [51.88, -148.207], [67.752, -103.305]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 121, "s": [{"i": [[0, 10.302], [-16.128, -2.282], [8.485, -4.878]], "o": [[0, -10.302], [16.128, 2.282], [-8.485, 4.878]], "v": [[30.175, -118.832], [54.38, -138.832], [67.752, -103.305]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 129, "s": [{"i": [[0, 10.302], [-16.128, -2.282], [8.485, -4.878]], "o": [[0, -10.302], [16.128, 2.282], [-8.485, 4.878]], "v": [[30.175, -118.832], [51.88, -148.207], [67.752, -103.305]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 135, "s": [{"i": [[0, 10.302], [-16.128, -2.282], [8.485, -4.878]], "o": [[0, -10.302], [16.128, 2.282], [-8.485, 4.878]], "v": [[30.175, -118.832], [50.13, -146.332], [67.752, -103.305]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 141, "s": [{"i": [[0, 10.302], [-16.128, -2.282], [8.485, -4.878]], "o": [[0, -10.302], [16.128, 2.282], [-8.485, 4.878]], "v": [[30.175, -118.832], [51.88, -148.207], [67.752, -103.305]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 213, "s": [{"i": [[0, 10.302], [-16.128, -2.282], [8.485, -4.878]], "o": [[0, -10.302], [16.128, 2.282], [-8.485, 4.878]], "v": [[30.175, -118.832], [51.88, -148.207], [67.752, -103.305]], "c": true}]}, {"t": 224, "s": [{"i": [[-0.55, 3.207], [-7.38, -3], [-3.002, -3.32]], "o": [[0.643, -3.75], [13.788, 5.605], [-10.252, -0.07]], "v": [[30.175, -118.832], [48.88, -114.332], [67.752, -103.305]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.658823549747, 0.243137255311, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "耳洞右", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "右眼 3", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 99, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [53.117, -37.807, 0], "to": [-0.285, 0.227, 0], "ti": [0.675, -0.61, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [52.626, -34.499, 0], "to": [-3.254, 2.94, 0], "ti": [0.675, -0.61, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [52.626, -34.499, 0], "to": [-3.254, 2.94, 0], "ti": [1.661, -1.318, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [53.117, -37.807, 0], "to": [-0.285, 0.227, 0], "ti": [0.675, -0.61, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [52.626, -34.499, 0], "to": [-3.254, 2.94, 0], "ti": [0.675, -0.61, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [52.626, -34.499, 0], "to": [-3.254, 2.94, 0], "ti": [1.661, -1.318, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [53.117, -37.807, 0], "to": [-0.285, 0.227, 0], "ti": [-1.385, 2.497, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [43.151, -29.896, 0], "to": [1.672, -3.015, 0], "ti": [-5.667, 4.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [63.151, -55.896, 0], "to": [5.667, -4.333, 0], "ti": [-8, 2, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [77.151, -55.896, 0], "to": [4.602, -1.151, 0], "ti": [-7.368, 2.61, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [96.868, -61.966, 0], "to": [3.403, -1.206, 0], "ti": [4.469, 10.188, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [85.237, -70.594, 0], "to": [-2.706, -6.169, 0], "ti": [-16.427, 5.352, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [100.441, -96.513, 0], "to": [16.427, -5.352, 0], "ti": [0.132, 0.137, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [128.151, -82.896, 0], "to": [0, 0, 0], "ti": [-0.097, 0.17, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [128.151, -82.896, 0], "to": [-2.091, 0.674, 0], "ti": [16.427, -5.352, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [100.441, -96.513, 0], "to": [-16.427, 5.352, 0], "ti": [-2.706, -6.169, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [85.237, -70.594, 0], "to": [4.469, 10.188, 0], "ti": [3.403, -1.206, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [96.868, -61.966, 0], "to": [-7.368, 2.61, 0], "ti": [4.602, -1.151, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [77.151, -55.896, 0], "to": [-8, 2, 0], "ti": [5.667, -4.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [63.151, -55.896, 0], "to": [-5.667, 4.333, 0], "ti": [1.672, -3.015, 0]}, {"t": 268, "s": [43.151, -29.896, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [74.311, -23.93, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23, "s": [18, 18, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 28, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 38, "s": [18, 18, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 43, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 48, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 53, "s": [18, 18, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [18, 18, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 89, "s": [38, 38, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 95, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 131, "s": [104, 104, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 195, "s": [104, 104, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 231, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 237, "s": [38, 38, 100]}, {"t": 268, "s": [18, 18, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-12.496, 0], [0, 12.496], [12.496, 0], [0, -12.496]], "o": [[12.496, 0], [0, -12.496], [-12.496, 0], [0, 12.496]], "v": [[74.311, -1.304], [96.938, -23.93], [74.311, -46.557], [51.685, -23.93]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.4, 0.184, 0.039, 0.7, 0.384, 0.178, 0.031, 0.987, 0.369, 0.173, 0.024], "ix": 9}}, "s": {"a": 0, "k": [51, -23], "ix": 5}, "e": {"a": 0, "k": [96.253, -23], "ix": 6}, "t": 1, "nm": "Gradient 17", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "右眼", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 26, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "左眼 3", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [-35.4, -42.772, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [-33.27, -40.248, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [-33.27, -40.248, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [-35.4, -42.772, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [-33.27, -40.248, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [-33.27, -40.248, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [-35.4, -42.772, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [-26.4, -34.772, 0], "to": [-0.153, -1.021, 0], "ti": [-0.257, 2.255, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [-34.136, -44.945, 0], "to": [0.582, -5.107, 0], "ti": [0.925, 2.775, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [-26.041, -56.871, 0], "to": [-1.333, -4, 0], "ti": [1.5, 2, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [-34.4, -58.772, 0], "to": [-0.31, -0.413, 0], "ti": [-0.055, 1.983, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [-34.724, -63.339, 0], "to": [0.015, -0.551, 0], "ti": [5.408, 8.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [-56.543, -68.77, 0], "to": [-5.408, -8.112, 0], "ti": [-16.994, 8.445, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [-46.919, -95.817, 0], "to": [16.994, -8.445, 0], "ti": [-0.972, 1.446, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [-20.025, -83.772, 0], "to": [0.252, -0.114, 0], "ti": [0.097, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [-20.025, -83.772, 0], "to": [-0.972, 1.446, 0], "ti": [16.994, -8.445, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [-46.919, -95.817, 0], "to": [-16.994, 8.445, 0], "ti": [-5.408, -8.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [-56.543, -68.77, 0], "to": [5.408, 8.112, 0], "ti": [0.015, -0.551, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [-34.724, -63.339, 0], "to": [-0.055, 1.983, 0], "ti": [-0.31, -0.413, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [-34.4, -58.772, 0], "to": [1.5, 2, 0], "ti": [-1.333, -4, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [-26.041, -56.871, 0], "to": [0.925, 2.775, 0], "ti": [0.582, -5.107, 0]}, {"t": 260, "s": [-34.136, -44.945, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-77.301, -23.93, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23, "s": [17, 17, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 28, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 38, "s": [17, 17, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 43, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 48, "s": [48, 48, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 53, "s": [17, 17, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [17, 17, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 89, "s": [39, 39, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 95, "s": [56, 56, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 131, "s": [104, 104, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 195, "s": [104, 104, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 231, "s": [56, 56, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 237, "s": [39, 39, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 256, "s": [32, 32, 100]}, {"t": 260, "s": [2, 2, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-12.496, 0], [0, 12.496], [12.496, 0], [0, -12.496]], "o": [[12.496, 0], [0, -12.496], [-12.496, 0], [0, 12.496]], "v": [[-77.958, -4.066], [-55.332, -26.693], [-77.958, -49.319], [-100.585, -26.693]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.4, 0.184, 0.039, 0.7, 0.384, 0.178, 0.031, 0.987, 0.369, 0.173, 0.024], "ix": 9}}, "s": {"a": 0, "k": [-57, -33], "ix": 5}, "e": {"a": 0, "k": [-97.928, -12.872], "ix": 6}, "t": 1, "nm": "Gradient 18", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "左眼", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 26, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "左眼", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 99, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-42.953, -41.283, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-42.953, -41.283, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[-6.899, -8.809], [6.481, 0.725], [0, 0], [-6.552, -0.815]], "o": [[-7.181, -9.17], [-6.481, -0.725], [0, 0], [6.552, 0.815]], "v": [[-26.4, -34.772], [-48.34, -47.685], [-59.507, -46.843], [-48.128, -47.415]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[0, -10.641], [10.641, 0], [0, 10.641], [-10.641, 0]], "o": [[0, 10.641], [-10.641, 0], [0, -10.641], [10.641, 0]], "v": [[-21.709, -44.497], [-40.976, -25.23], [-60.243, -44.497], [-40.976, -63.764]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 33, "s": [{"i": [[0, -10.641], [10.641, 0], [0, 10.641], [-10.641, 0]], "o": [[0, 10.641], [-10.641, 0], [0, -10.641], [10.641, 0]], "v": [[-21.709, -44.497], [-40.976, -25.23], [-60.243, -44.497], [-40.976, -63.764]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [{"i": [[-6.899, -8.809], [6.481, 0.725], [0, 0], [-6.552, -0.815]], "o": [[-7.181, -9.17], [-6.481, -0.725], [0, 0], [6.552, 0.815]], "v": [[-26.4, -34.772], [-48.34, -47.685], [-59.507, -46.843], [-48.128, -47.415]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [{"i": [[0, -10.641], [10.641, 0], [0, 10.641], [-10.641, 0]], "o": [[0, 10.641], [-10.641, 0], [0, -10.641], [10.641, 0]], "v": [[-21.709, -44.497], [-40.976, -25.23], [-60.243, -44.497], [-40.976, -63.764]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [{"i": [[0, -10.641], [10.641, 0], [0, 10.641], [-10.641, 0]], "o": [[0, 10.641], [-10.641, 0], [0, -10.641], [10.641, 0]], "v": [[-21.709, -44.497], [-40.976, -25.23], [-60.243, -44.497], [-40.976, -63.764]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 53, "s": [{"i": [[-6.899, -8.809], [6.481, 0.725], [0, 0], [-6.552, -0.815]], "o": [[-7.181, -9.17], [-6.481, -0.725], [0, 0], [6.552, 0.815]], "v": [[-26.4, -34.772], [-48.34, -47.685], [-59.507, -46.843], [-48.128, -47.415]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [{"i": [[-6.899, -8.809], [6.481, 0.725], [0, 0], [-6.552, -0.815]], "o": [[-7.181, -9.17], [-6.481, -0.725], [0, 0], [6.552, 0.815]], "v": [[-26.4, -34.772], [-48.34, -47.685], [-59.507, -46.843], [-48.128, -47.415]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 89, "s": [{"i": [[-6.165, 2.625], [4.114, 9.664], [6.165, -2.625], [-4.114, -9.664]], "o": [[6.165, -2.625], [-4.114, -9.664], [-6.165, 2.625], [4.114, 9.664]], "v": [[-22.446, -49.729], [-18.733, -71.98], [-37.346, -84.727], [-41.059, -62.475]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 95, "s": [{"i": [[-10.11, -4.021], [-3.452, 14.455], [10.11, 4.021], [3.452, -14.455]], "o": [[10.11, 4.021], [3.452, -14.455], [-10.11, -4.021], [-3.452, 14.455]], "v": [[-42.971, -44.438], [-18.415, -63.33], [-30.471, -96.785], [-55.027, -77.893]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[-21.342, 0], [0, 21.342], [21.342, 0], [0, -21.342]], "o": [[21.342, 0], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[-39.927, -40.766], [-1.283, -79.41], [-39.927, -118.054], [-78.571, -79.41]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [{"i": [[-21.342, 0], [0, 21.342], [21.342, 0], [0, -21.342]], "o": [[21.342, 0], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[-39.927, -40.766], [-1.283, -79.41], [-39.927, -118.054], [-78.571, -79.41]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 134, "s": [{"i": [[-20.759, 0], [0, 21.064], [19.277, 0], [0, -21.064]], "o": [[20.759, 0], [0, -21.064], [-19.277, 0], [0, 21.064]], "v": [[-37.083, -63.893], [2.513, -78.618], [-37.083, -115.786], [-76.679, -78.618]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 192, "s": [{"i": [[-20.759, 0], [0, 21.064], [19.277, 0], [0, -21.064]], "o": [[20.759, 0], [0, -21.064], [-19.277, 0], [0, 21.064]], "v": [[-37.083, -63.893], [2.513, -78.618], [-37.083, -115.786], [-76.679, -78.618]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 203, "s": [{"i": [[-21.342, 0], [0, 21.342], [21.342, 0], [0, -21.342]], "o": [[21.342, 0], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[-39.927, -40.766], [-1.283, -79.41], [-39.927, -118.054], [-78.571, -79.41]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [{"i": [[-21.342, 0], [0, 21.342], [21.342, 0], [0, -21.342]], "o": [[21.342, 0], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[-39.927, -40.766], [-1.283, -79.41], [-39.927, -118.054], [-78.571, -79.41]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 231, "s": [{"i": [[-10.11, -4.021], [-3.452, 14.455], [10.11, 4.021], [3.452, -14.455]], "o": [[10.11, 4.021], [3.452, -14.455], [-10.11, -4.021], [-3.452, 14.455]], "v": [[-42.971, -44.438], [-18.415, -63.33], [-30.471, -96.785], [-55.027, -77.893]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 237, "s": [{"i": [[-6.165, 2.625], [4.114, 9.664], [6.165, -2.625], [-4.114, -9.664]], "o": [[6.165, -2.625], [-4.114, -9.664], [-6.165, 2.625], [4.114, 9.664]], "v": [[-22.446, -49.729], [-18.733, -71.98], [-37.346, -84.727], [-41.059, -62.475]], "c": true}]}, {"t": 268, "s": [{"i": [[-6.899, -8.809], [6.481, 0.725], [0, 0], [-6.552, -0.815]], "o": [[-7.181, -9.17], [-6.481, -0.725], [0, 0], [6.552, 0.815]], "v": [[-26.4, -34.772], [-48.34, -47.685], [-59.507, -46.843], [-48.128, -47.415]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235300779, 0.164705887437, 0.164705887437, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "右眼", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [61.024, -35.686, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [60.696, -35.348, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[5.045, -5.597], [-6.618, 3.228], [-2.629, 0.54], [-7.122, -2.383], [5.146, -0.855]], "o": [[0, 0], [2.021, -0.986], [4.971, -1.02], [-6.258, -1.882], [-6.976, 1.159]], "v": [[43.151, -29.896], [53.117, -37.807], [60.095, -40.183], [78.242, -38.73], [61.149, -40.201]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[0, -10.641], [-3.487, -3.487], [-5.32, 0], [0, 10.641], [10.641, 0]], "o": [[0, 5.321], [3.487, 3.487], [10.641, 0], [0, -10.641], [-10.641, 0]], "v": [[40.135, -38.73], [45.778, -25.106], [59.402, -19.463], [78.669, -38.73], [59.402, -57.997]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 33, "s": [{"i": [[0, -10.641], [-3.487, -3.487], [-5.32, 0], [0, 10.641], [10.641, 0]], "o": [[0, 5.321], [3.487, 3.487], [10.641, 0], [0, -10.641], [-10.641, 0]], "v": [[40.135, -38.73], [45.778, -25.106], [59.402, -19.463], [78.669, -38.73], [59.402, -57.997]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [{"i": [[5.045, -5.597], [-6.618, 3.228], [-2.629, 0.54], [-7.122, -2.383], [5.146, -0.855]], "o": [[0, 0], [2.021, -0.986], [4.971, -1.02], [-6.258, -1.882], [-6.976, 1.159]], "v": [[43.151, -29.896], [53.117, -37.807], [60.095, -40.183], [78.242, -38.73], [61.149, -40.201]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [{"i": [[0, -10.641], [-3.487, -3.487], [-5.32, 0], [0, 10.641], [10.641, 0]], "o": [[0, 5.321], [3.487, 3.487], [10.641, 0], [0, -10.641], [-10.641, 0]], "v": [[40.135, -38.73], [45.778, -25.106], [59.402, -19.463], [78.669, -38.73], [59.402, -57.997]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [{"i": [[0, -10.641], [-3.487, -3.487], [-5.32, 0], [0, 10.641], [10.641, 0]], "o": [[0, 5.321], [3.487, 3.487], [10.641, 0], [0, -10.641], [-10.641, 0]], "v": [[40.135, -38.73], [45.778, -25.106], [59.402, -19.463], [78.669, -38.73], [59.402, -57.997]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 53, "s": [{"i": [[5.045, -5.597], [-6.618, 3.228], [-2.629, 0.54], [-7.122, -2.383], [5.146, -0.855]], "o": [[0, 0], [2.021, -0.986], [4.971, -1.02], [-6.258, -1.882], [-6.976, 1.159]], "v": [[43.151, -29.896], [53.117, -37.807], [60.095, -40.183], [78.242, -38.73], [61.149, -40.201]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [{"i": [[5.045, -5.597], [-6.618, 3.228], [-2.629, 0.54], [-7.122, -2.383], [5.146, -0.855]], "o": [[0, 0], [2.021, -0.986], [4.971, -1.02], [-6.258, -1.882], [-6.976, 1.159]], "v": [[43.151, -29.896], [53.117, -37.807], [60.095, -40.183], [78.242, -38.73], [61.149, -40.201]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[-6.344, -5.855], [-4.526, 0.808], [-4.164, 4.511], [6.344, 5.855], [8.328, -9.023]], "o": [[3.172, 2.928], [4.526, -0.808], [8.328, -9.023], [-6.344, -5.855], [-8.328, 9.023]], "v": [[53.684, -50.133], [65.66, -46.962], [79.125, -54.949], [83.841, -82.808], [57.276, -77.072]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [{"i": [[-11.279, -3.141], [-4.907, 3.577], [-2.141, 7.688], [11.279, 3.141], [4.281, -15.376]], "o": [[5.639, 1.571], [4.907, -3.577], [4.281, -15.376], [-11.279, -3.141], [-4.281, 15.376]], "v": [[71.749, -42.249], [88.078, -45.518], [99.158, -62.675], [87.253, -97.929], [59.078, -75.776]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[-21.342, 0], [-6.993, 6.993], [0, 10.671], [21.342, 0], [0, -21.342]], "o": [[10.671, 0], [6.993, -6.993], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[108.41, -41.306], [135.735, -52.625], [147.053, -79.95], [108.41, -118.594], [69.766, -79.95]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [{"i": [[-21.342, 0], [-6.993, 6.993], [0, 10.671], [21.342, 0], [0, -21.342]], "o": [[10.671, 0], [6.993, -6.993], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[108.41, -41.306], [135.735, -52.625], [147.053, -79.95], [108.41, -118.594], [69.766, -79.95]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 134, "s": [{"i": [[-28.172, 0], [-9.913, 1.048], [0, 10.532], [26.162, 0], [0, -21.064]], "o": [[14.086, 0], [9.913, -1.048], [0, -21.064], [-26.162, 0], [0, 21.064]], "v": [[100.45, -63.893], [137.883, -63.357], [154.188, -78.618], [100.45, -115.786], [46.712, -78.618]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 192, "s": [{"i": [[-28.172, 0], [-9.913, 1.048], [0, 10.532], [26.162, 0], [0, -21.064]], "o": [[14.086, 0], [9.913, -1.048], [0, -21.064], [-26.162, 0], [0, 21.064]], "v": [[100.45, -63.893], [137.883, -63.357], [154.188, -78.618], [100.45, -115.786], [46.712, -78.618]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 203, "s": [{"i": [[-21.342, 0], [-6.993, 6.993], [0, 10.671], [21.342, 0], [0, -21.342]], "o": [[10.671, 0], [6.993, -6.993], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[108.41, -41.306], [135.735, -52.625], [147.053, -79.95], [108.41, -118.594], [69.766, -79.95]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [{"i": [[-21.342, 0], [-6.993, 6.993], [0, 10.671], [21.342, 0], [0, -21.342]], "o": [[10.671, 0], [6.993, -6.993], [0, -21.342], [-21.342, 0], [0, 21.342]], "v": [[108.41, -41.306], [135.735, -52.625], [147.053, -79.95], [108.41, -118.594], [69.766, -79.95]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [{"i": [[-11.279, -3.141], [-4.907, 3.577], [-2.141, 7.688], [11.279, 3.141], [4.281, -15.376]], "o": [[5.639, 1.571], [4.907, -3.577], [4.281, -15.376], [-11.279, -3.141], [-4.281, 15.376]], "v": [[71.749, -42.249], [88.078, -45.518], [99.158, -62.675], [87.253, -97.929], [59.078, -75.776]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[-6.344, -5.855], [-4.526, 0.808], [-4.164, 4.511], [6.344, 5.855], [8.328, -9.023]], "o": [[3.172, 2.928], [4.526, -0.808], [8.328, -9.023], [-6.344, -5.855], [-8.328, 9.023]], "v": [[53.684, -50.133], [65.66, -46.962], [79.125, -54.949], [83.841, -82.808], [57.276, -77.072]], "c": true}]}, {"t": 268, "s": [{"i": [[5.045, -5.597], [-6.618, 3.228], [-2.629, 0.54], [-7.122, -2.383], [5.146, -0.855]], "o": [[0, 0], [2.021, -0.986], [4.971, -1.02], [-6.258, -1.882], [-6.976, 1.159]], "v": [[43.151, -29.896], [53.117, -37.807], [60.095, -40.183], [78.242, -38.73], [61.149, -40.201]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235300779, 0.164705887437, 0.164705887437, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 3, "nm": "控制", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [256, 256, 0], "to": [0, -3.167, 0], "ti": [0, -2.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [256, 237, 0], "to": [0, 2.5, 0], "ti": [0, -3.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [256, 271, 0], "to": [0, 3.167, 0], "ti": [0, 2.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [256, 256, 0], "to": [0, 2.5, 0], "ti": [0, 3.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [256, 271, 0], "to": [0, -3.167, 0], "ti": [0, 2.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [256, 237, 0], "to": [0, -2.5, 0], "ti": [0, -3.167, 0]}, {"t": 250, "s": [256, 256, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 316, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "鼻子 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [-17]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [-3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [-3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-17]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [15]}, {"t": 276, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [264.116, 224.998, 0], "to": [-3.833, 0, 0], "ti": [1.667, -5.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [241.116, 224.998, 0], "to": [-0.677, 2.233, 0], "ti": [-3.191, -7.697, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [286.359, 242.735, 0], "to": [4.67, 11.265, 0], "ti": [2.574, 7.03, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [254.116, 257.998, 0], "to": [-4.333, -11.833, 0], "ti": [10.333, -8.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [215.116, 153.998, 0], "to": [-10.333, 8.833, 0], "ti": [1.458, -24.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [192.116, 310.998, 0], "to": [-1.458, 24.5, 0], "ti": [-2.375, 1.666, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [206.366, 301, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [206.366, 301, 0], "to": [-2.375, 1.666, 0], "ti": [-1.458, 24.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [192.116, 310.998, 0], "to": [1.458, -24.5, 0], "ti": [-10.333, 8.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [215.116, 153.998, 0], "to": [10.333, -8.833, 0], "ti": [-4.333, -11.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [254.116, 257.998, 0], "to": [2.574, 7.03, 0], "ti": [4.67, 11.265, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [286.359, 242.735, 0], "to": [-3.191, -7.697, 0], "ti": [-0.677, 2.233, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [241.116, 224.998, 0], "to": [1.667, -5.5, 0], "ti": [-3.833, 0, 0]}, {"t": 276, "s": [264.116, 224.998, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.01;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [8.116, -16.002, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 71, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255, "s": [100, 100, 100]}, {"t": 276, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[4.812, -4.155], [-10.225, -1.521], [2.941, 5.309]], "o": [[-4.812, 4.155], [10.225, 1.521], [-2.941, -5.309]], "v": [[-4.635, -25.577], [5.036, -4.561], [22.322, -21.567]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[6.75, -6.804], [-10.953, -8.657], [5.636, 7.121]], "o": [[-6.75, 6.804], [11.49, 9.081], [-5.636, -7.121]], "v": [[-12.906, -33.54], [-1.387, -6.162], [30.16, -32.437]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[7.485, -7.809], [-18.652, 0], [6.659, 7.809]], "o": [[-7.485, 7.809], [18.652, 0], [-6.659, -7.809]], "v": [[-16.043, -36.561], [6.798, -5.177], [33.133, -36.561]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[7.485, -7.809], [-18.652, 0], [6.659, 7.809]], "o": [[-7.485, 7.809], [18.652, 0], [-6.659, -7.809]], "v": [[-16.043, -36.561], [6.798, -5.177], [33.133, -36.561]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[6.75, -6.804], [-10.953, -8.657], [5.636, 7.121]], "o": [[-6.75, 6.804], [11.49, 9.081], [-5.636, -7.121]], "v": [[-12.906, -33.54], [-1.387, -6.162], [30.16, -32.437]], "c": true}]}, {"t": 268, "s": [{"i": [[4.812, -4.155], [-10.225, -1.521], [2.941, 5.309]], "o": [[-4.812, 4.155], [10.225, 1.521], [-2.941, -5.309]], "v": [[-4.635, -25.577], [5.036, -4.561], [22.322, -21.567]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.4, 0.184, 0.039, 0.7, 0.384, 0.178, 0.031, 0.987, 0.369, 0.173, 0.024], "ix": 9}}, "s": {"a": 0, "k": [-19.727, 176.364], "ix": 5}, "e": {"a": 0, "k": [9.396, 180.696], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "鼻子", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "眉毛", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [-14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [14]}, {"t": 276, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [264, 170, 0], "to": [-0.667, -0.833, 0], "ti": [-0.333, 0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [260, 165, 0], "to": [0.333, -0.167, 0], "ti": [-0.667, -0.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [266, 169, 0], "to": [0.667, 0.833, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [264, 170, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [264, 170, 0], "to": [0.333, -0.167, 0], "ti": [0.667, 0.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [266, 169, 0], "to": [-0.667, -0.833, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [260, 165, 0], "to": [-0.333, 0.167, 0], "ti": [-0.667, -0.833, 0]}, {"t": 276, "s": [264, 170, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [264, 170, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-76, -3.5], [0, 0], [-4.5, -56], [0, 0]], "v": [[272, 147], [172, 213.5], [346.5, 221], [308.75, 151.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-76, -3.5], [0, 0], [-4.5, -56], [0, 0]], "v": [[269.5, 149.5], [172, 213.5], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-76, -3.5], [0, 0], [-4.5, -56], [0, 0]], "v": [[270.5, 147.75], [172, 213.5], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[30.25, -1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-61, -10.25], [0, 0], [-4.5, -56], [0, 0]], "v": [[270.5, 147.75], [168.683, 211.156], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-66.403, 16.573], [0, 0], [-4.5, -56], [0, 0]], "v": [[270.5, 147.75], [188.857, 212.904], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[28, 1.25], [0, 0], [-0.608, 54.815], [0, 0]], "o": [[-67.879, 13.485], [0, 0], [1.038, -55.538], [0, 0]], "v": [[252.428, 151.683], [175.849, 247.772], [355.731, 246.077], [322.212, 143.673]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [{"i": [[28, 1.25], [0, 0], [-28.703, 48.295], [0, 0]], "o": [[-76, -3.5], [0, 0], [31.5, -53], [0, 0]], "v": [[200.5, 35.75], [77, 375.5], [406.5, 384], [390.75, 82.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [{"i": [[28, 1.25], [0, 0], [-28.703, 48.295], [0, 0]], "o": [[-76, -3.5], [0, 0], [31.5, -53], [0, 0]], "v": [[200.5, 35.75], [77, 375.5], [406.5, 384], [390.75, 82.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [{"i": [[28, 1.25], [0, 0], [-0.608, 54.815], [0, 0]], "o": [[-67.879, 13.485], [0, 0], [1.038, -55.538], [0, 0]], "v": [[252.428, 151.683], [175.849, 247.772], [355.731, 246.077], [322.212, 143.673]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-66.403, 16.573], [0, 0], [-4.5, -56], [0, 0]], "v": [[270.5, 147.75], [188.857, 212.904], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [{"i": [[30.25, -1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-61, -10.25], [0, 0], [-4.5, -56], [0, 0]], "v": [[270.5, 147.75], [168.683, 211.156], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-76, -3.5], [0, 0], [-4.5, -56], [0, 0]], "v": [[270.5, 147.75], [172, 213.5], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 300, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-76, -3.5], [0, 0], [-4.5, -56], [0, 0]], "v": [[269.5, 149.5], [172, 213.5], [346.5, 221], [309.75, 154.75]], "c": true}]}, {"t": 326, "s": [{"i": [[28, 1.25], [0, 0], [4.5, 56], [0, 0]], "o": [[-76, -3.5], [0, 0], [-4.5, -56], [0, 0]], "v": [[272, 147], [172, 213.5], [346.5, 221], [308.75, 151.5]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "w": 512, "h": 512, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "红晕", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [-16]}, {"t": 71, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [256, 256, 0], "to": [-4.667, 0, 0], "ti": [-4, 1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [228, 256, 0], "to": [4, -1, 0], "ti": [-4.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [280, 250, 0], "to": [4.667, 0, 0], "ti": [4, -1, 0]}, {"t": 71, "s": [256, 256, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "嘴巴 2", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8.063, -4.41, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.768, -4.864, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[0, 0], [-10.733, -2.147], [-6.16, 0.093], [-9.986, -1.213], [1.567, 15.679]], "o": [[0, 0], [10.733, 2.147], [6.16, -0.093], [7.405, 1.012], [0, 0]], "v": [[-22.924, -16.002], [-17.511, -0.85], [4.234, -3.465], [23.047, 6.083], [39.624, -11.209]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [-20.515, -1.942], [-3.805, 2.619], [-13.468, -2.323], [7.934, 24.793]], "o": [[-0.322, 1.735], [10.659, 0.217], [4.413, 3.329], [16.397, 2.422], [0, 0]], "v": [[-27.543, -16.978], [-15.175, 10.996], [2.128, 3.343], [22.904, 18.323], [48.104, -17.691]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[0, 0], [-7.419, 0.056], [-2.911, 3.577], [-14.789, -2.744], [-6.176, 11.98]], "o": [[2.201, 3.302], [10.631, -0.515], [3.75, 4.627], [13.025, 2.477], [0, 0]], "v": [[-22.095, 6.69], [-10.599, 14.739], [6.241, 5.448], [27.257, 21.57], [59.505, 8.436]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[0, 0], [-6.176, 0.882], [-1.693, 4.884], [-16.59, -3.318], [-9.079, 10.593]], "o": [[3.026, 4.54], [10.593, -1.513], [2.846, 6.397], [15.132, 3.026], [0, 0]], "v": [[-24.397, 15.241], [-9.265, 21.294], [6.048, 7.331], [30.079, 25.833], [69.423, 13.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [{"i": [[0, 0], [-7.78, 1.251], [-1.693, 4.884], [-16.906, -0.643], [-5.968, 10.317]], "o": [[1.227, 4.429], [10.888, -1.75], [2.846, 6.397], [22.001, 0.836], [0, 0]], "v": [[-26.897, 9.991], [-10.89, 20.544], [6.048, 7.331], [33.829, 25.208], [71.798, 5.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [{"i": [[0, 0], [-6.176, 0.882], [-1.693, 4.884], [-16.59, -3.318], [-9.079, 10.593]], "o": [[3.026, 4.54], [10.593, -1.513], [2.846, 6.397], [15.132, 3.026], [0, 0]], "v": [[-24.397, 15.241], [-9.265, 21.294], [6.048, 7.331], [30.079, 25.833], [69.423, 13.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [{"i": [[0, 0], [-7.78, 1.251], [-1.693, 4.884], [-16.906, -0.643], [-5.968, 10.317]], "o": [[1.227, 4.429], [10.888, -1.75], [2.846, 6.397], [22.001, 0.836], [0, 0]], "v": [[-26.897, 9.991], [-10.89, 20.544], [6.048, 7.331], [33.829, 25.208], [71.798, 5.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [{"i": [[0, 0], [-6.176, 0.882], [-1.693, 4.884], [-16.59, -3.318], [-9.079, 10.593]], "o": [[3.026, 4.54], [10.593, -1.513], [2.846, 6.397], [15.132, 3.026], [0, 0]], "v": [[-24.397, 15.241], [-9.265, 21.294], [6.048, 7.331], [30.079, 25.833], [69.423, 13.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [{"i": [[0, 0], [-6.176, 0.882], [-1.693, 4.884], [-16.59, -3.318], [-9.079, 10.593]], "o": [[3.026, 4.54], [10.593, -1.513], [2.846, 6.397], [15.132, 3.026], [0, 0]], "v": [[-24.397, 15.241], [-9.265, 21.294], [6.048, 7.331], [30.079, 25.833], [69.423, 13.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [{"i": [[0, 0], [-7.78, 1.251], [-1.693, 4.884], [-16.906, -0.643], [-5.968, 10.317]], "o": [[1.227, 4.429], [10.888, -1.75], [2.846, 6.397], [22.001, 0.836], [0, 0]], "v": [[-26.897, 9.991], [-10.89, 20.544], [6.048, 7.331], [33.829, 25.208], [71.798, 5.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [{"i": [[0, 0], [-6.176, 0.882], [-1.693, 4.884], [-16.59, -3.318], [-9.079, 10.593]], "o": [[3.026, 4.54], [10.593, -1.513], [2.846, 6.397], [15.132, 3.026], [0, 0]], "v": [[-24.397, 15.241], [-9.265, 21.294], [6.048, 7.331], [30.079, 25.833], [69.423, 13.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [{"i": [[0, 0], [-7.78, 1.251], [-1.693, 4.884], [-16.906, -0.643], [-5.968, 10.317]], "o": [[1.227, 4.429], [10.888, -1.75], [2.846, 6.397], [22.001, 0.836], [0, 0]], "v": [[-26.897, 9.991], [-10.89, 20.544], [6.048, 7.331], [33.829, 25.208], [71.798, 5.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [{"i": [[0, 0], [-6.176, 0.882], [-1.693, 4.884], [-16.59, -3.318], [-9.079, 10.593]], "o": [[3.026, 4.54], [10.593, -1.513], [2.846, 6.397], [15.132, 3.026], [0, 0]], "v": [[-24.397, 15.241], [-9.265, 21.294], [6.048, 7.331], [30.079, 25.833], [69.423, 13.728]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[0, 0], [-7.419, 0.056], [-2.911, 3.577], [-14.789, -2.744], [-6.176, 11.98]], "o": [[2.201, 3.302], [10.631, -0.515], [3.75, 4.627], [13.025, 2.477], [0, 0]], "v": [[-22.095, 6.69], [-10.599, 14.739], [6.241, 5.448], [27.257, 21.57], [59.505, 8.436]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[0, 0], [-20.515, -1.942], [-3.805, 2.619], [-13.468, -2.323], [7.934, 24.793]], "o": [[-0.322, 1.735], [10.659, 0.217], [4.413, 3.329], [16.397, 2.422], [0, 0]], "v": [[-27.543, -16.978], [-15.175, 10.996], [2.128, 3.343], [22.904, 18.323], [48.104, -17.691]], "c": false}]}, {"t": 268, "s": [{"i": [[0, 0], [-10.733, -2.147], [-6.16, 0.093], [-9.986, -1.213], [1.567, 15.679]], "o": [[0, 0], [10.733, 2.147], [6.16, -0.093], [7.405, 1.012], [0, 0]], "v": [[-22.924, -16.002], [-17.511, -0.85], [4.234, -3.465], [23.047, 6.083], [39.624, -11.209]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235300779, 0.164705887437, 0.164705887437, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "嘴巴上 2", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-49.952, 56.83, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-49.952, 56.83, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.298, -16.817], [5.235, -5.398]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.708, -10.832], [3.424, 1.482]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.175, -8.819], [6.777, 3.046]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.811, -6.073], [6.499, 6.846]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.811, -6.073], [6.499, 6.846]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.175, -8.819], [6.777, 3.046]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.708, -10.832], [3.424, 1.482]], "c": false}]}, {"t": 268, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.298, -16.817], [5.235, -5.398]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235300779, 0.164705887437, 0.164705887437, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "嘴巴上", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "脸 描边 2", "parent": 9, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [100]}, {"t": 250, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250.287, 322.717, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.713, 65.717, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 77, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 89, "s": [117, 118, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100, "s": [100, 87, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 108, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 138, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 188, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 218, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 226, "s": [100, 87, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 237, "s": [117, 118, 100]}, {"t": 249, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 58, "s": [{"i": [[-18, -5], [-5.311, -58.113], [0, -11.418], [82.464, 0], [2, 42], [-0.828, 7.267], [-23.353, 27.732], [-17.312, 5.771], [-14, 0]], "o": [[28, 13], [0.994, 10.872], [0, 49], [-94.057, 0], [-0.335, -7.045], [4.642, -40.756], [16, -19], [3, -1], [18, 0]], "v": [[54, -136], [112, -44], [113, -13], [19.862, 95.262], [-95, 1], [-94.642, -19.244], [-64, -101], [-12, -136], [19, -142]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[-12.923, -9.054], [-10.708, -16.953], [-3.126, -14.854], [114.87, -0.97], [12.232, 38.037], [-0.11, 9.74], [-38.833, 28.485], [-16.69, 3.946], [-33.033, -2.186]], "o": [[19.327, 13.696], [6.188, 9.797], [9.261, 70.07], [-96.993, 0.819], [-1.619, -9.249], [0.615, -52.202], [9.882, -7.321], [19.56, -3.554], [13.359, 2.976]], "v": [[66.031, -119.608], [107.669, -78.709], [128.096, -31.482], [5.351, 106.269], [-141.874, 25.051], [-144.224, -3.309], [-78.525, -116.091], [-36.953, -134.358], [14.891, -138.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[-11.593, -4.07], [-12.74, -71.664], [1.411, -19.775], [104.869, -0.412], [26.148, 69.508], [-4.251, 21.113], [-40.166, 28.549], [-14.607, 4.304], [-21.33, 0.45]], "o": [[38.907, 15.18], [2.888, 14.462], [-5.299, 74.276], [-60.518, 0.238], [-9.964, -29.433], [8.526, -42.344], [18.617, -13.574], [12.232, -3.424], [15.188, 0.354]], "v": [[57.451, -163.592], [164.13, -45.418], [169.51, 9.489], [3.359, 144.92], [-143.56, 46.885], [-148.205, -30.568], [-75.387, -140.915], [-29.088, -162.7], [15.669, -169.016]], "c": true}]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[-12.603, -4.368], [-15.442, -68.858], [-8.342, -13.412], [132.189, -4.928], [-58.614, 51.655], [0, 10.117], [-41.196, 28.599], [-24.397, 5.808], [-14.673, 0.754]], "o": [[36.397, 14.023], [3.305, 14.735], [56.378, 90.644], [-158.736, 5.918], [-0.364, -10.134], [0, -53.949], [21.481, -14.791], [11.353, -2.238], [28.827, -0.683]], "v": [[69.96, -154.714], [155.3, -47.696], [164.979, -0.876], [3.094, 162.011], [-150.028, 19.722], [-151.792, -0.876], [-83.623, -131.073], [-20.496, -159.431], [16.031, -163.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.123}, "t": 108, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [129.48, -2.741], [-24.032, 57.126], [0, 10.117], [-41.196, 28.599], [-26.564, 2.101], [-14.923, -21.65]], "o": [[45.689, 2.905], [1.374, 15.039], [16.97, 72.428], [-130.071, 2.754], [-1.815, -9.586], [0, -53.949], [6.358, -29.371], [22.129, -1.75], [4.4, -11.182]], "v": [[59.9, -195.567], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-35.079, -194.513], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-4.504, -15.106], [122.15, -1.438], [-28.589, 59.142], [0, 10.117], [-41.196, 28.599], [-22.993, 2.182], [-17.673, -18.4]], "o": [[44.386, 2.64], [1.374, 15.039], [22.923, 75.758], [-130.086, 1.444], [-1.815, -9.586], [0, -53.949], [11.481, -31.339], [18.981, -0.918], [9.077, -15.15]], "v": [[63.205, -184.263], [158.3, -46.546], [164.979, -0.876], [6.118, 158.7], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-32.849, -182.094], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 124, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 147, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 179, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 202, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-4.504, -15.106], [122.15, -1.438], [-28.589, 59.142], [0, 10.117], [-41.196, 28.599], [-22.993, 2.182], [-17.673, -18.4]], "o": [[44.386, 2.64], [1.374, 15.039], [22.923, 75.758], [-130.086, 1.444], [-1.815, -9.586], [0, -53.949], [11.481, -31.339], [18.981, -0.918], [9.077, -15.15]], "v": [[63.205, -184.263], [158.3, -46.546], [164.979, -0.876], [6.118, 158.7], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-32.849, -182.094], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.123}, "t": 218, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [129.48, -2.741], [-24.032, 57.126], [0, 10.117], [-41.196, 28.599], [-26.564, 2.101], [-14.923, -21.65]], "o": [[45.689, 2.905], [1.374, 15.039], [16.97, 72.428], [-130.071, 2.754], [-1.815, -9.586], [0, -53.949], [6.358, -29.371], [22.129, -1.75], [4.4, -11.182]], "v": [[59.9, -195.567], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-35.079, -194.513], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[-12.603, -4.368], [-15.442, -68.858], [-8.342, -13.412], [132.189, -4.928], [-58.614, 51.655], [0, 10.117], [-41.196, 28.599], [-24.397, 5.808], [-14.673, 0.754]], "o": [[36.397, 14.023], [3.305, 14.735], [56.378, 90.644], [-158.736, 5.918], [-0.364, -10.134], [0, -53.949], [21.481, -14.791], [11.353, -2.238], [28.827, -0.683]], "v": [[69.96, -154.714], [155.3, -47.696], [164.979, -0.876], [3.094, 162.011], [-150.028, 19.722], [-151.792, -0.876], [-83.623, -131.073], [-20.496, -159.431], [16.031, -163.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[-11.593, -4.07], [-12.74, -71.664], [1.411, -19.775], [104.869, -0.412], [26.148, 69.508], [-4.251, 21.113], [-40.166, 28.549], [-14.607, 4.304], [-21.33, 0.45]], "o": [[38.907, 15.18], [2.888, 14.462], [-5.299, 74.276], [-60.518, 0.238], [-9.964, -29.433], [8.526, -42.344], [18.617, -13.574], [12.232, -3.424], [15.188, 0.354]], "v": [[57.451, -163.592], [164.13, -45.418], [169.51, 9.489], [3.359, 144.92], [-143.56, 46.885], [-148.205, -30.568], [-75.387, -140.915], [-29.088, -162.7], [15.669, -169.016]], "c": true}]}, {"t": 249, "s": [{"i": [[-12.923, -9.054], [-10.708, -16.953], [-3.126, -14.854], [114.87, -0.97], [12.232, 38.037], [-0.11, 9.74], [-38.833, 28.485], [-16.69, 3.946], [-33.033, -2.186]], "o": [[19.327, 13.696], [6.188, 9.797], [9.261, 70.07], [-96.993, 0.819], [-1.619, -9.249], [0.615, -52.202], [9.882, -7.321], [19.56, -3.554], [13.359, 2.976]], "v": [[66.031, -119.608], [107.669, -78.709], [128.096, -31.482], [5.351, 106.269], [-141.874, 25.051], [-144.224, -3.309], [-78.525, -116.091], [-36.953, -134.358], [14.891, -138.226]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.474509805441, 0.600000023842, 0.20000000298, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.643, 0.412], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "脸", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "脸白阴影", "parent": 9, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [80]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [80]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [100]}, {"t": 242, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [366.498, 174.308, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [110.498, -81.692, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [0.965, 2.264], [3.3, 7.394], [30.895, 26.466], [-20.018, -22.901], [-4.957, -18.825]], "o": [[0, 0], [-1.623, -5.783], [-1.035, -5.236], [-7.2, -17.856], [14.395, 7.716], [9.938, 11.37], [0, 0]], "v": [[131.444, -94.697], [103.123, -79.967], [100.535, -87.014], [96.45, -99.394], [55.105, -159.716], [116.518, -118.099], [128.322, -99.659]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [{"i": [[0, 0], [0, 0], [9.014, -1.403], [0.339, 10.788], [37.168, 24.712], [-21.569, -33.371], [-2.554, -8.174]], "o": [[0, 0], [-12.07, 3.317], [1.045, -9.335], [-4.843, -19.644], [34.331, 10.301], [8.197, 12.683], [0, 0]], "v": [[124.429, -93.966], [112.93, -88.931], [96.744, -82.482], [94.843, -97.856], [54.1, -168.163], [126.694, -120.129], [136.929, -104.576]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [{"i": [[0, 0], [0, 0], [9.058, -1.468], [4.263, 9.818], [28.905, 30.61], [-28.932, -21.342], [-4.581, -11.172]], "o": [[0, 0], [-12.037, 3.377], [-0.702, -11.049], [-8.237, -28.432], [32.493, 10.291], [7.068, 7.408], [0, 0]], "v": [[122.665, -98.985], [111.236, -93.395], [91.202, -84.951], [86.487, -109.318], [45.845, -179.86], [120.432, -135.158], [136.973, -113.869]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [{"i": [[0, 0], [0, 0], [9.101, -1.534], [4.369, 9.28], [17.161, 31.557], [-26.17, -31.062], [-2.893, -3.213]], "o": [[0, 0], [-0.543, 0.36], [1.045, -9.335], [-9.881, -23.22], [30.654, 10.281], [2.83, 3.938], [0, 0]], "v": [[131.401, -111.754], [108.293, -96.86], [98.661, -93.919], [93.381, -121.53], [57.339, -181.057], [132.67, -131.688], [139.768, -121.287]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [{"i": [[0, 0], [0, 0], [9.145, -1.599], [4.976, 12.242], [19.066, 24.77], [-13.487, -15.532], [-6.109, -6.22]], "o": [[0, 0], [-11.97, 3.499], [1.045, -9.335], [-4.524, -9.508], [28.816, 10.27], [1.763, 1.468], [0, 0]], "v": [[133.186, -121.974], [125.099, -112.324], [110.369, -104.888], [101.274, -137.492], [72.684, -185.02], [128.987, -149.468], [143.109, -133.03]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.881, -132.902], [125.95, -123.943], [114.446, -114.925], [98.946, -159.173], [66.778, -206.107], [124.503, -169.107], [145.243, -146.382]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[0, 0], [0, 0], [3.688, -2.269], [0, 0], [29.123, 24.738], [-14.276, -16.936], [-6.177, -10.721]], "o": [[0, 0], [-6.35, 4.372], [1.045, -9.335], [0, 0], [30.603, 10.28], [10.724, 8.564], [0, 0]], "v": [[149.298, -103.67], [139.1, -91.997], [124.687, -81.731], [104.454, -136.695], [70.418, -182.736], [130.276, -143.564], [155.319, -113.284]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [{"i": [[0, 0], [0, 0], [7.226, -3.111], [2.116, 11.344], [37.168, 24.712], [-19.553, -46.38], [-1.333, -4.415]], "o": [[0, 0], [-7.304, 3.708], [-2.024, -6.111], [-8.134, -48.406], [32.887, 8.231], [3.947, 8.62], [0, 0]], "v": [[143.918, -28.913], [132.054, -18.958], [113.524, -8.639], [108.884, -39.594], [51.814, -140.177], [147.303, -69.62], [154.333, -41.085]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [{"i": [[0, 0], [0, 0], [8.184, -3.087], [1.524, 11.158], [37.168, 24.712], [-20.372, -52.729], [-2.544, -7.886]], "o": [[0, 0], [-8.614, 4.084], [-1.001, -7.186], [-4.75, -57.966], [36.302, 0], [5.628, 12.771], [0, 0]], "v": [[144.511, 9.794], [131.52, 22.672], [112.436, 33.494], [111.078, -30.318], [41.915, -130.047], [139.872, -57.271], [153.699, 1.201]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [{"i": [[0, 0], [0, 0], [10.101, -3.037], [0.339, 10.788], [37.168, 24.712], [-5.924, -64.81], [-4.966, -14.83]], "o": [[0, 0], [-11.234, 4.834], [1.045, -9.335], [2.018, -77.087], [43.133, -16.462], [1.375, 15.039], [0, 0]], "v": [[151.696, 7.71], [143.95, 11.431], [114.26, 25.76], [115.465, -11.766], [57.617, -187.288], [153.509, -48.072], [161.43, 4.775]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[0, 0], [0, 0], [10.709, -3.952], [0.339, 10.788], [37.168, 24.712], [-5.924, -64.81], [-3.603, -15.378]], "o": [[0, 0], [-10.766, 5.684], [1.045, -9.335], [2.018, -77.087], [41.685, 10.343], [1.375, 15.039], [0, 0]], "v": [[154.936, 4.061], [149.954, 6.926], [117.66, 21.391], [118.769, -8.759], [65.271, -184.776], [148.516, -44.509], [155.725, 3.545]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [{"i": [[0, 0], [0, 0], [10.709, -3.952], [0.339, 10.788], [37.168, 24.712], [-5.924, -64.81], [-3.603, -15.378]], "o": [[0, 0], [-10.766, 5.684], [1.045, -9.335], [2.018, -77.087], [41.685, 10.343], [1.375, 15.039], [0, 0]], "v": [[154.936, 4.061], [149.954, 6.926], [117.66, 21.391], [118.769, -8.759], [65.271, -184.776], [150.766, -48.509], [155.725, 3.545]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [{"i": [[0, 0], [0, 0], [10.709, -3.952], [0.339, 10.788], [37.168, 24.712], [-5.924, -64.81], [-3.603, -15.378]], "o": [[0, 0], [-10.766, 5.684], [1.045, -9.335], [2.018, -77.087], [41.685, 10.343], [1.375, 15.039], [0, 0]], "v": [[154.936, 4.061], [149.954, 6.926], [117.66, 21.391], [118.769, -8.759], [65.271, -184.776], [150.766, -48.509], [155.725, 3.545]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [{"i": [[0, 0], [0, 0], [10.709, -3.952], [0.339, 10.788], [37.168, 24.712], [-5.924, -64.81], [-3.603, -15.378]], "o": [[0, 0], [-10.766, 5.684], [1.045, -9.335], [2.018, -77.087], [41.685, 10.343], [1.375, 15.039], [0, 0]], "v": [[154.936, 4.061], [149.954, 6.926], [117.66, 21.391], [118.769, -8.759], [65.271, -184.776], [148.516, -44.509], [155.725, 3.545]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [{"i": [[0, 0], [0, 0], [10.101, -3.037], [0.339, 10.788], [37.168, 24.712], [-5.924, -64.81], [-4.966, -14.83]], "o": [[0, 0], [-11.234, 4.834], [1.045, -9.335], [2.018, -77.087], [43.133, -16.462], [1.375, 15.039], [0, 0]], "v": [[151.696, 7.71], [143.95, 11.431], [114.26, 25.76], [115.465, -11.766], [57.617, -187.288], [153.509, -48.072], [161.43, 4.775]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [{"i": [[0, 0], [0, 0], [8.184, -3.087], [1.524, 11.158], [37.168, 24.712], [-20.372, -52.729], [-2.544, -7.886]], "o": [[0, 0], [-8.614, 4.084], [-1.001, -7.186], [-4.75, -57.966], [36.302, 0], [5.628, 12.771], [0, 0]], "v": [[144.511, 9.794], [131.52, 22.672], [112.436, 33.494], [111.078, -30.318], [41.915, -130.047], [139.872, -57.271], [153.699, 1.201]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [{"i": [[0, 0], [0, 0], [7.226, -3.111], [2.116, 11.344], [37.168, 24.712], [-19.553, -46.38], [-1.333, -4.415]], "o": [[0, 0], [-7.304, 3.708], [-2.024, -6.111], [-8.134, -48.406], [32.887, 8.231], [3.947, 8.62], [0, 0]], "v": [[143.918, -28.913], [132.054, -18.958], [113.524, -8.639], [108.884, -39.594], [51.814, -140.177], [147.303, -69.62], [154.333, -41.085]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [{"i": [[0, 0], [0, 0], [3.688, -2.269], [0, 0], [29.123, 24.738], [-14.276, -16.936], [-6.177, -10.721]], "o": [[0, 0], [-6.35, 4.372], [1.045, -9.335], [0, 0], [30.603, 10.28], [10.724, 8.564], [0, 0]], "v": [[149.298, -103.67], [139.1, -91.997], [124.687, -81.731], [104.454, -136.695], [70.418, -182.736], [130.276, -143.564], [155.319, -113.284]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.881, -132.902], [125.95, -123.943], [114.446, -114.925], [98.946, -159.173], [66.778, -206.107], [124.503, -169.107], [145.243, -146.382]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[0, 0], [0, 0], [9.145, -1.599], [4.976, 12.242], [19.066, 24.77], [-13.487, -15.532], [-6.109, -6.22]], "o": [[0, 0], [-11.97, 3.499], [1.045, -9.335], [-4.524, -9.508], [28.816, 10.27], [1.763, 1.468], [0, 0]], "v": [[133.186, -121.974], [125.099, -112.324], [110.369, -104.888], [101.274, -137.492], [72.684, -185.02], [128.987, -149.468], [143.109, -133.03]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [{"i": [[0, 0], [0, 0], [9.101, -1.534], [4.369, 9.28], [17.161, 31.557], [-26.17, -31.062], [-2.893, -3.213]], "o": [[0, 0], [-0.543, 0.36], [1.045, -9.335], [-9.881, -23.22], [30.654, 10.281], [2.83, 3.938], [0, 0]], "v": [[131.401, -111.754], [108.293, -96.86], [98.661, -93.919], [93.381, -121.53], [57.339, -181.057], [132.67, -131.688], [139.768, -121.287]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [{"i": [[0, 0], [0, 0], [9.058, -1.468], [4.263, 9.818], [28.905, 30.61], [-28.932, -21.342], [-4.581, -11.172]], "o": [[0, 0], [-12.037, 3.377], [-0.702, -11.049], [-8.237, -28.432], [32.493, 10.291], [7.068, 7.408], [0, 0]], "v": [[122.665, -98.985], [111.236, -93.395], [91.202, -84.951], [86.487, -109.318], [45.845, -179.86], [120.432, -135.158], [136.973, -113.869]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [{"i": [[0, 0], [0, 0], [9.014, -1.403], [0.339, 10.788], [37.168, 24.712], [-21.569, -33.371], [-2.554, -8.174]], "o": [[0, 0], [-12.07, 3.317], [1.045, -9.335], [-4.843, -19.644], [34.331, 10.301], [8.197, 12.683], [0, 0]], "v": [[124.429, -93.966], [112.93, -88.931], [96.744, -82.482], [94.843, -97.856], [54.1, -168.163], [126.694, -120.129], [136.929, -104.576]], "c": true}]}, {"t": 243, "s": [{"i": [[0, 0], [0, 0], [0.965, 2.264], [3.3, 7.394], [30.895, 26.466], [-20.018, -22.901], [-4.957, -18.825]], "o": [[0, 0], [-1.623, -5.783], [-1.035, -5.236], [-7.2, -17.856], [14.395, 7.716], [9.938, 11.37], [0, 0]], "v": [[131.444, -94.697], [103.123, -79.967], [100.535, -87.014], [96.45, -99.394], [55.105, -159.716], [116.518, -118.099], [128.322, -99.659]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.588, 0.71, 0.263, 0.5, 0.588, 0.71, 0.263, 1, 0.588, 0.71, 0.263, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [112, -84], "to": [-0.287, -1.424], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [110.279, -92.543], "to": [0, 0], "ti": [-0.287, -1.424]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [112, -84], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [112, -84], "to": [-0.287, -1.424], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [110.279, -92.543], "to": [0, 0], "ti": [-0.287, -1.424]}, {"t": 239, "s": [112, -84]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [137.677, -87.72], "to": [3.303, -3.199], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [157.493, -106.911], "to": [0, 0], "ti": [3.303, -3.199]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [137.677, -87.72], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [137.677, -87.72], "to": [3.303, -3.199], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [157.493, -106.911], "to": [0, 0], "ti": [3.303, -3.199]}, {"t": 239, "s": [137.677, -87.72]}], "ix": 6}, "t": 1, "nm": "lianse2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 496, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "脸阴影", "parent": 9, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [100]}, {"t": 251, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [292.279, 335.928, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [32.279, 80.928, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 75, "s": [71, 71, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 130, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 196, "s": [100, 100, 100]}, {"t": 251, "s": [71, 71, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [{"i": [[24.837, 81.194], [0, 0], [11.058, -3.39], [0, 0], [0, 0], [-27.431, 7.742], [-24.428, 7.262]], "o": [[0, 0], [-12.434, 1.568], [-29.435, 102.244], [0, 0], [0, 0], [6.02, -2.821], [6.205, -23.372]], "v": [[136.274, -81.15], [123.546, -80.046], [92.308, -81.425], [20.937, 105.847], [32.299, 103.582], [83.613, 89.484], [115.47, 64.612]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[22.488, 58.392], [0, 0], [11.395, -3.36], [0, 0], [0, 0], [-29.028, 4.968], [-19.219, 12.696]], "o": [[0, 0], [-13.789, 2.459], [-5.169, 171.669], [0, 0], [10.625, 3.54], [47.403, -4.59], [36.543, -66.881]], "v": [[142.449, -104.491], [123.609, -104.955], [94.532, -102.625], [-20.894, 115.336], [-32.412, 122.474], [41.577, 116.036], [125.53, 74.079]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [{"i": [[20.922, 43.192], [0, 0], [11.621, -3.339], [0, 0], [0, 0], [-30.092, 3.118], [-15.746, 16.319]], "o": [[0, 0], [-14.693, 3.053], [11.009, 217.953], [0, 0], [17.709, 5.901], [74.992, -5.769], [56.768, -95.887]], "v": [[146.566, -120.051], [122.503, -114.098], [95.441, -110.444], [-48.781, 121.663], [-69.812, 127.032], [13.553, 133.737], [132.237, 80.39]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [{"i": [[20.138, 35.591], [0, 0], [11.733, -3.329], [0, 0], [0, 0], [-30.624, 2.193], [-16.999, 13.75]], "o": [[0, 0], [-15.145, 3.35], [19.097, 241.095], [0, 0], [21.251, 7.081], [88.786, -6.358], [66.881, -110.39]], "v": [[148.624, -127.831], [122.294, -120.909], [96.067, -116.247], [-62.724, 124.826], [-90.234, 131.722], [9.232, 138.062], [140.984, 85.568]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[55.902, 108.639], [0, 0], [10.81, -3.799], [0, 0], [0, 0], [-30.627, 2.113], [-22.294, 15.007]], "o": [[0, 0], [-11.544, 2.745], [51.314, 275.897], [0, 0], [32.635, 55.555], [87.809, -6.127], [42.432, -37.836]], "v": [[158.143, -148.596], [136.888, -137.819], [118.194, -131.059], [-118.137, 104.242], [-164.251, 103.907], [10.785, 146.895], [140.737, 94.237]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [{"i": [[58.477, 102.508], [0, 0], [10.349, -4.035], [60.721, -24.355], [0, 0], [-30.629, 2.073], [-21.732, 15.597]], "o": [[0, 0], [-9.743, 2.443], [-6.214, 166.858], [-60.721, 24.355], [23.492, 42.097], [87.321, -6.011], [51.064, -42.842]], "v": [[163.544, -158.787], [136.978, -143.92], [115.112, -136.966], [-28.168, 84.392], [-162.548, 110.217], [5.005, 151.337], [142.592, 103.877]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [{"i": [[67.681, 101.886], [0, 0], [8.263, -2.691], [34.497, -16.121], [0, 0], [-30.63, 2.034], [-22.861, 16.464]], "o": [[0, 0], [-7.943, 2.14], [0.622, 173.824], [-67.93, 31.198], [71.518, 61.849], [86.832, -5.896], [59.697, -47.847]], "v": [[162.861, -174.261], [144.304, -153.847], [121.982, -142.777], [-21.432, 107.512], [-173.336, 108.327], [3.277, 157.105], [144.275, 107.306]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [{"i": [[59.454, 73.62], [0, 0], [9.426, -4.505], [57.415, -24.19], [0, 0], [-30.632, 1.994], [-23.991, 17.33]], "o": [[0, 0], [-6.143, 1.838], [7.458, 180.791], [-57.415, 24.19], [31.006, 40.233], [86.344, -5.78], [73.39, -51.209]], "v": [[167.124, -185.958], [145.699, -159.711], [126.88, -150.445], [5.347, 86.779], [-166.408, 115.659], [10.727, 164.175], [151.234, 108.793]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [{"i": [[86.377, 119.053], [0, 0], [9.452, -4.494], [47.846, -20.159], [0, 0], [-30.633, 1.954], [-24.046, 17.678]], "o": [[0, 0], [-6.235, 1.915], [6.775, 180.094], [-47.846, 20.159], [29.38, 34.708], [85.856, -5.664], [68.676, -51.161]], "v": [[169.266, -193.863], [142.043, -163.205], [124.174, -154.409], [-4.755, 101.568], [-153.169, 125.634], [10.138, 166.816], [151.967, 111.914]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[119.181, 138.233], [0, 0], [-1.879, -20.597], [28.708, -12.095], [0, 0], [-30.636, 1.874], [-24.155, 18.375]], "o": [[0, 0], [4.213, 9.106], [1.858, 184.414], [-28.707, 12.095], [26.129, 23.657], [84.879, -5.433], [57.254, -39.291]], "v": [[169.817, -209.034], [132.326, -169.213], [141.289, -126.752], [-24.959, 131.144], [-142.638, 150.05], [4.494, 181.668], [161.187, 126.523]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[73.177, 151.238], [0, 0], [-1.452, -38.272], [0, 0], [0, 0], [-30.64, 1.754], [-24.32, 19.42]], "o": [[0, 0], [6.561, 25.081], [-5.516, 190.895], [0, 0], [21.251, 7.081], [83.414, -5.086], [40.121, -21.486]], "v": [[182.605, -164.013], [141.672, -123.207], [155, -25.463], [-55.264, 175.509], [-86.973, 175.51], [7.191, 180.024], [175.018, 118.136]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [{"i": [[10.872, 35.846], [0, 0], [9.708, -4.383], [0, 0], [0, 0], [-30.647, 1.555], [-24.594, 21.162]], "o": [[0, 0], [-9.301, 6.892], [-0.061, 173.128], [0, 0], [21.251, 7.081], [80.972, -4.508], [42.418, -48.305]], "v": [[173.273, -64.514], [153.379, -43.915], [134.203, -30.835], [-44.541, 156.011], [-83.397, 163.138], [7.688, 168.418], [181.493, 102.494]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [{"i": [[21.784, 40.032], [0, 0], [9.811, -4.339], [0, 0], [0, 0], [-30.653, 1.396], [-24.814, 22.555]], "o": [[0, 0], [-8.957, 5.797], [-1.106, 105.621], [0, 0], [21.251, 7.081], [79.018, -4.046], [40.268, -46.212]], "v": [[173.076, -11.552], [155.59, 3.036], [128.677, 21.432], [-49.684, 158.935], [-83.966, 163.531], [6.028, 169.423], [170.69, 105.088]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [{"i": [[9.356, 34.6], [0, 0], [10.016, -4.251], [0, 0], [0, 0], [-30.664, 1.077], [-25.253, 25.343]], "o": [[0, 0], [-8.269, 3.607], [-8.265, 164.768], [0, 0], [21.251, 7.081], [75.111, -3.121], [35.969, -42.025]], "v": [[172.682, -8.331], [152.93, 1.317], [130.021, 10.865], [-59.97, 164.783], [-85.105, 164.317], [2.707, 171.435], [140.088, 112.524]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [{"i": [[7.966, 33.457], [0, 0], [10.299, -4.129], [0, 0], [0, 0], [-30.68, 0.638], [-25.857, 29.175]], "o": [[0, 0], [-9.286, 4.454], [-15.784, 157.105], [0, 0], [21.251, 7.081], [69.739, -1.85], [30.058, -36.268]], "v": [[165.452, -3.282], [151.717, 3.602], [124.985, 15.154], [-63.015, 157.843], [-83.896, 157.074], [-0.195, 165.878], [140.771, 104.407]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [{"i": [[5.945, 31.795], [0, 0], [10.709, -3.952], [0, 0], [0, 0], [-30.703, 0], [-26.735, 34.75]], "o": [[0, 0], [-10.766, 5.684], [-26.722, 145.959], [0, 0], [21.251, 7.081], [61.925, 0], [21.46, -27.894]], "v": [[154.936, 4.061], [149.954, 6.926], [117.66, 21.391], [-67.443, 147.748], [-82.138, 146.539], [-4.415, 160.294], [133.11, 102.711]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [{"i": [[5.945, 31.795], [0, 0], [10.709, -3.952], [0, 0], [0, 0], [-30.703, 0], [-26.735, 34.75]], "o": [[0, 0], [-10.766, 5.684], [-26.722, 145.959], [0, 0], [21.251, 7.081], [61.925, 0], [21.46, -27.894]], "v": [[154.936, 4.061], [149.954, 6.926], [117.66, 21.391], [-67.443, 147.748], [-82.138, 146.539], [-4.415, 160.294], [133.11, 102.711]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [{"i": [[7.966, 33.457], [0, 0], [10.299, -4.129], [0, 0], [0, 0], [-30.68, 0.638], [-25.857, 29.175]], "o": [[0, 0], [-9.286, 4.454], [-15.784, 157.105], [0, 0], [21.251, 7.081], [69.739, -1.85], [30.058, -36.268]], "v": [[165.452, -3.282], [151.717, 3.602], [124.985, 15.154], [-63.015, 157.843], [-83.896, 157.074], [-0.195, 165.878], [140.771, 104.407]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [{"i": [[9.356, 34.6], [0, 0], [10.016, -4.251], [0, 0], [0, 0], [-30.664, 1.077], [-25.253, 25.343]], "o": [[0, 0], [-8.269, 3.607], [-8.265, 164.768], [0, 0], [21.251, 7.081], [75.111, -3.121], [35.969, -42.025]], "v": [[172.682, -8.331], [152.93, 1.317], [130.021, 10.865], [-59.97, 164.783], [-85.105, 164.317], [2.707, 171.435], [140.088, 112.524]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [{"i": [[21.784, 40.032], [0, 0], [9.811, -4.339], [0, 0], [0, 0], [-30.653, 1.396], [-24.814, 22.555]], "o": [[0, 0], [-8.957, 5.797], [-1.106, 105.621], [0, 0], [21.251, 7.081], [79.018, -4.046], [40.268, -46.212]], "v": [[173.076, -11.552], [155.59, 3.036], [128.677, 21.432], [-49.684, 158.935], [-83.966, 163.531], [6.028, 169.423], [170.69, 105.088]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [{"i": [[10.872, 35.846], [0, 0], [9.708, -4.383], [0, 0], [0, 0], [-30.647, 1.555], [-24.594, 21.162]], "o": [[0, 0], [-9.301, 6.892], [-0.061, 173.128], [0, 0], [21.251, 7.081], [80.972, -4.508], [42.418, -48.305]], "v": [[173.273, -64.514], [153.379, -43.915], [134.203, -30.835], [-44.541, 156.011], [-83.397, 163.138], [7.688, 168.418], [181.493, 102.494]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [{"i": [[73.177, 151.238], [0, 0], [-1.452, -38.272], [0, 0], [0, 0], [-30.64, 1.754], [-24.32, 19.42]], "o": [[0, 0], [6.561, 25.081], [-5.516, 190.895], [0, 0], [21.251, 7.081], [83.414, -5.086], [40.121, -21.486]], "v": [[182.605, -164.013], [141.672, -123.207], [155, -25.463], [-55.264, 175.509], [-86.973, 175.51], [7.191, 180.024], [175.018, 118.136]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[119.181, 138.233], [0, 0], [-1.879, -20.597], [28.708, -12.095], [0, 0], [-30.636, 1.874], [-24.155, 18.375]], "o": [[0, 0], [4.213, 9.106], [1.858, 184.414], [-28.707, 12.095], [26.129, 23.657], [84.879, -5.433], [57.254, -39.291]], "v": [[169.817, -209.034], [132.326, -169.213], [141.289, -126.752], [-24.959, 131.144], [-142.638, 150.05], [4.494, 181.668], [161.187, 126.523]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[86.377, 119.053], [0, 0], [9.452, -4.494], [47.846, -20.159], [0, 0], [-30.633, 1.954], [-24.046, 17.678]], "o": [[0, 0], [-6.235, 1.915], [6.775, 180.094], [-47.846, 20.159], [29.38, 34.708], [85.856, -5.664], [68.676, -51.161]], "v": [[169.266, -193.863], [142.043, -163.205], [124.174, -154.409], [-4.755, 101.568], [-158.988, 130.16], [7.551, 173.282], [154.554, 122.259]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [{"i": [[59.454, 73.62], [0, 0], [9.426, -4.505], [57.415, -24.19], [0, 0], [-30.632, 1.994], [-23.991, 17.33]], "o": [[0, 0], [-6.143, 1.838], [7.458, 180.791], [-57.415, 24.19], [31.006, 40.233], [86.344, -5.78], [73.39, -51.209]], "v": [[167.124, -185.958], [145.699, -159.711], [126.88, -150.445], [5.347, 86.779], [-166.408, 115.659], [10.727, 164.175], [151.234, 108.793]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [{"i": [[67.681, 101.886], [0, 0], [8.263, -2.691], [34.497, -16.121], [0, 0], [-30.63, 2.034], [-22.861, 16.464]], "o": [[0, 0], [-7.943, 2.14], [0.622, 173.824], [-67.93, 31.198], [71.518, 61.849], [86.832, -5.896], [59.697, -47.847]], "v": [[162.861, -174.261], [144.304, -153.847], [121.982, -142.777], [-21.432, 107.512], [-173.336, 108.327], [3.277, 157.105], [144.275, 107.306]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [{"i": [[58.477, 102.508], [0, 0], [10.349, -4.035], [60.721, -24.355], [0, 0], [-30.629, 2.073], [-21.732, 15.597]], "o": [[0, 0], [-9.743, 2.443], [-6.214, 166.858], [-60.721, 24.355], [23.492, 42.097], [87.321, -6.011], [51.064, -42.842]], "v": [[163.544, -158.787], [136.978, -143.92], [115.112, -136.966], [-28.168, 84.392], [-162.548, 110.217], [5.005, 151.337], [142.592, 103.877]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [{"i": [[55.902, 108.639], [0, 0], [10.81, -3.799], [0, 0], [0, 0], [-30.627, 2.113], [-22.294, 15.007]], "o": [[0, 0], [-11.544, 2.745], [51.314, 275.897], [0, 0], [32.635, 55.555], [87.809, -6.127], [42.432, -37.836]], "v": [[158.143, -148.596], [136.888, -137.819], [118.194, -131.059], [-118.137, 104.242], [-164.251, 103.907], [10.785, 146.895], [140.737, 94.237]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [{"i": [[20.138, 35.591], [0, 0], [11.733, -3.329], [0, 0], [0, 0], [-30.624, 2.193], [-16.999, 13.75]], "o": [[0, 0], [-15.145, 3.35], [19.097, 241.095], [0, 0], [21.251, 7.081], [88.786, -6.358], [66.881, -110.39]], "v": [[148.624, -127.831], [122.294, -120.909], [96.067, -116.247], [-62.724, 124.826], [-90.234, 131.722], [9.232, 138.062], [140.984, 85.568]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [{"i": [[20.922, 43.192], [0, 0], [11.621, -3.339], [0, 0], [0, 0], [-30.092, 3.118], [-15.746, 16.319]], "o": [[0, 0], [-14.693, 3.053], [11.009, 217.953], [0, 0], [17.709, 5.901], [74.992, -5.769], [56.768, -95.887]], "v": [[146.566, -120.051], [122.503, -114.098], [95.441, -110.444], [-48.781, 121.663], [-69.812, 127.032], [13.553, 133.737], [132.237, 80.39]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [{"i": [[22.488, 58.392], [0, 0], [11.395, -3.36], [0, 0], [0, 0], [-29.028, 4.968], [-19.219, 12.696]], "o": [[0, 0], [-13.789, 2.459], [-5.169, 171.669], [0, 0], [10.625, 3.54], [47.403, -4.59], [36.543, -66.881]], "v": [[142.449, -104.491], [123.609, -104.955], [94.532, -102.625], [-20.894, 115.336], [-32.412, 122.474], [41.577, 116.036], [125.53, 74.079]], "c": true}]}, {"t": 251, "s": [{"i": [[24.837, 81.194], [0, 0], [11.058, -3.39], [0, 0], [0, 0], [-27.431, 7.742], [-24.428, 7.262]], "o": [[0, 0], [-12.434, 1.568], [-29.435, 102.244], [0, 0], [0, 0], [6.02, -2.821], [6.205, -23.372]], "v": [[136.274, -81.15], [123.546, -80.046], [92.308, -81.425], [20.937, 105.847], [32.299, 103.582], [83.613, 89.484], [115.47, 64.612]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.535, 0.659, 0.839, 0.341, 0.678, 0.702, 0.855, 0.431, 0.821, 0.745, 0.871, 0.522, 0.535, 0, 0.678, 0.5, 0.821, 1], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [22, 3.101], "to": [-0.319, 0.284], "ti": [4, 10.756]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [-22.073, -48.496], "to": [-4.012, -10.786], "ti": [0.554, -0.492]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [5.103, -39.207], "to": [-0.731, 0.649], "ti": [-0.122, -0.504]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [-42.475, -68.057], "to": [0.161, 0.666], "ti": [-0.36, -3.081]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [-2.776, -40.197], "to": [0.379, 3.247], "ti": [2.117, -3.392]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [0.061, -14.14], "to": [-2.624, 4.205], "ti": [5.222, -1.605]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [13.416, 54.962], "to": [-11.214, 3.447], "ti": [1.565, -1.391]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [3.445, 3.191], "to": [-4.5, 4], "ti": [1.565, -1.391]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [3.445, 3.191], "to": [-4.5, 4], "ti": [-1.565, 1.391]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [3.445, 3.191], "to": [1.565, -1.391], "ti": [-4.5, 4]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [3.445, 3.191], "to": [1.565, -1.391], "ti": [-11.214, 3.447]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [13.416, 54.962], "to": [5.222, -1.605], "ti": [-2.624, 4.205]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [0.061, -14.14], "to": [2.117, -3.392], "ti": [0.379, 3.247]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [-2.776, -40.197], "to": [-0.36, -3.081], "ti": [0.161, 0.666]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [-42.475, -68.057], "to": [-0.122, -0.504], "ti": [-0.731, 0.649]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [5.103, -39.207], "to": [0.554, -0.492], "ti": [-4.012, -10.786]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [-22.073, -48.496], "to": [4, 10.756], "ti": [-0.319, 0.284]}, {"t": 251, "s": [22, 3.101]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [123.526, 39.33], "to": [-0.479, 2.225], "ti": [-17.022, -21.818]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [187.337, 59.97], "to": [16.648, 21.339], "ti": [0.81, -3.768]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [189.591, 131.629], "to": [-0.371, 1.724], "ti": [16.687, -26.572]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [166.516, 210.305], "to": [-16.661, 26.531], "ti": [0.787, -1.588]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [121.736, 241.148], "to": [-0.594, 1.199], "ti": [0.557, -1.034]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [111.453, 280.303], "to": [-17.705, 32.861], "ti": [5.13, -23.856]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [94.619, 282.575], "to": [-6.667, 31], "ti": [5.13, -23.856]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [83.031, 265.485], "to": [-6.667, 31], "ti": [5.13, -23.856]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [83.031, 265.485], "to": [-6.667, 31], "ti": [-5.13, 23.856]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [83.031, 265.485], "to": [5.13, -23.856], "ti": [-6.667, 31]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [83.031, 265.485], "to": [5.13, -23.856], "ti": [-6.667, 31]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [94.619, 282.575], "to": [5.13, -23.856], "ti": [-17.705, 32.861]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [111.453, 280.303], "to": [0.557, -1.034], "ti": [-0.594, 1.199]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [121.736, 241.148], "to": [0.787, -1.588], "ti": [-16.661, 26.531]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [166.516, 210.305], "to": [16.687, -26.572], "ti": [-0.371, 1.724]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [189.591, 131.629], "to": [0.81, -3.768], "ti": [16.648, 21.339]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [187.337, 59.97], "to": [-17.022, -21.818], "ti": [-0.479, 2.225]}, {"t": 251, "s": [123.526, 39.33]}], "ix": 6}, "t": 2, "h": {"a": 0, "k": 21.767, "ix": 7}, "a": {"a": 0, "k": -234.165, "ix": 8}, "nm": "lianse3", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.393, -3.461], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 496, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "脸白 3", "parent": 9, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [100]}, {"t": 250, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [259.056, 397.507, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3.056, 141.507, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[0.44, 2.229], [0, 0], [0, 0], [34.796, 28.679], [4.746, -5.191], [0, -5.888], [-1.312, -6.93], [-94.057, 0], [-6.155, 43.911]], "o": [[-6.023, -30.524], [-29.608, -15.217], [0, 0], [-39.228, -33.26], [-0.396, 5.285], [0, 7.314], [-11.63, 38.97], [67.617, 0], [-1.214, -0.517]], "v": [[129.574, -7.983], [100.063, -51.275], [41.491, -40.49], [-21.471, -43.935], [-97.549, -49.463], [-98.142, -32.744], [-96.144, -11.346], [16.362, 81.762], [132.089, -3.931]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [{"i": [[0.316, 1.601], [6.748, -0.971], [14.182, -2.46], [26.095, 12.851], [12.031, 1.141], [0, -4.229], [-1.021, -5.395], [-104.213, 0], [-0.659, 51.657]], "o": [[-11.532, -18.048], [-28.137, -16.494], [-18.653, 3.578], [-33.064, -12.286], [-0.284, 3.796], [0.067, 5.675], [-12.707, 43.059], [80.019, 0], [-0.872, -0.371]], "v": [[135.499, 6.235], [100.637, -22.006], [22.97, -21.726], [-28.13, -31.271], [-121.041, -19.592], [-122.103, -2.021], [-119.495, 20.698], [10.746, 113.167], [138.681, 8.424]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0.211, 1.068], [14.984, -0.899], [14.844, 2.014], [22.736, -12.544], [5.897, 10.322], [0, -2.82], [-0.895, -9.947], [-149.384, -3.084], [11.005, 44.836]], "o": [[-10.311, -5.097], [-34.143, -3.116], [-19.791, -18.822], [-21.756, 1.912], [-0.19, 2.531], [-0.218, 4.2], [-14.325, 38.884], [132.428, 3.052], [-0.581, -0.247]], "v": [[118.478, -44.794], [95.5, -52.098], [26.765, -50.735], [-43.986, -55.706], [-125.748, -66.884], [-139.202, -37.645], [-144.465, -16.581], [1.025, 107.09], [120.998, -35.538]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[0.105, 0.534], [20.672, -4.778], [14.691, 11.244], [27.436, -23.775], [-0.238, 19.502], [0, -1.41], [-0.077, -6.649], [-194.554, -6.168], [24.122, 51.552]], "o": [[-9.09, 7.854], [-30.92, 3.811], [-32.059, -36.756], [-15.937, 6.574], [-0.095, 1.265], [-0.503, 2.725], [-15.942, 34.708], [184.837, 6.104], [-0.291, -0.124]], "v": [[133.574, -95.499], [91.531, -77.514], [19.309, -82.244], [-65.686, -80.725], [-125.781, -105.41], [-148.119, -70.931], [-153.656, -56.198], [-0.203, 135.636], [146.873, -77.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [25.851, -9.448], [16.167, 10.962], [44.055, -36.638], [-6.372, 28.682], [13.6, -25.75], [0.878, -1.78], [-193.869, -25.507], [37.53, 60.976]], "o": [[-7.869, 20.805], [-25.851, 9.448], [-52.354, -35.497], [-11.216, 9.328], [0, 0], [-13.6, 25.75], [-6.309, 17.282], [233.131, 1.993], [0, 0]], "v": [[145.293, -149.639], [87.797, -101.996], [17.854, -102.003], [-88.555, -105.862], [-127.878, -146.682], [-155.4, -103.75], [-180.691, -27.282], [-8.131, 162.007], [173.06, -120.462]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[0, 0], [29.774, -10.099], [16.328, 10.67], [42.537, -34.721], [-2.939, 26.335], [12.68, -40.785], [0.767, -1.752], [-159.641, 1.057], [30.963, 49.627]], "o": [[-9.577, 20.104], [-29.774, 10.099], [-53.238, -34.713], [-11.345, 9.144], [0, 0], [-12.68, 40.785], [-9.596, 18.481], [217.023, 3.206], [0, 0]], "v": [[156.407, -98.619], [88.97, -50.623], [11.406, -48.788], [-94.432, -54.075], [-147.86, -89.297], [-164.82, -55.285], [-167.222, 33.11], [-0.859, 156.443], [166.503, -76.936]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [{"i": [[0, 0], [30.388, -9.869], [16.45, 10.451], [41.398, -33.283], [-0.364, 24.574], [0, 0], [0.683, -1.73], [-222.532, 2.006], [26.038, 41.115]], "o": [[-10.858, 19.578], [-30.388, 9.869], [-53.9, -34.125], [-11.442, 9.007], [0, 0], [-0.615, 1.292], [-3.624, 9.443], [201.855, -1.256], [0, 0]], "v": [[155.43, -57.479], [85.194, -10.656], [6.569, -8.877], [-98.84, -15.236], [-154.597, -40.446], [-161.822, -7.561], [-160.058, 22.466], [4.596, 152.27], [162.335, -26.042]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[0, 0], [30.997, -9.793], [16.531, 10.306], [40.639, -32.324], [1.352, 23.401], [0, 0], [4.136, -8.044], [-225.45, 1.65], [22.755, 35.441]], "o": [[-11.712, 19.227], [-30.997, 9.793], [-54.341, -33.733], [-11.506, 8.915], [0, 0], [-0.565, 1.303], [-2.017, 5.417], [215.993, -6.981], [0, 0]], "v": [[153.612, -27.219], [81.111, 18.977], [1.381, 20.873], [-104.529, 15.764], [-152.874, -9.022], [-160.282, 11.797], [-160.199, 36.287], [4.982, 149.988], [158.682, -13.404]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [{"i": [[0, 0], [31.302, -9.755], [16.572, 10.233], [40.259, -31.845], [2.21, 22.814], [0, 0], [5.863, -11.201], [-226.909, 1.472], [21.113, 32.604]], "o": [[-12.138, 19.052], [-31.302, 9.754], [-54.562, -33.537], [-11.539, 8.87], [0, 0], [-0.541, 1.309], [-1.214, 3.404], [223.063, -9.843], [0, 0]], "v": [[152.703, -12.089], [79.07, 33.793], [-1.213, 35.748], [-107.373, 31.265], [-152.012, 6.691], [-156.512, 25.226], [-166.019, 43.698], [5.175, 148.847], [156.855, -7.085]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[0, 0], [31.606, -9.716], [16.612, 10.16], [39.879, -31.365], [3.069, 22.227], [0, 0], [7.59, -14.358], [-228.368, 1.294], [19.472, 29.766]], "o": [[-12.566, 18.877], [-31.606, 9.716], [-54.783, -33.341], [-11.571, 8.824], [0, 0], [-0.516, 1.315], [-0.41, 1.392], [230.132, -12.706], [0, 0]], "v": [[151.794, 3.041], [77.028, 48.61], [-3.808, 50.623], [-110.217, 46.765], [-151.151, 22.403], [-158.742, 31.155], [-171.84, 51.108], [5.368, 147.706], [155.028, -0.766]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[0, 0], [27.54, -6.337], [17.271, 8.974], [33.695, -23.555], [17.05, 12.668], [0, 0], [0.117, -1.584], [-154.935, 3.934], [18.499, 51.134]], "o": [[-19.522, 16.02], [-27.54, 6.337], [-58.38, -30.149], [-12.097, 8.077], [0, 0], [-0.113, 1.412], [-10.64, 33.2], [155.065, -8.066], [0, 0]], "v": [[153.364, 4.56], [78.2, 39.504], [6.412, 36.956], [-99.998, 33.097], [-152.572, 24.224], [-156.926, 33.489], [-154.994, 37.267], [4.935, 157.066], [157.531, 1.255]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[0, 0], [23.982, -3.381], [17.848, 7.936], [28.283, -16.722], [29.283, 4.304], [0, 0], [-0.281, -1.481], [-130.102, -0.27], [17.647, 69.831]], "o": [[-25.608, 13.522], [-23.982, 3.381], [-61.527, -27.356], [-12.557, 7.424], [0, 0], [0.239, 1.496], [-23.747, 64.532], [132.906, 0.276], [0, 0]], "v": [[154.738, 5.889], [79.226, 31.536], [15.354, 24.997], [-91.055, 21.138], [-153.816, 25.818], [-156.025, 23.5], [-155.253, 27.968], [0.369, 156.757], [159.72, 3.024]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [{"i": [[0, 0], [23.982, -3.381], [17.848, 7.936], [28.283, -16.722], [29.283, 4.304], [0, 0], [-0.281, -1.481], [-130.102, -0.27], [17.647, 69.831]], "o": [[-25.608, 13.522], [-23.982, 3.381], [-61.527, -27.356], [-12.557, 7.424], [0, 0], [0.239, 1.496], [-16.747, 60.032], [132.906, 0.276], [0, 0]], "v": [[154.738, 5.889], [79.226, 31.536], [15.354, 24.997], [-91.055, 21.138], [-153.816, 25.818], [-156.025, 23.5], [-155.253, 27.968], [0.369, 156.757], [159.72, 3.024]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [{"i": [[0, 0], [23.982, -3.381], [17.848, 7.936], [28.283, -16.722], [29.283, 4.304], [0, 0], [-0.281, -1.481], [-130.102, -0.27], [17.647, 69.831]], "o": [[-25.608, 13.522], [-23.982, 3.381], [-61.527, -27.356], [-12.557, 7.424], [0, 0], [0.239, 1.496], [-16.747, 60.032], [132.906, 0.276], [0, 0]], "v": [[154.738, 5.889], [79.226, 31.536], [15.354, 24.997], [-91.055, 21.138], [-153.816, 25.818], [-156.025, 23.5], [-155.253, 27.968], [0.369, 156.757], [159.72, 3.024]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [{"i": [[0, 0], [23.982, -3.381], [17.848, 7.936], [28.283, -16.722], [29.283, 4.304], [0, 0], [-0.281, -1.481], [-130.102, -0.27], [17.647, 69.831]], "o": [[-25.608, 13.522], [-23.982, 3.381], [-61.527, -27.356], [-12.557, 7.424], [0, 0], [0.239, 1.496], [-23.747, 64.532], [132.906, 0.276], [0, 0]], "v": [[154.738, 5.889], [79.226, 31.536], [15.354, 24.997], [-91.055, 21.138], [-153.816, 25.818], [-156.025, 23.5], [-155.253, 27.968], [0.369, 156.757], [159.72, 3.024]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [{"i": [[0, 0], [27.54, -6.337], [17.271, 8.974], [33.695, -23.555], [17.05, 12.668], [0, 0], [0.117, -1.584], [-154.935, 3.934], [18.499, 51.134]], "o": [[-19.522, 16.02], [-27.54, 6.337], [-58.38, -30.149], [-12.097, 8.077], [0, 0], [-0.113, 1.412], [-10.64, 33.2], [155.065, -8.066], [0, 0]], "v": [[153.364, 4.56], [78.2, 39.504], [6.412, 36.956], [-99.998, 33.097], [-152.572, 24.224], [-156.926, 33.489], [-154.994, 37.267], [4.935, 157.066], [157.531, 1.255]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[0, 0], [31.606, -9.716], [16.612, 10.16], [39.879, -31.365], [3.069, 22.227], [0, 0], [7.59, -14.358], [-228.368, 1.294], [19.472, 29.766]], "o": [[-12.566, 18.877], [-31.606, 9.716], [-54.783, -33.341], [-11.571, 8.824], [0, 0], [-0.516, 1.315], [-0.41, 1.392], [230.132, -12.706], [0, 0]], "v": [[151.794, 3.041], [77.028, 48.61], [-3.808, 50.623], [-110.217, 46.765], [-151.151, 22.403], [-158.742, 31.155], [-171.84, 51.108], [5.368, 147.706], [155.028, -0.766]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [{"i": [[0, 0], [31.302, -9.755], [16.572, 10.233], [40.259, -31.845], [2.21, 22.814], [0, 0], [5.863, -11.201], [-226.909, 1.472], [21.113, 32.604]], "o": [[-12.138, 19.052], [-31.302, 9.754], [-54.562, -33.537], [-11.539, 8.87], [0, 0], [-0.541, 1.309], [-1.214, 3.404], [223.063, -9.843], [0, 0]], "v": [[152.703, -12.089], [79.07, 33.793], [-1.213, 35.748], [-107.373, 31.265], [-152.012, 6.691], [-156.512, 25.226], [-166.019, 43.698], [5.175, 148.847], [156.855, -7.085]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [{"i": [[0, 0], [30.997, -9.793], [16.531, 10.306], [40.639, -32.324], [1.352, 23.401], [0, 0], [4.136, -8.044], [-225.45, 1.65], [22.755, 35.441]], "o": [[-11.712, 19.227], [-30.997, 9.793], [-54.341, -33.733], [-11.506, 8.915], [0, 0], [-0.565, 1.303], [-2.017, 5.417], [215.993, -6.981], [0, 0]], "v": [[153.612, -27.219], [81.111, 18.977], [1.381, 20.873], [-104.529, 15.764], [-152.874, -9.022], [-160.282, 11.797], [-160.199, 36.287], [4.982, 149.988], [158.682, -13.404]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [{"i": [[0, 0], [30.388, -9.869], [16.45, 10.451], [41.398, -33.283], [-0.364, 24.574], [0, 0], [0.683, -1.73], [-222.532, 2.006], [26.038, 41.115]], "o": [[-10.858, 19.578], [-30.388, 9.869], [-53.9, -34.125], [-11.442, 9.007], [0, 0], [-0.615, 1.292], [-3.624, 9.443], [201.855, -1.256], [0, 0]], "v": [[155.43, -57.479], [85.194, -10.656], [6.569, -8.877], [-98.84, -15.236], [-154.597, -40.446], [-161.822, -7.561], [-160.058, 22.466], [4.596, 152.27], [162.335, -26.042]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [{"i": [[0, 0], [29.774, -10.099], [16.328, 10.67], [42.537, -34.721], [-2.939, 26.335], [12.68, -40.785], [0.767, -1.752], [-159.641, 1.057], [30.963, 49.627]], "o": [[-9.577, 20.104], [-29.774, 10.099], [-53.238, -34.713], [-11.345, 9.144], [0, 0], [-12.68, 40.785], [-9.596, 18.481], [217.023, 3.206], [0, 0]], "v": [[156.407, -98.619], [88.97, -50.623], [11.406, -48.788], [-94.432, -54.075], [-147.86, -89.297], [-164.82, -55.285], [-167.222, 33.11], [-0.859, 156.443], [166.503, -76.936]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[0, 0], [25.851, -9.448], [16.167, 10.962], [44.055, -36.638], [-6.372, 28.682], [13.6, -25.75], [0.878, -1.78], [-193.869, -25.507], [37.53, 60.976]], "o": [[-7.869, 20.805], [-25.851, 9.448], [-52.354, -35.497], [-11.216, 9.328], [0, 0], [-13.6, 25.75], [-6.309, 17.282], [233.131, 1.993], [0, 0]], "v": [[145.293, -149.639], [87.797, -101.996], [17.854, -102.003], [-88.555, -105.862], [-127.878, -146.682], [-155.4, -103.75], [-180.691, -27.282], [-8.131, 162.007], [173.06, -120.462]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [{"i": [[0.105, 0.534], [20.672, -4.778], [14.691, 11.244], [27.436, -23.775], [-0.238, 19.502], [0, -1.41], [-0.077, -6.649], [-194.554, -6.168], [24.122, 51.552]], "o": [[-9.09, 7.854], [-30.92, 3.811], [-32.059, -36.756], [-15.937, 6.574], [-0.095, 1.265], [-0.503, 2.725], [-15.942, 34.708], [184.837, 6.104], [-0.291, -0.124]], "v": [[133.574, -95.499], [91.531, -77.514], [19.309, -82.244], [-65.686, -80.725], [-125.781, -105.41], [-148.119, -70.931], [-153.656, -56.198], [-0.203, 135.636], [146.873, -77.5]], "c": true}]}, {"t": 249, "s": [{"i": [[0.211, 1.068], [14.984, -0.899], [14.844, 2.014], [22.736, -12.544], [5.897, 10.322], [0, -2.82], [-0.895, -9.947], [-149.384, -3.084], [11.005, 44.836]], "o": [[-10.311, -5.097], [-34.143, -3.116], [-19.791, -18.822], [-21.756, 1.912], [-0.19, 2.531], [-0.218, 4.2], [-14.325, 38.884], [132.428, 3.052], [-0.581, -0.247]], "v": [[118.478, -44.794], [95.5, -52.098], [26.765, -50.735], [-43.986, -55.706], [-125.748, -66.884], [-139.202, -37.645], [-144.465, -16.581], [1.025, 107.09], [120.998, -35.538]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "脸 3", "parent": 9, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [100]}, {"t": 250, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250.287, 322.717, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.713, 65.717, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 77, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 89, "s": [117, 118, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100, "s": [100, 87, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 108, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 138, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 188, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 218, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 226, "s": [100, 87, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 237, "s": [117, 118, 100]}, {"t": 249, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 58, "s": [{"i": [[-18, -5], [-5.311, -58.113], [0, -11.418], [82.464, 0], [2, 42], [-0.828, 7.267], [-23.353, 27.732], [-17.312, 5.771], [-14, 0]], "o": [[28, 13], [0.994, 10.872], [0, 49], [-94.057, 0], [-0.335, -7.045], [4.642, -40.756], [16, -19], [3, -1], [18, 0]], "v": [[54, -136], [112, -44], [113, -13], [19.862, 95.262], [-95, 1], [-94.642, -19.244], [-64, -101], [-12, -136], [19, -142]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[-12.923, -9.054], [-10.708, -16.953], [-3.126, -14.854], [114.87, -0.97], [12.232, 38.037], [-0.11, 9.74], [-38.833, 28.485], [-16.69, 3.946], [-33.033, -2.186]], "o": [[19.327, 13.696], [6.188, 9.797], [9.261, 70.07], [-96.993, 0.819], [-1.619, -9.249], [0.615, -52.202], [9.882, -7.321], [19.56, -3.554], [13.359, 2.976]], "v": [[66.031, -119.608], [107.669, -78.709], [128.096, -31.482], [5.351, 106.269], [-141.874, 25.051], [-144.224, -3.309], [-78.525, -116.091], [-36.953, -134.358], [14.891, -138.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[-11.593, -4.07], [-13.588, -72.064], [-0.171, -19.825], [112.229, 1.757], [4.139, 58.703], [-4.251, 21.113], [-40.166, 28.549], [-14.768, 5.076], [-20.312, 0.354]], "o": [[38.907, 15.18], [2.888, 14.462], [0.618, 71.783], [-90.003, -1.409], [-0.562, -9.518], [8.526, -42.344], [18.617, -13.574], [12.232, -3.424], [15.188, 0.354]], "v": [[57.451, -163.592], [164.985, -44.995], [172.074, 9.489], [3.359, 144.92], [-150.825, 23.156], [-147.35, -26.331], [-70.259, -141.338], [-28.875, -162.489], [15.669, -169.016]], "c": true}]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[-12.603, -4.368], [-15.442, -68.858], [-6.515, -14.388], [114.009, -3.605], [-40.114, 53.866], [0, 10.117], [-41.196, 28.599], [-24.397, 5.808], [-14.673, 0.754]], "o": [[36.397, 14.023], [3.305, 14.735], [39.378, 86.964], [-144.736, 4.577], [-0.364, -10.134], [0, -53.949], [21.481, -14.791], [11.353, -2.238], [28.827, -0.683]], "v": [[69.96, -154.714], [155.3, -47.696], [164.979, -0.876], [3.094, 162.011], [-150.028, 19.722], [-151.792, -0.876], [-83.623, -131.073], [-20.496, -159.431], [16.031, -163.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.123}, "t": 108, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [129.48, -2.741], [-24.032, 57.126], [0, 10.117], [-41.196, 28.599], [-26.564, 2.101], [-14.923, -21.65]], "o": [[45.689, 2.905], [1.374, 15.039], [16.97, 72.428], [-130.071, 2.754], [-1.815, -9.586], [0, -53.949], [6.358, -29.371], [22.129, -1.75], [4.4, -11.182]], "v": [[59.9, -195.567], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-35.079, -194.513], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-4.504, -15.106], [122.15, -1.438], [-28.589, 59.142], [0, 10.117], [-41.196, 28.599], [-22.993, 2.182], [-17.673, -18.4]], "o": [[44.386, 2.64], [1.374, 15.039], [22.923, 75.758], [-130.086, 1.444], [-1.815, -9.586], [0, -53.949], [11.481, -31.339], [18.981, -0.918], [9.077, -15.15]], "v": [[63.205, -184.263], [158.3, -46.546], [164.979, -0.876], [6.118, 158.7], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-32.849, -182.094], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 124, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 147, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 179, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 202, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-4.504, -15.106], [122.15, -1.438], [-28.589, 59.142], [0, 10.117], [-41.196, 28.599], [-22.993, 2.182], [-17.673, -18.4]], "o": [[44.386, 2.64], [1.374, 15.039], [22.923, 75.758], [-130.086, 1.444], [-1.815, -9.586], [0, -53.949], [11.481, -31.339], [18.981, -0.918], [9.077, -15.15]], "v": [[63.205, -184.263], [158.3, -46.546], [164.979, -0.876], [6.118, 158.7], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-32.849, -182.094], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.123}, "t": 218, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [129.48, -2.741], [-24.032, 57.126], [0, 10.117], [-41.196, 28.599], [-26.564, 2.101], [-14.923, -21.65]], "o": [[45.689, 2.905], [1.374, 15.039], [16.97, 72.428], [-130.071, 2.754], [-1.815, -9.586], [0, -53.949], [6.358, -29.371], [22.129, -1.75], [4.4, -11.182]], "v": [[59.9, -195.567], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-35.079, -194.513], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[-12.603, -4.368], [-15.442, -68.858], [-6.515, -14.388], [114.009, -3.605], [-40.114, 53.866], [0, 10.117], [-41.196, 28.599], [-24.397, 5.808], [-14.673, 0.754]], "o": [[36.397, 14.023], [3.305, 14.735], [39.378, 86.964], [-144.736, 4.577], [-0.364, -10.134], [0, -53.949], [21.481, -14.791], [11.353, -2.238], [28.827, -0.683]], "v": [[69.96, -154.714], [155.3, -47.696], [164.979, -0.876], [3.094, 162.011], [-150.028, 19.722], [-151.792, -0.876], [-83.623, -131.073], [-20.496, -159.431], [16.031, -163.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[-11.593, -4.07], [-13.588, -72.064], [-0.171, -19.825], [112.229, 1.757], [4.139, 58.703], [-4.251, 21.113], [-40.166, 28.549], [-14.768, 5.076], [-20.312, 0.354]], "o": [[38.907, 15.18], [2.888, 14.462], [0.618, 71.783], [-90.003, -1.409], [-0.562, -9.518], [8.526, -42.344], [18.617, -13.574], [12.232, -3.424], [15.188, 0.354]], "v": [[57.451, -163.592], [164.985, -44.995], [172.074, 9.489], [3.359, 144.92], [-150.825, 23.156], [-147.35, -26.331], [-70.259, -141.338], [-28.875, -162.489], [15.669, -169.016]], "c": true}]}, {"t": 249, "s": [{"i": [[-12.923, -9.054], [-10.708, -16.953], [-3.126, -14.854], [114.87, -0.97], [12.232, 38.037], [-0.11, 9.74], [-38.833, 28.485], [-16.69, 3.946], [-33.033, -2.186]], "o": [[19.327, 13.696], [6.188, 9.797], [9.261, 70.07], [-96.993, 0.819], [-1.619, -9.249], [0.615, -52.202], [9.882, -7.321], [19.56, -3.554], [13.359, 2.976]], "v": [[66.031, -119.608], [107.669, -78.709], [128.096, -31.482], [5.351, 106.269], [-141.874, 25.051], [-144.224, -3.309], [-78.525, -116.091], [-36.953, -134.358], [14.891, -138.226]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"t": 77, "s": [100]}], "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [0, 1, 0.859, 0.376, 0.499, 1, 0.833, 0.188, 0.999, 1, 0.808, 0]}, {"t": 78.064453125, "s": [0, 0.745, 0.871, 0.522, 0.499, 0.702, 0.855, 0.431, 0.998, 0.659, 0.839, 0.341]}], "ix": 9}}, "s": {"a": 0, "k": [-101, -30], "ix": 5}, "e": {"a": 0, "k": [130.405, -30], "ix": 6}, "t": 1, "nm": "Gradient 14", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.643, 0.412], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "脸", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "叶子", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [-10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [5]}, {"t": 276, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [259.5, 266, 0], "to": [-2.333, 0.167, 0], "ti": [-2.5, -0.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [245.5, 267, 0], "to": [2.5, 0.5, 0], "ti": [-2.333, 0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [274.5, 269, 0], "to": [2.333, -0.167, 0], "ti": [2.5, 0.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [259.5, 266, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [260.5, 266, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [259.5, 266, 0], "to": [2.5, 0.5, 0], "ti": [2.333, -0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [274.5, 269, 0], "to": [-2.333, 0.167, 0], "ti": [2.5, 0.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [245.5, 267, 0], "to": [-2.5, -0.5, 0], "ti": [-2.333, 0.167, 0]}, {"t": 276, "s": [259.5, 266, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [259.5, 266, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "形状图层 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [256, 256, 0], "to": [-1.5, 4.833, 0], "ti": [1.5, -4.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [247, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [247, 285, 0], "to": [1.5, -4.833, 0], "ti": [-1.5, 4.833, 0]}, {"t": 256, "s": [256, 256, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 51, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 71, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 75, "s": [118, 118, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [106, 106, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 81, "s": [77.208, 77.208, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 221, "s": [110.801, 110.801, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 230, "s": [70.961, 70.961, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 248, "s": [102.566, 102.566, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 251, "s": [107, 107, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255, "s": [100, 100, 100]}, {"t": 275, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [276.371, 283.395], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 30, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0, 0, 0, 0.347, 0, 0.09, 0.012, 0.693, 0, 0.18, 0.024, 0.847, 0.009, 0.288, 0.044, 1, 0.019, 0.396, 0.064, 0, 0, 0.287, 0, 0.575, 0, 0.787, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [113, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "corlor 11", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [6.566, -23.117], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "脸白 4", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [-14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [14]}, {"t": 276, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [259.056, 397.507, 0], "to": [-10.333, -1.5, 0], "ti": [-9.833, 0.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [197.056, 388.507, 0], "to": [9.833, -0.5, 0], "ti": [-10.333, -1.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [318.056, 394.507, 0], "to": [10.333, 1.5, 0], "ti": [9.833, -0.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [259.056, 397.507, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [259.056, 397.507, 0], "to": [9.833, -0.5, 0], "ti": [10.333, 1.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [318.056, 394.507, 0], "to": [-10.333, -1.5, 0], "ti": [9.833, -0.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [197.056, 388.507, 0], "to": [-9.833, 0.5, 0], "ti": [-10.333, -1.5, 0]}, {"t": 276, "s": [259.056, 397.507, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3.056, 141.507, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[0.44, 2.229], [0, 0], [0, 0], [34.796, 28.679], [4.746, -5.191], [0, -5.888], [-1.312, -6.93], [-94.057, 0], [-6.155, 43.911]], "o": [[-6.023, -30.524], [-29.608, -15.217], [0, 0], [-39.228, -33.26], [-0.396, 5.285], [0, 7.314], [-11.63, 38.97], [67.617, 0], [-1.214, -0.517]], "v": [[129.574, -7.983], [100.063, -51.275], [41.491, -40.49], [-21.471, -43.935], [-97.549, -49.463], [-98.142, -32.744], [-96.144, -11.346], [16.362, 81.762], [132.089, -3.931]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [{"i": [[0.316, 1.601], [6.748, -0.971], [14.182, -2.46], [26.095, 12.851], [12.031, 1.141], [0, -4.229], [-1.021, -5.395], [-104.213, 0], [-0.659, 51.657]], "o": [[-11.532, -18.048], [-28.137, -16.494], [-18.653, 3.578], [-33.064, -12.286], [-0.284, 3.796], [0.067, 5.675], [-12.707, 43.059], [80.019, 0], [-0.872, -0.371]], "v": [[126.766, 16.551], [91.904, -11.691], [14.237, -11.41], [-36.863, -20.956], [-129.773, -9.277], [-130.835, 8.294], [-128.227, 31.013], [10.746, 113.167], [129.949, 18.739]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[0.193, 0.979], [16.357, -0.887], [14.954, 2.76], [25.129, -9.722], [4.874, 11.852], [0, -2.585], [-0.874, -10.706], [-156.912, -3.598], [12.949, 43.699]], "o": [[-10.107, -2.938], [-35.143, -0.887], [-19.98, -22.555], [-19.871, 4.278], [-0.174, 2.32], [-0.265, 3.954], [-14.594, 38.188], [141.163, 3.561], [-0.533, -0.227]], "v": [[123.808, -50.382], [94.643, -57.113], [20.98, -52.945], [-46.629, -59.778], [-126.533, -74.766], [-142.052, -43.582], [-148.626, -22.794], [6.404, 113.66], [125.051, -41.699]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [25.851, -9.448], [16.166, 10.962], [44.055, -36.638], [-6.372, 28.682], [0, 0], [0.878, -1.78], [-218.869, -20.007], [37.53, 60.976]], "o": [[-7.869, 20.805], [-25.851, 9.448], [-52.354, -35.497], [-11.216, 9.328], [0, 0], [-0.788, 1.25], [-17.559, 30.532], [236.438, 21.613], [0, 0]], "v": [[145.293, -149.639], [87.797, -101.996], [17.854, -102.003], [-88.555, -105.862], [-124.878, -142.182], [-155.4, -103.75], [-159.691, -96.282], [-8.131, 162.007], [173.06, -120.462]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [{"i": [[0, 0], [28.888, -8.619], [16.45, 10.451], [41.398, -33.283], [-0.364, 24.574], [0, 0], [0.683, -1.73], [-222.532, 2.006], [26.038, 41.115]], "o": [[-10.858, 19.578], [-28.888, 8.619], [-53.9, -34.125], [-11.442, 9.007], [0, 0], [-0.615, 1.292], [-3.624, 9.443], [201.855, -1.256], [0, 0]], "v": [[149.43, -52.479], [82.194, -8.156], [6.569, -8.877], [-98.84, -15.236], [-154.597, -40.446], [-161.822, -7.561], [-159.058, 19.466], [4.596, 152.27], [161.585, -44.292]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[0, 0], [30.247, -9.168], [16.531, 10.306], [40.639, -32.324], [1.352, 23.401], [0, 0], [0.627, -1.716], [-217.62, 5.222], [22.755, 35.441]], "o": [[-11.712, 19.227], [-30.247, 9.168], [-54.341, -33.733], [-11.506, 8.915], [0, 0], [-0.565, 1.303], [0.358, 3.417], [191.743, -4.231], [0, 0]], "v": [[150.612, -24.719], [79.611, 20.227], [1.381, 20.873], [-104.529, 15.764], [-152.874, -9.022], [-157.657, 25.422], [-163.199, 36.037], [8.232, 149.488], [158.307, -22.529]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[0, 0], [31.606, -9.716], [16.612, 10.16], [39.879, -31.365], [3.069, 22.227], [0, 0], [0.571, -1.701], [-212.708, 8.439], [19.472, 29.766]], "o": [[-12.566, 18.877], [-31.606, 9.716], [-54.783, -33.341], [-11.571, 8.824], [0, 0], [-0.516, 1.315], [4.34, -2.608], [181.632, -7.206], [0, 0]], "v": [[151.794, 3.041], [77.028, 48.61], [-3.808, 50.623], [-110.217, 46.765], [-151.151, 22.403], [-165.492, 47.405], [-167.34, 52.608], [11.868, 146.706], [155.028, -0.766]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[0, 0], [23.982, -3.381], [17.848, 7.936], [28.283, -16.722], [29.283, 4.304], [0, 0], [-0.281, -1.481], [-130.102, -0.27], [17.647, 69.831]], "o": [[-25.608, 13.522], [-23.982, 3.381], [-61.527, -27.356], [-12.557, 7.424], [0, 0], [0.239, 1.496], [-23.747, 64.532], [132.906, 0.276], [0, 0]], "v": [[154.738, 5.889], [79.226, 31.536], [15.354, 24.997], [-91.055, 21.138], [-153.816, 25.818], [-156.025, 23.5], [-155.253, 27.968], [0.369, 156.757], [159.72, 3.024]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [{"i": [[0, 0], [23.982, -3.381], [17.848, 7.936], [28.283, -16.722], [29.283, 4.304], [0, 0], [-0.281, -1.481], [-130.102, -0.27], [17.647, 69.831]], "o": [[-25.608, 13.522], [-23.982, 3.381], [-61.527, -27.356], [-12.557, 7.424], [0, 0], [0.239, 1.496], [-16.747, 60.032], [132.906, 0.276], [0, 0]], "v": [[154.738, 5.889], [79.226, 31.536], [15.354, 24.997], [-91.055, 21.138], [-153.816, 25.818], [-156.025, 23.5], [-155.253, 27.968], [0.369, 156.757], [159.72, 3.024]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [{"i": [[0.193, 0.979], [16.357, -0.887], [14.954, 2.76], [25.129, -9.722], [4.874, 11.852], [0, -2.585], [-0.874, -10.706], [-156.912, -3.598], [12.949, 43.699]], "o": [[-10.107, -2.938], [-35.143, -0.887], [-19.98, -22.555], [-19.871, 4.278], [-0.174, 2.32], [-0.265, 3.954], [-14.594, 38.188], [141.163, 3.561], [-0.533, -0.227]], "v": [[123.808, -50.382], [94.643, -57.113], [20.98, -52.945], [-46.629, -59.778], [-126.533, -74.766], [-142.052, -43.582], [-148.626, -22.794], [6.404, 113.66], [125.051, -41.699]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [{"i": [[0.316, 1.601], [6.748, -0.971], [14.182, -2.46], [26.095, 12.851], [12.031, 1.141], [0, -4.229], [-1.021, -5.395], [-104.213, 0], [-0.659, 51.657]], "o": [[-11.532, -18.048], [-28.137, -16.494], [-18.653, 3.578], [-33.064, -12.286], [-0.284, 3.796], [0.067, 5.675], [-12.707, 43.059], [80.019, 0], [-0.872, -0.371]], "v": [[126.766, 16.551], [91.904, -11.691], [14.237, -11.41], [-36.863, -20.956], [-129.773, -9.277], [-130.835, 8.294], [-128.227, 31.013], [10.746, 113.167], [129.949, 18.739]], "c": true}]}, {"t": 268, "s": [{"i": [[0.44, 2.229], [0, 0], [0, 0], [34.796, 28.679], [4.746, -5.191], [0, -5.888], [-1.312, -6.93], [-94.057, 0], [-6.155, 43.911]], "o": [[-6.023, -30.524], [-29.608, -15.217], [0, 0], [-39.228, -33.26], [-0.396, 5.285], [0, 7.314], [-11.63, 38.97], [67.617, 0], [-1.214, -0.517]], "v": [[129.574, -7.983], [100.063, -51.275], [41.491, -40.49], [-21.471, -43.935], [-97.549, -49.463], [-98.142, -32.744], [-96.144, -11.346], [16.362, 81.762], [132.089, -3.931]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "脸", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250.287, 322.717, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.713, 65.717, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 77, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 89, "s": [117, 118, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [100, 87, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 108, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 138, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 188, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 218, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 226, "s": [100, 87, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 237, "s": [117, 118, 100]}, {"t": 249, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[-18, -5], [-5.311, -58.113], [0, -11.418], [82.464, 0], [2, 42], [-0.828, 7.267], [-23.353, 27.732], [-17.312, 5.771], [-14, 0]], "o": [[28, 13], [0.994, 10.872], [0, 49], [-94.057, 0], [-0.335, -7.045], [4.642, -40.756], [16, -19], [3, -1], [18, 0]], "v": [[54, -136], [112, -44], [113, -13], [19.862, 95.262], [-95, 1], [-94.642, -19.244], [-64, -101], [-12, -136], [19, -142]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[-12.923, -9.054], [-10.708, -16.953], [-3.126, -14.854], [114.87, -0.97], [12.232, 38.037], [-0.11, 9.74], [-38.833, 28.485], [-16.69, 3.946], [-33.033, -2.186]], "o": [[19.327, 13.696], [6.188, 9.797], [9.261, 70.07], [-96.993, 0.819], [-1.619, -9.249], [0.615, -52.202], [9.882, -7.321], [19.56, -3.554], [13.359, 2.976]], "v": [[66.031, -119.608], [107.669, -78.709], [128.096, -31.482], [5.351, 106.269], [-141.874, 25.051], [-144.224, -3.309], [-78.525, -116.091], [-36.953, -134.358], [14.891, -138.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[-11.593, -4.07], [-13.588, -72.064], [-0.171, -19.825], [112.234, -1.407], [4.139, 58.703], [-4.251, 21.113], [-40.166, 28.549], [-14.768, 5.076], [-20.312, 0.354]], "o": [[38.907, 15.18], [2.888, 14.462], [0.618, 71.783], [-90.43, 1.134], [-0.562, -9.518], [8.526, -42.344], [18.617, -13.574], [12.232, -3.424], [15.188, 0.354]], "v": [[57.451, -163.592], [164.985, -44.995], [172.074, 9.489], [3.359, 144.92], [-150.825, 23.156], [-147.35, -26.331], [-70.259, -141.338], [-28.875, -162.489], [15.669, -169.016]], "c": true}]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[-12.603, -4.368], [-15.442, -68.858], [-6.515, -14.388], [114.009, -3.605], [-40.114, 53.866], [0, 10.117], [-41.196, 28.599], [-24.397, 5.808], [-14.673, 0.754]], "o": [[36.397, 14.023], [3.305, 14.735], [39.378, 86.964], [-144.736, 4.577], [-0.364, -10.134], [0, -53.949], [21.481, -14.791], [11.353, -2.238], [28.827, -0.683]], "v": [[69.96, -154.714], [155.3, -47.696], [164.979, -0.876], [3.094, 162.011], [-150.028, 19.722], [-151.792, -0.876], [-83.623, -131.073], [-20.496, -159.431], [16.031, -163.285]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.123}, "t": 108, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [129.48, -2.741], [-24.032, 57.126], [0, 10.117], [-41.196, 28.599], [-20.862, 3.293], [-22.205, -18.725]], "o": [[45.689, 2.905], [1.374, 15.039], [16.97, 72.428], [-130.071, 2.754], [-1.815, -9.586], [0, -53.949], [6.358, -29.371], [18.889, -2.982], [4.4, -11.182]], "v": [[61.4, -182.067], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-43.079, -179.513], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 124, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 147, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 179, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0}, "t": 202, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [114.066, 0], [-16.087, 53.904], [0, 10.117], [-41.196, 28.599], [-21.12, 0], [-15.796, -20.499]], "o": [[41.671, 2.785], [1.374, 15.039], [16.97, 72.428], [-130.102, 0], [-1.815, -9.586], [0, -53.949], [4.798, -35.211], [15.508, 0], [9.519, -20.499]], "v": [[64.409, -190.01], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-30.676, -190.01], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.123}, "t": 218, "s": [{"i": [[-23.382, 0], [-7.347, -80.384], [-3.603, -15.378], [129.48, -2.741], [-24.032, 57.126], [0, 10.117], [-41.196, 28.599], [-20.862, 3.293], [-22.205, -18.725]], "o": [[45.689, 2.905], [1.374, 15.039], [16.97, 72.428], [-130.071, 2.754], [-1.815, -9.586], [0, -53.949], [6.358, -29.371], [18.889, -2.982], [4.4, -11.182]], "v": [[61.4, -182.067], [158.3, -46.546], [164.979, -0.876], [6.594, 157.511], [-149.028, 28.722], [-151.792, -0.876], [-83.623, -131.073], [-43.079, -179.513], [16.281, -159.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[-12.603, -4.368], [-15.442, -68.858], [-6.515, -14.388], [114.009, -3.605], [-40.114, 53.866], [0, 10.117], [-41.196, 28.599], [-24.397, 5.808], [-14.673, 0.754]], "o": [[36.397, 14.023], [3.305, 14.735], [39.378, 86.964], [-144.736, 4.577], [-0.364, -10.134], [0, -53.949], [21.481, -14.791], [11.353, -2.238], [28.827, -0.683]], "v": [[69.96, -154.714], [155.3, -47.696], [164.979, -0.876], [3.094, 162.011], [-150.028, 19.722], [-151.792, -0.876], [-83.623, -131.073], [-20.496, -159.431], [16.031, -163.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[-11.593, -4.07], [-13.588, -72.064], [-0.171, -19.825], [112.234, -1.407], [4.139, 58.703], [-4.251, 21.113], [-40.166, 28.549], [-14.768, 5.076], [-20.312, 0.354]], "o": [[38.907, 15.18], [2.888, 14.462], [0.618, 71.783], [-90.43, 1.134], [-0.562, -9.518], [8.526, -42.344], [18.617, -13.574], [12.232, -3.424], [15.188, 0.354]], "v": [[57.451, -163.592], [164.985, -44.995], [172.074, 9.489], [3.359, 144.92], [-150.825, 23.156], [-147.35, -26.331], [-70.259, -141.338], [-28.875, -162.489], [15.669, -169.016]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[-12.923, -9.054], [-10.708, -16.953], [-3.126, -14.854], [114.87, -0.97], [12.232, 38.037], [-0.11, 9.74], [-38.833, 28.485], [-16.69, 3.946], [-33.033, -2.186]], "o": [[19.327, 13.696], [6.188, 9.797], [9.261, 70.07], [-96.993, 0.819], [-1.619, -9.249], [0.615, -52.202], [9.882, -7.321], [19.56, -3.554], [13.359, 2.976]], "v": [[66.031, -119.608], [107.669, -78.709], [128.096, -31.482], [5.351, 106.269], [-141.874, 25.051], [-144.224, -3.309], [-78.525, -116.091], [-36.953, -134.358], [14.891, -138.226]], "c": true}]}, {"t": 260, "s": [{"i": [[-18, -5], [-5.311, -58.113], [0, -11.418], [82.464, 0], [2, 42], [-0.828, 7.267], [-23.353, 27.732], [-17.312, 5.771], [-14, 0]], "o": [[28, 13], [0.994, 10.872], [0, 49], [-94.057, 0], [-0.335, -7.045], [4.642, -40.756], [16, -19], [3, -1], [18, 0]], "v": [[54, -136], [112, -44], [113, -13], [19.862, 95.262], [-95, 1], [-94.642, -19.244], [-64, -101], [-12, -136], [19, -142]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [0, 1, 0.859, 0.376, 0.499, 1, 0.833, 0.188, 0.999, 1, 0.808, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [0, 0.747, 0.871, 0.521, 0.499, 0.704, 0.855, 0.43, 0.998, 0.661, 0.839, 0.339]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78.064, "s": [0, 0.745, 0.871, 0.522, 0.499, 0.702, 0.855, 0.431, 0.998, 0.659, 0.839, 0.341]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [0, 0.745, 0.871, 0.522, 0.499, 0.702, 0.855, 0.431, 0.998, 0.659, 0.839, 0.341]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 236.064, "s": [0, 0.747, 0.871, 0.521, 0.499, 0.704, 0.855, 0.43, 0.998, 0.661, 0.839, 0.339]}, {"t": 248.064453125, "s": [0, 1, 0.859, 0.376, 0.499, 1, 0.833, 0.188, 0.999, 1, 0.808, 0]}], "ix": 9}}, "s": {"a": 0, "k": [-101, -30], "ix": 5}, "e": {"a": 0, "k": [130.405, -30], "ix": 6}, "t": 1, "nm": "corl<PERSON> ", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.643, 0.412], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "脸", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "右边眉毛", "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [305.017, 172.862, 0], "to": [0, 0, 0], "ti": [0, 2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 28, "s": [305.017, 156.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 33, "s": [305.017, 156.862, 0], "to": [0, 2.667, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [305.017, 172.862, 0], "to": [0, 0, 0], "ti": [0, 2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 43, "s": [305.017, 156.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [305.017, 156.862, 0], "to": [0, 2.667, 0], "ti": [0, -2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [305.017, 172.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 58, "s": [305.017, 172.862, 0], "to": [-1.333, 4, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.626, "y": 1}, "o": {"x": 0.158, "y": 0}, "t": 70, "s": [297.017, 196.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.437, "y": 0}, "t": 83, "s": [306.017, 200.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [299.017, 198.862, 0], "to": [0, 13.652, 0], "ti": [0.154, -36.221, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [289.358, 327.504, 0], "to": [-0.119, 27.895, 0], "ti": [-0.689, 0.363, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [297.017, 341.862, 0], "to": [0.829, -0.436, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [312.045, 310.098, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [312.045, 310.098, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [294.045, 308.098, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [294.045, 308.098, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [312.045, 310.098, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [312.045, 310.098, 0], "to": [0, 0, 0], "ti": [0.829, -0.436, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [297.017, 341.862, 0], "to": [-0.689, 0.363, 0], "ti": [-0.119, 27.895, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [289.358, 327.504, 0], "to": [0.154, -36.221, 0], "ti": [0, 13.652, 0]}, {"i": {"x": 0.563, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [299.017, 198.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.842, "y": 1}, "o": {"x": 0.374, "y": 0}, "t": 243, "s": [306.017, 200.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 256, "s": [297.017, 196.862, 0], "to": [0, 0, 0], "ti": [-1.333, 4, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0.333}, "t": 268, "s": [305.017, 172.862, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 273, "s": [305.017, 172.862, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [49.017, -65.138, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[0.695, 0.195], [3.645, -0.372], [3.203, -2.058], [0.733, -3.794], [-1.736, -5.53], [-3.134, 0.358], [-4.959, 3.166], [-1.403, 3.634], [1.042, 0.92], [0.695, 0.404]], "o": [[-4.087, -1.316], [-3.645, 0.372], [-3.203, 2.058], [-0.733, 3.794], [0.936, 2.233], [3.134, -0.358], [7.438, -4.748], [1.403, -3.634], [-0.695, -0.613], [-0.695, -0.404]], "v": [[60.414, -78.335], [48.815, -79.75], [38.544, -76.105], [32.64, -67.326], [34.145, -53.34], [40.344, -50.473], [52.577, -55.704], [64.94, -68.678], [64.583, -75.909], [62.498, -77.435]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [{"i": [[0.695, 0.195], [3.645, -0.372], [3.203, -2.058], [0.733, -3.794], [-1.736, -5.53], [-3.134, 0.358], [-4.959, 3.166], [-1.403, 3.634], [1.042, 0.92], [0.695, 0.404]], "o": [[-4.087, -1.316], [-3.645, 0.372], [-3.203, 2.058], [-0.733, 3.794], [0.936, 2.233], [3.134, -0.358], [7.438, -4.748], [1.403, -3.634], [-0.695, -0.613], [-0.695, -0.404]], "v": [[60.414, -78.335], [48.815, -79.75], [38.544, -76.105], [32.64, -67.326], [34.145, -53.34], [40.344, -50.473], [52.577, -55.704], [64.94, -68.678], [64.583, -75.909], [62.498, -77.435]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [{"i": [[0.992, 1.207], [3.345, 1.287], [4.177, 0.571], [0.944, -8.494], [-2.225, -5.77], [-2.68, 0.906], [-3.618, 2.31], [-2.593, 2.392], [-1.513, 2.189], [-0.831, 2.549]], "o": [[-2.758, -2.356], [-4.143, -1.622], [-7.834, -1.804], [-0.744, 5.691], [1.048, 2.291], [2.287, -0.261], [5.617, -4.278], [2.094, -1.733], [1.8, -2.248], [0.831, -2.549]], "v": [[47.57, -128.957], [39.205, -134.753], [28.073, -137.883], [13.801, -123.997], [15.618, -105.132], [23.696, -102.192], [32.383, -105.972], [41.843, -113.517], [47.388, -119.064], [49.701, -123.638]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0.338, 0.095], [3.075, 2.78], [9.351, 4.483], [1.134, -12.725], [-2.664, -5.985], [-2.27, 1.4], [-2.412, 1.54], [-0.888, 1.911], [0.507, 0.447], [-3.314, 1.016]], "o": [[-0.644, -0.96], [-3.175, -4.97], [-12.002, -5.279], [-0.753, 7.398], [1.149, 2.342], [1.525, -0.174], [3.783, -2.498], [0.682, -1.768], [2.662, -2.393], [-0.338, -0.197]], "v": [[36.011, -174.517], [30.675, -183.78], [18.649, -193.483], [-3.155, -175], [-1.056, -151.743], [8.712, -148.74], [11.595, -153.587], [18.68, -160.879], [22.056, -167.524], [30.286, -172.703]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [{"i": [[0, 0], [7.694, 2.585], [5.925, 3.563], [1.513, -21.185], [-3.542, -6.417], [-1.452, 2.387], [0, 0], [-0.399, 0.279], [0, 0], [-7.112, 1.596]], "o": [[2.619, -0.624], [-5.972, -2.006], [-20.338, -12.231], [-0.772, 10.813], [1.35, 2.446], [0, 0], [0.321, -0.367], [0, 0], [5.843, -4.079], [0, 0]], "v": [[44.537, -170.458], [51.949, -174.743], [34.853, -182.972], [-8.098, -170.632], [-2.273, -146.625], [3.574, -146.413], [8.796, -151.871], [10.881, -153.78], [17.793, -159.869], [35.795, -168.509]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [{"i": [[0, 0], [7.694, 2.585], [5.925, 3.563], [1.513, -21.185], [-3.542, -6.417], [-1.452, 2.387], [0, 0], [-0.399, 0.279], [0, 0], [-7.112, 1.596]], "o": [[2.619, -0.624], [-5.972, -2.006], [-20.338, -12.231], [-0.772, 10.813], [1.35, 2.446], [0, 0], [0.321, -0.367], [0, 0], [5.843, -4.079], [0, 0]], "v": [[44.537, -170.458], [51.949, -174.743], [34.853, -182.972], [-8.098, -170.632], [-2.273, -146.625], [3.574, -146.413], [8.796, -151.871], [10.881, -153.78], [17.793, -159.869], [35.795, -168.509]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[0.338, 0.095], [3.075, 2.78], [9.351, 4.483], [1.134, -12.725], [-2.664, -5.985], [-2.27, 1.4], [-2.412, 1.54], [-0.888, 1.911], [0.507, 0.447], [-3.314, 1.016]], "o": [[-0.644, -0.96], [-3.175, -4.97], [-12.002, -5.279], [-0.753, 7.398], [1.149, 2.342], [1.525, -0.174], [3.783, -2.498], [0.682, -1.768], [2.662, -2.393], [-0.338, -0.197]], "v": [[36.011, -174.517], [30.675, -183.78], [18.649, -193.483], [-3.155, -175], [-1.056, -151.743], [8.712, -148.74], [11.595, -153.587], [18.68, -160.879], [22.056, -167.524], [30.286, -172.703]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [{"i": [[0.992, 1.207], [3.345, 1.287], [4.177, 0.571], [0.944, -8.494], [-2.225, -5.77], [-2.68, 0.906], [-3.618, 2.31], [-2.593, 2.392], [-1.513, 2.189], [-0.831, 2.549]], "o": [[-2.758, -2.356], [-4.143, -1.622], [-7.834, -1.804], [-0.744, 5.691], [1.048, 2.291], [2.287, -0.261], [5.617, -4.278], [2.094, -1.733], [1.8, -2.248], [0.831, -2.549]], "v": [[47.57, -128.957], [39.205, -134.753], [28.073, -137.883], [13.801, -123.997], [15.618, -105.132], [23.696, -102.192], [32.383, -105.972], [41.843, -113.517], [47.388, -119.064], [49.701, -123.638]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [{"i": [[0.695, 0.195], [3.645, -0.372], [3.203, -2.058], [0.733, -3.794], [-1.736, -5.53], [-3.134, 0.358], [-4.959, 3.166], [-1.403, 3.634], [1.042, 0.92], [0.695, 0.404]], "o": [[-4.087, -1.316], [-3.645, 0.372], [-3.203, 2.058], [-0.733, 3.794], [0.936, 2.233], [3.134, -0.358], [7.438, -4.748], [1.403, -3.634], [-0.695, -0.613], [-0.695, -0.404]], "v": [[60.414, -78.335], [48.815, -79.75], [38.544, -76.105], [32.64, -67.326], [34.145, -53.34], [40.344, -50.473], [52.577, -55.704], [64.94, -68.678], [64.583, -75.909], [62.498, -77.435]], "c": true}]}, {"t": 268, "s": [{"i": [[0.695, 0.195], [3.645, -0.372], [3.203, -2.058], [0.733, -3.794], [-1.736, -5.53], [-3.134, 0.358], [-4.959, 3.166], [-1.403, 3.634], [1.042, 0.92], [0.695, 0.404]], "o": [[-4.087, -1.316], [-3.645, 0.372], [-3.203, 2.058], [-0.733, 3.794], [0.936, 2.233], [3.134, -0.358], [7.438, -4.748], [1.403, -3.634], [-0.695, -0.613], [-0.695, -0.404]], "v": [[60.414, -78.335], [48.815, -79.75], [38.544, -76.105], [32.64, -67.326], [34.145, -53.34], [40.344, -50.473], [52.577, -55.704], [64.94, -68.678], [64.583, -75.909], [62.498, -77.435]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Path-2-<PERSON><PERSON>", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 317, "st": 52, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "左边眉毛", "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [230.442, 170.048, 0], "to": [0, -2.667, 0], "ti": [0, 2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 28, "s": [230.442, 154.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 33, "s": [230.442, 154.048, 0], "to": [0, 2.667, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [230.442, 170.048, 0], "to": [0, 0, 0], "ti": [0, 2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 43, "s": [230.442, 154.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [230.442, 154.048, 0], "to": [0, 2.667, 0], "ti": [0, -2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [230.442, 170.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 58, "s": [230.442, 170.048, 0], "to": [0.167, 4.167, 0], "ti": [-0.167, -4.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [231.442, 195.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [231.442, 195.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [218.65, 245.398, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [206.961, 273.421, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [209.702, 256.735, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [208.442, 226.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [226.944, 195.296, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [226.942, 191.798, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [226.942, 191.798, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [226.944, 195.296, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [208.442, 226.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [209.702, 256.735, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [206.961, 273.421, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [218.65, 245.398, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [231.442, 195.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 256, "s": [231.442, 195.048, 0], "to": [-0.167, -4.167, 0], "ti": [0.167, 4.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0.333}, "t": 268, "s": [230.442, 170.048, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 273, "s": [230.442, 170.048, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-25.558, -67.952, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [{"i": [[-1.372, 0.669], [-3.649, 0.168], [-3.267, -1.82], [-0.864, -4.19], [1.539, -6.56], [2.957, 0.053], [5.057, 2.793], [-2.016, 2.426]], "o": [[4.031, -2.154], [3.649, -0.168], [3.267, 1.82], [0.864, 4.19], [-0.856, 2.687], [-2.956, -0.053], [-15.171, -8.379], [1.344, -1.617]], "v": [[-37.354, -80.244], [-25.834, -83.727], [-15.46, -81.249], [-9.264, -72.235], [-10.276, -56.109], [-15.995, -52.158], [-28.015, -56.427], [-41.428, -76.815]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[-1.372, 0.669], [-3.649, 0.168], [-3.267, -1.82], [-0.864, -4.19], [1.539, -6.56], [2.957, 0.053], [5.057, 2.793], [-2.016, 2.426]], "o": [[4.031, -2.154], [3.649, -0.168], [3.267, 1.82], [0.864, 4.19], [-0.856, 2.687], [-2.956, -0.053], [-15.171, -8.379], [1.344, -1.617]], "v": [[-37.354, -80.244], [-25.834, -83.727], [-15.46, -81.249], [-9.264, -72.235], [-10.276, -56.109], [-15.995, -52.158], [-28.015, -56.427], [-41.428, -76.815]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [{"i": [[-1.372, 0.669], [-3.649, 0.168], [-3.267, -1.82], [-0.864, -4.19], [1.539, -6.56], [2.957, 0.053], [5.057, 2.793], [-2.016, 2.426]], "o": [[4.031, -2.154], [3.649, -0.168], [3.267, 1.82], [0.864, 4.19], [-0.856, 2.687], [-2.956, -0.053], [-15.171, -8.379], [1.344, -1.617]], "v": [[-37.354, -80.244], [-25.834, -83.727], [-15.46, -81.249], [-9.264, -72.235], [-10.276, -56.109], [-15.995, -52.158], [-28.015, -56.427], [-41.428, -76.815]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [{"i": [[-1.332, 0.751], [-3.464, 0.724], [-2.792, -5.416], [1.177, -5.388], [1.778, -3.092], [1.875, 0.976], [2.876, 2.781], [0.632, 2.455]], "o": [[3.159, -1.948], [5.136, -0.997], [1.827, 2.967], [-0.698, 3.987], [-1.097, 1.97], [-2.497, -1.328], [-8.597, -8.687], [-0.289, -1.422]], "v": [[-60.16, -129.354], [-48.949, -133.565], [-34.952, -130.717], [-34.427, -113.612], [-37.715, -102.845], [-43.128, -102.124], [-51.522, -108.606], [-61.274, -124.265]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[-1.332, 0.751], [-3.926, 1.253], [-2.792, -5.416], [1.177, -5.388], [1.778, -3.092], [1.875, 0.976], [2.876, 2.781], [0.632, 2.455]], "o": [[3.159, -1.948], [5.449, -1.997], [1.827, 2.967], [-0.698, 3.987], [-1.097, 1.97], [-2.497, -1.328], [-8.597, -8.687], [-0.289, -1.422]], "v": [[-83.66, -183.604], [-73.324, -188.378], [-58.327, -185.842], [-57.927, -167.862], [-61.215, -157.095], [-66.628, -156.374], [-75.022, -162.856], [-84.774, -178.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[-1.304, 0.808], [-3.695, 1.52], [-2.464, -7.902], [1.986, -5.969], [1.229, -1.969], [1.458, 0.759], [2.797, 2.784], [0.492, 1.91]], "o": [[2.556, -1.805], [6.15, -3.179], [1.646, 5.142], [-0.648, 3.276], [-0.854, 1.532], [-2.179, -2.209], [-6.686, -6.756], [-0.225, -1.106]], "v": [[-94.918, -176.652], [-83.638, -183.45], [-66.128, -179.021], [-68.743, -156.631], [-71.319, -150.566], [-75.891, -151.253], [-83.883, -157.788], [-92.886, -171.232]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [{"i": [[-1.206, 1.007], [-2.531, 2.285], [-2.341, -20.446], [4.817, -8.003], [0.461, 0.796], [0, 0], [5.77, 3.551], [0, 0]], "o": [[2.692, -1.888], [13.685, -12.353], [1.456, 12.713], [-0.475, 0.788], [0, 0], [-3.396, -5.862], [0, 0], [0, 0]], "v": [[-88.571, -59.321], [-80.737, -65.579], [-46.433, -59.396], [-51.475, -28.323], [-53.559, -28.338], [-56.308, -33.082], [-70.272, -47.422], [-81.154, -54.118]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [{"i": [[-1.206, 1.007], [-2.531, 2.285], [-2.341, -20.446], [4.817, -8.003], [0.461, 0.796], [0, 0], [5.77, 3.551], [0, 0]], "o": [[2.692, -1.888], [13.685, -12.353], [1.456, 12.713], [-0.475, 0.788], [0, 0], [-3.396, -5.862], [0, 0], [0, 0]], "v": [[-88.571, -59.321], [-80.737, -65.579], [-46.433, -59.396], [-51.475, -28.323], [-53.559, -28.338], [-56.308, -33.082], [-70.272, -47.422], [-81.154, -54.118]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [{"i": [[-1.304, 0.808], [-3.695, 1.52], [-2.464, -7.902], [1.986, -5.969], [1.229, -1.969], [1.458, 0.759], [2.797, 2.784], [0.492, 1.91]], "o": [[2.556, -1.805], [6.15, -3.179], [1.646, 5.142], [-0.648, 3.276], [-0.854, 1.532], [-2.179, -2.209], [-6.686, -6.756], [-0.225, -1.106]], "v": [[-94.918, -176.652], [-83.638, -183.45], [-66.128, -179.021], [-68.743, -156.631], [-71.319, -150.566], [-75.891, -151.253], [-83.883, -157.788], [-92.886, -171.232]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [{"i": [[-1.332, 0.751], [-3.926, 1.253], [-2.792, -5.416], [1.177, -5.388], [1.778, -3.092], [1.875, 0.976], [2.876, 2.781], [0.632, 2.455]], "o": [[3.159, -1.948], [5.449, -1.997], [1.827, 2.967], [-0.698, 3.987], [-1.097, 1.97], [-2.497, -1.328], [-8.597, -8.687], [-0.289, -1.422]], "v": [[-83.66, -183.604], [-73.324, -188.378], [-58.327, -185.842], [-57.927, -167.862], [-61.215, -157.095], [-66.628, -156.374], [-75.022, -162.856], [-84.774, -178.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [{"i": [[-1.332, 0.751], [-3.464, 0.724], [-2.792, -5.416], [1.177, -5.388], [1.778, -3.092], [1.875, 0.976], [2.876, 2.781], [0.632, 2.455]], "o": [[3.159, -1.948], [5.136, -0.997], [1.827, 2.967], [-0.698, 3.987], [-1.097, 1.97], [-2.497, -1.328], [-8.597, -8.687], [-0.289, -1.422]], "v": [[-60.16, -129.354], [-48.949, -133.565], [-34.952, -130.717], [-34.427, -113.612], [-37.715, -102.845], [-43.128, -102.124], [-51.522, -108.606], [-61.274, -124.265]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [{"i": [[-1.372, 0.669], [-3.649, 0.168], [-3.267, -1.82], [-0.864, -4.19], [1.539, -6.56], [2.957, 0.053], [5.057, 2.793], [-2.016, 2.426]], "o": [[4.031, -2.154], [3.649, -0.168], [3.267, 1.82], [0.864, 4.19], [-0.856, 2.687], [-2.956, -0.053], [-15.171, -8.379], [1.344, -1.617]], "v": [[-37.354, -80.244], [-25.834, -83.727], [-15.46, -81.249], [-9.264, -72.235], [-10.276, -56.109], [-15.995, -52.158], [-28.015, -56.427], [-41.428, -76.815]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [{"i": [[-1.372, 0.669], [-3.649, 0.168], [-3.267, -1.82], [-0.864, -4.19], [1.539, -6.56], [2.957, 0.053], [5.057, 2.793], [-2.016, 2.426]], "o": [[4.031, -2.154], [3.649, -0.168], [3.267, 1.82], [0.864, 4.19], [-0.856, 2.687], [-2.956, -0.053], [-15.171, -8.379], [1.344, -1.617]], "v": [[-37.354, -80.244], [-25.834, -83.727], [-15.46, -81.249], [-9.264, -72.235], [-10.276, -56.109], [-15.995, -52.158], [-28.015, -56.427], [-41.428, -76.815]], "c": true}]}, {"t": 273, "s": [{"i": [[-1.372, 0.669], [-3.649, 0.168], [-3.267, -1.82], [-0.864, -4.19], [1.539, -6.56], [2.957, 0.053], [5.057, 2.793], [-2.016, 2.426]], "o": [[4.031, -2.154], [3.649, -0.168], [3.267, 1.82], [0.864, 4.19], [-0.856, 2.687], [-2.956, -0.053], [-15.171, -8.379], [1.344, -1.617]], "v": [[-37.354, -80.244], [-25.834, -83.727], [-15.46, -81.249], [-9.264, -72.235], [-10.276, -56.109], [-15.995, -52.158], [-28.015, -56.427], [-41.428, -76.815]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Path-2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 317, "st": 52, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "红晕1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [25]}, {"t": 210, "s": [25]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [182, 239, 0], "to": [0, 0, 0], "ti": [-4.833, 4.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [182, 267, 0], "to": [2.416, -2.166, 0], "ti": [-6.041, 5.416, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 89, "s": [178.498, 121.002, 0], "to": [2.698, -2.419, 0], "ti": [-6.349, -21.047, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [186.833, 253.15, 0], "to": [7.868, 26.084, 0], "ti": [-1.338, 1.199, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 115.4, "s": [211, 213, 0], "to": [4.833, -4.333, 0], "ti": [1.338, -1.199, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [211, 213, 0], "to": [-1.338, 1.199, 0], "ti": [7.868, 26.084, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 226, "s": [186.833, 253.15, 0], "to": [-6.349, -21.047, 0], "ti": [2.698, -2.419, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 237, "s": [178.498, 121.002, 0], "to": [-6.041, 5.416, 0], "ti": [2.416, -2.166, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 259, "s": [182, 267, 0], "to": [-4.833, 4.333, 0], "ti": [0, 0, 0]}, {"t": 268, "s": [182, 239, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-59, -17, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [134, 140, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[4.986, -9.942], [12.236, 6.136], [-4.986, 9.942], [-12.236, -6.136]], "o": [[-4.986, 9.942], [-12.236, -6.136], [4.986, -9.942], [12.236, 6.136]], "v": [[-36.845, -5.89], [-68.027, 1.001], [-81.155, -28.11], [-49.973, -35.001]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 115.4, "s": [{"i": [[6.372, -12.706], [15.639, 7.842], [-6.372, 12.706], [-15.639, -7.842]], "o": [[-6.372, 12.706], [-15.639, -7.842], [6.372, -12.706], [15.639, 7.842]], "v": [[-111.088, 59.59], [-150.941, 68.397], [-167.72, 31.19], [-127.867, 22.383]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [{"i": [[6.372, -12.706], [15.639, 7.842], [-6.372, 12.706], [-15.639, -7.842]], "o": [[-6.372, 12.706], [-15.639, -7.842], [6.372, -12.706], [15.639, 7.842]], "v": [[-111.088, 59.59], [-150.941, 68.397], [-167.72, 31.19], [-127.867, 22.383]], "c": true}]}, {"t": 259.400390625, "s": [{"i": [[4.986, -9.942], [12.236, 6.136], [-4.986, 9.942], [-12.236, -6.136]], "o": [[-4.986, 9.942], [-12.236, -6.136], [4.986, -9.942], [12.236, 6.136]], "v": [[-36.845, -5.89], [-68.027, 1.001], [-81.155, -28.11], [-49.973, -35.001]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.979, 0.254, 0.254, 0.5, 0.99, 0.253, 0.253, 1, 1, 0.252, 0.252, 0.003, 1, 0.502, 0.5, 1, 0], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [-58.639, -18.036], "to": [-13.063, 10.778], "ti": [13.063, -10.778]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 115.4, "s": [-137.017, 46.635], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [-137.017, 46.635], "to": [13.063, -10.778], "ti": [-13.063, 10.778]}, {"t": 259.400390625, "s": [-58.639, -18.036]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [-50.79, -36.772], "to": [-12.973, 9.697], "ti": [12.973, -9.697]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 115.4, "s": [-128.63, 21.412], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [-128.63, 21.412], "to": [12.973, -9.697], "ti": [-12.973, 9.697]}, {"t": 259.400390625, "s": [-50.79, -36.772]}], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient 28", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.829, 41.991], "ix": 2}, "a": {"a": 0, "k": [-140.032, 45.642], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 66, "s": [100, 100]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0, 0]}, "t": 115.4, "s": [115, 115]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 210, "s": [115, 115]}, {"t": 259.400390625, "s": [100, 100]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "红晕2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [25]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 210, "s": [40]}, {"t": 259, "s": [25]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [332, 247, 0], "to": [0, 0, 0], "ti": [0, 2.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [332, 266, 0], "to": [0, -1.334, 0], "ti": [0, 3.334, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 89, "s": [315, 124.999, 0], "to": [0, -1.516, 0], "ti": [0, -11.08, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [318, 266.427, 0], "to": [0, 13.278, 0], "ti": [0, 0.727, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 115.4, "s": [332, 231, 0], "to": [0, -2.667, 0], "ti": [0, -0.727, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [332, 231, 0], "to": [0, 0.727, 0], "ti": [0, 13.278, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 225, "s": [318, 266.427, 0], "to": [0, -11.08, 0], "ti": [0, -1.516, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 236, "s": [315, 124.999, 0], "to": [0, 3.334, 0], "ti": [0, -1.334, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 259, "s": [332, 266, 0], "to": [0, 2.667, 0], "ti": [0, 0, 0]}, {"t": 267, "s": [332, 247, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [76, -9, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [153, 127, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[-0.859, -11.089], [13.647, -1.057], [0.859, 11.089], [-13.647, 1.057]], "o": [[0.859, 11.089], [-13.647, 1.057], [-0.859, -11.089], [13.647, -1.057]], "v": [[100.711, -10.914], [77.555, 11.078], [51.289, -7.086], [74.445, -29.078]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 115.4, "s": [{"i": [[-1.204, -15.546], [19.134, -1.482], [1.204, 15.546], [-19.134, 1.482]], "o": [[1.204, 15.546], [-19.134, 1.482], [-1.204, -15.546], [19.134, -1.482]], "v": [[110.136, 44.085], [77.672, 74.917], [40.847, 49.452], [73.311, 18.62]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [{"i": [[-1.204, -15.546], [19.134, -1.482], [1.204, 15.546], [-19.134, 1.482]], "o": [[1.204, 15.546], [-19.134, 1.482], [-1.204, -15.546], [19.134, -1.482]], "v": [[110.136, 44.085], [77.672, 74.917], [40.847, 49.452], [73.311, 18.62]], "c": true}]}, {"t": 259.400390625, "s": [{"i": [[-0.859, -11.089], [13.647, -1.057], [0.859, 11.089], [-13.647, 1.057]], "o": [[0.859, 11.089], [-13.647, 1.057], [-0.859, -11.089], [13.647, -1.057]], "v": [[100.711, -10.914], [77.555, 11.078], [51.289, -7.086], [74.445, -29.078]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.979, 0.254, 0.254, 0.5, 0.99, 0.253, 0.253, 1, 1, 0.252, 0.252, 0.003, 1, 0.502, 0.5, 1, 0], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [75.983, -9.365], "to": [0, 9.333], "ti": [0, -9.333]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 115.4, "s": [75.983, 46.635], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [75.983, 46.635], "to": [0, -9.333], "ti": [0, 9.333]}, {"t": 259.400390625, "s": [75.983, -9.365]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [77.37, 9.412], "to": [0, 10.833], "ti": [0, -10.833]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 115.4, "s": [77.37, 74.412], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 210, "s": [77.37, 74.412], "to": [0, -10.833], "ti": [0, 10.833]}, {"t": 259.400390625, "s": [77.37, 9.412]}], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient 21", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 316, "st": 52, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "在一层影子", "parent": 10, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [30]}, {"t": 244, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-114.066, -93.409, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-120.066, -97.409, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[0, 0], [5.352, -17.374], [0, 0], [0, 0], [-47.718, 23.911], [0, 0], [24.393, -19.268]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [15.532, -52.089], [0, 0], [-5.959, 4.707]], "v": [[-84.412, -154.936], [-96.602, -133.126], [-159.757, -48.884], [-153.915, -37.323], [-88.782, -128.411], [-20.125, -171.494], [-74.143, -166.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [{"i": [[0, 0], [5.56, -21.124], [0, 0], [0, 0], [-59.135, 39.245], [0, 0], [33.102, -22.393]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [10.636, -50.11], [0, 0], [-5.587, 5.137]], "v": [[-95.454, -164.936], [-105.977, -137.501], [-166.674, -38.425], [-159.415, -27.781], [-91.865, -132.495], [-42.542, -188.578], [-85.602, -177.607]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[0, 0], [5.602, -21.874], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [30.893, -29.268]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [9.657, -49.714], [0, 0], [-5.513, 5.223]], "v": [[-97.662, -166.936], [-107.852, -138.376], [-170.757, -30.634], [-141.165, 17.927], [-74.782, -117.411], [-17.625, -181.494], [-87.893, -179.732]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [{"i": [[0, 0], [15.647, -16.427], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [70.69, -22.036]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [33.827, -36.392], [0, 0], [-15.811, 4.929]], "v": [[-82.162, -160.436], [-107.852, -138.376], [-170.757, -30.634], [-141.165, 17.927], [-55.282, -90.911], [6.875, -126.994], [-58.144, -169.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[0, 0], [12.968, -17.879], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [47.955, -11.333]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [39.36, -13.436], [0, 0], [-12.545, 0.667]], "v": [[-67.295, -131.67], [-101.852, -109.876], [-167.257, -19.134], [-141.165, 17.927], [-60.482, -97.978], [18.342, -105.528], [-41.577, -136.582]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [{"i": [[0, 0], [5.602, -21.874], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [30.893, -29.268]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [9.657, -49.714], [0, 0], [-5.513, 5.223]], "v": [[-97.662, -166.936], [-107.852, -138.376], [-170.757, -30.634], [-141.165, 17.927], [-74.782, -117.411], [-17.625, -181.494], [-87.893, -179.732]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [{"i": [[0, 0], [5.602, -21.874], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [30.893, -29.268]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [9.657, -49.714], [0, 0], [-5.513, 5.223]], "v": [[-97.662, -166.936], [-107.852, -138.376], [-170.757, -30.634], [-141.165, 17.927], [-74.782, -117.411], [-17.625, -181.494], [-87.893, -179.732]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [{"i": [[0, 0], [12.968, -17.879], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [47.955, -11.333]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [39.36, -13.436], [0, 0], [-12.545, 0.667]], "v": [[-67.295, -131.67], [-101.852, -109.876], [-167.257, -19.134], [-141.165, 17.927], [-60.482, -97.978], [18.342, -105.528], [-41.577, -136.582]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [{"i": [[0, 0], [15.647, -16.427], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [70.69, -22.036]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [33.827, -36.392], [0, 0], [-15.811, 4.929]], "v": [[-82.162, -160.436], [-107.852, -138.376], [-170.757, -30.634], [-141.165, 17.927], [-55.282, -90.911], [6.875, -126.994], [-58.144, -169.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [{"i": [[0, 0], [5.602, -21.874], [0, 0], [0, 0], [-64.093, 42.286], [0, 0], [30.893, -29.268]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [9.657, -49.714], [0, 0], [-5.513, 5.223]], "v": [[-97.662, -166.936], [-107.852, -138.376], [-170.757, -30.634], [-141.165, 17.927], [-74.782, -117.411], [-17.625, -181.494], [-87.893, -179.732]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[0, 0], [5.56, -21.124], [0, 0], [0, 0], [-59.135, 39.245], [0, 0], [33.102, -22.393]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [10.636, -50.11], [0, 0], [-5.587, 5.137]], "v": [[-95.454, -164.936], [-105.977, -137.501], [-166.674, -38.425], [-159.415, -27.781], [-91.865, -132.495], [-42.542, -188.578], [-85.602, -177.607]], "c": true}]}, {"t": 244, "s": [{"i": [[0, 0], [5.352, -17.374], [0, 0], [0, 0], [-47.718, 23.911], [0, 0], [24.393, -19.268]], "o": [[0, 0], [-57.396, 37.221], [0, 0], [0, 0], [15.532, -52.089], [0, 0], [-5.959, 4.707]], "v": [[-84.412, -154.936], [-96.602, -133.126], [-159.757, -48.884], [-153.915, -37.323], [-88.782, -128.411], [-20.125, -171.494], [-74.143, -166.982]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.588235318661, 0.709803938866, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "有一层影子", "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [207.498, 410.381, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-48.502, 154.381, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[0, 0], [-10.433, -5.846], [-8.762, -3.155], [-7.624, -1.999], [-10.606, -0.326], [-45.589, 0.629], [-1.372, 2.531], [0, 0], [15.231, -2.531], [47.417, 19.995], [10.011, 9.457], [-0.258, -2.541], [0, 0], [-4.669, -6.932]], "o": [[0, 0], [5.798, 3.249], [6.352, 2.287], [8.651, 2.268], [24.39, 0.749], [1.203, -2.761], [2.383, -4.395], [0, 0], [-30.646, 5.093], [-11.119, -6.077], [-0.22, 3.341], [1.367, 3.459], [0, 0], [1.027, 1.2]], "v": [[-103.304, 116.257], [-88.841, 124.209], [-72.182, 130.652], [-58.454, 135.038], [-27.752, 137.703], [48.325, 130.681], [52.617, 123.395], [56.647, 117.499], [25.019, 122.031], [-96.917, 101.505], [-132.905, 74.534], [-134.617, 89.291], [-124.62, 97.258], [-114.456, 107.932]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [-10.433, -5.846], [-8.762, -3.155], [-7.624, -1.999], [-10.41, 2.056], [-45.589, 0.629], [-1.372, 2.531], [0, 0], [13.49, -0.748], [44.417, 22.995], [10.011, 9.457], [0.492, -2.416], [0, 0], [-5.169, -8.557]], "o": [[0, 0], [5.798, 3.249], [6.352, 2.287], [8.651, 2.268], [18.752, -3.703], [9.675, 2.319], [2.383, -4.395], [0, 0], [-31.019, 1.719], [-11.119, -6.077], [-0.845, 3.841], [-0.383, 1.834], [0, 0], [1.027, 1.2]], "v": [[-134.929, 130.757], [-122.341, 144.959], [-101.182, 150.902], [-76.704, 165.038], [-41.252, 166.703], [32.325, 159.681], [49.367, 154.895], [57.647, 142.249], [14.519, 140.531], [-109.167, 115.505], [-136.905, 94.034], [-142.867, 113.166], [-143.245, 117.383], [-136.956, 128.557]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[0, 0], [-10.433, -5.846], [-8.762, -3.155], [-7.624, -1.999], [-10.416, 1.978], [-50.14, 13.659], [-1.372, 2.531], [0, 0], [13.49, -0.748], [44.51, 23.075], [10.011, 9.457], [0.513, -2.556], [0, 0], [-4.242, -8.367]], "o": [[0, 0], [5.798, 3.249], [6.352, 2.287], [8.651, 2.268], [18.936, -3.558], [4.11, -1.591], [2.383, -4.395], [0, 0], [-31.019, 1.719], [-11.119, -6.077], [-0.818, 3.865], [-0.393, 1.869], [0, 0], [1.027, 1.2]], "v": [[-127.513, 147.159], [-112.702, 159.03], [-93.353, 166.34], [-73.823, 178.519], [-39.584, 177.024], [45.64, 170.341], [49.617, 160.395], [55.18, 141.651], [15.166, 152.417], [-98.537, 131.76], [-138.364, 107.643], [-139.826, 124.671], [-138.7, 129.896], [-132.633, 140.992]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [{"i": [[0, 0], [-10.433, -5.846], [-8.762, -3.155], [-7.624, -1.999], [-10.416, 1.978], [-50.14, 13.659], [-1.372, 2.531], [0, 0], [13.49, -0.748], [44.51, 23.075], [10.011, 9.457], [0.513, -2.556], [0, 0], [-4.242, -8.367]], "o": [[0, 0], [5.798, 3.249], [6.352, 2.287], [8.651, 2.268], [18.936, -3.558], [4.11, -1.591], [2.383, -4.395], [0, 0], [-31.019, 1.719], [-11.119, -6.077], [-0.818, 3.865], [-0.393, 1.869], [0, 0], [1.027, 1.2]], "v": [[-127.513, 147.159], [-112.702, 159.03], [-93.353, 166.34], [-73.823, 178.519], [-39.584, 177.024], [45.64, 170.341], [49.617, 160.395], [55.18, 141.651], [15.166, 152.417], [-98.537, 131.76], [-138.364, 107.643], [-139.826, 124.671], [-138.7, 129.896], [-132.633, 140.992]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [{"i": [[0, 0], [-10.433, -5.846], [-8.762, -3.155], [-7.624, -1.999], [-10.41, 2.056], [-45.589, 0.629], [-1.372, 2.531], [0, 0], [13.49, -0.748], [44.417, 22.995], [10.011, 9.457], [0.492, -2.416], [0, 0], [-5.169, -8.557]], "o": [[0, 0], [5.798, 3.249], [6.352, 2.287], [8.651, 2.268], [18.752, -3.703], [9.675, 2.319], [2.383, -4.395], [0, 0], [-31.019, 1.719], [-11.119, -6.077], [-0.845, 3.841], [-0.383, 1.834], [0, 0], [1.027, 1.2]], "v": [[-134.929, 130.757], [-122.341, 144.959], [-101.182, 150.902], [-76.704, 165.038], [-41.252, 166.703], [32.325, 159.681], [49.367, 154.895], [57.647, 142.249], [14.519, 140.531], [-109.167, 115.505], [-136.905, 94.034], [-142.867, 113.166], [-143.245, 117.383], [-136.956, 128.557]], "c": true}]}, {"t": 244, "s": [{"i": [[0, 0], [-10.433, -5.846], [-8.762, -3.155], [-7.624, -1.999], [-10.606, -0.326], [-45.589, 0.629], [-1.372, 2.531], [0, 0], [15.231, -2.531], [47.417, 19.995], [10.011, 9.457], [-0.258, -2.541], [0, 0], [-4.669, -6.932]], "o": [[0, 0], [5.798, 3.249], [6.352, 2.287], [8.651, 2.268], [24.39, 0.749], [1.203, -2.761], [2.383, -4.395], [0, 0], [-30.646, 5.093], [-11.119, -6.077], [-0.22, 3.341], [1.367, 3.459], [0, 0], [1.027, 1.2]], "v": [[-103.304, 116.257], [-88.841, 124.209], [-72.182, 130.652], [-58.454, 135.038], [-27.752, 137.703], [48.325, 130.681], [52.617, 123.395], [56.647, 117.499], [25.019, 122.031], [-96.917, 101.505], [-132.905, 74.534], [-134.617, 89.291], [-124.62, 97.258], [-114.456, 107.932]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.588235318661, 0.709803938866, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [100]}, {"t": 247, "s": [0]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "2层投影", "parent": 9, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [30]}, {"t": 248, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [155, -21.631, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, -15.429, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [{"i": [[0, 0], [-1.092, 0.828], [-3.926, 7.978], [0.933, 16.239], [-4.68, 21.59], [5.6, 16.335], [2.473, 26.274], [0, 0], [5.197, 0.367], [0, 0], [-4.219, -24.623], [6.749, -30.799]], "o": [[0, 0], [-0.429, 0.08], [5.983, -12.16], [-0.933, -16.239], [3.09, -14.251], [-5.6, -16.335], [-2.473, -26.274], [0, 0], [23.329, 36.147], [0, 0], [1.812, 10.577], [-6.625, 30.229]], "v": [[128.543, 81.67], [151.179, 70.92], [154.926, 49.522], [160.271, 13.136], [164.16, -22.999], [151.538, -48.696], [135.132, -84.767], [125.008, -115.595], [97.671, -127.647], [141.836, -46.734], [149.719, -7.877], [141.251, 50.549]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[0, 0], [-1.092, 0.828], [-8.666, 10.419], [0.933, 16.239], [-3.387, 21.83], [5.6, 16.335], [2.473, 26.274], [0, 0], [23.597, -9.853], [0, 0], [-2.8, -10.359], [8.771, -29.678]], "o": [[0, 0], [1.092, -0.828], [8.666, -10.419], [-0.933, -16.239], [3.387, -21.83], [-5.6, -16.335], [-2.473, -26.274], [0, 0], [22.562, 42.37], [0, 0], [2.8, 10.359], [-8.771, 29.678]], "v": [[110.043, 125.92], [145.179, 105.67], [155.926, 94.772], [172.271, 48.636], [188.91, -2.499], [168.538, -50.946], [165.132, -87.017], [138.258, -131.095], [64.921, -123.147], [127.586, -56.234], [138.469, -5.127], [139.251, 51.049]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [{"i": [[0, 0], [-1.092, 0.828], [-8.666, 10.419], [0.933, 16.239], [-3.387, 21.83], [5.6, 16.335], [2.473, 26.274], [0, 0], [23.597, -9.853], [0, 0], [-2.8, -10.359], [8.771, -29.678]], "o": [[0, 0], [1.092, -0.828], [8.666, -10.419], [-0.933, -16.239], [3.387, -21.83], [-5.6, -16.335], [-2.473, -26.274], [0, 0], [22.562, 42.37], [0, 0], [2.8, 10.359], [-8.771, 29.678]], "v": [[110.043, 125.92], [145.179, 105.67], [155.926, 94.772], [172.271, 48.636], [188.91, -2.499], [168.538, -50.946], [165.132, -87.017], [138.258, -131.095], [64.921, -123.147], [127.586, -56.234], [138.469, -5.127], [139.251, 51.049]], "c": true}]}, {"t": 247, "s": [{"i": [[0, 0], [-1.092, 0.828], [-3.926, 7.978], [0.933, 16.239], [-4.68, 21.59], [5.6, 16.335], [2.473, 26.274], [0, 0], [5.197, 0.367], [0, 0], [-4.219, -24.623], [6.749, -30.799]], "o": [[0, 0], [-0.429, 0.08], [5.983, -12.16], [-0.933, -16.239], [3.09, -14.251], [-5.6, -16.335], [-2.473, -26.274], [0, 0], [23.329, 36.147], [0, 0], [1.812, 10.577], [-6.625, 30.229]], "v": [[128.543, 81.67], [151.179, 70.92], [154.926, 49.522], [160.271, 13.136], [164.16, -22.999], [151.538, -48.696], [135.132, -84.767], [125.008, -115.595], [97.671, -127.647], [141.836, -46.734], [149.719, -7.877], [141.251, 50.549]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.588235318661, 0.709803938866, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "叶脉 28", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [0]}, {"t": 247, "s": [1]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.5;\n    frequency = 2;\n    decay = 5;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [-61.137, 147.015, 0], "to": [0, 0.497, 0], "ti": [0, -0.497, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [-61.137, 150, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [-61.137, 150, 0], "to": [0, -0.497, 0], "ti": [0, 0.497, 0]}, {"t": 238, "s": [-61.137, 147.015, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.07;\n    frequency = 2;\n    decay = 5;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-57.328, 146.32, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [14.415, -16.902], [0, 0], [-12.169, -19.686], [0, 0], [-21.303, -1.323], [0, 0], [-36.984, 23.788], [0, 0], [-17.671, 16.679], [-18.987, 1.465], [39.286, -15.945], [32.128, 6.609], [24.731, 3.042], [-3.388, 15.417], [2.754, 4.563]], "o": [[0, 0], [-7.763, 6.624], [0, 0], [6.1, 7.307], [0, 0], [5.692, 0], [0, 0], [25.712, -18.962], [0, 0], [17.671, -16.679], [0, 0], [-32.624, 13.585], [-25.651, -5.927], [-23.288, -3.986], [1.364, -13.7], [0, 0]], "v": [[-83.46, -32.493], [-91.238, -6.282], [-95.472, 19.733], [-97.593, 46.624], [-92.558, 62.042], [-66.866, 82.4], [-54.339, 90.833], [-3.031, 85.901], [35.969, 73.08], [60.026, 63.792], [116.416, 22.571], [63.588, 39.14], [-12.07, 50.679], [-54.584, 37.776], [-81.124, -3.24], [-83.46, -32.493]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [{"i": [[0, 0], [9.112, -11.047], [0, 0], [-14.803, -18.766], [0, 0], [-21.308, -0.443], [0, 0], [-35.918, 25.256], [0, 0], [-16.213, 16.4], [-18.788, 2.482], [37.152, -11.27], [28.227, 5.286], [14.871, 3.393], [12.9, 13.767], [2.935, 4.439]], "o": [[0, 0], [-7.472, 6.929], [0, 0], [6.386, 7.039], [0, 0], [5.678, -0.234], [0, 0], [24.871, -19.977], [0, 0], [17.003, -17.317], [0, 0], [-27.062, 9.215], [-21.567, -4.395], [-20.484, -6.364], [-6.412, -8.068], [0, 0]], "v": [[-114.178, 24.13], [-117.507, 50.423], [-110.041, 67.6], [-108.065, 97.868], [-96.959, 108.52], [-72.651, 130.667], [-59.807, 138.565], [-10.04, 131.345], [29.561, 117.133], [53.18, 106.876], [99.564, 68.425], [45.48, 82.845], [-22.857, 89.48], [-61.655, 77.565], [-103.434, 45.47], [-114.026, 23.955]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [7.184, -8.918], [0, 0], [-15.761, -18.431], [0, 0], [-21.31, -0.123], [0, 0], [-35.531, 25.789], [0, 0], [-15.683, 16.299], [-18.715, 2.852], [36.376, -9.57], [26.808, 4.805], [11.286, 3.521], [18.822, 13.167], [3.001, 4.394]], "o": [[0, 0], [-7.366, 7.039], [0, 0], [6.491, 6.942], [0, 0], [5.674, -0.32], [0, 0], [24.566, -20.346], [0, 0], [16.76, -17.549], [0, 0], [-25.039, 7.625], [-20.082, -3.838], [-19.464, -7.229], [-9.24, -6.02], [0, 0]], "v": [[-128.305, 54.165], [-128.059, 73.543], [-115.339, 85.006], [-111.872, 116.502], [-98.559, 125.422], [-74.755, 148.219], [-61.795, 155.922], [-12.589, 147.87], [27.231, 133.152], [50.69, 122.543], [92.329, 78.981], [38.896, 98.737], [-26.779, 103.589], [-64.227, 92.033], [-109.888, 70.387], [-127.742, 52.78]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[0, 0], [6.604, -9.507], [0, 0], [-16.121, -18.305], [0, 0], [-21.311, -0.002], [0, 0], [-35.385, 25.99], [0, 0], [-15.484, 16.261], [-18.688, 2.991], [36.085, -8.931], [26.275, 4.624], [11.419, 3.419], [16.883, 12.291], [3.026, 4.377]], "o": [[0, 0], [-7.326, 7.081], [0, 0], [6.53, 6.905], [0, 0], [5.672, -0.352], [0, 0], [24.451, -20.485], [0, 0], [16.668, -17.637], [0, 0], [-24.279, 7.028], [-19.524, -3.629], [-18.9, -6.938], [-9.354, -6.607], [0, 0]], "v": [[-127.873, 53.649], [-130.85, 80.014], [-117.33, 91.547], [-113.303, 123.505], [-99.161, 131.773], [-75.545, 154.815], [-62.542, 162.444], [-13.547, 154.08], [26.355, 139.172], [49.755, 128.43], [91.533, 84.102], [36.421, 104.709], [-28.253, 108.891], [-66.17, 97.796], [-113.78, 78.68], [-126.85, 57.326]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [{"i": [[0, 0], [6.025, -10.096], [0, 0], [-16.481, -18.18], [0, 0], [-21.312, 0.118], [0, 0], [-35.24, 26.191], [0, 0], [-15.285, 16.223], [-18.661, 3.13], [35.793, -8.292], [30.021, 2.101], [11.553, 3.317], [14.944, 11.416], [3.051, 4.36]], "o": [[0, 0], [-7.287, 7.123], [0, 0], [6.569, 6.868], [0, 0], [5.67, -0.384], [0, 0], [24.336, -20.624], [0, 0], [16.577, -17.724], [0, 0], [-27.215, 2], [-18.966, -3.419], [-18.336, -6.647], [-9.468, -7.194], [0, 0]], "v": [[-132.221, 58.593], [-133.641, 86.485], [-119.321, 98.088], [-114.734, 130.507], [-99.762, 138.124], [-76.336, 161.41], [-63.289, 168.967], [-14.505, 160.29], [25.48, 145.192], [48.819, 134.318], [87.219, 88.285], [31.851, 105.217], [-28.349, 107.168], [-70.24, 96.344], [-111.904, 73.619], [-130.409, 57.574]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88.709, "s": [{"i": [[0, 0], [4.871, -11.332], [0, 0], [-19.353, -17.177], [0, 0], [-21.317, 1.078], [0, 0], [-34.078, 27.791], [0, 0], [-13.696, 15.918], [-18.443, 4.238], [33.61, -0.805], [21.487, 3.001], [8.12, 2.96], [7.069, 4.693], [3.249, 4.225]], "o": [[0, 0], [-6.969, 7.454], [0, 0], [6.882, 6.576], [0, 0], [5.656, -0.639], [0, 0], [23.419, -21.73], [0, 0], [15.848, -18.42], [0, 0], [-19.644, 0.47], [-14.513, -1.749], [-14.384, -6.195], [-14.21, -9.805], [0, 0]], "v": [[-140.195, 66.689], [-140.381, 131.868], [-135.206, 150.279], [-126.151, 186.38], [-104.561, 188.802], [-82.644, 214.038], [-69.25, 221.011], [-22.148, 209.84], [18.493, 193.225], [41.355, 181.294], [86.279, 114.396], [28.204, 113.334], [-40.737, 115.749], [-75.745, 108.665], [-101.528, 93.639], [-122.045, 71.075]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 237.291, "s": [{"i": [[0, 0], [4.871, -11.332], [0, 0], [-19.353, -17.177], [0, 0], [-21.317, 1.078], [0, 0], [-34.078, 27.791], [0, 0], [-13.696, 15.918], [-18.443, 4.238], [33.61, -0.805], [21.487, 3.001], [8.12, 2.96], [7.069, 4.693], [3.249, 4.225]], "o": [[0, 0], [-6.969, 7.454], [0, 0], [6.882, 6.576], [0, 0], [5.656, -0.639], [0, 0], [23.419, -21.73], [0, 0], [15.848, -18.42], [0, 0], [-19.644, 0.47], [-14.513, -1.749], [-14.384, -6.195], [-14.21, -9.805], [0, 0]], "v": [[-140.195, 66.689], [-140.381, 131.868], [-135.206, 150.279], [-126.151, 186.38], [-104.561, 188.802], [-82.644, 214.038], [-69.25, 221.011], [-22.148, 209.84], [18.493, 193.225], [41.355, 181.294], [86.279, 114.396], [28.204, 113.334], [-40.737, 115.749], [-75.745, 108.665], [-101.528, 93.639], [-122.045, 71.075]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [{"i": [[0, 0], [6.025, -10.096], [0, 0], [-16.481, -18.18], [0, 0], [-21.312, 0.118], [0, 0], [-35.24, 26.191], [0, 0], [-15.285, 16.223], [-18.661, 3.13], [35.793, -8.292], [30.021, 2.101], [11.553, 3.317], [14.944, 11.416], [3.051, 4.36]], "o": [[0, 0], [-7.287, 7.123], [0, 0], [6.569, 6.868], [0, 0], [5.67, -0.384], [0, 0], [24.336, -20.624], [0, 0], [16.577, -17.724], [0, 0], [-27.215, 2], [-18.966, -3.419], [-18.336, -6.647], [-9.468, -7.194], [0, 0]], "v": [[-132.221, 58.593], [-133.641, 86.485], [-119.321, 98.088], [-114.734, 130.507], [-99.762, 138.124], [-76.336, 161.41], [-63.289, 168.967], [-14.505, 160.29], [25.48, 145.192], [48.819, 134.318], [87.219, 88.285], [31.851, 105.217], [-28.349, 107.168], [-70.24, 96.344], [-111.904, 73.619], [-130.409, 57.574]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [{"i": [[0, 0], [6.604, -9.507], [0, 0], [-16.121, -18.305], [0, 0], [-21.311, -0.002], [0, 0], [-35.385, 25.99], [0, 0], [-15.484, 16.261], [-18.688, 2.991], [36.085, -8.931], [26.275, 4.624], [11.419, 3.419], [16.883, 12.291], [3.026, 4.377]], "o": [[0, 0], [-7.326, 7.081], [0, 0], [6.53, 6.905], [0, 0], [5.672, -0.352], [0, 0], [24.451, -20.485], [0, 0], [16.668, -17.637], [0, 0], [-24.279, 7.028], [-19.524, -3.629], [-18.9, -6.938], [-9.354, -6.607], [0, 0]], "v": [[-127.873, 53.649], [-130.85, 80.014], [-117.33, 91.547], [-113.303, 123.505], [-99.161, 131.773], [-75.545, 154.815], [-62.542, 162.444], [-13.547, 154.08], [26.355, 139.172], [49.755, 128.43], [91.533, 84.102], [36.421, 104.709], [-28.253, 108.891], [-66.17, 97.796], [-113.78, 78.68], [-126.85, 57.326]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[0, 0], [7.184, -8.918], [0, 0], [-15.761, -18.431], [0, 0], [-21.31, -0.123], [0, 0], [-35.531, 25.789], [0, 0], [-15.683, 16.299], [-18.715, 2.852], [36.376, -9.57], [26.808, 4.805], [11.286, 3.521], [18.822, 13.167], [3.001, 4.394]], "o": [[0, 0], [-7.366, 7.039], [0, 0], [6.491, 6.942], [0, 0], [5.674, -0.32], [0, 0], [24.566, -20.346], [0, 0], [16.76, -17.549], [0, 0], [-25.039, 7.625], [-20.082, -3.838], [-19.464, -7.229], [-9.24, -6.02], [0, 0]], "v": [[-128.305, 54.165], [-128.059, 73.543], [-115.339, 85.006], [-111.872, 116.502], [-98.559, 125.422], [-74.755, 148.219], [-61.795, 155.922], [-12.589, 147.87], [27.231, 133.152], [50.69, 122.543], [92.329, 78.981], [38.896, 98.737], [-26.779, 103.589], [-64.227, 92.033], [-109.888, 70.387], [-127.742, 52.78]], "c": false}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [{"i": [[0, 0], [9.112, -11.047], [0, 0], [-14.803, -18.766], [0, 0], [-21.308, -0.443], [0, 0], [-35.918, 25.256], [0, 0], [-16.213, 16.4], [-18.788, 2.482], [37.152, -11.27], [28.227, 5.286], [14.871, 3.393], [12.9, 13.767], [2.935, 4.439]], "o": [[0, 0], [-7.472, 6.929], [0, 0], [6.386, 7.039], [0, 0], [5.678, -0.234], [0, 0], [24.871, -19.977], [0, 0], [17.003, -17.317], [0, 0], [-27.062, 9.215], [-21.567, -4.395], [-20.484, -6.364], [-6.412, -8.068], [0, 0]], "v": [[-114.178, 24.13], [-117.507, 50.423], [-110.041, 67.6], [-108.065, 97.868], [-96.959, 108.52], [-72.651, 130.667], [-59.807, 138.565], [-10.04, 131.345], [29.561, 117.133], [53.18, 106.876], [99.564, 68.425], [45.48, 82.845], [-22.857, 89.48], [-61.655, 77.565], [-103.434, 45.47], [-114.026, 23.955]], "c": false}]}, {"t": 260, "s": [{"i": [[0, 0], [14.415, -16.902], [0, 0], [-12.169, -19.686], [0, 0], [-21.303, -1.323], [0, 0], [-36.984, 23.788], [0, 0], [-17.671, 16.679], [-18.987, 1.465], [39.286, -15.945], [32.128, 6.609], [24.731, 3.042], [-3.388, 15.417], [2.754, 4.563]], "o": [[0, 0], [-7.763, 6.624], [0, 0], [6.1, 7.307], [0, 0], [5.692, 0], [0, 0], [25.712, -18.962], [0, 0], [17.671, -16.679], [0, 0], [-32.624, 13.585], [-25.651, -5.927], [-23.288, -3.986], [1.364, -13.7], [0, 0]], "v": [[-83.46, -32.493], [-91.238, -6.282], [-95.472, 19.733], [-97.593, 46.624], [-92.558, 62.042], [-66.866, 82.4], [-54.339, 90.833], [-3.031, 85.901], [35.969, 73.08], [60.026, 63.792], [116.416, 22.571], [63.588, 39.14], [-12.07, 50.679], [-54.584, 37.776], [-81.124, -3.24], [-83.46, -32.493]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.474509805441, 0.607843160629, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960790157, 0.976470589638, 0.615686297417, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "叶子影子 2", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 40, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15.683, 72.937, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [15.683, 72.937, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.765}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-18.097, 23.062], [-6.609, -0.629], [0, 0], [2.537, -0.315], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.097, -23.062], [-13.307, -4.031], [0, 0], [-2.537, 0.315], [0, 0]], "v": [[-92.558, 62.042], [-71.999, 98.167], [-22.619, 115.783], [36.075, 93.434], [92.46, 63.274], [123.93, 29.758], [92.145, 41.245], [-36.707, 80.111], [-92.558, 62.042]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.235}, "t": 74.516, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-18.097, 23.062], [-6.609, -0.629], [0, 0], [16.549, 10.599], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.097, -23.062], [-15.876, -1.863], [0, 0], [-10.454, -6.233], [0, 0]], "v": [[-100.267, 76.535], [-80.839, 108.137], [-23.03, 136.34], [30.319, 117.897], [87.937, 87.943], [108.928, 73.133], [76.416, 78.2], [-50.994, 93.679], [-96.772, 75.404]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-18.097, 23.062], [-6.609, -0.629], [0, 0], [36.617, 26.23], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.097, -23.062], [-19.555, 1.242], [0, 0], [-21.793, -15.611], [0, 0]], "v": [[-111.308, 97.292], [-93.499, 122.417], [-23.619, 165.783], [22.075, 152.934], [81.46, 123.274], [92.305, 90.258], [58.145, 113.495], [-71.457, 113.111], [-102.808, 94.542]], "c": false}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-19.743, 21.611], [-6.609, -0.629], [0, 0], [36.617, 26.23], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.717, -20.572], [-18.158, 1.981], [0, 0], [-21.793, -15.611], [0, 0]], "v": [[-130.813, 120.865], [-113.876, 141.315], [-49.255, 190.104], [-15.721, 179.227], [47.279, 157.455], [62.725, 134.957], [18.705, 146.854], [-97.585, 134.146], [-129.997, 114.289]], "c": false}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-20.601, 20.855], [-6.609, -0.629], [0, 0], [36.617, 26.23], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [19.04, -19.274], [-17.43, 2.367], [0, 0], [-21.793, -15.611], [0, 0]], "v": [[-137.558, 129.542], [-124.499, 151.167], [-62.619, 202.783], [-35.425, 192.934], [29.46, 175.274], [47.305, 158.258], [-1.855, 164.245], [-111.207, 145.111], [-140.558, 121.542]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-20.601, 20.855], [-6.609, -0.629], [0, 0], [36.617, 26.23], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [19.04, -19.274], [-17.43, 2.367], [0, 0], [-21.793, -15.611], [0, 0]], "v": [[-137.558, 129.542], [-124.499, 151.167], [-62.619, 202.783], [-35.425, 192.934], [29.46, 175.274], [47.305, 158.258], [-1.855, 164.245], [-111.207, 145.111], [-140.558, 121.542]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-19.743, 21.611], [-6.609, -0.629], [0, 0], [36.617, 26.23], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.717, -20.572], [-18.158, 1.981], [0, 0], [-21.793, -15.611], [0, 0]], "v": [[-130.813, 120.865], [-113.876, 141.315], [-49.255, 190.104], [-15.721, 179.227], [47.279, 157.455], [62.725, 134.957], [18.705, 146.854], [-97.585, 134.146], [-129.997, 114.289]], "c": false}]}, {"i": {"x": 0.833, "y": 0.765}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-18.097, 23.062], [-6.609, -0.629], [0, 0], [36.617, 26.23], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.097, -23.062], [-19.555, 1.242], [0, 0], [-21.793, -15.611], [0, 0]], "v": [[-111.308, 97.292], [-93.499, 122.417], [-23.619, 165.783], [22.075, 152.934], [81.46, 123.274], [92.305, 90.258], [58.145, 113.495], [-71.457, 113.111], [-102.808, 94.542]], "c": false}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.235}, "t": 251.484, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-18.097, 23.062], [-6.609, -0.629], [0, 0], [16.549, 10.599], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.097, -23.062], [-15.876, -1.863], [0, 0], [-10.454, -6.233], [0, 0]], "v": [[-100.267, 76.535], [-80.839, 108.137], [-23.03, 136.34], [30.319, 117.897], [87.937, 87.943], [108.928, 73.133], [76.416, 78.2], [-50.994, 93.679], [-96.772, 75.404]], "c": false}]}, {"t": 260, "s": [{"i": [[0, 0], [-21.434, -9.303], [-30.161, 4.726], [-18.463, 0], [-18.097, 23.062], [-6.609, -0.629], [0, 0], [2.537, -0.315], [0, 0]], "o": [[0, 0], [21.434, 9.303], [30.161, -4.726], [18.463, 0], [18.097, -23.062], [-13.307, -4.031], [0, 0], [-2.537, 0.315], [0, 0]], "v": [[-92.558, 62.042], [-71.999, 98.167], [-22.619, 115.783], [36.075, 93.434], [92.46, 63.274], [123.93, 29.758], [92.145, 41.245], [-36.707, 80.111], [-92.558, 62.042]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.588235318661, 0.709803938866, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "叶子影子", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-112.766, -5.168, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-112.766, -5.168, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [-1.259, -23.959], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0, 0], [1.259, 23.959], [0, 0], [0, 0], [8.667, -8.759], [-5.163, -3.481], [0, 0]], "v": [[-139.129, -28.868], [-104.59, -1.331], [-104.039, 21.327], [-100.342, 17], [-96.552, -7.652], [-87.193, -30.284], [-124.849, -29.615]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.42, "s": [{"i": [[0, 0], [-1.718, -21.543], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0.162, 0.426], [2.665, 22.171], [0, 0], [0, 0], [8.007, -9.652], [-5.163, -3.481], [0, 0]], "v": [[-138.916, -30.125], [-105.237, 8.345], [-103.275, 29.18], [-99.577, 24.853], [-98.258, -1.152], [-91.009, -32.512], [-122.842, -30.498]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [{"i": [[0, 0], [1.656, -51.726], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0.572, 1.5], [6.214, 17.657], [0, 0], [0, 0], [5.935, -9.378], [-5.653, -6.345], [0, 0]], "v": [[-144.118, -20.76], [-109.73, 34.193], [-110.675, 49.431], [-103.991, 46.846], [-99.505, 2.321], [-93.348, -22.709], [-126.07, -22.531]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [{"i": [[0, 0], [-1.238, -31.217], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0.914, 2.399], [9.185, 13.877], [0, 0], [0, 0], [1.739, -13.929], [-5.445, -5.129], [0, 0]], "v": [[-155.884, 11.209], [-113.492, 55.835], [-108.707, 67.643], [-103.291, 64.318], [-117.927, 23.564], [-121.487, -1.91], [-146.953, 5.162]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [{"i": [[0, 0], [-2.203, -24.377], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.029, 2.699], [10.176, 12.617], [0, 0], [0, 0], [0.34, -15.447], [-5.376, -4.724], [0, 0]], "v": [[-157.72, 17.313], [-114.746, 63.052], [-108.05, 73.716], [-103.057, 70.145], [-122.755, 33.783], [-127.803, 4.986], [-146.05, 7.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [-3.168, -17.538], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.143, 2.999], [11.167, 11.356], [0, 0], [0, 0], [-1.059, -16.965], [-5.306, -4.318], [0, 0]], "v": [[-160.19, 25.268], [-116, 70.269], [-107.394, 79.789], [-102.824, 75.971], [-127.584, 44.002], [-139.283, 8.622], [-152.474, 17.519]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78.064, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [-3.948, -20.098], [-5.163, -3.481], [0, 0]], "v": [[-133.879, 74.632], [-118.59, 85.169], [-106.039, 92.327], [-102.342, 88], [-137.552, 65.098], [-149.193, 32.216], [-147.349, 58.885]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [-0.962, -18.499], [-5.163, -3.481], [0, 0]], "v": [[-133.986, 117.708], [-122.215, 128.458], [-109.665, 135.617], [-105.967, 131.289], [-133.5, 102.736], [-138.758, 84.575], [-139.117, 95.795]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 83.033, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [-0.179, -18.079], [-5.163, -3.481], [0, 0]], "v": [[-134.014, 129.017], [-123.167, 139.823], [-110.616, 146.981], [-106.919, 142.654], [-132.437, 112.618], [-142.174, 87.274], [-142.638, 105.327]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [3.052, -16.348], [-5.163, -3.481], [0, 0]], "v": [[-134.129, 175.632], [-127.09, 186.669], [-114.539, 193.827], [-110.842, 189.5], [-128.052, 153.348], [-129.193, 114.966], [-138.599, 145.135]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [3.052, -16.348], [-5.163, -3.481], [0, 0]], "v": [[-134.129, 175.632], [-127.09, 186.669], [-114.539, 193.827], [-110.842, 189.5], [-128.052, 153.348], [-129.193, 114.966], [-138.599, 145.135]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242.967, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [-0.179, -18.079], [-5.163, -3.481], [0, 0]], "v": [[-134.014, 129.017], [-123.167, 139.823], [-110.616, 146.981], [-106.919, 142.654], [-132.437, 112.618], [-142.174, 87.274], [-142.638, 105.327]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [-0.962, -18.499], [-5.163, -3.481], [0, 0]], "v": [[-133.986, 117.708], [-122.215, 128.458], [-109.665, 135.617], [-105.967, 131.289], [-133.5, 102.736], [-138.758, 84.575], [-139.117, 95.795]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247.936, "s": [{"i": [[0, 0], [-5.16, -3.419], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.379, 3.618], [13.213, 8.754], [0, 0], [0, 0], [-3.948, -20.098], [-5.163, -3.481], [0, 0]], "v": [[-133.879, 74.632], [-118.59, 85.169], [-106.039, 92.327], [-102.342, 88], [-137.552, 65.098], [-149.193, 32.216], [-147.349, 58.885]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [{"i": [[0, 0], [-3.168, -17.538], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.143, 2.999], [11.167, 11.356], [0, 0], [0, 0], [-1.059, -16.965], [-5.306, -4.318], [0, 0]], "v": [[-160.19, 25.268], [-116, 70.269], [-107.394, 79.789], [-102.824, 75.971], [-127.584, 44.002], [-139.283, 8.622], [-152.474, 17.519]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [{"i": [[0, 0], [-2.203, -24.377], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[1.029, 2.699], [10.176, 12.617], [0, 0], [0, 0], [0.34, -15.447], [-5.376, -4.724], [0, 0]], "v": [[-157.72, 17.313], [-114.746, 63.052], [-108.05, 73.716], [-103.057, 70.145], [-122.755, 33.783], [-127.803, 4.986], [-146.05, 7.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [{"i": [[0, 0], [-1.238, -31.217], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0.914, 2.399], [9.185, 13.877], [0, 0], [0, 0], [1.739, -13.929], [-5.445, -5.129], [0, 0]], "v": [[-155.884, 11.209], [-113.492, 55.835], [-108.707, 67.643], [-103.291, 64.318], [-117.927, 23.564], [-121.487, -1.91], [-146.953, 5.162]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [{"i": [[0, 0], [1.656, -51.726], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0.572, 1.5], [6.214, 17.657], [0, 0], [0, 0], [5.935, -9.378], [-5.653, -6.345], [0, 0]], "v": [[-144.118, -20.76], [-109.73, 34.193], [-110.675, 49.431], [-103.991, 46.846], [-99.505, 2.321], [-93.348, -22.709], [-126.07, -22.531]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 258.58, "s": [{"i": [[0, 0], [-1.718, -21.543], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0.162, 0.426], [2.665, 22.171], [0, 0], [0, 0], [8.007, -9.652], [-5.163, -3.481], [0, 0]], "v": [[-138.916, -30.125], [-105.237, 8.345], [-103.275, 29.18], [-99.577, 24.853], [-98.258, -1.152], [-91.009, -32.512], [-122.842, -30.498]], "c": true}]}, {"t": 260, "s": [{"i": [[0, 0], [-1.259, -23.959], [0, 0], [0, 0], [-5.758, 8.838], [2.413, 4.406], [0, 0]], "o": [[0, 0], [1.259, 23.959], [0, 0], [0, 0], [8.667, -8.759], [-5.163, -3.481], [0, 0]], "v": [[-139.129, -28.868], [-104.59, -1.331], [-104.039, 21.327], [-100.342, 17], [-96.552, -7.652], [-87.193, -30.284], [-124.849, -29.615]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.588235318661, 0.709803938866, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [0]}, {"t": 249, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "下面叶瑾", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-26.539, 126.348, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-26.539, 126.348, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[-12.775, 15.695], [12.156, -0.396], [-0.753, 11.359], [0, 0], [-1.35, -12.165], [0, 0], [-7.924, -2.207], [-10.61, -10.733], [0, 0], [11.561, 13.465]], "o": [[-18.311, 14.959], [-7.784, -10.206], [-11.381, -1.132], [0, 0], [-31.921, -7.791], [15.243, 27.817], [2.969, 11.735], [17.151, 2.727], [0, 0], [15.836, -1.76]], "v": [[45.726, 118.19], [-23.56, 135.98], [-36.707, 102.613], [-50.16, 99.662], [-49.899, 121.517], [-98.805, 88.008], [-49.094, 127.265], [-32.64, 159.233], [0.089, 161.222], [-19.244, 141.303]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[-13.325, 15.626], [12.288, -0.54], [-0.279, 10.931], [0, 0], [-1.304, -11.757], [0, 0], [-7.924, -2.207], [-11.669, -7.736], [0, 0], [10.513, 12.68]], "o": [[-18.311, 14.959], [-7.686, -9.455], [-11.381, -1.132], [0, 0], [-31.656, -7.375], [16.844, 27.336], [2.331, 12.355], [17.25, 2.464], [0, 0], [15.546, -1.816]], "v": [[43.827, 125.1], [-24.522, 142.352], [-38.161, 110.674], [-51.614, 107.723], [-51.184, 128.595], [-100.98, 95.993], [-50.379, 134.282], [-31.157, 167.996], [3.478, 169.068], [-19.339, 147.698]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [{"i": [[-17.251, 15.127], [13.233, -1.563], [3.102, 7.873], [0, 0], [-0.975, -8.841], [0, 0], [-7.924, -2.207], [-7.553, -4.405], [0, 0], [3.027, 7.081]], "o": [[-18.311, 14.959], [-6.987, -4.093], [-11.381, -1.132], [0, 0], [-29.761, -4.404], [28.274, 23.904], [3.038, 7.451], [17.953, 0.579], [0, 0], [13.477, -2.219]], "v": [[41.101, 145.065], [-20.56, 158.48], [-37.707, 138.863], [-51.16, 135.912], [-49.524, 149.767], [-105.68, 123.633], [-48.719, 155.015], [-33.828, 174.421], [1.026, 173.41], [-9.181, 163.991]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88, "s": [{"i": [[-6.476, 12.31], [14.31, -2.73], [6.957, 4.387], [0, 0], [1.649, 0.733], [0, 0], [-7.924, -2.207], [-2.86, -0.608], [0, 0], [-5.506, 0.697]], "o": [[-18.311, 14.959], [-6.19, 2.02], [-7.293, 3.387], [0, 0], [-27.601, -1.017], [41.305, 19.992], [3.844, 1.86], [17.151, 2.727], [0, 0], [11.119, -2.678]], "v": [[43.976, 155.69], [-12.81, 161.73], [-29.707, 161.363], [-39.16, 158.912], [-44.399, 158.767], [-107.805, 140.008], [-43.594, 163.515], [-33.64, 166.608], [-14.786, 169.222], [5.631, 167.428]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[-6.476, 12.31], [14.31, -2.73], [6.957, 4.387], [0, 0], [1.649, 0.733], [0, 0], [-7.924, -2.207], [-2.86, -0.608], [0, 0], [-5.506, 0.697]], "o": [[-18.311, 14.959], [-6.19, 2.02], [-7.293, 3.387], [0, 0], [-27.601, -1.017], [41.305, 19.992], [3.844, 1.86], [17.151, 2.727], [0, 0], [11.119, -2.678]], "v": [[43.976, 155.69], [-12.81, 161.73], [-29.707, 161.363], [-39.16, 158.912], [-44.399, 158.767], [-107.805, 140.008], [-43.594, 163.515], [-33.64, 166.608], [-14.786, 169.222], [5.631, 167.428]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [{"i": [[-17.251, 15.127], [13.233, -1.563], [3.102, 7.873], [0, 0], [-0.975, -8.841], [0, 0], [-7.924, -2.207], [-7.553, -4.405], [0, 0], [3.027, 7.081]], "o": [[-18.311, 14.959], [-6.987, -4.093], [-11.381, -1.132], [0, 0], [-29.761, -4.404], [28.274, 23.904], [3.038, 7.451], [17.953, 0.579], [0, 0], [13.477, -2.219]], "v": [[41.101, 145.065], [-20.56, 158.48], [-37.707, 138.863], [-51.16, 135.912], [-49.524, 149.767], [-105.68, 123.633], [-48.719, 155.015], [-33.828, 174.421], [1.026, 173.41], [-9.181, 163.991]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [{"i": [[-13.325, 15.626], [12.288, -0.54], [-0.279, 10.931], [0, 0], [-1.304, -11.757], [0, 0], [-7.924, -2.207], [-11.669, -7.736], [0, 0], [10.513, 12.68]], "o": [[-18.311, 14.959], [-7.686, -9.455], [-11.381, -1.132], [0, 0], [-31.656, -7.375], [16.844, 27.336], [2.331, 12.355], [17.25, 2.464], [0, 0], [15.546, -1.816]], "v": [[43.827, 125.1], [-24.522, 142.352], [-38.161, 110.674], [-51.614, 107.723], [-51.184, 128.595], [-100.98, 95.993], [-50.379, 134.282], [-31.157, 167.996], [3.478, 169.068], [-19.339, 147.698]], "c": true}]}, {"t": 260, "s": [{"i": [[-12.775, 15.695], [12.156, -0.396], [-0.753, 11.359], [0, 0], [-1.35, -12.165], [0, 0], [-7.924, -2.207], [-10.61, -10.733], [0, 0], [11.561, 13.465]], "o": [[-18.311, 14.959], [-7.784, -10.206], [-11.381, -1.132], [0, 0], [-31.921, -7.791], [15.243, 27.817], [2.969, 11.735], [17.151, 2.727], [0, 0], [15.836, -1.76]], "v": [[45.726, 118.19], [-23.56, 135.98], [-36.707, 102.613], [-50.16, 99.662], [-49.899, 121.517], [-98.805, 88.008], [-49.094, 127.265], [-32.64, 159.233], [0.089, 161.222], [-19.244, 141.303]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "下面叶子", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [0]}, {"t": 301, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [250.287, 322.717, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [250.287, 316.717, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [250.287, 322.717, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [250.287, 322.717, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [250.287, 316.717, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 301, "s": [250.287, 322.717, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-5.713, 66.717, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[-1.296, -3.535], [-128.348, 8.685], [-2.413, 9.65], [-4.343, 10.615], [36.671, -8.685], [22.437, -4.101], [7.961, 4.584], [0, 0], [-3.378, 28.79], [11.58, 2.252], [18.335, 0.965], [-3.86, -12.867], [11.259, -9.007], [0, 0]], "o": [[0, 0], [124.427, -14.964], [2.413, -9.65], [4.343, -10.615], [-36.671, 8.685], [-22.437, 4.101], [-7.961, -4.584], [0, 0], [3.378, -28.79], [-11.58, -2.252], [-18.335, -0.965], [3.86, 12.867], [-4.842, 2.091], [0, 0]], "v": [[-152.658, 67.632], [10.927, 165.756], [140.776, 85.927], [144.895, 47.427], [98.73, 26.377], [21.529, 52.433], [-34.684, 43.747], [-57.603, 37.716], [-79.799, 4.744], [-89.127, -35.465], [-144.937, -33.213], [-151.371, -5.871], [-152.979, 33.052], [-153.944, 45.114]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73.807, "s": [{"i": [[-4.959, -15.522], [-93.708, 2.346], [-9.975, 12.16], [-4.343, 10.615], [42.648, -12.126], [24.7, -1.961], [7.252, 5.445], [0, 0], [7.056, 26.29], [1.387, 9.981], [8.823, -3.62], [1.114, -9.851], [5.95, -13.332], [0, 0]], "o": [[0, 0], [93.713, -5.669], [17.712, -19.387], [4.343, -10.615], [-32.827, 9.19], [-18.066, 0.96], [-9.264, -7.76], [0, 0], [-3.9, -12.486], [-8.05, -5.692], [-11.589, 6.499], [-1.594, 15.345], [-3.194, 8.953], [0, 0]], "v": [[-138.659, 100.998], [-12.855, 177.025], [103.258, 130.294], [136.527, 71.444], [68.179, 76.794], [-0.956, 92.216], [-70.735, 79.864], [-94.846, 60.174], [-112.1, 27.986], [-115.512, -5.356], [-152.847, 3.087], [-165.422, 28.504], [-147.506, 50.835], [-145.079, 73.623]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[-10.398, -11.081], [-81.112, 0.041], [-12.725, 13.073], [-4.343, 10.615], [44.821, -13.377], [25.523, -1.182], [6.994, 5.759], [0, 0], [10.85, 25.381], [1.553, 12.715], [5.364, -5.287], [2.922, -8.754], [-5.201, -13.501], [0, 0]], "o": [[0, 0], [81.118, -4.572], [23.275, -22.927], [4.343, -10.615], [-31.429, 9.373], [-16.477, -0.182], [-9.738, -8.916], [0, 0], [-2.9, -11.369], [-6.767, -6.943], [-3.993, 6.764], [-0.184, 17.047], [-2.594, 11.448], [0, 0]], "v": [[-137.959, 121.132], [-21.388, 179.959], [89.725, 146.427], [133.594, 80.177], [57.179, 95.127], [-9.023, 106.683], [-83.735, 92.997], [-110.779, 74.841], [-139.35, 39.119], [-146.053, 10.535], [-155.614, 16.287], [-162.922, 41.004], [-158.906, 68.302], [-151.746, 95.239]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [{"i": [[-7.989, -7.997], [-84.85, 2.281], [-12.716, 10.539], [11.233, 19.64], [52.996, -11.669], [25.239, -0.074], [7.658, 5.024], [0, 0], [6.123, 14.869], [4.889, 13.765], [2.864, -6.379], [-1.826, -10.962], [-7.259, -9.618], [0, 0]], "o": [[0, 0], [68.466, -4.379], [18.893, -18.359], [-5.694, -9.956], [-27.254, 6.706], [-21.136, -0.949], [-9.556, -5.581], [0, 0], [-4.877, -7.131], [-2.569, -9.967], [-3.936, 10.421], [1.674, 14.788], [2.491, 10.882], [0, 0]], "v": [[-126.117, 133.548], [-20.388, 180.009], [94.041, 139.544], [118.66, 69.41], [45.629, 106.544], [-16.489, 110.824], [-78.481, 97.597], [-104.804, 82.874], [-132.729, 53.682], [-147.495, 27.285], [-161.314, 49.429], [-161.031, 74.763], [-150.847, 100.668], [-139.912, 118.806]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [{"i": [[-7.575, -6.852], [-85.784, 2.841], [-12.714, 9.906], [17.467, 14.957], [55.04, -11.242], [25.169, 0.203], [7.824, 4.84], [0, 0], [9.855, 19.037], [1.012, 12.746], [2.239, -6.652], [-2.174, -6.589], [-6.524, -8.959], [0, 0]], "o": [[0, 0], [65.303, -4.331], [17.798, -17.217], [-8.712, -7.46], [-26.21, 6.04], [-22.3, -1.141], [-9.51, -4.748], [0, 0], [-3.538, -8.416], [-1.519, -10.723], [-0.618, 6.524], [1.576, 13.911], [3.841, 11.162], [0, 0]], "v": [[-124.282, 138.652], [-20.138, 180.021], [95.12, 137.823], [120.427, 66.094], [44.241, 108.148], [-16.981, 119.485], [-75.668, 108.497], [-97.186, 96.883], [-136.386, 61.885], [-150.981, 24.848], [-164.739, 57.527], [-160.933, 82.39], [-147.833, 110.76], [-136.391, 127.51]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[-6.524, -8.115], [-83.488, 5.121], [-12.708, 8.006], [11.667, 20.907], [61.171, -9.96], [24.956, 1.034], [8.323, 4.289], [0, 0], [14.152, 12.031], [3.705, 11.44], [0.307, -6.37], [-7.111, -10.171], [-2.561, -5.335], [0, 0]], "o": [[0, 0], [55.813, -4.186], [14.512, -13.791], [-5.589, -10.015], [-23.079, 4.04], [-25.794, -1.716], [-9.373, -2.247], [0, 0], [-18.348, -14.469], [1.187, 4.39], [-1.193, 11.38], [6.389, 12.829], [7.702, 10.99], [0, 0]], "v": [[-101.476, 165.365], [-16.388, 176.459], [98.358, 132.661], [125.727, 71.144], [38.579, 115.96], [-18.956, 119.466], [-64.127, 108.747], [-89.079, 99.983], [-120.758, 79.519], [-147.812, 42.11], [-162.164, 89.42], [-154.389, 123.171], [-138.689, 140.585], [-122.279, 152.873]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [{"i": [[-7.306, -6.31], [-69.908, 5.094], [-14.044, 7.831], [6.432, 20.968], [67.049, -10.087], [25.669, 2.08], [8.724, 3.845], [0, 0], [16.6, 13.64], [5.282, 8.182], [-1.714, -2.3], [-7.28, -10.483], [-5.978, -5.755], [0, 0]], "o": [[0, 0], [48.177, -4.069], [15.107, -11.567], [-2.086, -6.8], [-19.758, 3.259], [-24.677, -2.295], [-8.837, -2.326], [0, 0], [-8.206, -6.201], [-1.669, -0.375], [1.879, 8.901], [6.622, 11.956], [9.885, 11.32], [0, 0]], "v": [[-90.002, 156.458], [-7.421, 174.303], [95.467, 137.624], [138.961, 70.083], [34.13, 125.83], [-27.393, 145.956], [-70.888, 138.559], [-94.898, 131.806], [-122.871, 118.754], [-142.399, 103.423], [-137.173, 107.436], [-130.78, 123.684], [-119.177, 137.678], [-107.498, 145.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[-7.306, -6.31], [-69.908, 5.094], [-14.044, 7.831], [6.432, 20.968], [67.049, -10.087], [25.669, 2.08], [8.724, 3.845], [0, 0], [16.6, 13.64], [5.282, 8.182], [-1.714, -2.3], [-7.28, -10.483], [-5.978, -5.755], [0, 0]], "o": [[0, 0], [48.177, -4.069], [15.107, -11.567], [-2.086, -6.8], [-19.758, 3.259], [-24.677, -2.295], [-8.837, -2.326], [0, 0], [-8.206, -6.201], [-1.669, -0.375], [1.879, 8.901], [6.622, 11.956], [9.885, 11.32], [0, 0]], "v": [[-90.002, 156.458], [-7.421, 174.303], [95.467, 137.624], [150.461, 72.583], [34.13, 125.83], [-27.393, 145.956], [-70.888, 138.559], [-94.898, 131.806], [-122.871, 118.754], [-142.399, 103.423], [-137.173, 107.436], [-130.78, 123.684], [-119.177, 137.678], [-107.498, 145.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [{"i": [[-7.306, -6.31], [-69.908, 5.094], [-14.044, 7.831], [6.432, 20.968], [67.049, -10.087], [25.669, 2.08], [8.724, 3.845], [0, 0], [16.6, 13.64], [5.282, 8.182], [-1.714, -2.3], [-7.28, -10.483], [-5.978, -5.755], [0, 0]], "o": [[0, 0], [48.177, -4.069], [15.107, -11.567], [-2.086, -6.8], [-19.758, 3.259], [-24.677, -2.295], [-8.837, -2.326], [0, 0], [-8.206, -6.201], [-1.669, -0.375], [1.879, 8.901], [6.622, 11.956], [9.885, 11.32], [0, 0]], "v": [[-90.002, 156.458], [-7.421, 174.303], [95.467, 137.624], [150.461, 72.583], [34.13, 125.83], [-27.393, 145.956], [-70.888, 138.559], [-94.898, 131.806], [-122.871, 118.754], [-142.399, 103.423], [-137.173, 107.436], [-130.78, 123.684], [-119.177, 137.678], [-107.498, 145.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [{"i": [[-7.306, -6.31], [-69.908, 5.094], [-14.044, 7.831], [6.432, 20.968], [67.049, -10.087], [25.669, 2.08], [8.724, 3.845], [0, 0], [16.6, 13.64], [5.282, 8.182], [-1.714, -2.3], [-7.28, -10.483], [-5.978, -5.755], [0, 0]], "o": [[0, 0], [48.177, -4.069], [15.107, -11.567], [-2.086, -6.8], [-19.758, 3.259], [-24.677, -2.295], [-8.837, -2.326], [0, 0], [-8.206, -6.201], [-1.669, -0.375], [1.879, 8.901], [6.622, 11.956], [9.885, 11.32], [0, 0]], "v": [[-90.002, 156.458], [-7.421, 174.303], [95.467, 137.624], [138.961, 70.083], [34.13, 125.83], [-27.393, 145.956], [-70.888, 138.559], [-94.898, 131.806], [-122.871, 118.754], [-142.399, 103.423], [-137.173, 107.436], [-130.78, 123.684], [-119.177, 137.678], [-107.498, 145.285]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [{"i": [[-6.524, -8.115], [-83.488, 5.121], [-12.708, 8.006], [11.667, 20.907], [61.171, -9.96], [24.956, 1.034], [8.323, 4.289], [0, 0], [14.152, 12.031], [3.705, 11.44], [0.307, -6.37], [-7.111, -10.171], [-2.561, -5.335], [0, 0]], "o": [[0, 0], [55.813, -4.186], [14.512, -13.791], [-5.589, -10.015], [-23.079, 4.04], [-25.794, -1.716], [-9.373, -2.247], [0, 0], [-18.348, -14.469], [1.187, 4.39], [-1.193, 11.38], [6.389, 12.829], [7.702, 10.99], [0, 0]], "v": [[-101.476, 165.365], [-16.388, 176.459], [98.358, 132.661], [125.727, 71.144], [38.579, 115.96], [-18.956, 119.466], [-64.127, 108.747], [-89.079, 99.983], [-120.758, 79.519], [-147.812, 42.11], [-162.164, 89.42], [-154.389, 123.171], [-138.689, 140.585], [-122.279, 152.873]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [{"i": [[-7.575, -6.852], [-85.784, 2.841], [-12.714, 9.906], [17.467, 14.957], [55.04, -11.242], [25.169, 0.203], [7.824, 4.84], [0, 0], [9.855, 19.037], [1.012, 12.746], [2.239, -6.652], [-2.174, -6.589], [-6.524, -8.959], [0, 0]], "o": [[0, 0], [65.303, -4.331], [17.798, -17.217], [-8.712, -7.46], [-26.21, 6.04], [-22.3, -1.141], [-9.51, -4.748], [0, 0], [-3.538, -8.416], [-1.519, -10.723], [-0.618, 6.524], [1.576, 13.911], [3.841, 11.162], [0, 0]], "v": [[-124.282, 138.652], [-20.138, 180.021], [95.12, 137.823], [120.427, 66.094], [44.241, 108.148], [-16.981, 119.485], [-75.668, 108.497], [-97.186, 96.883], [-136.386, 61.885], [-150.981, 24.848], [-164.739, 57.527], [-160.933, 82.39], [-147.833, 110.76], [-136.391, 127.51]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [{"i": [[-7.989, -7.997], [-84.85, 2.281], [-12.716, 10.539], [11.233, 19.64], [52.996, -11.669], [25.239, -0.074], [7.658, 5.024], [0, 0], [6.123, 14.869], [4.889, 13.765], [2.864, -6.379], [-1.826, -10.962], [-7.259, -9.618], [0, 0]], "o": [[0, 0], [68.466, -4.379], [18.893, -18.359], [-5.694, -9.956], [-27.254, 6.706], [-21.136, -0.949], [-9.556, -5.581], [0, 0], [-4.877, -7.131], [-2.569, -9.967], [-3.936, 10.421], [1.674, 14.788], [2.491, 10.882], [0, 0]], "v": [[-126.117, 133.548], [-20.388, 180.009], [94.041, 139.544], [118.66, 69.41], [45.629, 106.544], [-16.489, 110.824], [-78.481, 97.597], [-104.804, 82.874], [-132.729, 53.682], [-147.495, 27.285], [-161.314, 49.429], [-161.031, 74.763], [-150.847, 100.668], [-139.912, 118.806]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[-10.398, -11.081], [-81.112, 0.041], [-12.725, 13.073], [-4.343, 10.615], [44.821, -13.377], [25.523, -1.182], [6.994, 5.759], [0, 0], [10.85, 25.381], [1.553, 12.715], [5.364, -5.287], [2.922, -8.754], [-5.201, -13.501], [0, 0]], "o": [[0, 0], [81.118, -4.572], [23.275, -22.927], [4.343, -10.615], [-31.429, 9.373], [-16.477, -0.182], [-9.738, -8.916], [0, 0], [-2.9, -11.369], [-6.767, -6.943], [-3.993, 6.764], [-0.184, 17.047], [-2.594, 11.448], [0, 0]], "v": [[-137.959, 121.132], [-21.388, 179.959], [89.725, 146.427], [133.594, 80.177], [57.179, 95.127], [-9.023, 106.683], [-83.735, 92.997], [-110.779, 74.841], [-139.35, 39.119], [-146.053, 10.535], [-155.614, 16.287], [-162.922, 41.004], [-158.906, 68.302], [-151.746, 95.239]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 252.193, "s": [{"i": [[-4.959, -15.522], [-93.708, 2.346], [-9.975, 12.16], [-4.343, 10.615], [42.648, -12.126], [24.7, -1.961], [7.252, 5.445], [0, 0], [7.056, 26.29], [1.387, 9.981], [8.823, -3.62], [1.114, -9.851], [5.95, -13.332], [0, 0]], "o": [[0, 0], [93.713, -5.669], [17.712, -19.387], [4.343, -10.615], [-32.827, 9.19], [-18.066, 0.96], [-9.264, -7.76], [0, 0], [-3.9, -12.486], [-8.05, -5.692], [-11.589, 6.499], [-1.594, 15.345], [-3.194, 8.953], [0, 0]], "v": [[-138.659, 100.998], [-12.855, 177.025], [103.258, 130.294], [136.527, 71.444], [68.179, 76.794], [-0.956, 92.216], [-70.735, 79.864], [-94.846, 60.174], [-112.1, 27.986], [-115.512, -5.356], [-152.847, 3.087], [-165.422, 28.504], [-147.506, 50.835], [-145.079, 73.623]], "c": true}]}, {"t": 260, "s": [{"i": [[-1.296, -3.535], [-128.348, 8.685], [-2.413, 9.65], [-4.343, 10.615], [36.671, -8.685], [22.437, -4.101], [7.961, 4.584], [0, 0], [-3.378, 28.79], [11.58, 2.252], [18.335, 0.965], [-3.86, -12.867], [11.259, -9.007], [0, 0]], "o": [[0, 0], [124.427, -14.964], [2.413, -9.65], [4.343, -10.615], [-36.671, 8.685], [-22.437, 4.101], [-7.961, -4.584], [0, 0], [3.378, -28.79], [-11.58, -2.252], [-18.335, -0.965], [3.86, 12.867], [-4.842, 2.091], [0, 0]], "v": [[-152.658, 67.632], [10.927, 165.756], [140.776, 85.927], [144.895, 47.427], [98.73, 26.377], [21.529, 52.433], [-34.684, 43.747], [-57.603, 37.716], [-79.799, 4.744], [-89.127, -35.465], [-144.937, -33.213], [-151.371, -5.871], [-152.979, 33.052], [-153.944, 45.114]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.474509805441, 0.607843160629, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.816, 0.91, 0.478, 0.498, 0.733, 0.843, 0.41, 0.995, 0.651, 0.776, 0.341], "ix": 9}}, "s": {"a": 0, "k": [-6, 11], "ix": 5}, "e": {"a": 0, "k": [-6, 171.719], "ix": 6}, "t": 1, "nm": "Gradient 10", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.106, -0.051], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "叶脉7", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [49.786, -66.581, 0], "to": [0.833, 0, 0], "ti": [-0.833, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [54.786, -66.581, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [54.786, -66.581, 0], "to": [-0.833, 0, 0], "ti": [0.833, 0, 0]}, {"t": 239, "s": [49.786, -66.581, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.05;\n    frequency = 2;\n    decay = 5;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [49.786, -72.783, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27, "s": [{"i": [[0, 0], [-36.676, -8.854], [0, 0], [-24.128, -6.503], [4.155, -11.85], [-0.589, -10.19], [-8.182, -7.973], [5.624, -6.097], [-3.11, -4.172], [5.665, -8.602], [-3.147, -8.182], [0, 0], [1.148, 55.973], [10.91, 7.32], [22.931, 6.714], [1.88, 6.943]], "o": [[0, 0], [21.732, 4.933], [0, 0], [37.345, 1.678], [-4.155, 11.85], [0.589, 10.19], [8.182, 7.973], [-5.624, 6.097], [3.11, 4.172], [-5.665, 8.602], [3.147, 8.182], [0, 0], [-1.148, -55.973], [-10.91, -7.32], [-22.931, -6.714], [0.067, -3.018]], "v": [[-40.73, -164.506], [13.447, -180.485], [51.138, -153.729], [87.872, -128.078], [132.325, -95.364], [118.349, -65.065], [131.317, -47.742], [139.878, -26.145], [125.898, -15.618], [128.589, -2.634], [124.183, 32.822], [117.385, 34.113], [95.65, -29.489], [61.595, -110.832], [-5.055, -156.42], [-42.505, -159.252]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [-36.676, -8.854], [0, 0], [-35.232, -2.37], [4.155, -11.85], [-0.589, -10.19], [-8.182, -7.973], [5.624, -6.097], [-3.11, -4.172], [5.665, -8.602], [-3.147, -8.182], [0, 0], [1.148, 55.973], [10.91, 7.32], [22.931, 6.714], [1.88, 6.943]], "o": [[0, 0], [21.732, 4.933], [0, 0], [37.345, 1.678], [-4.155, 11.85], [0.589, 10.19], [8.182, 7.973], [-5.624, 6.097], [3.11, 4.172], [-5.665, 8.602], [3.147, 8.182], [0, 0], [-1.148, -55.973], [-10.91, -7.32], [-22.931, -6.714], [0.067, -3.018]], "v": [[-40.73, -164.506], [13.447, -180.485], [45.717, -150.231], [87.872, -128.078], [132.325, -95.364], [118.349, -65.065], [131.317, -47.742], [139.878, -26.145], [125.898, -15.618], [128.589, -2.634], [124.183, 32.822], [91.385, 33.113], [95.65, -29.489], [61.595, -110.832], [-5.055, -156.42], [-42.505, -159.252]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [{"i": [[0, 0], [-36.486, -9.388], [0, 0], [-34.946, -2.793], [4.32, -11.757], [-0.422, -10.13], [-8.2, -7.996], [5.704, -5.996], [-3.044, -4.209], [5.78, -8.494], [-2.879, -8.075], [0, 0], [1.045, 55.608], [10.715, 7.474], [22.553, 7.145], [2.091, 6.886]], "o": [[0, 0], [21.624, 5.25], [0, 0], [37.258, 2.241], [-4.32, 11.757], [0.406, 10.148], [8.198, 7.994], [-5.704, 5.996], [3.044, 4.209], [-5.78, 8.494], [2.823, 8.1], [0, 0], [-1.062, -55.397], [-10.74, -7.604], [-22.213, -6.914], [-0.228, -3.148]], "v": [[-36.851, -163.989], [17.25, -179.072], [48.95, -148.565], [90.506, -126.026], [134.233, -92.783], [120.116, -62.786], [132.741, -45.174], [140.968, -23.51], [126.938, -13.093], [129.478, 0.043], [124.594, 34.65], [79.799, 84.956], [97.188, -27.818], [63.817, -108.783], [-1.247, -154.712], [-37.84, -158.353]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [-32.649, -20.138], [0, 0], [-29.177, -11.304], [7.654, -9.87], [2.952, -8.919], [-8.55, -8.467], [7.3, -3.959], [-1.716, -4.942], [8.11, -6.315], [2.518, -5.91], [0, 0], [-1.036, 48.254], [5.908, 13.298], [14.938, 15.829], [11.838, 5.532]], "o": [[0, 0], [19.443, 11.637], [0, 0], [35.502, 13.569], [-7.654, 9.87], [-3.282, 9.308], [8.507, 8.424], [-7.3, 3.959], [1.716, 4.942], [-8.11, 6.315], [-3.7, 6.436], [0, 0], [0.682, -43.796], [-7.312, -13.326], [-10.5, -10.895], [-13.162, -4.968]], "v": [[8.303, -142.069], [93.887, -150.596], [114.097, -115.004], [143.568, -84.682], [172.688, -40.775], [155.715, -16.853], [161.45, 6.566], [162.925, 29.583], [147.884, 37.801], [147.396, 53.993], [132.875, 71.48], [88.13, 78.097], [125.185, 4.863], [113.592, -62.5], [85, -104.307], [46.162, -130.234]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [{"i": [[0, 0], [-31.035, -24.662], [0, 0], [-26.75, -14.885], [9.056, -9.077], [4.371, -8.41], [-8.697, -8.666], [7.972, -3.103], [-1.157, -5.251], [9.09, -5.398], [4.789, -4.999], [0, 0], [-1.911, 45.16], [4.913, 12.559], [10.917, 19.367], [7.575, 9.771]], "o": [[0, 0], [18.525, 14.324], [0, 0], [34.763, 18.335], [-9.056, 9.077], [-4.833, 8.955], [8.636, 8.605], [-7.972, 3.103], [1.157, 5.251], [-9.09, 5.398], [-6.444, 5.735], [0, 0], [1.415, -38.915], [-5.87, -15.734], [-6.958, -12.732], [-11.982, -9.363]], "v": [[63.344, -146.891], [126.131, -138.616], [141.505, -100.884], [165.892, -67.288], [188.867, -18.894], [170.692, 2.472], [173.529, 28.334], [172.162, 51.92], [156.696, 59.213], [154.934, 76.692], [136.359, 86.975], [114.432, 89.965], [140.48, 19.785], [128.672, -48.889], [113.024, -102.658], [88.482, -130.339]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82.322, "s": [{"i": [[0, 0], [-30.501, -26.157], [0, 0], [-25.948, -16.069], [9.52, -8.814], [4.84, -8.242], [-8.746, -8.732], [8.194, -2.819], [-0.973, -5.353], [9.414, -5.095], [5.54, -4.698], [0, 0], [-2.2, 44.137], [4.584, 12.314], [9.588, 20.536], [6.166, 11.172]], "o": [[0, 0], [18.222, 15.212], [0, 0], [34.519, 19.911], [-9.52, 8.814], [-5.346, 8.838], [8.679, 8.664], [-8.194, 2.819], [0.973, 5.353], [-9.414, 5.095], [-7.352, 5.504], [0, 0], [1.657, -37.302], [-5.393, -16.529], [-5.787, -13.339], [-8.709, -12.578]], "v": [[94.179, -149.815], [136.789, -134.656], [150.566, -96.217], [173.272, -61.538], [194.216, -11.661], [175.643, 8.861], [177.521, 35.53], [175.216, 59.304], [159.609, 66.291], [157.426, 84.195], [137.51, 92.098], [123.127, 93.888], [145.536, 24.718], [133.657, -44.389], [122.287, -102.113], [108.459, -130.374]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [{"i": [[0, 0], [-28.913, -30.609], [0, 0], [-23.559, -19.594], [10.9, -8.033], [6.237, -7.74], [-8.891, -8.927], [8.856, -1.976], [-0.423, -5.657], [10.379, -4.193], [7.775, -3.802], [0, 0], [-3.062, 41.091], [2.956, 13.6], [6.055, 24.582], [4.691, 11.408]], "o": [[0, 0], [17.318, 17.857], [0, 0], [33.792, 24.603], [-10.9, 8.033], [-6.873, 8.49], [8.807, 8.842], [-8.856, 1.976], [0.423, 5.657], [-10.379, 4.193], [-10.053, 4.815], [0, 0], [2.379, -32.497], [-3.974, -18.899], [-3.583, -14.645], [-3.526, -7.745]], "v": [[122.667, -145.971], [168.528, -122.863], [177.546, -82.317], [195.247, -44.416], [210.142, 9.878], [190.386, 27.883], [189.411, 56.957], [184.309, 81.292], [168.284, 87.368], [164.846, 106.538], [145.59, 108.83], [128.938, 112.018], [166.934, 40.146], [155.201, -26.792], [145.006, -88.787], [134.681, -122.75]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 1, "y": 0}, "t": 88, "s": [{"i": [[0, 0], [-28.622, -31.423], [0, 0], [-23.122, -20.239], [11.152, -7.89], [6.492, -7.649], [-8.918, -8.963], [8.976, -1.822], [-0.322, -5.713], [10.556, -4.028], [8.184, -3.638], [0, 0], [-6.344, 32.833], [2.658, 13.835], [5.41, 25.321], [4.421, 11.451]], "o": [[0, 0], [17.153, 18.34], [0, 0], [33.659, 25.46], [-11.152, 7.89], [-7.152, 8.426], [8.831, 8.875], [-8.976, 1.822], [0.322, 5.713], [-10.556, 4.028], [-10.547, 4.689], [0, 0], [6.018, -31.143], [-3.715, -19.332], [-3.18, -14.883], [-2.579, -6.861]], "v": [[125.211, -144.381], [174.328, -120.708], [182.476, -79.777], [199.263, -41.287], [213.052, 13.814], [193.08, 31.36], [191.584, 60.873], [185.971, 85.31], [169.869, 91.22], [166.202, 110.621], [147.066, 111.888], [112.5, 101.331], [170.844, 42.965], [155.59, -24.168], [147.68, -86.943], [136.517, -121.653]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88.709, "s": [{"i": [[0, 0], [-28.828, -39.494], [0, 0], [-23.122, -20.239], [11.152, -7.89], [6.492, -7.649], [-8.918, -8.963], [8.976, -1.822], [-0.322, -5.713], [10.556, -4.028], [8.271, -3.434], [0, 0], [-3.219, 40.535], [2.658, 13.835], [5.41, 25.321], [4.421, 11.451]], "o": [[0, 0], [17.153, 18.34], [0, 0], [33.659, 25.46], [-11.152, 7.89], [-7.152, 8.426], [8.831, 8.875], [-8.976, 1.822], [0.322, 5.713], [-10.556, 4.028], [-22.066, 9.16], [0, 0], [2.511, -31.619], [-3.715, -19.332], [-3.18, -14.883], [-2.579, -6.861]], "v": [[77.211, -144.381], [174.328, -120.708], [182.476, -79.777], [199.263, -41.287], [213.052, 13.814], [193.08, 31.36], [191.584, 60.873], [185.971, 85.31], [169.869, 91.22], [166.202, 110.621], [141.566, 110.138], [55.25, 105.331], [115.344, 26.215], [120.09, -25.668], [114.68, -82.443], [103.517, -118.653]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100, "s": [{"i": [[0, 0], [-54.81, -47.994], [0, 0], [-23.122, -20.239], [11.152, -7.89], [6.492, -7.649], [-8.918, -8.963], [8.976, -1.822], [-0.322, -5.713], [10.556, -4.028], [8.271, -3.434], [0, 0], [-3.219, 40.535], [2.658, 13.835], [5.41, 25.321], [10.001, 8.451]], "o": [[0, 0], [17.153, 18.34], [0, 0], [33.659, 25.46], [-11.152, 7.89], [-7.152, 8.426], [8.831, 8.875], [-8.976, 1.822], [0.322, 5.713], [-10.556, 4.028], [-22.066, 9.16], [0, 0], [2.511, -31.619], [-3.715, -19.332], [-3.18, -14.883], [-13.499, -8.549]], "v": [[24.711, -117.381], [174.328, -120.708], [182.476, -79.777], [199.263, -41.287], [213.052, 13.814], [193.08, 31.36], [191.584, 60.873], [185.971, 85.31], [169.869, 91.22], [166.202, 110.621], [141.566, 110.138], [55.25, 105.331], [115.344, 26.215], [120.09, -25.668], [111.68, -72.943], [76.017, -104.153]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 226, "s": [{"i": [[0, 0], [-54.81, -47.994], [0, 0], [-23.122, -20.239], [11.152, -7.89], [6.492, -7.649], [-8.918, -8.963], [8.976, -1.822], [-0.322, -5.713], [10.556, -4.028], [8.271, -3.434], [0, 0], [-3.219, 40.535], [2.658, 13.835], [5.41, 25.321], [10.001, 8.451]], "o": [[0, 0], [17.153, 18.34], [0, 0], [33.659, 25.46], [-11.152, 7.89], [-7.152, 8.426], [8.831, 8.875], [-8.976, 1.822], [0.322, 5.713], [-10.556, 4.028], [-22.066, 9.16], [0, 0], [2.511, -31.619], [-3.715, -19.332], [-3.18, -14.883], [-13.499, -8.549]], "v": [[24.711, -117.381], [174.328, -120.708], [182.476, -79.777], [199.263, -41.287], [213.052, 13.814], [193.08, 31.36], [191.584, 60.873], [185.971, 85.31], [169.869, 91.22], [166.202, 110.621], [141.566, 110.138], [55.25, 105.331], [115.344, 26.215], [120.09, -25.668], [111.68, -72.943], [76.017, -104.153]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 237.291, "s": [{"i": [[0, 0], [-28.828, -39.494], [0, 0], [-23.122, -20.239], [11.152, -7.89], [6.492, -7.649], [-8.918, -8.963], [8.976, -1.822], [-0.322, -5.713], [10.556, -4.028], [8.271, -3.434], [0, 0], [-3.219, 40.535], [2.658, 13.835], [5.41, 25.321], [4.421, 11.451]], "o": [[0, 0], [17.153, 18.34], [0, 0], [33.659, 25.46], [-11.152, 7.89], [-7.152, 8.426], [8.831, 8.875], [-8.976, 1.822], [0.322, 5.713], [-10.556, 4.028], [-22.066, 9.16], [0, 0], [2.511, -31.619], [-3.715, -19.332], [-3.18, -14.883], [-2.579, -6.861]], "v": [[77.211, -144.381], [174.328, -120.708], [182.476, -79.777], [199.263, -41.287], [213.052, 13.814], [193.08, 31.36], [191.584, 60.873], [185.971, 85.31], [169.869, 91.22], [166.202, 110.621], [141.566, 110.138], [55.25, 105.331], [115.344, 26.215], [120.09, -25.668], [114.68, -82.443], [103.517, -118.653]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[0, 0], [-28.622, -31.423], [0, 0], [-23.122, -20.239], [11.152, -7.89], [6.492, -7.649], [-8.918, -8.963], [8.976, -1.822], [-0.322, -5.713], [10.556, -4.028], [8.184, -3.638], [0, 0], [-6.344, 32.833], [2.658, 13.835], [5.41, 25.321], [4.421, 11.451]], "o": [[0, 0], [17.153, 18.34], [0, 0], [33.659, 25.46], [-11.152, 7.89], [-7.152, 8.426], [8.831, 8.875], [-8.976, 1.822], [0.322, 5.713], [-10.556, 4.028], [-10.547, 4.689], [0, 0], [6.018, -31.143], [-3.715, -19.332], [-3.18, -14.883], [-2.579, -6.861]], "v": [[125.211, -144.381], [174.328, -120.708], [182.476, -79.777], [199.263, -41.287], [213.052, 13.814], [193.08, 31.36], [191.584, 60.873], [185.971, 85.31], [169.869, 91.22], [166.202, 110.621], [147.066, 111.888], [112.5, 101.331], [170.844, 42.965], [155.59, -24.168], [147.68, -86.943], [136.517, -121.653]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[0, 0], [-28.913, -30.609], [0, 0], [-23.559, -19.594], [10.9, -8.033], [6.237, -7.74], [-8.891, -8.927], [8.856, -1.976], [-0.423, -5.657], [10.379, -4.193], [7.775, -3.802], [0, 0], [-3.062, 41.091], [2.956, 13.6], [6.055, 24.582], [4.691, 11.408]], "o": [[0, 0], [17.318, 17.857], [0, 0], [33.792, 24.603], [-10.9, 8.033], [-6.873, 8.49], [8.807, 8.842], [-8.856, 1.976], [0.423, 5.657], [-10.379, 4.193], [-10.053, 4.815], [0, 0], [2.379, -32.497], [-3.974, -18.899], [-3.583, -14.645], [-3.526, -7.745]], "v": [[122.667, -145.971], [168.528, -122.863], [177.546, -82.317], [195.247, -44.416], [210.142, 9.878], [190.386, 27.883], [189.411, 56.957], [184.309, 81.292], [168.284, 87.368], [164.846, 106.538], [145.59, 108.83], [128.938, 112.018], [166.934, 40.146], [155.201, -26.792], [145.006, -88.787], [134.681, -122.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243.678, "s": [{"i": [[0, 0], [-30.501, -26.157], [0, 0], [-25.948, -16.069], [9.52, -8.814], [4.84, -8.242], [-8.746, -8.732], [8.194, -2.819], [-0.973, -5.353], [9.414, -5.095], [5.54, -4.698], [0, 0], [-2.2, 44.137], [4.584, 12.314], [9.588, 20.536], [6.166, 11.172]], "o": [[0, 0], [18.222, 15.212], [0, 0], [34.519, 19.911], [-9.52, 8.814], [-5.346, 8.838], [8.679, 8.664], [-8.194, 2.819], [0.973, 5.353], [-9.414, 5.095], [-7.352, 5.504], [0, 0], [1.657, -37.302], [-5.393, -16.529], [-5.787, -13.339], [-8.709, -12.578]], "v": [[94.179, -149.815], [136.789, -134.656], [150.566, -96.217], [173.272, -61.538], [194.216, -11.661], [175.643, 8.861], [177.521, 35.53], [175.216, 59.304], [159.609, 66.291], [157.426, 84.195], [137.51, 92.098], [123.127, 93.888], [145.536, 24.718], [133.657, -44.389], [122.287, -102.113], [108.459, -130.374]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [{"i": [[0, 0], [-31.035, -24.662], [0, 0], [-26.75, -14.885], [9.056, -9.077], [4.371, -8.41], [-8.697, -8.666], [7.972, -3.103], [-1.157, -5.251], [9.09, -5.398], [4.789, -4.999], [0, 0], [-1.911, 45.16], [4.913, 12.559], [10.917, 19.367], [7.575, 9.771]], "o": [[0, 0], [18.525, 14.324], [0, 0], [34.763, 18.335], [-9.056, 9.077], [-4.833, 8.955], [8.636, 8.605], [-7.972, 3.103], [1.157, 5.251], [-9.09, 5.398], [-6.444, 5.735], [0, 0], [1.415, -38.915], [-5.87, -15.734], [-6.958, -12.732], [-11.982, -9.363]], "v": [[63.344, -146.891], [126.131, -138.616], [141.505, -100.884], [165.892, -67.288], [188.867, -18.894], [170.692, 2.472], [173.529, 28.334], [172.162, 51.92], [156.696, 59.213], [154.934, 76.692], [136.359, 86.975], [114.432, 89.965], [140.48, 19.785], [128.672, -48.889], [113.024, -102.658], [88.482, -130.339]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[0, 0], [-32.649, -20.138], [0, 0], [-29.177, -11.304], [7.654, -9.87], [2.952, -8.919], [-8.55, -8.467], [7.3, -3.959], [-1.716, -4.942], [8.11, -6.315], [2.518, -5.91], [0, 0], [-1.036, 48.254], [5.908, 13.298], [14.938, 15.829], [11.838, 5.532]], "o": [[0, 0], [19.443, 11.637], [0, 0], [35.502, 13.569], [-7.654, 9.87], [-3.282, 9.308], [8.507, 8.424], [-7.3, 3.959], [1.716, 4.942], [-8.11, 6.315], [-3.7, 6.436], [0, 0], [0.682, -43.796], [-7.312, -13.326], [-10.5, -10.895], [-13.162, -4.968]], "v": [[8.303, -142.069], [93.887, -150.596], [114.097, -115.004], [143.568, -84.682], [172.688, -40.775], [155.715, -16.853], [161.45, 6.566], [162.925, 29.583], [147.884, 37.801], [147.396, 53.993], [132.875, 71.48], [88.13, 78.097], [125.185, 4.863], [113.592, -62.5], [85, -104.307], [46.162, -130.234]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [{"i": [[0, 0], [-36.486, -9.388], [0, 0], [-34.946, -2.793], [4.32, -11.757], [-0.422, -10.13], [-8.2, -7.996], [5.704, -5.996], [-3.044, -4.209], [5.78, -8.494], [-2.879, -8.075], [0, 0], [1.045, 55.608], [10.715, 7.474], [22.553, 7.145], [2.091, 6.886]], "o": [[0, 0], [21.624, 5.25], [0, 0], [37.258, 2.241], [-4.32, 11.757], [0.406, 10.148], [8.198, 7.994], [-5.704, 5.996], [3.044, 4.209], [-5.78, 8.494], [2.823, 8.1], [0, 0], [-1.062, -55.397], [-10.74, -7.604], [-22.213, -6.914], [-0.228, -3.148]], "v": [[-36.851, -163.989], [17.25, -179.072], [48.95, -148.565], [90.506, -126.026], [134.233, -92.783], [120.116, -62.786], [132.741, -45.174], [140.968, -23.51], [126.938, -13.093], [129.478, 0.043], [124.594, 34.65], [79.799, 84.956], [97.188, -27.818], [63.817, -108.783], [-1.247, -154.712], [-37.84, -158.353]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 260, "s": [{"i": [[0, 0], [-36.676, -8.854], [0, 0], [-35.232, -2.37], [4.155, -11.85], [-0.589, -10.19], [-8.182, -7.973], [5.624, -6.097], [-3.11, -4.172], [5.665, -8.602], [-3.147, -8.182], [0, 0], [1.148, 55.973], [10.91, 7.32], [22.931, 6.714], [1.88, 6.943]], "o": [[0, 0], [21.732, 4.933], [0, 0], [37.345, 1.678], [-4.155, 11.85], [0.589, 10.19], [8.182, 7.973], [-5.624, 6.097], [3.11, 4.172], [-5.665, 8.602], [3.147, 8.182], [0, 0], [-1.148, -55.973], [-10.91, -7.32], [-22.931, -6.714], [0.067, -3.018]], "v": [[-40.73, -164.506], [13.447, -180.485], [45.717, -150.231], [87.872, -128.078], [132.325, -95.364], [118.349, -65.065], [131.317, -47.742], [139.878, -26.145], [125.898, -15.618], [128.589, -2.634], [124.183, 32.822], [91.385, 33.113], [95.65, -29.489], [61.595, -110.832], [-5.055, -156.42], [-42.505, -159.252]], "c": true}]}, {"t": 283, "s": [{"i": [[0, 0], [-36.676, -8.854], [0, 0], [-24.128, -6.503], [4.155, -11.85], [-0.589, -10.19], [-8.182, -7.973], [5.624, -6.097], [-3.11, -4.172], [5.665, -8.602], [-3.147, -8.182], [0, 0], [1.148, 55.973], [10.91, 7.32], [22.931, 6.714], [1.88, 6.943]], "o": [[0, 0], [21.732, 4.933], [0, 0], [37.345, 1.678], [-4.155, 11.85], [0.589, 10.19], [8.182, 7.973], [-5.624, 6.097], [3.11, 4.172], [-5.665, 8.602], [3.147, 8.182], [0, 0], [-1.148, -55.973], [-10.91, -7.32], [-22.931, -6.714], [0.067, -3.018]], "v": [[-40.73, -164.506], [13.447, -180.485], [51.138, -153.729], [87.872, -128.078], [132.325, -95.364], [118.349, -65.065], [131.317, -47.742], [139.878, -26.145], [125.898, -15.618], [128.589, -2.634], [124.183, 32.822], [117.385, 34.113], [95.65, -29.489], [61.595, -110.832], [-5.055, -156.42], [-42.505, -159.252]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.474509805441, 0.607843160629, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960790157, 0.976470589638, 0.615686297417, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "叶脉6", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [-120.115, -115.882, 0], "to": [-1, -0.667, 0], "ti": [1, 0.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [-126.115, -119.882, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [-126.115, -119.882, 0], "to": [1, 0.667, 0], "ti": [-1, -0.667, 0]}, {"t": 251, "s": [-120.115, -115.882, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.05;\n    frequency = 2;\n    decay = 5;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-120.115, -115.882, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [1.886, 7.286], [-20.682, 15.927], [-14.597, 6.169], [-9.873, 8.509], [-13.103, 6.139], [-13.043, 0.409], [-16.471, -3.334], [0, 0], [38.185, -13.359], [9.024, -57.403]], "o": [[0, 0], [-1.886, -7.286], [7.658, -9.239], [14.597, -6.169], [9.873, -8.509], [13.103, -6.139], [13.043, -0.409], [16.471, 3.334], [0, 0], [-23.492, 8.219], [-9.024, 57.403]], "v": [[-108.196, 24.889], [-125.175, -43.038], [-109.599, -90.834], [-85.487, -108.376], [-55.122, -137.971], [-23.048, -148.055], [3.56, -156.018], [29.172, -159.344], [73.279, -104.961], [-21.185, -110.141], [-92.966, -21.845]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [2.228, 7.18], [-19.877, 16.875], [-14.271, 6.85], [-9.446, 8.96], [-12.781, 6.749], [-12.994, 1.029], [-16.592, -2.542], [0, 0], [55.206, -12.406], [11.852, -7.346]], "o": [[0, 0], [-2.228, -7.18], [7.201, -9.582], [14.271, -6.85], [9.446, -8.96], [12.781, -6.749], [12.994, -1.029], [16.592, 2.542], [0, 0], [-9.794, 2.094], [-43.648, 28.154]], "v": [[-145.618, -22.507], [-153.543, -51.275], [-145.345, -99.721], [-116.11, -116.355], [-98.504, -164.895], [-74.52, -176.039], [-49.102, -188.001], [-17.831, -189.663], [22.959, -133.143], [-51.206, -129.094], [-79.852, -113.654]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[0, 0], [2.4, 7.126], [-19.474, 17.35], [-14.107, 7.191], [-9.232, 9.186], [-12.62, 7.054], [-12.969, 1.339], [-16.068, -4.835], [0, 0], [26.008, -14.293], [11.089, -8.699]], "o": [[0, 0], [-2.4, -7.126], [6.972, -9.755], [14.107, -7.191], [9.232, -9.186], [12.62, -7.054], [12.969, -1.339], [15.613, 4.694], [0, 0], [-12.992, 5.707], [-53.053, 30.423]], "v": [[-152.468, -23.269], [-167.756, -55.403], [-163.256, -104.174], [-131.454, -120.353], [-110.97, -164.106], [-91.04, -175.78], [-66.218, -189.745], [-31.658, -188.648], [2.394, -158.628], [-53.008, -147.207], [-86.089, -125.301]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [{"i": [[0, 0], [2.502, 7.095], [-19.233, 17.633], [-14.009, 7.395], [-9.104, 9.32], [-12.523, 7.236], [-12.955, 1.524], [-16.689, -1.909], [0, 0], [24.924, -33.194], [2.06, -8.599]], "o": [[0, 0], [-2.502, -7.095], [6.836, -9.857], [14.009, -7.395], [9.104, -9.32], [12.523, -7.236], [12.955, -1.524], [16.689, 1.909], [0, 0], [-9.576, 15.206], [-56.29, 33.651]], "v": [[-156.555, -23.723], [-176.237, -57.865], [-173.942, -106.831], [-140.609, -122.738], [-118.409, -163.635], [-100.897, -175.626], [-76.431, -190.786], [-35.034, -189.917], [-18.772, -175.888], [-74.824, -157.556], [-88.06, -128.001]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 1, "y": 0}, "t": 88, "s": [{"i": [[0, 0], [2.571, 7.073], [-19.072, 17.823], [-13.944, 7.531], [-9.019, 9.411], [-12.459, 7.358], [-12.945, 1.648], [-16.713, -1.751], [0, 0], [24.728, -36.954], [1.237, -8.787]], "o": [[0, 0], [-2.571, -7.073], [6.744, -9.926], [13.944, -7.531], [9.019, -9.411], [12.459, -7.358], [12.945, -1.648], [16.713, 1.751], [0, 0], [-10.772, 18.046], [-71.263, 50.963]], "v": [[-157.04, -6.902], [-182.41, -46.012], [-181.092, -108.608], [-146.734, -124.334], [-123.385, -163.319], [-107.492, -175.523], [-86.513, -194.483], [-52.584, -193.981], [-23.611, -185.825], [-76.728, -162.046], [-89.237, -129.463]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88.709, "s": [{"i": [[0, 0], [2.571, 7.073], [-19.072, 17.823], [-13.944, 7.531], [-9.019, 9.411], [-12.459, 7.358], [-12.945, 1.648], [-16.713, -1.751], [0, 0], [24.728, -36.954], [1.237, -8.787]], "o": [[0, 0], [-2.571, -7.073], [6.744, -9.926], [13.944, -7.531], [9.019, -9.411], [12.459, -7.358], [12.945, -1.648], [16.713, 1.751], [0, 0], [-10.772, 18.046], [-71.263, 50.963]], "v": [[-130.04, 35.598], [-182.41, -46.012], [-181.092, -108.608], [-146.734, -124.334], [-123.385, -163.319], [-107.492, -175.523], [-86.513, -194.483], [-52.584, -193.981], [-22.611, -187.325], [6.272, -126.046], [-27.237, -44.463]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100, "s": [{"i": [[0, 0], [2.571, 7.073], [-19.072, 17.823], [-13.944, 7.531], [-9.019, 9.411], [-12.459, 7.358], [-12.945, 1.648], [-16.713, -1.751], [0, 0], [22.107, -55.368], [1.237, -8.787]], "o": [[0, 0], [-2.571, -7.073], [6.744, -9.926], [13.944, -7.531], [9.019, -9.411], [12.459, -7.358], [12.945, -1.648], [16.713, 1.751], [0, 0], [-10.772, 18.046], [-71.263, 50.963]], "v": [[-130.04, 35.598], [-182.41, -46.012], [-181.092, -108.608], [-146.734, -124.334], [-123.385, -163.319], [-107.492, -175.523], [-86.513, -194.483], [-52.584, -193.981], [-22.611, -187.325], [21.272, -104.046], [-27.237, -44.463]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 226, "s": [{"i": [[0, 0], [2.571, 7.073], [-19.072, 17.823], [-13.944, 7.531], [-9.019, 9.411], [-12.459, 7.358], [-12.945, 1.648], [-16.713, -1.751], [0, 0], [22.107, -55.368], [1.237, -8.787]], "o": [[0, 0], [-2.571, -7.073], [6.744, -9.926], [13.944, -7.531], [9.019, -9.411], [12.459, -7.358], [12.945, -1.648], [16.713, 1.751], [0, 0], [-10.772, 18.046], [-71.263, 50.963]], "v": [[-130.04, 35.598], [-182.41, -46.012], [-181.092, -108.608], [-146.734, -124.334], [-123.385, -163.319], [-107.492, -175.523], [-86.513, -194.483], [-52.584, -193.981], [-22.611, -187.325], [21.272, -104.046], [-27.237, -44.463]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 237.291, "s": [{"i": [[0, 0], [2.571, 7.073], [-19.072, 17.823], [-13.944, 7.531], [-9.019, 9.411], [-12.459, 7.358], [-12.945, 1.648], [-16.713, -1.751], [0, 0], [24.728, -36.954], [1.237, -8.787]], "o": [[0, 0], [-2.571, -7.073], [6.744, -9.926], [13.944, -7.531], [9.019, -9.411], [12.459, -7.358], [12.945, -1.648], [16.713, 1.751], [0, 0], [-10.772, 18.046], [-71.263, 50.963]], "v": [[-130.04, 35.598], [-182.41, -46.012], [-181.092, -108.608], [-146.734, -124.334], [-123.385, -163.319], [-107.492, -175.523], [-86.513, -194.483], [-52.584, -193.981], [-22.611, -187.325], [6.272, -126.046], [-27.237, -44.463]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[0, 0], [2.571, 7.073], [-19.072, 17.823], [-13.944, 7.531], [-9.019, 9.411], [-12.459, 7.358], [-12.945, 1.648], [-16.713, -1.751], [0, 0], [24.728, -36.954], [1.237, -8.787]], "o": [[0, 0], [-2.571, -7.073], [6.744, -9.926], [13.944, -7.531], [9.019, -9.411], [12.459, -7.358], [12.945, -1.648], [16.713, 1.751], [0, 0], [-10.772, 18.046], [-71.263, 50.963]], "v": [[-157.04, -6.902], [-182.41, -46.012], [-181.092, -108.608], [-146.734, -124.334], [-123.385, -163.319], [-107.492, -175.523], [-86.513, -194.483], [-52.584, -193.981], [-23.611, -185.825], [-76.728, -162.046], [-89.237, -129.463]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [{"i": [[0, 0], [2.502, 7.095], [-19.233, 17.633], [-14.009, 7.395], [-9.104, 9.32], [-12.523, 7.236], [-12.955, 1.524], [-16.689, -1.909], [0, 0], [24.924, -33.194], [2.06, -8.599]], "o": [[0, 0], [-2.502, -7.095], [6.836, -9.857], [14.009, -7.395], [9.104, -9.32], [12.523, -7.236], [12.955, -1.524], [16.689, 1.909], [0, 0], [-9.576, 15.206], [-56.29, 33.651]], "v": [[-156.555, -23.723], [-176.237, -57.865], [-173.942, -106.831], [-140.609, -122.738], [-118.409, -163.635], [-100.897, -175.626], [-76.431, -190.786], [-35.034, -189.917], [-18.772, -175.888], [-74.824, -157.556], [-88.06, -128.001]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [{"i": [[0, 0], [2.4, 7.126], [-19.474, 17.35], [-14.107, 7.191], [-9.232, 9.186], [-12.62, 7.054], [-12.969, 1.339], [-16.068, -4.835], [0, 0], [26.008, -14.293], [11.089, -8.699]], "o": [[0, 0], [-2.4, -7.126], [6.972, -9.755], [14.107, -7.191], [9.232, -9.186], [12.62, -7.054], [12.969, -1.339], [15.613, 4.694], [0, 0], [-12.992, 5.707], [-53.053, 30.423]], "v": [[-152.468, -23.269], [-167.756, -55.403], [-163.256, -104.174], [-131.454, -120.353], [-110.97, -164.106], [-91.04, -175.78], [-66.218, -189.745], [-31.658, -188.648], [2.394, -158.628], [-53.008, -147.207], [-86.089, -125.301]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[0, 0], [2.228, 7.18], [-19.877, 16.875], [-14.271, 6.85], [-9.446, 8.96], [-12.781, 6.749], [-12.994, 1.029], [-16.592, -2.542], [0, 0], [55.206, -12.406], [11.852, -7.346]], "o": [[0, 0], [-2.228, -7.18], [7.201, -9.582], [14.271, -6.85], [9.446, -8.96], [12.781, -6.749], [12.994, -1.029], [16.592, 2.542], [0, 0], [-9.794, 2.094], [-43.648, 28.154]], "v": [[-145.618, -22.507], [-153.543, -51.275], [-145.345, -99.721], [-116.11, -116.355], [-98.504, -164.895], [-74.52, -176.039], [-49.102, -188.001], [-17.831, -189.663], [22.959, -133.143], [-51.206, -129.094], [-79.852, -113.654]], "c": true}]}, {"t": 260, "s": [{"i": [[0, 0], [1.886, 7.286], [-20.682, 15.927], [-14.597, 6.169], [-9.873, 8.509], [-13.103, 6.139], [-13.043, 0.409], [-16.471, -3.334], [0, 0], [38.185, -13.359], [9.024, -57.403]], "o": [[0, 0], [-1.886, -7.286], [7.658, -9.239], [14.597, -6.169], [9.873, -8.509], [13.103, -6.139], [13.043, -0.409], [16.471, 3.334], [0, 0], [-23.492, 8.219], [-9.024, 57.403]], "v": [[-108.196, 24.889], [-125.175, -43.038], [-109.599, -90.834], [-85.487, -108.376], [-55.122, -137.971], [-23.048, -148.055], [3.56, -156.018], [29.172, -159.344], [73.279, -104.961], [-21.185, -110.141], [-92.966, -21.845]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.474509805441, 0.607843160629, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960790157, 0.976470589638, 0.615686297417, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "上面叶子 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 31, "s": [3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 269, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 282.527, "s": [3]}, {"t": 300, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [196.246, 201.776, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [196.246, 201.776, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [196.246, 201.776, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 300, "s": [196.246, 201.776, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-59.754, -54.224, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [23.582, -133.981], [2.994, 14.915], [-48.872, 39.081], [-62.02, -16.23], [0, 0]], "o": [[0, 0], [-23.582, 133.98], [-6.867, -27.135], [27.916, -22.323], [19.137, 5.365], [0, 0]], "v": [[28.678, -148.377], [-101.981, -0.972], [-150.913, -16.945], [-100.261, -143.95], [11.507, -169.001], [32.22, -149.82]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [18.031, -91.754], [26.099, 47.898], [-23.467, 22.799], [-25.054, -26.979], [0, 0]], "o": [[0, 0], [-28.769, 45.472], [-35.151, -74.352], [23.943, -24.912], [19.137, 5.365], [0, 0]], "v": [[-29.822, -161.377], [-127.731, -79.972], [-160.599, -3.398], [-133.209, -151.745], [-13.196, -172.021], [-8.03, -159.82]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 80.193, "s": [{"i": [[0, 0], [18.999, -76.699], [18.398, 36.903], [-20.381, 23.796], [-49.793, -0.229], [0, 0]], "o": [[0, 0], [-18.186, 38.639], [-13.151, -39.769], [19.865, -23.693], [9.698, 3.917], [0, 0]], "v": [[-53.989, -165.044], [-135.648, -74.305], [-162.016, -56.231], [-128.376, -152.245], [-27.071, -177.105], [-35.197, -168.737]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88, "s": [{"i": [[0, 0], [20.935, -46.589], [-19.151, 18.398], [-14.208, 25.791], [-27.929, -2.479], [0, 0]], "o": [[0, 0], [-13.019, 28.972], [4.349, -5.102], [11.709, -21.255], [-9.179, 1.021], [0, 0]], "v": [[-86.822, -161.377], [-165.981, -38.972], [-160.349, -95.398], [-118.709, -153.245], [-54.821, -187.271], [-71.53, -176.07]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[0, 0], [20.935, -46.589], [-19.151, 18.398], [-14.208, 25.791], [-27.929, -2.479], [0, 0]], "o": [[0, 0], [-13.019, 28.972], [4.349, -5.102], [11.709, -21.255], [-9.179, 1.021], [0, 0]], "v": [[-86.822, -161.377], [-165.981, -38.972], [-160.349, -95.398], [-118.709, -153.245], [-54.821, -187.271], [-71.53, -176.07]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245.807, "s": [{"i": [[0, 0], [18.999, -76.699], [18.398, 36.903], [-20.381, 23.796], [-49.793, -0.229], [0, 0]], "o": [[0, 0], [-18.186, 38.639], [-13.151, -39.769], [19.865, -23.693], [9.698, 3.917], [0, 0]], "v": [[-53.989, -165.044], [-135.648, -74.305], [-162.016, -56.231], [-128.376, -152.245], [-27.071, -177.105], [-35.197, -168.737]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[0, 0], [18.031, -91.754], [26.099, 47.898], [-23.467, 22.799], [-25.054, -26.979], [0, 0]], "o": [[0, 0], [-28.769, 45.472], [-35.151, -74.352], [23.943, -24.912], [19.137, 5.365], [0, 0]], "v": [[-29.822, -161.377], [-127.731, -79.972], [-160.599, -3.398], [-133.209, -151.745], [-13.196, -172.021], [-8.03, -159.82]], "c": true}]}, {"t": 260, "s": [{"i": [[0, 0], [23.582, -133.981], [2.994, 14.915], [-48.872, 39.081], [-62.02, -16.23], [0, 0]], "o": [[0, 0], [-23.582, 133.98], [-6.867, -27.135], [27.916, -22.323], [19.137, 5.365], [0, 0]], "v": [[28.678, -148.377], [-101.981, -0.972], [-150.913, -16.945], [-100.261, -143.95], [11.507, -169.001], [32.22, -149.82]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.474509805441, 0.607843160629, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.816, 0.91, 0.478, 0.498, 0.733, 0.843, 0.41, 0.995, 0.651, 0.776, 0.341], "ix": 9}}, "s": {"a": 0, "k": [-214.527, 317.263], "ix": 5}, "e": {"a": 0, "k": [-77.603, 362.189], "ix": 6}, "t": 1, "nm": "Gradient 12", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "右上叶瑾", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 84, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [147.681, -71.924, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [147.681, -71.924, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[2.689, 10.362], [5.715, 2.876], [2.793, 2.403], [0.349, -7.944], [0, 0], [2.308, -3.567], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-5.347, -8.318], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [4.086, 1.51], [-0.002, 5.958], [2.292, -5.762], [8.212, 8.135], [0, -0.629]], "v": [[161.672, -71.138], [142.863, -87.45], [137.611, -103.655], [140.403, -88.615], [135.303, -90.542], [129.848, -80.262], [147.688, -66.088], [147.209, -40.194], [151.064, -62.768], [165.515, -47.952]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [{"i": [[1.203, 10.263], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [-0.981, -4.755], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[164.547, -43.513], [142.897, -59.3], [137.644, -75.505], [140.436, -60.465], [129.495, -63.975], [126.731, -53.37], [147.722, -37.938], [144.993, -14.169], [151.097, -34.618], [164.89, -21.327]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74.516, "s": [{"i": [[2.689, 10.362], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [1.716, -3.829], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[160.854, -20.388], [143.578, -34.3], [138.326, -50.505], [141.118, -35.465], [136.551, -37.225], [133.163, -25.245], [148.404, -12.938], [147.924, 12.956], [151.779, -9.618], [162.446, 1.548]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 78.064, "s": [{"i": [[2.689, 10.362], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [1.716, -3.829], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[159.956, -9.846], [143.306, -23.411], [138.053, -39.616], [140.845, -24.576], [136.279, -26.336], [132.89, -14.356], [148.131, -2.049], [147.652, 23.845], [151.506, 1.271], [162.174, 12.437]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88, "s": [{"i": [[2.689, 10.362], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [1.716, -3.829], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[186.422, -5.388], [171.397, -18.05], [166.144, -34.255], [168.936, -19.215], [164.37, -20.975], [160.981, -8.995], [176.222, 3.312], [175.743, 29.206], [179.597, 6.632], [190.265, 17.798]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[2.689, 10.362], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [1.716, -3.829], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[186.422, -5.388], [171.397, -18.05], [166.144, -34.255], [168.936, -19.215], [164.37, -20.975], [160.981, -8.995], [176.222, 3.312], [175.743, 29.206], [179.597, 6.632], [190.265, 17.798]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247.936, "s": [{"i": [[2.689, 10.362], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [1.716, -3.829], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[159.956, -9.846], [143.306, -23.411], [138.053, -39.616], [140.845, -24.576], [136.279, -26.336], [132.89, -14.356], [148.131, -2.049], [147.652, 23.845], [151.506, 1.271], [162.174, 12.437]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251.484, "s": [{"i": [[2.689, 10.362], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [1.716, -3.829], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[160.854, -20.388], [143.578, -34.3], [138.326, -50.505], [141.118, -35.465], [136.551, -37.225], [133.163, -25.245], [148.404, -12.938], [147.924, 12.956], [151.779, -9.618], [162.446, 1.548]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [{"i": [[1.203, 10.263], [4.46, 2.163], [2.793, 2.403], [0.349, -7.944], [0, 0], [-0.981, -4.755], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-4, -6.33], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [2.72, 0.16], [-0.002, 5.958], [2.292, -5.762], [6.072, 6.054], [0, -0.629]], "v": [[164.547, -43.513], [142.897, -59.3], [137.644, -75.505], [140.436, -60.465], [129.495, -63.975], [126.731, -53.37], [147.722, -37.938], [144.993, -14.169], [151.097, -34.618], [164.89, -21.327]], "c": true}]}, {"t": 260, "s": [{"i": [[2.689, 10.362], [5.715, 2.876], [2.793, 2.403], [0.349, -7.944], [0, 0], [2.308, -3.567], [-6.736, -6.526], [0.417, -7.991], [-0.104, 6.409], [0, 0.331]], "o": [[-5.347, -8.318], [-0.834, -5.77], [0, 0], [-3.058, -1.356], [0, 0], [4.086, 1.51], [-0.002, 5.958], [2.292, -5.762], [8.212, 8.135], [0, -0.629]], "v": [[161.672, -71.138], [142.863, -87.45], [137.611, -103.655], [140.403, -88.615], [135.303, -90.542], [129.848, -80.262], [147.688, -66.088], [147.209, -40.194], [151.064, -62.768], [165.515, -47.952]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "上面叶子", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [-3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 269, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 282.736, "s": [-3]}, {"t": 298, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [320.878, 196.523, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [320.878, 196.523, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [320.878, 196.523, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 298, "s": [320.878, 196.523, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [64.878, -59.477, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [-22.227, 6.301], [-25.078, -20.203], [0, 0], [-20.076, -6.782], [44.278, -128.307], [0, 0], [-1.634, 14.941], [0, 0], [30.57, 20.821], [43.953, 8.893]], "o": [[0, 0], [22.227, -6.301], [22.286, 15.833], [0, 0], [14.622, 4.94], [-15.251, 40.831], [0, 0], [1.634, -14.941], [0, 0], [-30.57, -20.821], [-28.085, 2.775]], "v": [[-41.406, -159.233], [-22.974, -176.539], [61.62, -167.877], [90.19, -149.779], [127.076, -135.468], [153.969, 50.792], [98.869, 6.181], [95.222, -24.15], [94.125, -44.279], [60.919, -111.147], [-16.008, -158.312]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[0, 0], [-21.672, 4.709], [-23.44, -19.88], [0, 0], [-17.49, -10.028], [43.362, -122.54], [0, 0], [-1.634, 14.941], [0, 0], [28.749, 21.445], [42.039, 9.888]], "o": [[0, 0], [21.342, -5.167], [21.931, 17.329], [0, 0], [13.118, 6.632], [-15.658, 39.378], [0, 0], [1.634, -14.941], [0, 0], [-28.773, -21.843], [-26.549, 1.396]], "v": [[-30.217, -157.946], [-11.324, -172.377], [66.63, -161.364], [98.154, -140.344], [130.18, -124.19], [153.356, 54.048], [116.216, 15.07], [112.467, -19.785], [106.734, -57.341], [68.153, -107.176], [-5.629, -155.278]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [-17.858, -6.228], [-12.187, -17.663], [0, 0], [0.273, -32.325], [37.067, -82.925], [0, 0], [-1.634, 14.941], [0, 0], [16.238, 25.734], [28.892, 16.727]], "o": [[0, 0], [15.262, 2.619], [19.495, 27.605], [0, 0], [2.786, 18.255], [-18.448, 29.394], [0, 0], [1.634, -14.941], [0, 0], [-16.432, -28.868], [-16.001, -8.081]], "v": [[46.645, -149.108], [68.702, -143.789], [101.046, -116.627], [152.866, -75.529], [151.502, -46.718], [149.145, 76.417], [135.045, 58.431], [142.398, 16.1], [136.301, -29.029], [117.845, -79.897], [65.668, -134.437]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[0, 0], [-15.392, -13.298], [-4.913, -16.23], [0, 0], [6.748, -44.573], [32.997, -57.317], [0, 0], [-1.634, 14.941], [0, 0], [8.151, 28.506], [20.395, 21.148]], "o": [[0, 0], [11.333, 7.652], [17.92, 34.247], [0, 0], [-1.963, 22.564], [-20.252, 22.941], [0, 0], [1.634, -14.941], [0, 0], [-8.455, -33.409], [-9.183, -14.207]], "v": [[115.697, -131.645], [127.299, -122.81], [139.557, -89.684], [182.771, -35.467], [164.903, 3.36], [146.042, 90.876], [140.791, 75.142], [149.697, 46.375], [149.55, 1.475], [139.416, -48.968], [118.122, -120.465]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [{"i": [[0, 0], [-13.827, -17.785], [-0.296, -15.32], [0, 0], [10.858, -52.348], [30.414, -41.062], [0, 0], [-1.634, 14.941], [0, 0], [3.017, 30.266], [15, 23.954]], "o": [[0, 0], [8.838, 10.847], [16.92, 38.464], [0, 0], [-4.977, 25.3], [-21.397, 18.845], [0, 0], [1.634, -14.941], [0, 0], [-3.392, -36.291], [-4.855, -18.096]], "v": [[130.354, -135.111], [153.934, -113.134], [164.002, -72.581], [201.754, -10.037], [173.41, 35.148], [155.374, 103.342], [155.179, 92.024], [169.385, 56.835], [165.785, 6.198], [154.153, -35.347], [138.316, -111.77]], "c": true}]}, {"i": {"x": 0.95, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88, "s": [{"i": [[0, 0], [-13.488, -18.757], [0.704, -15.123], [0, 0], [11.748, -54.032], [29.855, -37.542], [0, 0], [-1.634, 14.941], [0, 0], [1.905, 30.647], [13.832, 24.562]], "o": [[0, 0], [8.298, 11.539], [16.704, 39.377], [0, 0], [-5.63, 25.892], [-21.645, 17.958], [0, 0], [1.634, -14.941], [0, 0], [-2.295, -36.916], [-3.918, -18.938]], "v": [[129.27, -133.733], [159.702, -111.039], [169.296, -68.877], [205.866, -4.529], [175.252, 42.032], [157.395, 106.042], [158.295, 95.681], [173.648, 59.1], [169.301, 7.221], [157.345, -32.397], [141.168, -108.062]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.05, "y": 0}, "t": 238, "s": [{"i": [[0, 0], [-13.488, -18.757], [0.704, -15.123], [0, 0], [11.748, -54.032], [29.855, -37.542], [0, 0], [-1.634, 14.941], [0, 0], [1.905, 30.647], [13.832, 24.562]], "o": [[0, 0], [8.298, 11.539], [16.704, 39.377], [0, 0], [-5.63, 25.892], [-21.645, 17.958], [0, 0], [1.634, -14.941], [0, 0], [-2.295, -36.916], [-3.918, -18.938]], "v": [[129.27, -133.733], [159.702, -111.039], [169.296, -68.877], [205.866, -4.529], [175.252, 42.032], [157.395, 106.042], [158.295, 95.681], [173.648, 59.1], [169.301, 7.221], [157.345, -32.397], [141.168, -108.062]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[0, 0], [-13.827, -17.785], [-0.296, -15.32], [0, 0], [10.858, -52.348], [30.414, -41.062], [0, 0], [-1.634, 14.941], [0, 0], [3.017, 30.266], [15, 23.954]], "o": [[0, 0], [8.838, 10.847], [16.92, 38.464], [0, 0], [-4.977, 25.3], [-21.397, 18.845], [0, 0], [1.634, -14.941], [0, 0], [-3.392, -36.291], [-4.855, -18.096]], "v": [[130.354, -135.111], [153.934, -113.134], [164.002, -72.581], [201.754, -10.037], [173.41, 35.148], [155.374, 103.342], [155.179, 92.024], [169.385, 56.835], [165.785, 6.198], [154.153, -35.347], [138.316, -111.77]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [{"i": [[0, 0], [-15.392, -13.298], [-4.913, -16.23], [0, 0], [6.748, -44.573], [32.997, -57.317], [0, 0], [-1.634, 14.941], [0, 0], [8.151, 28.506], [20.395, 21.148]], "o": [[0, 0], [11.333, 7.652], [17.92, 34.247], [0, 0], [-1.963, 22.564], [-20.252, 22.941], [0, 0], [1.634, -14.941], [0, 0], [-8.455, -33.409], [-9.183, -14.207]], "v": [[115.697, -131.645], [127.299, -122.81], [139.557, -89.684], [182.771, -35.467], [164.903, 3.36], [146.042, 90.876], [140.791, 75.142], [149.697, 46.375], [149.55, 1.475], [139.416, -48.968], [118.122, -120.465]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [{"i": [[0, 0], [-17.858, -6.228], [-12.187, -17.663], [0, 0], [0.273, -32.325], [37.067, -82.925], [0, 0], [-1.634, 14.941], [0, 0], [16.238, 25.734], [28.892, 16.727]], "o": [[0, 0], [15.262, 2.619], [19.495, 27.605], [0, 0], [2.786, 18.255], [-18.448, 29.394], [0, 0], [1.634, -14.941], [0, 0], [-16.432, -28.868], [-16.001, -8.081]], "v": [[46.645, -149.108], [68.702, -143.789], [101.046, -116.627], [152.866, -75.529], [151.502, -46.718], [149.145, 76.417], [135.045, 58.431], [142.398, 16.1], [136.301, -29.029], [117.845, -79.897], [65.668, -134.437]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [{"i": [[0, 0], [-21.672, 4.709], [-23.44, -19.88], [0, 0], [-17.49, -10.028], [43.362, -122.54], [0, 0], [-1.634, 14.941], [0, 0], [28.749, 21.445], [42.039, 9.888]], "o": [[0, 0], [21.342, -5.167], [21.931, 17.329], [0, 0], [13.118, 6.632], [-15.658, 39.378], [0, 0], [1.634, -14.941], [0, 0], [-28.773, -21.843], [-26.549, 1.396]], "v": [[-30.217, -157.946], [-11.324, -172.377], [66.63, -161.364], [98.154, -140.344], [130.18, -124.19], [153.356, 54.048], [116.216, 15.07], [112.467, -19.785], [106.734, -57.341], [68.153, -107.176], [-5.629, -155.278]], "c": true}]}, {"t": 260, "s": [{"i": [[0, 0], [-22.227, 6.301], [-25.078, -20.203], [0, 0], [-20.076, -6.782], [44.278, -128.307], [0, 0], [-1.634, 14.941], [0, 0], [30.57, 20.821], [43.953, 8.893]], "o": [[0, 0], [22.227, -6.301], [22.286, 15.833], [0, 0], [14.622, 4.94], [-15.251, 40.831], [0, 0], [1.634, -14.941], [0, 0], [-30.57, -20.821], [-28.085, 2.775]], "v": [[-41.406, -159.233], [-22.974, -176.539], [61.62, -167.877], [90.19, -149.779], [127.076, -135.468], [153.969, 50.792], [98.869, 6.181], [95.222, -24.15], [94.125, -44.279], [60.919, -111.147], [-16.008, -158.312]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.474509805441, 0.607843160629, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.816, 0.91, 0.478, 0.498, 0.733, 0.843, 0.41, 0.995, 0.651, 0.776, 0.341], "ix": 9}}, "s": {"a": 0, "k": [-41, -60], "ix": 5}, "e": {"a": 0, "k": [170.708, -60], "ix": 6}, "t": 1, "nm": "Gradient 11", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 319, "st": 52, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "叶子2描边", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [-4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 88, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 248, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 270, "s": [-4]}, {"t": 297, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 4;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [302.18, 354.593, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [302.18, 354.593, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [305.18, 358.593, 0], "to": [1, 2.833, 0], "ti": [-1, -2.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 88, "s": [311.18, 375.593, 0], "to": [1, 2.833, 0], "ti": [1, 2.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [311.18, 375.593, 0], "to": [-1, -2.833, 0], "ti": [1, 2.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 270, "s": [305.18, 358.593, 0], "to": [-1, -2.833, 0], "ti": [0, 0, 0]}, {"t": 297, "s": [302.18, 354.593, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 2;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [55.18, 119.593, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [92, 92, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [92, 92, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 88, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 248, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 270, "s": [92, 92, 100]}, {"t": 297, "s": [92, 92, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [13.144, -5.996], [-9.425, -1.66], [1.224, -4.419], [13.021, -2.997], [-2.439, -3.828], [-0.591, -10.112], [1.783, -1.676], [4.699, -3.852], [0, 0], [11.205, -4.391], [7.295, -2.102], [39.353, 9.959], [36.947, 41.52], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [9.425, 1.66], [-1.224, 4.419], [-13.021, 2.997], [2.44, 3.828], [0.591, 10.112], [-1.783, 1.676], [-2.876, 2.52], [0, 0], [-11.205, 4.391], [-7.295, 2.102], [-39.353, -9.959], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [192.678, 36.947], [191.454, 62.938], [193.349, 68.705], [205.283, 93.383], [180.596, 117.985], [169.767, 130.807], [178.499, 143.163], [176.575, 166.723], [165.747, 173.096], [156.825, 187.693], [134.147, 208.385], [111.375, 224.243], [40.858, 235.968], [-50.362, 209.586], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [4.231, -6.108], [-9.631, -7.834], [1.224, -4.419], [12.862, -3.617], [-1.404, -4.317], [-0.591, -10.112], [4.654, -6.516], [6.494, -1.354], [0, 0], [18.706, -7.613], [7.295, -2.102], [27.18, 30.559], [13.704, 55.438], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [12.388, 10.076], [-1.224, 4.419], [-4.772, 1.342], [0.49, 1.509], [0.591, 10.112], [-2.494, 3.492], [-7.758, 1.971], [0, 0], [-11.147, 4.537], [-7.295, 2.102], [-26.978, -30.332], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [185.376, 39.768], [198.789, 61.227], [199.565, 74.197], [203.333, 104.402], [185.044, 120.364], [180.831, 129.177], [186.873, 147.3], [179.723, 168.25], [165.747, 173.096], [156.825, 187.693], [138.044, 214.438], [109.668, 218.298], [43.022, 226.978], [-35.168, 200.562], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [4.231, -6.108], [-9.631, -7.834], [1.224, -4.419], [12.862, -3.617], [-1.404, -4.317], [-0.591, -10.112], [4.654, -6.516], [6.494, -1.354], [0, 0], [18.706, -7.613], [7.295, -2.102], [27.18, 30.559], [13.704, 55.438], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [12.388, 10.076], [-1.224, 4.419], [-4.772, 1.342], [0.49, 1.509], [0.591, 10.112], [-2.494, 3.492], [-7.758, 1.971], [0, 0], [-11.147, 4.537], [-7.295, 2.102], [-26.978, -30.332], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [185.376, 39.768], [198.789, 61.227], [199.565, 74.197], [203.333, 104.402], [185.044, 120.364], [180.831, 129.177], [186.873, 147.3], [179.723, 168.25], [165.747, 173.096], [156.825, 187.693], [138.044, 214.438], [109.668, 218.298], [43.022, 226.978], [-35.168, 200.562], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}, {"t": 269, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [13.144, -5.996], [-9.425, -1.66], [1.224, -4.419], [13.021, -2.997], [-2.439, -3.828], [-0.591, -10.112], [1.783, -1.676], [4.699, -3.852], [0, 0], [11.205, -4.391], [7.295, -2.102], [39.353, 9.959], [36.947, 41.52], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [9.425, 1.66], [-1.224, 4.419], [-13.021, 2.997], [2.44, 3.828], [0.591, 10.112], [-1.783, 1.676], [-2.876, 2.52], [0, 0], [-11.205, 4.391], [-7.295, 2.102], [-39.353, -9.959], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [192.678, 36.947], [191.454, 62.938], [193.349, 68.705], [205.283, 93.383], [180.596, 117.985], [169.767, 130.807], [178.499, 143.163], [176.575, 166.723], [165.747, 173.096], [156.825, 187.693], [134.147, 208.385], [111.375, 224.243], [40.858, 235.968], [-50.362, 209.586], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.447058826685, 0.600000023842, 0.180392161012, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "叶子2投影", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36.534, 112.787, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [36.534, 112.787, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [2.46, -6.124], [9.074, -10.522], [2.737, -2.93], [0.585, -12.957], [92.104, 30.207], [-3.935, 7.87], [0, 0], [0, 0]], "o": [[0, 0], [-2.46, 6.124], [3.639, 24.804], [-2.737, 2.93], [-3.721, 73], [-16.397, -1.03], [10.016, -28.82], [0, 0], [0, 0]], "v": [[189.448, 40.299], [186.796, 49.917], [170.533, 75.319], [169.754, 108.945], [162.638, 124.799], [-47.647, 208.489], [-91.963, 175.843], [-57.071, 137.499], [149.983, 34.715]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 79, "s": [{"i": [[0, 0], [13.879, -3.502], [8.16, -13.364], [14.46, -17.438], [18.875, -11.009], [44.991, 10.538], [-3.935, 7.87], [0, 0], [0, 0]], "o": [[0, 0], [6.459, 32.133], [-4.246, 9.38], [-13.971, 9.442], [-24.546, 23.108], [-16.397, -1.03], [10.016, -28.82], [0, 0], [0, 0]], "v": [[195.71, 61.283], [180.455, 80.526], [154.175, 120.64], [122.546, 151.593], [87.116, 182.899], [-37.925, 218.204], [-91.963, 175.843], [-57.071, 137.499], [170.625, 17.573]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [20.906, -1.888], [0, 0], [25.291, -8.214], [26.936, 6.318], [15.999, -1.566], [-3.935, 7.87], [0, 0], [0, 0]], "o": [[0, 0], [11.948, 48.138], [0, 0], [-6.027, 16.892], [-13.697, 6.256], [-16.397, -1.03], [10.016, -28.82], [0, 0], [0, 0]], "v": [[199.565, 74.197], [176.552, 99.362], [123.233, 133.5], [65.647, 164.327], [2.479, 206.659], [-31.942, 224.183], [-91.963, 175.843], [-57.071, 137.499], [183.328, 7.023]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [20.906, -1.888], [0, 0], [25.291, -8.214], [26.936, 6.318], [15.999, -1.566], [-3.935, 7.87], [0, 0], [0, 0]], "o": [[0, 0], [11.948, 48.138], [0, 0], [-6.027, 16.892], [-13.697, 6.256], [-16.397, -1.03], [10.016, -28.82], [0, 0], [0, 0]], "v": [[199.565, 74.197], [176.552, 99.362], [123.233, 133.5], [65.647, 164.327], [2.479, 206.659], [-31.942, 224.183], [-91.963, 175.843], [-57.071, 137.499], [183.328, 7.023]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 256, "s": [{"i": [[0, 0], [13.879, -3.502], [8.16, -13.364], [14.46, -17.438], [18.875, -11.009], [44.991, 10.538], [-3.935, 7.87], [0, 0], [0, 0]], "o": [[0, 0], [6.459, 32.133], [-4.246, 9.38], [-13.971, 9.442], [-24.546, 23.108], [-16.397, -1.03], [10.016, -28.82], [0, 0], [0, 0]], "v": [[195.71, 61.283], [180.455, 80.526], [154.175, 120.64], [122.546, 151.593], [87.116, 182.899], [-37.925, 218.204], [-91.963, 175.843], [-57.071, 137.499], [170.625, 17.573]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [2.46, -6.124], [9.074, -10.522], [2.737, -2.93], [0.585, -12.957], [92.104, 30.207], [-3.935, 7.87], [0, 0], [0, 0]], "o": [[0, 0], [-2.46, 6.124], [3.639, 24.804], [-2.737, 2.93], [-3.721, 73], [-16.397, -1.03], [10.016, -28.82], [0, 0], [0, 0]], "v": [[189.448, 40.299], [186.796, 49.917], [170.533, 75.319], [169.754, 108.945], [162.638, 124.799], [-47.647, 208.489], [-91.963, 175.843], [-57.071, 137.499], [149.983, 34.715]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.266666680574, 0.607843160629, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "叶子2叶脉", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [84.279, 138.998, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [84.279, 138.998, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.864, 0.724], [21.605, -4.036], [0, 0], [0, 0], [0, 0], [-2.036, 3.828], [-3.056, 18.812], [0, 0], [-13.745, -19.066], [27.268, 28.155], [3.412, 3.545]], "o": [[-3.66, -0.685], [-17.31, -17.038], [0, 0], [0, 0], [0, 13.382], [1.668, -3.137], [0, 0], [0, 0], [13.745, 19.066], [-3.522, -3.893], [21.934, -9.156]], "v": [[155.266, 98.128], [53.182, 115.733], [22.706, 88.008], [13.145, 101.06], [18.314, 105.789], [15.669, 189.846], [29, 115.567], [55.18, 139.522], [104.569, 182.162], [73.753, 137.158], [63.328, 125.993]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "叶子2颜色", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [55.18, 119.593, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [55.18, 119.593, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [13.144, -5.996], [-9.425, -1.66], [1.224, -4.419], [13.021, -2.997], [-2.439, -3.828], [-0.591, -10.112], [1.783, -1.676], [4.699, -3.852], [0, 0], [11.205, -4.391], [7.295, -2.102], [39.353, 9.959], [36.947, 41.52], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [9.425, 1.66], [-1.224, 4.419], [-13.021, 2.997], [2.44, 3.828], [0.591, 10.112], [-1.783, 1.676], [-2.876, 2.52], [0, 0], [-11.205, 4.391], [-7.295, 2.102], [-39.353, -9.959], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [192.678, 36.947], [191.454, 62.938], [193.349, 68.705], [205.283, 93.383], [180.596, 117.985], [169.767, 130.807], [178.499, 143.163], [176.575, 166.723], [165.747, 173.096], [156.825, 187.693], [134.147, 208.385], [111.375, 224.243], [40.858, 235.968], [-50.362, 209.586], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [4.231, -6.108], [-9.631, -7.834], [1.224, -4.419], [12.862, -3.617], [-1.404, -4.317], [-0.591, -10.112], [4.654, -6.516], [6.494, -1.354], [0, 0], [18.706, -7.613], [7.295, -2.102], [27.18, 30.559], [13.704, 55.438], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [12.388, 10.076], [-1.224, 4.419], [-4.772, 1.342], [0.49, 1.509], [0.591, 10.112], [-2.494, 3.492], [-7.758, 1.971], [0, 0], [-11.147, 4.537], [-7.295, 2.102], [-26.978, -30.332], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [185.376, 39.768], [198.789, 61.227], [199.565, 74.197], [203.333, 104.402], [185.044, 120.364], [180.831, 129.177], [186.873, 147.3], [179.723, 168.25], [165.747, 173.096], [156.825, 187.693], [138.044, 214.438], [109.668, 218.298], [43.022, 226.978], [-35.168, 200.562], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [4.231, -6.108], [-9.631, -7.834], [1.224, -4.419], [12.862, -3.617], [-1.404, -4.317], [-0.591, -10.112], [4.654, -6.516], [6.494, -1.354], [0, 0], [18.706, -7.613], [7.295, -2.102], [27.18, 30.559], [13.704, 55.438], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [12.388, 10.076], [-1.224, 4.419], [-4.772, 1.342], [0.49, 1.509], [0.591, 10.112], [-2.494, 3.492], [-7.758, 1.971], [0, 0], [-11.147, 4.537], [-7.295, 2.102], [-26.978, -30.332], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [185.376, 39.768], [198.789, 61.227], [199.565, 74.197], [203.333, 104.402], [185.044, 120.364], [180.831, 129.177], [186.873, 147.3], [179.723, 168.25], [165.747, 173.096], [156.825, 187.693], [138.044, 214.438], [109.668, 218.298], [43.022, 226.978], [-35.168, 200.562], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}, {"t": 269, "s": [{"i": [[-96.512, 57.792], [-43.127, -8.136], [4.08, -8.564], [-11.566, -1.43], [-7.53, -4.491], [13.144, -5.996], [-9.425, -1.66], [1.224, -4.419], [13.021, -2.997], [-2.439, -3.828], [-0.591, -10.112], [1.783, -1.676], [4.699, -3.852], [0, 0], [11.205, -4.391], [7.295, -2.102], [39.353, 9.959], [36.947, 41.52], [0, 0], [-24.186, 19.286], [-0.97, 11.55]], "o": [[0, 0], [43.127, 8.136], [-3.844, 8.069], [11.566, 1.43], [12.021, 5.56], [0, 0], [9.425, 1.66], [-1.224, 4.419], [-13.021, 2.997], [2.44, 3.828], [0.591, 10.112], [-1.783, 1.676], [-2.876, 2.52], [0, 0], [-11.205, 4.391], [-7.295, 2.102], [-39.353, -9.959], [-17.939, -25.155], [0, 0], [24.186, -19.286], [0.97, -11.55]], "v": [[26.512, 1.413], [105.423, 5.51], [151.736, 25.508], [165.47, 36.074], [192.678, 36.947], [191.454, 62.938], [193.349, 68.705], [205.283, 93.383], [180.596, 117.985], [169.767, 130.807], [178.499, 143.163], [176.575, 166.723], [165.747, 173.096], [156.825, 187.693], [134.147, 208.385], [111.375, 224.243], [40.858, 235.968], [-50.362, 209.586], [-85.684, 187.814], [-83.924, 159.654], [-87.157, 120.617]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.816, 0.91, 0.478, 0.496, 0.696, 0.82, 0.382, 0.993, 0.576, 0.729, 0.286], "ix": 9}}, "s": {"a": 0, "k": [104, 218], "ix": 5}, "e": {"a": 0, "k": [58.309, 123.733], "ix": 6}, "t": 1, "nm": "Gradient 7", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "叶子1描边", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [7]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [2]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 248, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 269, "s": [2]}, {"t": 297, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [323.697, 156.792, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 34, "s": [330.697, 162.792, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [323.697, 156.792, 0], "to": [2.333, -2.833, 0], "ti": [-2.333, 2.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [337.697, 139.792, 0], "to": [2.333, -2.833, 0], "ti": [2.333, -2.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [337.697, 139.792, 0], "to": [-2.333, 2.833, 0], "ti": [2.333, -2.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 269, "s": [323.697, 156.792, 0], "to": [-2.333, 2.833, 0], "ti": [0, 0, 0]}, {"t": 297, "s": [323.697, 156.792, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 2;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [81.697, -116.208, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 87, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 248, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 269, "s": [100, 100, 100]}, {"t": 297, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [2.305, 16.517], [-13.06, 2.928], [-9.603, 3.841], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [-2.305, -16.517], [13.06, -2.928], [9.603, -3.841], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-33.291, -45.583]], "v": [[-31.52, -119.2], [-39.877, -165.807], [-26.142, -198.09], [3.957, -199.464], [27.67, -201.018], [37.482, -218.641], [59.518, -221.761], [79.492, -221.377], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [0, 16.677], [-2.426, 14.022], [-9.622, 3.795], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [0, -18.117], [2.282, -13.188], [17.736, -6.995], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-25.481, -46.467]], "v": [[-18.471, -107.569], [-23.757, -144.505], [-20.134, -179.318], [-3.286, -206.69], [24.175, -205.027], [43.496, -219.105], [63.736, -220.491], [85.281, -221.765], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [0, 16.677], [-2.426, 14.022], [-9.622, 3.795], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [0, -18.117], [2.282, -13.188], [17.736, -6.995], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-25.481, -46.467]], "v": [[-18.471, -107.569], [-23.757, -144.505], [-20.134, -179.318], [-3.286, -206.69], [24.175, -205.027], [43.496, -219.105], [63.736, -220.491], [85.281, -221.765], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [2.305, 16.517], [-13.06, 2.928], [-9.603, 3.841], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [-2.305, -16.517], [13.06, -2.928], [9.603, -3.841], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-33.291, -45.583]], "v": [[-31.52, -119.2], [-39.877, -165.807], [-26.142, -198.09], [3.957, -199.464], [27.67, -201.018], [37.482, -218.641], [59.518, -221.761], [79.492, -221.377], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.447058826685, 0.600000023842, 0.180392161012, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "叶子1投影", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [70.202, -101.219, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [70.202, -101.219, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.988, 13.951], [0, 0], [-19.18, -6.724], [-16.752, -13.538], [-15.281, -4.875], [-8.757, -14.299], [0, 0], [-0.887, -29.264]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [6.485, -14.673], [0, 0], [16.791, 2.961], [11.354, 9.673], [15.281, 4.875], [14.809, 23.14], [0, 0], [-3.273, 15.73]], "v": [[161.033, -6.772], [132.225, -28.251], [104.656, -15.979], [56.023, -48.598], [12.86, -110.958], [-17.179, -149.308], [-33.561, -186.904], [0.589, -198.388], [55.68, -203.276], [112.602, -176.549], [143.719, -167.875], [170.931, -133.749], [185.011, -78.145], [181.129, -27.33]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.988, 13.951], [0, 0], [-26.769, -11.971], [-17.126, -8.654], [0, 0], [-12.969, -19.579], [0, 0], [-0.887, -29.264]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [6.485, -14.673], [0, 0], [15.047, 4.169], [11.473, 5.986], [0, 0], [19.952, 29.1], [0, 0], [-3.273, 15.73]], "v": [[161.033, -6.772], [132.225, -28.251], [104.656, -15.979], [56.023, -48.598], [12.86, -110.958], [-17.179, -149.308], [-4.837, -158.804], [16.778, -189.267], [73.975, -198.709], [112.051, -177.925], [124.671, -158.97], [177.728, -136.897], [184.262, -90.377], [172.629, -31.33]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.988, 13.951], [0, 0], [-26.769, -11.971], [-17.126, -8.654], [0, 0], [-12.969, -19.579], [0, 0], [-0.887, -29.264]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [6.485, -14.673], [0, 0], [15.047, 4.169], [11.473, 5.986], [0, 0], [19.952, 29.1], [0, 0], [-3.273, 15.73]], "v": [[161.033, -6.772], [132.225, -28.251], [104.656, -15.979], [56.023, -48.598], [12.86, -110.958], [-17.179, -149.308], [-4.837, -158.804], [16.778, -189.267], [73.975, -198.709], [112.051, -177.925], [124.671, -158.97], [177.728, -136.897], [184.262, -90.377], [172.629, -31.33]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.988, 13.951], [0, 0], [-19.18, -6.724], [-16.752, -13.538], [-15.281, -4.875], [-8.757, -14.299], [0, 0], [-0.887, -29.264]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [6.485, -14.673], [0, 0], [16.791, 2.961], [11.354, 9.673], [15.281, 4.875], [14.809, 23.14], [0, 0], [-3.273, 15.73]], "v": [[161.033, -6.772], [132.225, -28.251], [104.656, -15.979], [56.023, -48.598], [12.86, -110.958], [-17.179, -149.308], [-33.561, -186.904], [0.589, -198.388], [55.68, -203.276], [112.602, -176.549], [143.719, -167.875], [170.931, -133.749], [185.011, -78.145], [181.129, -27.33]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.266666680574, 0.607843160629, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "叶子1叶脉", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [101.315, -106.979, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [101.315, -106.979, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.008, -14.049], [0, 0], [-52.15, -2.837]], "o": [[0, 0], [0, 0], [1.54, -3.936]], "v": [[84.297, -106.979], [75.97, -98.946], [159.105, -111.916]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-70.542, 96.204], [0, 0], [18.598, -28.26], [0.71, 10.897], [0, 0], [0.085, -0.089], [-0.348, -18.041], [-2.964, -6.563], [-0.026, -27.423], [0, 0]], "o": [[19.717, -23.846], [0, 0], [-2.863, -4.933], [-0.959, -14.711], [0, 0], [-0.331, -0.086], [0.223, 11.551], [-13.005, 21.295], [4.325, 9.178], [0, 0]], "v": [[117.799, -168.312], [111.036, -168.391], [71.387, -121.607], [63.616, -146.775], [52.477, -179.145], [52.291, -179.299], [59.977, -144.503], [67.323, -115.197], [43.496, -40.499], [56.31, -35.668]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "叶子1颜色", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [81.697, -116.208, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [81.697, -116.208, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [{"i": [[0, 0], [2.305, 16.517], [-13.06, 2.928], [-9.603, 3.841], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [-2.305, -16.517], [13.06, -2.928], [9.603, -3.841], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-33.291, -45.583]], "v": [[-31.52, -119.2], [-39.877, -165.807], [-26.142, -198.09], [3.957, -199.464], [27.67, -201.018], [37.482, -218.641], [59.518, -221.761], [79.492, -221.377], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [{"i": [[0, 0], [0, 16.677], [-2.426, 14.022], [-9.622, 3.795], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [0, -18.117], [2.282, -13.188], [17.736, -6.995], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-25.481, -46.467]], "v": [[-18.471, -107.569], [-23.757, -144.505], [-20.134, -179.318], [-3.286, -206.69], [24.175, -205.027], [43.496, -219.105], [63.736, -220.491], [85.281, -221.765], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [{"i": [[0, 0], [0, 16.677], [-2.426, 14.022], [-9.622, 3.795], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [0, -18.117], [2.282, -13.188], [17.736, -6.995], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-25.481, -46.467]], "v": [[-18.471, -107.569], [-23.757, -144.505], [-20.134, -179.318], [-3.286, -206.69], [24.175, -205.027], [43.496, -219.105], [63.736, -220.491], [85.281, -221.765], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [2.305, 16.517], [-13.06, 2.928], [-9.603, 3.841], [-11.105, 3.073], [-7.683, 6.53], [-7.642, -2.631], [0, 0], [-18.181, -12.888], [-4.225, -19.975], [-0.768, -24.968], [20.776, -27.872], [0, 0], [13.316, -4.097], [54.58, 18.397]], "o": [[-3.969, -16.261], [-2.305, -16.517], [13.06, -2.928], [9.603, -3.841], [11.105, -3.073], [7.683, -6.53], [15.143, 5.213], [0, 0], [10.831, 7.678], [4.635, 21.913], [0.768, 24.968], [-15.076, 20.271], [0, 0], [-13.316, 4.097], [-33.291, -45.583]], "v": [[-31.52, -119.2], [-39.877, -165.807], [-26.142, -198.09], [3.957, -199.464], [27.67, -201.018], [37.482, -218.641], [59.518, -221.761], [79.492, -221.377], [115.6, -221.569], [146.906, -194.296], [180.773, -163.886], [197.642, -81.468], [184.551, -43.911], [173.859, -14.333], [11.246, -14.333]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.816, 0.91, 0.478, 0.498, 0.698, 0.833, 0.388, 0.995, 0.58, 0.757, 0.298], "ix": 9}}, "s": {"a": 0, "k": [138, -202], "ix": 5}, "e": {"a": 0, "k": [102.602, -150.832], "ix": 6}, "t": 1, "nm": "Gradient 31", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "叶子3描边", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 248, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 269, "s": [3]}, {"t": 297, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [156.464, 231.218, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [156.464, 231.218, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [133.464, 233.218, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [133.464, 233.218, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 269, "s": [156.464, 231.218, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 297, "s": [156.464, 231.218, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 2;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-122.536, -22.782, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 87, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 248, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 269, "s": [100, 100, 100]}, {"t": 297, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[3.263, 5.73], [-50.174, 19.214], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-4.225, -19.59], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [-5.587, 12.103], [4.138, 5.575]], "o": [[-9.976, -17.517], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [4.225, 19.59], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [6.69, -14.494], [-3.93, -5.295]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-87.617, -184.322], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-226.506, 40.651], [-227.688, 10.776]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[4.659, 8.205], [-50.174, 19.214], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-5.903, -7.702], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [2.554, 13.083], [4.524, 5.267]], "o": [[-11.75, -20.69], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [12.191, 15.907], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [-3.287, -16.836], [-5.653, -6.582]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-88.867, -171.806], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-228.516, 50.56], [-220.823, 10.127]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[4.659, 8.205], [-50.174, 19.214], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-5.903, -7.702], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [2.554, 13.083], [4.524, 5.267]], "o": [[-11.75, -20.69], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [12.191, 15.907], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [-3.287, -16.836], [-5.653, -6.582]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-88.867, -171.806], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-228.516, 50.56], [-220.823, 10.127]], "c": true}]}, {"t": 269, "s": [{"i": [[3.263, 5.73], [-50.174, 19.214], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-4.225, -19.59], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [-5.587, 12.103], [4.138, 5.575]], "o": [[-9.976, -17.517], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [4.225, 19.59], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [6.69, -14.494], [-3.93, -5.295]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-87.617, -184.322], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-226.506, 40.651], [-227.688, 10.776]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.447058826685, 0.600000023842, 0.180392161012, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "叶子3投影", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-113.835, -19.014, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-113.835, -19.014, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [29.337, -50.99], [0, 0], [-0.443, -10.064], [8.424, -15.399], [-3.99, -4.673], [-1.53, -3.037], [-10.267, -0.508], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-29.337, 50.99], [0, 0], [0.443, 10.064], [-8.424, 15.399], [0.592, 0.693], [7.312, 14.515], [12.415, 0.615], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-79.975, -178.266], [-174.455, -119.085], [-203.65, -39.23], [-204.393, -23.102], [-201.242, 25.52], [-191.242, 83.47], [-174.769, 81.184], [-144.394, 131.222], [-114.255, 113.129], [-59.263, 91.391], [-36.238, -52.156], [-72.975, -169.266]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [29.337, -50.99], [0, 0], [-0.443, -10.064], [8.424, -15.4], [-3.99, -4.673], [-1.53, -3.037], [-10.267, -0.508], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-29.337, 50.99], [0, 0], [0.443, 10.064], [-8.424, 15.399], [0.592, 0.693], [7.312, 14.515], [12.415, 0.615], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-81.643, -181.433], [-178.015, -120.15], [-204.924, -39.664], [-206.154, -23.26], [-201.242, 25.521], [-191.242, 83.47], [-174.769, 81.184], [-144.394, 131.222], [-114.255, 113.129], [-59.263, 91.391], [-36.238, -52.156], [-72.975, -169.266]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [42.455, -77.415], [-1.35, -13.52], [-6.643, -16.352], [8.424, -15.399], [-3.99, -4.673], [-1.53, -3.037], [-10.267, -0.508], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-40.045, 9.335], [2.65, 24.73], [6.643, 16.352], [-8.424, 15.4], [0.592, 0.693], [7.312, 14.515], [12.415, 0.615], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-72.975, -169.266], [-163.205, -120.335], [-211.4, -76.73], [-198.143, -22.352], [-184.742, 28.02], [-184.742, 76.47], [-174.769, 81.184], [-144.394, 131.222], [-114.255, 113.129], [-59.263, 91.391], [-36.238, -52.156], [-72.975, -169.266]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [42.455, -77.415], [-1.35, -13.52], [-6.643, -16.352], [8.424, -15.399], [-3.99, -4.673], [-1.53, -3.037], [-10.267, -0.508], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-40.045, 9.335], [2.65, 24.73], [6.643, 16.352], [-8.424, 15.4], [0.592, 0.693], [7.312, 14.515], [12.415, 0.615], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-72.975, -169.266], [-163.205, -120.335], [-211.4, -76.73], [-198.143, -22.352], [-184.742, 28.02], [-184.742, 76.47], [-174.769, 81.184], [-144.394, 131.222], [-114.255, 113.129], [-59.263, 91.391], [-36.238, -52.156], [-72.975, -169.266]], "c": false}]}, {"t": 269, "s": [{"i": [[0, 0], [29.337, -50.99], [0, 0], [-0.443, -10.064], [8.424, -15.4], [-3.99, -4.673], [-1.53, -3.037], [-10.267, -0.508], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-29.337, 50.99], [0, 0], [0.443, 10.064], [-8.424, 15.399], [0.592, 0.693], [7.312, 14.515], [12.415, 0.615], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-81.643, -181.433], [-178.015, -120.15], [-204.924, -39.664], [-206.154, -23.26], [-201.242, 25.521], [-191.242, 83.47], [-174.769, 81.184], [-144.394, 131.222], [-114.255, 113.129], [-59.263, 91.391], [-36.238, -52.156], [-72.975, -169.266]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.266666680574, 0.607843160629, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "叶子3叶脉", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-116.292, -47.877, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-116.292, -47.877, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-19.896, -13.449], [0, 0], [14.623, 62.352]], "o": [[0, 0], [0, 0], [-4.674, -0.626]], "v": [[-115.375, -34.322], [-104.085, -26.932], [-145.857, -121.042]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[125.357, 54.095], [0, 0], [-36.106, -13.386], [11.26, -4.132], [0, 0], [-0.123, -0.074], [-18.922, 5.864], [-5.925, 5.48], [-28.931, 8.322], [0, 0]], "o": [[-31.827, -16.04], [0, 0], [-4.239, 4.867], [-15.201, 5.578], [0, 0], [0.021, 0.417], [12.115, -3.755], [26.867, 8.897], [8.225, -7.875], [0, 0]], "v": [[-191.42, -55.283], [-189.219, -47.285], [-126.451, -14.677], [-150.387, 2.097], [-180.785, 25.018], [-180.884, 25.284], [-146.76, 5.7], [-118.314, -11.821], [-31.434, -6.31], [-30.665, -22.88]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "叶子3渐变", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-121.706, -22.782, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-121.706, -22.782, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[1.324, 6.46], [-49.287, 23.039], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-4.225, -19.59], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [-5.587, 12.103], [4.138, 5.575]], "o": [[-6.725, -32.811], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [4.225, 19.59], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [6.69, -14.494], [-3.93, -5.295]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-87.617, -184.322], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-226.506, 40.651], [-227.688, 10.776]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[4.659, 8.205], [-50.174, 19.214], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-5.903, -7.702], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [2.554, 13.083], [4.524, 5.267]], "o": [[-11.75, -20.69], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [12.191, 15.907], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [-3.287, -16.836], [-5.653, -6.582]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-88.867, -171.806], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-228.516, 50.56], [-220.823, 10.127]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[4.659, 8.205], [-50.174, 19.214], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-5.903, -7.702], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [2.554, 13.083], [4.524, 5.267]], "o": [[-11.75, -20.69], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [12.191, 15.907], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [-3.287, -16.836], [-5.653, -6.582]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-88.867, -171.806], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-228.516, 50.56], [-220.823, 10.127]], "c": true}]}, {"t": 269, "s": [{"i": [[1.324, 6.46], [-49.287, 23.039], [0, 0], [-17.542, -13.572], [0, 0], [-6.914, 5.378], [-4.225, -19.59], [-13.06, -2.305], [5.159, -57.891], [5.997, -5.261], [8.451, 0.394], [2.305, 24.887], [7.683, 9.987], [10.756, 8.397], [-5.587, 12.103], [4.138, 5.575]], "o": [[-6.725, -32.811], [1.28, -44.174], [0, 0], [9.219, -7.939], [0, 0], [0, 0], [4.225, 19.59], [13.06, 2.305], [-4.61, 51.729], [-5.997, 5.261], [-8.451, -0.394], [-2.305, -24.887], [-7.683, -9.987], [-10.756, -8.397], [6.69, -14.494], [-3.93, -5.295]], "v": [[-235.067, -10.047], [-200.633, -113.184], [-162.093, -153.901], [-129.954, -164.912], [-111.004, -170.29], [-87.617, -184.322], [-72.975, -169.266], [-54.921, -144.682], [-3.705, 13.322], [-138.81, 126.628], [-147.88, 139.197], [-172.464, 106.071], [-174.769, 81.184], [-197.048, 85.74], [-226.506, 40.651], [-227.688, 10.776]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.816, 0.91, 0.478, 0.498, 0.698, 0.833, 0.388, 0.995, 0.58, 0.757, 0.298], "ix": 9}}, "s": {"a": 0, "k": [-238, -48], "ix": 5}, "e": {"a": 0, "k": [-146.232, -29.052], "ix": 6}, "t": 1, "nm": "Gradient 3", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "叶子4描边", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 248, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 269, "s": [6]}, {"t": 297, "s": [6]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [161.708, 354.618, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [161.708, 354.618, 0], "to": [-3.667, 4.667, 0], "ti": [3.667, -4.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [139.708, 382.618, 0], "to": [-3.667, 4.667, 0], "ti": [-3.667, 4.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [139.708, 382.618, 0], "to": [3.667, -4.667, 0], "ti": [-3.667, 4.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 269, "s": [161.708, 354.618, 0], "to": [3.667, -4.667, 0], "ti": [0, 0, 0]}, {"t": 297, "s": [161.708, 354.618, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 2;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-116.292, 126.618, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 27, "s": [93, 93, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 56, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 87, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 248, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 269, "s": [100, 100, 100]}, {"t": 297, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [27.324, 7.272], [13.966, 0.206], [2.66, -9.311], [-7.538, -28.038], [-3.547, -22.169], [-35.914, -20.396], [-19.066, 15.075], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.324, -7.272], [-27.933, -0.413], [-2.66, 9.311], [7.538, 28.038], [3.547, 22.169], [35.914, 20.396], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-109.421, 37.334], [-181.527, 22.533], [-212.564, 55.817], [-223.205, 94.287], [-202.81, 147.155], [-172.659, 195.927], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [27.933, 0.413], [2.66, -9.311], [1.663, -19.62], [5.764, -29.789], [-21.367, -2.993], [-6.48, -27.49], [-14.299, 11.306], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.933, -0.413], [-2.66, 9.311], [-2.452, 28.929], [-5.301, 27.397], [13.192, 2.088], [5.053, 24.608], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-181.527, 22.533], [-212.564, 55.817], [-228.305, 92.93], [-216.666, 135.828], [-186.985, 185.709], [-152.375, 208.987], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [27.933, 0.413], [2.66, -9.311], [1.663, -19.62], [5.764, -29.789], [-21.367, -2.993], [-6.48, -27.49], [-14.299, 11.306], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.933, -0.413], [-2.66, 9.311], [-2.452, 28.929], [-5.301, 27.397], [13.192, 2.088], [5.053, 24.608], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-181.527, 22.533], [-212.564, 55.817], [-228.305, 92.93], [-216.666, 135.828], [-186.985, 185.709], [-152.375, 208.987], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}, {"t": 269, "s": [{"i": [[0, 0], [27.324, 7.272], [13.966, 0.206], [2.66, -9.311], [-7.538, -28.038], [-3.547, -22.169], [-35.914, -20.396], [-19.066, 15.075], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.324, -7.272], [-27.933, -0.413], [-2.66, 9.311], [7.538, 28.038], [3.547, 22.169], [35.914, 20.396], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-109.421, 37.334], [-181.527, 22.533], [-212.564, 55.817], [-223.205, 94.287], [-202.81, 147.155], [-172.659, 195.927], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.270588248968, 0.607843160629, 0.270588248968, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "叶子4投影", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-138.004, 130.131, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-138.004, 130.131, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [-18.822, 1.239], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-8.274, -5.129], [0.222, -2.996], [-20.015, -12.773], [0, 0], [0, 0]], "o": [[0, 0], [11.056, -0.665], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [5.137, 5.276], [-0.222, 2.996], [18.087, 10.12], [0, 0], [0, 0]], "v": [[-117.34, 195.755], [-89.511, 207.881], [-47.271, 224.557], [-60.372, 150.792], [-84.243, 70.298], [-139.408, 49.133], [-192.39, 45.824], [-223.814, 65.123], [-220.324, 81.223], [-210.875, 97.442], [-198.702, 113.415], [-175.146, 170.688], [-142.355, 179.238], [-132.106, 183.63]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[-11.447, 2.217], [-29.283, 0.155], [-5.764, -10.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.094, 1.774], [0.222, -2.996], [-22.169, -0.887], [0, 0], [0, 0]], "o": [[0, 0], [11.009, 1.217], [5.764, 10.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [7.094, -1.774], [-0.222, 2.996], [-6.088, 14.725], [0, 0], [0, 0]], "v": [[-126.316, 206.383], [-92.467, 229.095], [-53.721, 213.42], [-60.372, 150.792], [-84.243, 70.298], [-139.408, 49.133], [-192.39, 45.824], [-223.814, 65.123], [-220.324, 81.223], [-195.383, 106.121], [-189.398, 108.667], [-159.912, 157.775], [-155.771, 185.794], [-144.93, 198.758]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[-11.447, 2.217], [-29.283, 0.155], [-5.764, -10.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.094, 1.774], [0.222, -2.996], [-22.169, -0.887], [0, 0], [0, 0]], "o": [[0, 0], [11.009, 1.217], [5.764, 10.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [7.094, -1.774], [-0.222, 2.996], [-6.088, 14.725], [0, 0], [0, 0]], "v": [[-126.316, 206.383], [-92.467, 229.095], [-53.721, 213.42], [-60.372, 150.792], [-84.243, 70.298], [-139.408, 49.133], [-192.39, 45.824], [-223.814, 65.123], [-220.324, 81.223], [-195.383, 106.121], [-189.398, 108.667], [-159.912, 157.775], [-155.771, 185.794], [-144.93, 198.758]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [-18.822, 1.239], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-8.274, -5.129], [0.222, -2.996], [-20.015, -12.773], [0, 0], [0, 0]], "o": [[0, 0], [11.056, -0.665], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [5.137, 5.276], [-0.222, 2.996], [18.087, 10.12], [0, 0], [0, 0]], "v": [[-117.34, 195.755], [-89.511, 207.881], [-47.271, 224.557], [-60.372, 150.792], [-84.243, 70.298], [-139.408, 49.133], [-192.39, 45.824], [-223.814, 65.123], [-220.324, 81.223], [-210.875, 97.442], [-198.702, 113.415], [-175.146, 170.688], [-142.355, 179.238], [-132.106, 183.63]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.266666680574, 0.607843160629, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "叶子4叶脉", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-107.226, 116.784, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-107.226, 116.784, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.13, -3.724], [-22.731, 15.682], [-5.83, -9.645], [0, 0], [0, 0], [-11.321, 1.644]], "o": [[0, 0], [0, 0], [-0.073, 2.083], [-1.591, 10.218], [-0.414, -31.824], [0, 0], [16.226, -10.844], [28.754, -4.174]], "v": [[-77.245, 75.373], [-115.021, 101.016], [-158.854, 144.307], [-116.128, 114.202], [-114.835, 158.195], [-109.972, 110.346], [-110.363, 110.285], [-65.997, 88.169]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "叶子4颜色", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.088, "ix": 10}, "p": {"a": 0, "k": [-116.292, 126.618, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-116.292, 126.618, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [99.868, 99.868, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [27.324, 7.272], [13.966, 0.206], [2.66, -9.311], [-7.538, -28.038], [-3.547, -22.169], [-35.914, -20.396], [-19.066, 15.075], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.324, -7.272], [-27.933, -0.413], [-2.66, 9.311], [7.538, 28.038], [3.547, 22.169], [35.914, 20.396], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-109.421, 37.334], [-181.527, 22.533], [-212.564, 55.817], [-223.205, 94.287], [-202.81, 147.155], [-172.659, 195.927], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [27.933, 0.413], [2.66, -9.311], [1.663, -19.62], [5.764, -29.789], [-21.367, -2.993], [-6.48, -27.49], [-14.299, 11.306], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.933, -0.413], [-2.66, 9.311], [-2.452, 28.929], [-5.301, 27.397], [13.192, 2.088], [5.053, 24.608], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-181.527, 22.533], [-212.564, 55.817], [-228.305, 92.93], [-216.666, 135.828], [-186.985, 185.709], [-152.375, 208.987], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [27.933, 0.413], [2.66, -9.311], [1.663, -19.62], [5.764, -29.789], [-21.367, -2.993], [-6.48, -27.49], [-14.299, 11.306], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.933, -0.413], [-2.66, 9.311], [-2.452, 28.929], [-5.301, 27.397], [13.192, 2.088], [5.053, 24.608], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-181.527, 22.533], [-212.564, 55.817], [-228.305, 92.93], [-216.666, 135.828], [-186.985, 185.709], [-152.375, 208.987], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}, {"t": 269, "s": [{"i": [[0, 0], [27.324, 7.272], [13.966, 0.206], [2.66, -9.311], [-7.538, -28.038], [-3.547, -22.169], [-35.914, -20.396], [-19.066, 15.075], [-28.82, -7.538], [-8.424, 20.396], [0, 0]], "o": [[0, 0], [-27.324, -7.272], [-27.933, -0.413], [-2.66, 9.311], [7.538, 28.038], [3.547, 22.169], [35.914, 20.396], [19.066, -15.075], [28.82, 7.538], [8.424, -20.396], [0, 0]], "v": [[-58.265, 51.826], [-109.421, 37.334], [-181.527, 22.533], [-212.564, 55.817], [-223.205, 94.287], [-202.81, 147.155], [-172.659, 195.927], [-87.085, 226.078], [-56.935, 218.54], [-8.162, 209.229], [-58.265, 51.826]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.037, 0.745, 0.871, 0.522, 0.487, 0.476, 0.69, 0.375, 0.937, 0.208, 0.51, 0.227], "ix": 9}}, "s": {"a": 0, "k": [-165, 189], "ix": 5}, "e": {"a": 0, "k": [-111.494, 126.72], "ix": 6}, "t": 1, "nm": "Gradient 4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "叶子5描边", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 248, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 269, "s": [7]}, {"t": 297, "s": [7]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [226.843, 137.608, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [226.843, 137.608, 0], "to": [-3.5, -1.667, 0], "ti": [3.5, 1.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [205.843, 127.608, 0], "to": [-3.5, -1.667, 0], "ti": [-3.5, -1.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [205.843, 127.608, 0], "to": [3.5, 1.667, 0], "ti": [-3.5, -1.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 269, "s": [226.843, 137.608, 0], "to": [3.5, 1.667, 0], "ti": [0, 0, 0]}, {"t": 297, "s": [226.843, 137.608, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 2;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-50.157, -128.392, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [93, 93, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 54, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 87, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 248, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 269, "s": [100, 100, 100]}, {"t": 297, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-9.533, 5.764], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [9.533, -5.764], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-151.932, -159.469], [-146.833, -181.195], [-122.89, -204.916], [-85.424, -230.633], [-54.608, -233.293], [-3.397, -235.732], [13.895, -226.864], [43.602, -202.256], [59.342, -132.566], [16.777, -21.621]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-10.018, 4.872], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [16.184, -7.87], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-155.478, -157.474], [-151.71, -182.968], [-120.066, -203.1], [-91.187, -233.071], [-51.965, -231.298], [-3.397, -235.732], [13.895, -226.864], [45.792, -203.1], [59.342, -132.566], [16.777, -21.621]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-10.018, 4.872], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [16.184, -7.87], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-155.478, -157.474], [-151.71, -182.968], [-120.066, -203.1], [-91.187, -233.071], [-51.965, -231.298], [-3.397, -235.732], [13.895, -226.864], [45.792, -203.1], [59.342, -132.566], [16.777, -21.621]], "c": true}]}, {"t": 269, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-9.533, 5.764], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [9.533, -5.764], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-151.932, -159.469], [-146.833, -181.195], [-122.89, -204.916], [-85.424, -230.633], [-54.608, -233.293], [-3.397, -235.732], [13.895, -226.864], [43.602, -202.256], [59.342, -132.566], [16.777, -21.621]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.270588248968, 0.607843160629, 0.270588248968, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "叶子5投影", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-42.525, -176.968, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-42.525, -176.968, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-18.844, 7.316], [-12.858, 1.33], [-16.215, 0.222], [-4.139, 1.939], [0, 0], [0.833, -0.343], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [18.844, -7.316], [12.858, -1.33], [16.214, -0.222], [4.139, -1.939], [0, 0], [-0.833, 0.343], [0, 0], [0, 0], [0, 0]], "v": [[-128.875, -170.554], [-98.783, -198.77], [-65.165, -203.794], [-36.913, -214.124], [22.706, -213.562], [43.825, -193.831], [30.247, -174.001], [-50.674, -140.252], [-95.178, -141.733], [-113.357, -148.384]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.266666680574, 0.607843160629, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "叶子5叶脉", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-37.864, -123.649, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-37.864, -123.649, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.85, -24.168], [0, 0], [7.301, 11.306]], "o": [[0, 0], [-0.85, 24.168], [0, 0], [-4.336, -5.321]], "v": [[-65.304, -198.044], [-31.82, -53.604], [-9.16, -59.477], [-64.362, -201.369]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "叶子5颜色", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-50.157, -128.392, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-50.157, -128.392, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-9.533, 5.764], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [9.533, -5.764], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-151.932, -159.469], [-146.833, -181.195], [-122.89, -204.916], [-85.424, -230.633], [-54.608, -233.293], [-3.397, -235.732], [13.895, -226.864], [43.602, -202.256], [59.342, -132.566], [16.777, -21.621]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-10.018, 4.872], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [16.184, -7.87], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-155.478, -157.474], [-151.71, -182.968], [-120.066, -203.1], [-91.187, -233.071], [-51.965, -231.298], [-3.397, -235.732], [13.895, -226.864], [45.792, -203.1], [59.342, -132.566], [16.777, -21.621]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-10.018, 4.872], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [16.184, -7.87], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-155.478, -157.474], [-151.71, -182.968], [-120.066, -203.1], [-91.187, -233.071], [-51.965, -231.298], [-3.397, -235.732], [13.895, -226.864], [45.792, -203.1], [59.342, -132.566], [16.777, -21.621]], "c": true}]}, {"t": 269, "s": [{"i": [[61.853, 20.396], [0.443, 36.358], [-5.321, 5.321], [-1.774, 3.547], [0, 0], [-7.981, 13.302], [-9.311, 8.868], [-9.533, 5.764], [-12.193, -1.774], [-12.415, -3.104], [-4.212, -5.099], [-9.755, -7.981], [16.627, -44.261], [28.599, -4.271]], "o": [[0, 0], [-0.443, -36.358], [5.321, -5.321], [-6.873, -1.33], [0, 0], [7.981, -13.302], [9.311, -8.868], [9.533, -5.764], [12.193, 1.774], [12.415, 3.104], [4.212, 5.099], [9.755, 7.981], [-16.627, 44.261], [-28.599, 4.271]], "v": [[-143.508, -43.301], [-165.455, -88.97], [-160.135, -130.427], [-141.956, -155.922], [-151.932, -159.469], [-146.833, -181.195], [-122.89, -204.916], [-85.424, -230.633], [-54.608, -233.293], [-3.397, -235.732], [13.895, -226.864], [43.602, -202.256], [59.342, -132.566], [16.777, -21.621]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.745, 0.871, 0.522, 0.495, 0.476, 0.69, 0.375, 0.99, 0.208, 0.51, 0.227], "ix": 9}}, "s": {"a": 0, "k": [-101, -223], "ix": 5}, "e": {"a": 0, "k": [-63.387, -148.869], "ix": 6}, "t": 1, "nm": "Gradient 5", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "叶子6描边", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33, "s": [9]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 87, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 248, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 269, "s": [0]}, {"t": 297, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [365.632, 256.044, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [365.632, 256.044, 0], "to": [3.667, 0, 0], "ti": [-3.667, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [387.632, 256.044, 0], "to": [3.667, 0, 0], "ti": [3.667, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [387.632, 256.044, 0], "to": [-3.667, 0, 0], "ti": [3.667, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 269, "s": [365.632, 256.044, 0], "to": [-3.667, 0, 0], "ti": [0, 0, 0]}, {"t": 297, "s": [365.632, 256.044, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 3;\n    frequency = 2;\n    decay = 2;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [131.632, 0.044, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 33, "s": [95, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 87, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 248, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 269, "s": [100, 100, 100]}, {"t": 297, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-3.665, 2.231], [-4.687, -2.012], [-14.912, -45.11], [0, 0], [9.527, -8.636], [15.21, -7.329], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [3.665, -2.231], [4.687, 2.012], [14.912, 45.11], [0, 0], [-9.527, 8.636], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [193.409, -96.874], [231.571, -52.055], [244.903, 8.5], [230.785, 37.249], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-4.198, 0.886], [-3.773, 0.283], [5.986, -35.028], [-1.329, -22.91], [9.556, -12.283], [38.539, -19.841], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [5.632, -1.189], [6.648, -0.499], [-6.54, 27.795], [1.663, 11.281], [-9.089, 12.641], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [194.355, -92.961], [225.392, -55.286], [242.906, -2.499], [229.382, 33.183], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-4.198, 0.886], [-3.773, 0.283], [5.986, -35.028], [-1.329, -22.91], [9.556, -12.283], [38.539, -19.841], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [5.632, -1.189], [6.648, -0.499], [-6.54, 27.795], [1.663, 11.281], [-9.089, 12.641], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [194.355, -92.961], [225.392, -55.286], [242.906, -2.499], [229.382, 33.183], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-3.665, 2.231], [-4.687, -2.012], [-14.912, -45.11], [0, 0], [9.527, -8.636], [15.21, -7.329], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [3.665, -2.231], [4.687, 2.012], [14.912, 45.11], [0, 0], [-9.527, 8.636], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [193.409, -96.874], [231.571, -52.055], [244.903, 8.5], [230.785, 37.249], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.270588248968, 0.607843160629, 0.270588248968, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "叶子6阴影", "parent": 21, "sr": 1, "ks": {"o": {"a": 0, "k": 63, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [135.303, 3.545, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [135.303, 3.545, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [0, -29.492], [8.424, -14.188], [-6.335, -20.277], [0.998, -20.468], [0, 0], [0, 0], [0, 0], [-0.998, -0.665]], "o": [[-0.069, 1.33], [0, 29.492], [-8.424, 14.188], [6.335, 20.277], [4.695, 21.03], [0, 0], [0, 0], [0, 0], [0.998, 0.665]], "v": [[205.508, -85.645], [217.793, -66.377], [204.942, -12.856], [216.09, 27.495], [213.055, 76.47], [154.598, 135.56], [56.023, 19.162], [104.685, -65.138], [199.675, -93.958]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [0, -29.492], [3.677, -19.598], [10.302, -8.038], [0.998, -20.468], [0, 0], [0, 0], [0, 0], [-0.998, -0.665]], "o": [[-0.069, 1.33], [13.856, 5.13], [-3.159, 16.839], [6.335, 20.277], [-0.998, 20.468], [0, 0], [0, 0], [0, 0], [0.998, 0.665]], "v": [[205.508, -85.645], [198.124, -51.061], [228.274, 1.924], [206.988, 34.403], [209.805, 76.47], [200.098, 101.06], [56.023, 19.162], [104.685, -65.138], [199.675, -93.958]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [0, -29.492], [3.677, -19.598], [10.302, -8.038], [0.998, -20.468], [0, 0], [0, 0], [0, 0], [-0.998, -0.665]], "o": [[-0.069, 1.33], [13.856, 5.13], [-3.159, 16.839], [6.335, 20.277], [-0.998, 20.468], [0, 0], [0, 0], [0, 0], [0.998, 0.665]], "v": [[205.508, -85.645], [198.124, -51.061], [228.274, 1.924], [206.988, 34.403], [209.805, 76.47], [200.098, 101.06], [56.023, 19.162], [104.685, -65.138], [199.675, -93.958]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [0, -29.492], [8.424, -14.188], [-6.335, -20.277], [0.998, -20.468], [0, 0], [0, 0], [0, 0], [-0.998, -0.665]], "o": [[-0.069, 1.33], [0, 29.492], [-8.424, 14.188], [6.335, 20.277], [4.695, 21.03], [0, 0], [0, 0], [0, 0], [0.998, 0.665]], "v": [[205.508, -85.645], [217.793, -66.377], [204.942, -12.856], [216.09, 27.495], [213.055, 76.47], [154.598, 135.56], [56.023, 19.162], [104.685, -65.138], [199.675, -93.958]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.266666680574, 0.607843160629, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "叶子6叶脉", "parent": 21, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [141.169, 14.162, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [141.169, 14.162, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.424, -3.631], [0, 0], [0, 0], [-20.842, 1.562], [0, 0], [-2.039, -0.054], [6.274, 5.807]], "o": [[-22.613, 5.323], [0, 0], [30.509, 0.835], [0, 0], [11.417, 1.953], [-2.044, -6.319], [48.29, -4.526]], "v": [[209.984, -5.168], [66.847, 0.41], [72.312, 6.274], [149.369, 4.709], [149.36, 4.712], [187.926, 34.403], [160.041, 3.811]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.913725495338, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "叶子6颜色", "parent": 22, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [131.632, 0.044, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [131.632, 0.044, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-3.665, 2.231], [-4.687, -2.012], [-14.912, -45.11], [0, 0], [9.527, -8.636], [15.21, -7.329], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [3.665, -2.231], [4.687, 2.012], [14.912, 45.11], [0, 0], [-9.527, 8.636], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [193.409, -96.874], [231.571, -52.055], [244.903, 8.5], [230.785, 37.249], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 87, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-4.198, 0.886], [-3.773, 0.283], [5.986, -35.028], [-1.329, -22.91], [9.556, -12.283], [38.539, -19.841], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [5.632, -1.189], [6.648, -0.499], [-6.54, 27.795], [1.663, 11.281], [-9.089, 12.641], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [194.355, -92.961], [225.392, -55.286], [242.906, -2.499], [229.382, 33.183], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-4.198, 0.886], [-3.773, 0.283], [5.986, -35.028], [-1.329, -22.91], [9.556, -12.283], [38.539, -19.841], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [5.632, -1.189], [6.648, -0.499], [-6.54, 27.795], [1.663, 11.281], [-9.089, 12.641], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [194.355, -92.961], [225.392, -55.286], [242.906, -2.499], [229.382, 33.183], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}, {"t": 269, "s": [{"i": [[0, 0], [-15.904, -8.171], [-9.32, -18.357], [-3.665, 2.231], [-4.687, -2.012], [-14.912, -45.11], [0, 0], [9.527, -8.636], [15.21, -7.329], [5.436, -1.379], [13.884, -7.426], [6.439, -1.416], [6.649, 7.831], [0, 0], [36.963, 59.624]], "o": [[0, 0], [9.128, 2.838], [9.32, 18.357], [3.665, -2.231], [4.687, 2.012], [14.912, 45.11], [0, 0], [-9.527, 8.636], [-15.21, 7.329], [-5.436, 1.379], [-13.884, 7.426], [-3.245, 3.318], [-6.649, -7.831], [0, 0], [-36.963, -59.624]], "v": [[70.436, -85.686], [145.015, -130.274], [170.191, -114.419], [184.262, -90.377], [193.409, -96.874], [231.571, -52.055], [244.903, 8.5], [230.785, 37.249], [211.017, 94.481], [195.645, 104.021], [164.542, 127.965], [137.153, 124.062], [123.972, 127.99], [119.14, 119.625], [28.137, 36.133]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.139, 0.745, 0.871, 0.522, 0.569, 0.476, 0.69, 0.375, 1, 0.208, 0.51, 0.227], "ix": 9}}, "s": {"a": 0, "k": [267, 8], "ix": 5}, "e": {"a": 0, "k": [148.384, 0.027], "ix": 6}, "t": 1, "nm": "Gradient 6", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "超级新新狗头", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [244, 260, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "d大叶子", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [9]}, {"t": 276, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [256, 256, 0], "to": [-1.5, 0, 0], "ti": [-1.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [247, 256, 0], "to": [1.167, 0, 0], "ti": [-1.5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [263, 256, 0], "to": [1.5, 0, 0], "ti": [1.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [256, 256, 0], "to": [1.167, 0, 0], "ti": [1.5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [263, 256, 0], "to": [-1.5, 0, 0], "ti": [1.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [247, 256, 0], "to": [-1.167, 0, 0], "ti": [-1.5, 0, 0]}, {"t": 276, "s": [256, 256, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 360, "st": 0, "bm": 0}], "markers": []}