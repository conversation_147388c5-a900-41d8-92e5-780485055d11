{"v": "5.7.13", "fr": 60, "ip": 40, "op": 270, "w": 512, "h": 512, "nm": "花朵脸 外", "ddd": 0, "assets": [{"id": "comp_0", "nm": "花朵脸 内", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [300, 300, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-10.25, -8.625]], "o": [[0, 0], [8.574, 7.215]], "v": [[50.75, -130.75], [76.875, -114.375]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 420, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [300, 300, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 308, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [300, 300, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [], "ip": 0, "op": 304, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "星星高光", "parent": 8, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 254, "s": [100]}, {"t": 266, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.001, 0.001, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.302, 0.203], [0, 0], [1.182, 0.932], [0, 0], [0.191, 2.32], [0, 0], [0.939, -1.177], [0, 0], [2.303, -0.189], [0, 0], [-1.177, -0.939], [0, 0], [-0.201, -2.285], [0, 0], [-0.932, 1.182], [0, 0]], "o": [[0, 0], [1.5, -0.132], [0, 0], [-1.828, -1.442], [0, 0], [-0.123, -1.5], [0, 0], [-1.442, 1.806], [0, 0], [-1.5, 0.123], [0, 0], [1.793, 1.431], [0, 0], [0.132, 1.5], [0, 0], [1.431, -1.815]], "v": [[8.392, 8.497], [19.798, 7.493], [20.682, 4.531], [11.73, -2.528], [8.565, -8.427], [7.632, -19.788], [4.675, -20.689], [-2.467, -11.741], [-8.335, -8.614], [-19.788, -7.674], [-20.689, -4.717], [-11.669, 2.483], [-8.547, 8.301], [-7.534, 19.798], [-4.572, 20.682], [2.543, 11.658]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.143, 1, 1, 1, 0.494, 1, 1, 1, 0.793, 1, 1, 1, 0.143, 1, 0.494, 0.5, 0.793, 0], "ix": 9}}, "s": {"a": 0, "k": [-30.133, -9.342], "ix": 5}, "e": {"a": 0, "k": [31.952, 9.969], "ix": 6}, "t": 1, "nm": "jianbian6", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 304, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "星星", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 254, "s": [100]}, {"t": 266, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [179.644, 202.233, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 140, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 141, "s": [10, 10, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 142, "s": [20, 20, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 143, "s": [30, 30, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 144, "s": [40, 40, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 145, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 146, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 147, "s": [70, 70, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 148, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 149, "s": [90, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 150, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 151, "s": [114.71, 114.71, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 152, "s": [127.747, 127.747, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 153, "s": [137.874, 137.874, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 154, "s": [144.154, 144.154, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 155, "s": [146.041, 146.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 156, "s": [143.424, 143.424, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 157, "s": [136.632, 136.632, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 158, "s": [126.394, 126.394, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 159, "s": [113.761, 113.761, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 160, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 161, "s": [86.466, 86.466, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 162, "s": [74.471, 74.471, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 163, "s": [65.154, 65.154, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 164, "s": [59.376, 59.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 165, "s": [57.64, 57.64, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 166, "s": [60.048, 60.048, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [66.297, 66.297, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 168, "s": [75.716, 75.716, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 169, "s": [87.339, 87.339, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 170, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 171, "s": [112.452, 112.452, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 172, "s": [123.488, 123.488, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 173, "s": [132.06, 132.06, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 174, "s": [137.376, 137.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 175, "s": [138.973, 138.973, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 176, "s": [136.758, 136.758, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 177, "s": [131.009, 131.009, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 178, "s": [122.342, 122.342, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 179, "s": [111.648, 111.648, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 180, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 181, "s": [88.544, 88.544, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 182, "s": [78.39, 78.39, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 183, "s": [70.504, 70.504, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 184, "s": [65.613, 65.613, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 185, "s": [64.143, 64.143, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 186, "s": [66.181, 66.181, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 187, "s": [71.471, 71.471, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 188, "s": [79.444, 79.444, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 189, "s": [89.283, 89.283, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 190, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 191, "s": [110.54, 110.54, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 192, "s": [119.882, 119.882, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 193, "s": [127.138, 127.138, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 194, "s": [131.638, 131.638, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 195, "s": [132.99, 132.99, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 196, "s": [131.115, 131.115, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 197, "s": [126.248, 126.248, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 198, "s": [118.912, 118.912, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 199, "s": [109.86, 109.86, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 201, "s": [90.303, 90.303, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 202, "s": [81.708, 81.708, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 203, "s": [75.032, 75.032, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 204, "s": [70.892, 70.892, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 205, "s": [69.648, 69.648, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 206, "s": [71.373, 71.373, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [75.85, 75.85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 208, "s": [82.6, 82.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 209, "s": [90.928, 90.928, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 210, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 211, "s": [108.922, 108.922, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 212, "s": [116.83, 116.83, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 213, "s": [122.972, 122.972, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 214, "s": [126.781, 126.781, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [127.925, 127.925, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 216, "s": [126.338, 126.338, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 217, "s": [122.219, 122.219, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 218, "s": [116.009, 116.009, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 219, "s": [108.346, 108.346, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 220, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 221, "s": [91.791, 91.791, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 222, "s": [84.516, 84.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 223, "s": [78.865, 78.865, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 224, "s": [75.36, 75.36, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 225, "s": [74.307, 74.307, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 226, "s": [75.768, 75.768, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 227, "s": [79.558, 79.558, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 228, "s": [85.271, 85.271, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 229, "s": [92.321, 92.321, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 230, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 231, "s": [107.552, 107.552, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 232, "s": [114.246, 114.246, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 233, "s": [119.445, 119.445, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 234, "s": [122.669, 122.669, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 235, "s": [123.638, 123.638, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 236, "s": [122.295, 122.295, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 237, "s": [118.808, 118.808, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 238, "s": [113.551, 113.551, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 239, "s": [107.065, 107.065, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 240, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 241, "s": [93.052, 93.052, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 242, "s": [86.893, 86.893, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 243, "s": [82.11, 82.11, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 244, "s": [79.143, 79.143, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 245, "s": [78.252, 78.252, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 246, "s": [79.488, 79.488, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 247, "s": [82.696, 82.696, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 248, "s": [87.532, 87.532, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 249, "s": [93.5, 93.5, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 250, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 251, "s": [106.393, 106.393, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 252, "s": [112.059, 112.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 253, "s": [116.46, 116.46, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 254, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255, "s": [98.032, 98.032, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 256, "s": [92.593, 92.593, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 257, "s": [84.375, 84.375, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 258, "s": [74.074, 74.074, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 259, "s": [62.384, 62.384, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 260, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 261, "s": [37.616, 37.616, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 262, "s": [25.926, 25.926, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 263, "s": [15.625, 15.625, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 264, "s": [7.407, 7.407, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 265, "s": [1.968, 1.968, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 266, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 267, "s": [-0.6, -0.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 268, "s": [-1.131, -1.131, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 269, "s": [-1.544, -1.544, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 270, "s": [-1.8, -1.8, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 271, "s": [-1.877, -1.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 272, "s": [-1.77, -1.77, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 273, "s": [-1.493, -1.493, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 274, "s": [-1.076, -1.076, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 275, "s": [-0.561, -0.561, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 276, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 277, "s": [0.552, 0.552, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 278, "s": [1.041, 1.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 279, "s": [1.42, 1.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 280, "s": [1.656, 1.656, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 281, "s": [1.727, 1.727, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 282, "s": [1.628, 1.628, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 283, "s": [1.374, 1.374, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 284, "s": [0.99, 0.99, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 285, "s": [0.516, 0.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 286, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 287, "s": [-0.508, -0.508, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 288, "s": [-0.957, -0.957, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 289, "s": [-1.307, -1.307, 100]}, {"t": 290, "s": [-1.523, -1.523, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "缩放 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.519, 0.31], [0, 0], [1.807, 1.425], [0, 0], [0.291, 3.547], [0, 0], [1.436, -1.799], [0, 0], [3.521, -0.289], [0, 0], [-1.799, -1.436], [0, 0], [-0.308, -3.493], [0, 0], [-1.425, 1.807], [0, 0]], "o": [[0, 0], [2.292, -0.202], [0, 0], [-2.795, -2.204], [0, 0], [-0.188, -2.294], [0, 0], [-2.204, 2.761], [0, 0], [-2.294, 0.188], [0, 0], [2.74, 2.187], [0, 0], [0.202, 2.292], [0, 0], [2.187, -2.774]], "v": [[12.829, 12.989], [30.265, 11.454], [31.615, 6.926], [17.932, -3.865], [13.093, -12.882], [11.667, -30.249], [7.147, -31.627], [-3.772, -17.947], [-12.742, -13.168], [-30.25, -11.73], [-31.627, -7.21], [-17.838, 3.796], [-13.065, 12.69], [-11.517, 30.264], [-6.989, 31.615], [3.887, 17.822]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.972549021244, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 304, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "星星高光", "parent": 10, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 242, "s": [100]}, {"t": 254, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.009, -0.009, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.028, 0.355], [0, 0], [2.069, 1.631], [0, 0], [0.333, 4.06], [0, 0], [1.643, -2.059], [0, 0], [4.03, -0.331], [0, 0], [-2.059, -1.643], [0, 0], [-0.352, -3.998], [0, 0], [-1.631, 2.069], [0, 0]], "o": [[0, 0], [2.624, -0.231], [0, 0], [-3.199, -2.523], [0, 0], [-0.216, -2.625], [0, 0], [-2.523, 3.16], [0, 0], [-2.625, 0.216], [0, 0], [3.137, 2.504], [0, 0], [0.231, 2.624], [0, 0], [2.504, -3.175]], "v": [[14.685, 14.869], [34.643, 13.111], [36.19, 7.928], [20.526, -4.424], [14.987, -14.746], [13.355, -34.626], [8.181, -36.203], [-4.318, -20.544], [-14.586, -15.073], [-34.626, -13.428], [-36.203, -8.254], [-20.419, 4.345], [-14.956, 14.526], [-13.184, 34.643], [-8.001, 36.19], [4.45, 20.4]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.143, 1, 1, 1, 0.494, 1, 1, 1, 0.793, 1, 1, 1, 0.143, 1, 0.494, 0.5, 0.793, 0], "ix": 9}}, "s": {"a": 0, "k": [-52.934, -16.895], "ix": 5}, "e": {"a": 0, "k": [55.704, 16.895], "ix": 6}, "t": 1, "nm": "jianbian7", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 304, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "星星", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 242, "s": [100]}, {"t": 254, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [456.093, 173.796, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 147, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 148, "s": [10, 10, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 149, "s": [20, 20, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 150, "s": [30, 30, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 151, "s": [40, 40, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 152, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 153, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 154, "s": [70, 70, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 155, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 156, "s": [90, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 157, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 158, "s": [114.71, 114.71, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 159, "s": [127.747, 127.747, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 160, "s": [137.874, 137.874, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 161, "s": [144.154, 144.154, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 162, "s": [146.041, 146.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 163, "s": [143.424, 143.424, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 164, "s": [136.632, 136.632, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 165, "s": [126.394, 126.394, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 166, "s": [113.761, 113.761, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 168, "s": [86.466, 86.466, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 169, "s": [74.471, 74.471, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 170, "s": [65.154, 65.154, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 171, "s": [59.376, 59.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 172, "s": [57.64, 57.64, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 173, "s": [60.048, 60.048, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 174, "s": [66.297, 66.297, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 175, "s": [75.716, 75.716, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 176, "s": [87.339, 87.339, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 177, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 178, "s": [112.452, 112.452, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 179, "s": [123.488, 123.488, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 180, "s": [132.06, 132.06, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 181, "s": [137.376, 137.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 182, "s": [138.973, 138.973, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 183, "s": [136.758, 136.758, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 184, "s": [131.009, 131.009, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 185, "s": [122.342, 122.342, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 186, "s": [111.648, 111.648, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 187, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 188, "s": [88.544, 88.544, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 189, "s": [78.39, 78.39, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 190, "s": [70.504, 70.504, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 191, "s": [65.613, 65.613, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 192, "s": [64.143, 64.143, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 193, "s": [66.181, 66.181, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 194, "s": [71.471, 71.471, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 195, "s": [79.444, 79.444, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 196, "s": [89.283, 89.283, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 197, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 198, "s": [110.54, 110.54, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 199, "s": [119.882, 119.882, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [127.138, 127.138, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 201, "s": [131.638, 131.638, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 202, "s": [132.99, 132.99, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 203, "s": [131.115, 131.115, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 204, "s": [126.248, 126.248, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 205, "s": [118.912, 118.912, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 206, "s": [109.86, 109.86, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 208, "s": [90.303, 90.303, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 209, "s": [81.708, 81.708, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 210, "s": [75.032, 75.032, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 211, "s": [70.892, 70.892, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 212, "s": [69.648, 69.648, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 213, "s": [71.373, 71.373, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 214, "s": [75.85, 75.85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [82.6, 82.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 216, "s": [90.928, 90.928, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 217, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 218, "s": [108.922, 108.922, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 219, "s": [116.83, 116.83, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 220, "s": [122.972, 122.972, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 221, "s": [126.781, 126.781, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 222, "s": [127.925, 127.925, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 223, "s": [126.338, 126.338, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 224, "s": [122.219, 122.219, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 225, "s": [116.009, 116.009, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 226, "s": [108.346, 108.346, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 227, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 228, "s": [91.791, 91.791, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 229, "s": [84.516, 84.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 230, "s": [78.865, 78.865, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 231, "s": [75.36, 75.36, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 232, "s": [74.307, 74.307, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 233, "s": [75.768, 75.768, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 234, "s": [79.558, 79.558, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 235, "s": [85.271, 85.271, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 236, "s": [92.321, 92.321, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 237, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 238, "s": [107.552, 107.552, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 239, "s": [114.246, 114.246, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 240, "s": [119.445, 119.445, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 241, "s": [122.669, 122.669, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 242, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 243, "s": [98.032, 98.032, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 244, "s": [92.593, 92.593, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 245, "s": [84.375, 84.375, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 246, "s": [74.074, 74.074, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 247, "s": [62.384, 62.384, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 248, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 249, "s": [37.616, 37.616, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 250, "s": [25.926, 25.926, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 251, "s": [15.625, 15.625, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 252, "s": [7.407, 7.407, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 253, "s": [1.968, 1.968, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 254, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255, "s": [-0.6, -0.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 256, "s": [-1.131, -1.131, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 257, "s": [-1.544, -1.544, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 258, "s": [-1.8, -1.8, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 259, "s": [-1.877, -1.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 260, "s": [-1.77, -1.77, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 261, "s": [-1.493, -1.493, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 262, "s": [-1.076, -1.076, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 263, "s": [-0.561, -0.561, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 264, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 265, "s": [0.552, 0.552, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 266, "s": [1.041, 1.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 267, "s": [1.42, 1.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 268, "s": [1.656, 1.656, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 269, "s": [1.727, 1.727, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 270, "s": [1.628, 1.628, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 271, "s": [1.374, 1.374, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 272, "s": [0.99, 0.99, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 273, "s": [0.516, 0.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 274, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 275, "s": [-0.508, -0.508, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 276, "s": [-0.957, -0.957, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 277, "s": [-1.307, -1.307, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 278, "s": [-1.523, -1.523, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 279, "s": [-1.589, -1.589, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 280, "s": [-1.498, -1.498, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 281, "s": [-1.264, -1.264, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 282, "s": [-0.911, -0.911, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 283, "s": [-0.475, -0.475, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 284, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 285, "s": [0.467, 0.467, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 286, "s": [0.881, 0.881, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 287, "s": [1.202, 1.202, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 288, "s": [1.402, 1.402, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 289, "s": [1.461, 1.461, 100]}, {"t": 290, "s": [1.378, 1.378, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "缩放 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.999, 0.44], [0, 0], [2.567, 2.024], [0, 0], [0.414, 5.038], [0, 0], [2.039, -2.555], [0, 0], [5.001, -0.411], [0, 0], [-2.555, -2.039], [0, 0], [-0.437, -4.961], [0, 0], [-2.024, 2.567], [0, 0]], "o": [[0, 0], [3.256, -0.287], [0, 0], [-3.97, -3.13], [0, 0], [-0.268, -3.258], [0, 0], [-3.13, 3.922], [0, 0], [-3.258, 0.268], [0, 0], [3.893, 3.107], [0, 0], [0.287, 3.256], [0, 0], [3.107, -3.94]], "v": [[18.223, 18.451], [42.989, 16.269], [44.908, 9.838], [25.471, -5.489], [18.598, -18.298], [16.572, -42.968], [10.152, -44.924], [-5.358, -25.493], [-18.099, -18.705], [-42.968, -16.662], [-44.925, -10.242], [-25.338, 5.391], [-18.558, 18.025], [-16.36, 42.989], [-9.928, 44.908], [5.522, 25.315]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.972549021244, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 304, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "星星高光", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 250, "s": [100]}, {"t": 262, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.041, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.199, 0.194], [0, 0], [1.129, 0.891], [0, 0], [0.182, 2.217], [0, 0], [0.897, -1.124], [0, 0], [2.201, -0.181], [0, 0], [-1.124, -0.897], [0, 0], [-0.192, -2.183], [0, 0], [-0.891, 1.129], [0, 0]], "o": [[0, 0], [1.433, -0.126], [0, 0], [-1.747, -1.377], [0, 0], [-0.118, -1.434], [0, 0], [-1.377, 1.726], [0, 0], [-1.434, 0.118], [0, 0], [1.713, 1.367], [0, 0], [0.126, 1.433], [0, 0], [1.367, -1.734]], "v": [[8.018, 8.119], [18.916, 7.159], [19.76, 4.329], [11.208, -2.415], [8.183, -8.051], [7.292, -18.906], [4.467, -19.768], [-2.358, -11.218], [-7.964, -8.23], [-18.907, -7.332], [-19.768, -4.507], [-11.149, 2.372], [-8.166, 7.932], [-7.199, 18.916], [-4.368, 19.76], [2.43, 11.139]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.143, 1, 1, 1, 0.494, 1, 1, 1, 0.793, 1, 1, 1, 0.143, 1, 0.494, 0.5, 0.793, 0], "ix": 9}}, "s": {"a": 0, "k": [-29.34, -9.19], "ix": 5}, "e": {"a": 0, "k": [29.979, 9.26], "ix": 6}, "t": 1, "nm": "jianbian3333", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.179, 0.039], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 304, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "星星", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 250, "s": [100]}, {"t": 262, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [451.811, 301.082, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 145, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 146, "s": [8.333, 8.333, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 147, "s": [16.667, 16.667, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 148, "s": [25, 25, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 149, "s": [33.333, 33.333, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 150, "s": [41.667, 41.667, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 151, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 152, "s": [58.333, 58.333, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 153, "s": [66.667, 66.667, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 154, "s": [75, 75, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 155, "s": [83.333, 83.333, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 156, "s": [91.667, 91.667, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 157, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 158, "s": [112.258, 112.258, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 159, "s": [123.123, 123.123, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 160, "s": [131.562, 131.562, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 161, "s": [136.795, 136.795, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 162, "s": [138.368, 138.368, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 163, "s": [136.187, 136.187, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 164, "s": [130.527, 130.527, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 165, "s": [121.995, 121.995, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 166, "s": [111.468, 111.468, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 168, "s": [88.722, 88.722, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 169, "s": [78.726, 78.726, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 170, "s": [70.962, 70.962, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 171, "s": [66.147, 66.147, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 172, "s": [64.7, 64.7, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 173, "s": [66.706, 66.706, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 174, "s": [71.914, 71.914, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 175, "s": [79.764, 79.764, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 176, "s": [89.449, 89.449, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 177, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 178, "s": [110.376, 110.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 179, "s": [119.573, 119.573, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 180, "s": [126.716, 126.716, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 181, "s": [131.146, 131.146, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 182, "s": [132.477, 132.477, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 183, "s": [130.632, 130.632, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 184, "s": [125.841, 125.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 185, "s": [118.618, 118.618, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 186, "s": [109.707, 109.707, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 187, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 188, "s": [90.453, 90.453, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 189, "s": [81.992, 81.992, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 190, "s": [75.42, 75.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 191, "s": [71.344, 71.344, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 192, "s": [70.119, 70.119, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 193, "s": [71.818, 71.818, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 194, "s": [76.226, 76.226, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 195, "s": [82.87, 82.87, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 196, "s": [91.069, 91.069, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 197, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 198, "s": [108.783, 108.783, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 199, "s": [116.568, 116.568, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [122.615, 122.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 201, "s": [126.365, 126.365, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 202, "s": [127.492, 127.492, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 203, "s": [125.929, 125.929, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 204, "s": [121.874, 121.874, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 205, "s": [115.76, 115.76, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 206, "s": [108.217, 108.217, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 208, "s": [91.919, 91.919, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 209, "s": [84.757, 84.757, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 210, "s": [79.193, 79.193, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 211, "s": [75.743, 75.743, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 212, "s": [74.707, 74.707, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 213, "s": [76.144, 76.144, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 214, "s": [79.875, 79.875, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [85.5, 85.5, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 216, "s": [92.44, 92.44, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 217, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 218, "s": [107.435, 107.435, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 219, "s": [114.025, 114.025, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 220, "s": [119.143, 119.143, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 221, "s": [122.317, 122.317, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 222, "s": [123.271, 123.271, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 223, "s": [121.948, 121.948, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 224, "s": [118.516, 118.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 225, "s": [113.341, 113.341, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 226, "s": [106.955, 106.955, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 227, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 228, "s": [93.16, 93.16, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 229, "s": [87.097, 87.097, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 230, "s": [82.387, 82.387, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 231, "s": [79.467, 79.467, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 232, "s": [78.59, 78.59, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 233, "s": [79.806, 79.806, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 234, "s": [82.965, 82.965, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 235, "s": [87.726, 87.726, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 236, "s": [93.601, 93.601, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 237, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 238, "s": [106.294, 106.294, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 239, "s": [111.872, 111.872, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 240, "s": [116.204, 116.204, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 241, "s": [118.891, 118.891, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 242, "s": [119.699, 119.699, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 243, "s": [118.579, 118.579, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 244, "s": [115.673, 115.673, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 245, "s": [111.293, 111.293, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 246, "s": [105.888, 105.888, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 247, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 248, "s": [94.21, 94.21, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 249, "s": [89.078, 89.078, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 250, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 251, "s": [98.032, 98.032, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 252, "s": [92.593, 92.593, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 253, "s": [84.375, 84.375, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 254, "s": [74.074, 74.074, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255, "s": [62.384, 62.384, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 256, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 257, "s": [37.616, 37.616, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 258, "s": [25.926, 25.926, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 259, "s": [15.625, 15.625, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 260, "s": [7.407, 7.407, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 261, "s": [1.968, 1.968, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 262, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 263, "s": [-0.6, -0.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 264, "s": [-1.131, -1.131, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 265, "s": [-1.544, -1.544, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 266, "s": [-1.8, -1.8, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 267, "s": [-1.877, -1.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 268, "s": [-1.77, -1.77, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 269, "s": [-1.493, -1.493, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 270, "s": [-1.076, -1.076, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 271, "s": [-0.561, -0.561, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 272, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 273, "s": [0.552, 0.552, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 274, "s": [1.041, 1.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 275, "s": [1.42, 1.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 276, "s": [1.656, 1.656, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 277, "s": [1.727, 1.727, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 278, "s": [1.628, 1.628, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 279, "s": [1.374, 1.374, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 280, "s": [0.99, 0.99, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 281, "s": [0.516, 0.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 282, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 283, "s": [-0.508, -0.508, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 284, "s": [-0.957, -0.957, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 285, "s": [-1.307, -1.307, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 286, "s": [-1.523, -1.523, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 287, "s": [-1.589, -1.589, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 288, "s": [-1.498, -1.498, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 289, "s": [-1.264, -1.264, 100]}, {"t": 290, "s": [-0.911, -0.911, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "缩放 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.629, 0.32], [0, 0], [1.863, 1.469], [0, 0], [0.3, 3.658], [0, 0], [1.48, -1.855], [0, 0], [3.631, -0.298], [0, 0], [-1.855, -1.48], [0, 0], [-0.317, -3.602], [0, 0], [-1.469, 1.863], [0, 0]], "o": [[0, 0], [2.364, -0.208], [0, 0], [-2.882, -2.272], [0, 0], [-0.194, -2.365], [0, 0], [-2.273, 2.847], [0, 0], [-2.365, 0.194], [0, 0], [2.826, 2.256], [0, 0], [0.208, 2.364], [0, 0], [2.256, -2.861]], "v": [[13.229, 13.395], [31.209, 11.811], [32.602, 7.142], [18.491, -3.985], [13.501, -13.284], [12.031, -31.193], [7.37, -32.614], [-3.89, -18.507], [-13.139, -13.579], [-31.193, -12.096], [-32.614, -7.435], [-18.395, 3.914], [-13.473, 13.086], [-11.877, 31.209], [-7.207, 32.602], [4.009, 18.378]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.972549021244, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 304, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 0, "nm": "花朵脸 右眼睛", "parent": 29, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 12.4, "ix": 10}, "p": {"a": 0, "k": [-9.509, -13.795, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-101.39, 101.39, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "花朵脸 右眼睛", "parent": 29, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 18.549, "ix": 10}, "p": {"a": 0, "k": [8.234, -9.74, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [101.39, 101.39, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "花朵脸 嘴巴 ", "parent": 29, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 14.328, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [13.811, -23.174, 0], "to": [-0.76, 3.802, 0], "ti": [0.76, -3.802, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 103, "s": [9.248, -0.361, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [8.234, 4.708, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.69, "y": 0}, "o": {"x": 0.333, "y": 0}, "t": 134, "s": [9.248, -0.361, 0], "to": [0.298, -1.145, 0], "ti": [-3.095, 11.329, 0]}, {"i": {"x": 0.667, "y": 0.931}, "o": {"x": 0.333, "y": 0.22}, "t": 135, "s": [9.445, 1.962, 0], "to": [3.37, -12.336, 0], "ti": [-0.792, 3.051, 0]}, {"i": {"x": 0.647, "y": 0.951}, "o": {"x": 0.304, "y": 0.206}, "t": 136, "s": [23.309, -52.033, 0], "to": [2.947, -11.347, 0], "ti": [1.052, -3.398, 0]}, {"i": {"x": 0.709, "y": 0}, "o": {"x": 0.331, "y": 0.108}, "t": 138, "s": [30.947, -84.246, 0], "to": [-0.16, 0.516, 0], "ti": [2.091, -8.172, 0]}, {"i": {"x": 0.674, "y": 1}, "o": {"x": 0.271, "y": 1}, "t": 139, "s": [31.633, -83.476, 0], "to": [-2.117, 8.273, 0], "ti": [-0.057, 0.214, 0]}, {"i": {"x": 0.667, "y": 0.887}, "o": {"x": 0.333, "y": 0}, "t": 140, "s": [30.54, -78.431, 0], "to": [0.141, -0.528, 0], "ti": [0.705, -2.657, 0]}, {"i": {"x": 0.667, "y": 0.582}, "o": {"x": 0.333, "y": 0.296}, "t": 141, "s": [24.504, -58.068, 0], "to": [-1.911, 7.197, 0], "ti": [2.533, -9.704, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.527}, "t": 144, "s": [18.936, -34.308, 0], "to": [-1.023, 3.919, 0], "ti": [-0.462, 1.443, 0]}, {"i": {"x": 0.738, "y": 0}, "o": {"x": 0.333, "y": 0}, "t": 145, "s": [18.373, -28.75, 0], "to": [0.213, -0.665, 0], "ti": [-0.574, 2.022, 0]}, {"i": {"x": 0.667, "y": 0.293}, "o": {"x": 0.333, "y": 0.513}, "t": 147, "s": [17.548, -29.825, 0], "to": [0.754, -2.659, 0], "ti": [-0.971, 3.56, 0]}, {"i": {"x": 0.667, "y": 0.688}, "o": {"x": 0.333, "y": 0.188}, "t": 149, "s": [16.099, -28.088, 0], "to": [1.977, -7.25, 0], "ti": [-3.537, 11.911, 0]}, {"i": {"x": 0.667, "y": 0.664}, "o": {"x": 0.333, "y": 0.508}, "t": 153, "s": [27.317, -65.741, 0], "to": [2.644, -8.903, 0], "ti": [-1.721, 5.986, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.214, "y": 1}, "t": 156, "s": [32.219, -83.172, 0], "to": [2.172, -7.553, 0], "ti": [-0.942, 3.25, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [34.596, -89.077, 0], "to": [3.549, -12.251, 0], "ti": [-0.942, 3.25, 0]}, {"i": {"x": 0.667, "y": 0.435}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [34.596, -89.077, 0], "to": [3.549, -12.251, 0], "ti": [2.172, -7.553, 0]}, {"i": {"x": 0.667, "y": 0.492}, "o": {"x": 0.333, "y": 0.336}, "t": 254, "s": [32.219, -83.172, 0], "to": [-1.721, 5.986, 0], "ti": [2.644, -8.903, 0]}, {"i": {"x": 0.667, "y": 0.214}, "o": {"x": 0.333, "y": 0.798}, "t": 257, "s": [27.317, -65.741, 0], "to": [-2.655, 8.94, 0], "ti": [2.604, -8.819, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.17, "y": 1}, "t": 260, "s": [26.294, -54.6, 0], "to": [-0.866, 2.931, 0], "ti": [2.081, -5.238, 0]}, {"t": 282, "s": [13.811, -23.174, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [101.39, 101.39, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "左腮红 2", "parent": 29, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [-120.147, -6.535, 0], "to": [-1.859, 5.576, 0], "ti": [1.859, -5.576, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [-131.3, 26.924, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [-131.3, 26.924, 0], "to": [1.859, -4.9, 0], "ti": [-3.38, 13.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [-120.147, -2.479, 0], "to": [3.38, -13.012, 0], "ti": [-1.521, 8.111, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [-111.022, -51.146, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [-111.022, -51.146, 0], "to": [-1.521, 7.435, 0], "ti": [1.521, -7.435, 0]}, {"t": 263, "s": [-120.147, -6.535, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [158.89, 158.89, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0, 0, 0.862, 1, 0.426, 0.113, 1, 1, 0.851, 0.226, 0, 0.75, 0.5, 0.375, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian14", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 328, "st": 23, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "右腮红 2", "parent": 29, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [95.305, 51.257, 0], "to": [-1.859, 5.576, 0], "ti": [1.859, -5.576, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [84.152, 84.716, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [84.152, 84.716, 0], "to": [1.859, -4.9, 0], "ti": [-3.38, 13.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [95.305, 55.313, 0], "to": [3.38, -13.012, 0], "ti": [-1.521, 8.111, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [104.43, 6.646, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [104.43, 6.646, 0], "to": [-1.521, 7.435, 0], "ti": [1.521, -7.435, 0]}, {"t": 263, "s": [95.305, 51.257, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [158.89, 158.89, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0, 0, 0.862, 1, 0.426, 0.113, 1, 1, 0.851, 0.226, 0, 0.75, 0.5, 0.375, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian15", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 328, "st": 23, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "形状图层 8", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 5.514, "ix": 10}, "p": {"a": 0, "k": [0.865, -0.997, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [311.555, 311.555], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 1, 1, 1, 0.24, 1, 1, 1, 0.481, 1, 1, 1, 0.74, 1, 0.933, 0.676, 1, 1, 0.867, 0.352, 0, 0.78, 0.24, 0.415, 0.48, 0.05, 0.739, 0.025, 0.997, 0], "ix": 9}}, "s": {"a": 0, "k": [53.273, -142.016], "ix": 5}, "e": {"a": 0, "k": [-26.352, 127.297], "ix": 6}, "t": 1, "nm": "jianbian213213123", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-8.492, 12.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 420, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 3, "nm": "脸高光", "parent": 29, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -5.514, "ix": 10}, "p": {"a": 0, "k": [6.915, -9.384, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 337, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 3, "nm": "头 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [291.879, 309.606, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 86, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [2.394, 2.394, 0.585]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 115, "s": [120, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0.265, 0.265, 0.474]}, "t": 129, "s": [120, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 145, "s": [114, 114, 100]}, {"i": {"x": [0.67, 0.67, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.167], "y": [0, 0, 0]}, "t": 161, "s": [96, 96, 100]}, {"i": {"x": [0.67, 0.67, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.167], "y": [0, 0, 0]}, "t": 177, "s": [104, 104, 100]}, {"i": {"x": [0.67, 0.67, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.167], "y": [0, 0, 0]}, "t": 192, "s": [98, 98, 100]}, {"t": 206, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "缩放 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 85, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 73, "ix": 1}}]}], "ip": 0, "op": 337, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "头 2", "parent": 28, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [0.441, 0.852, 0], "to": [0.109, 0.076, 0], "ti": [0.034, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [1.097, 1.311, 0], "to": [-0.034, 0.077, 0], "ti": [0.126, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [0.238, 1.314, 0], "to": [-0.126, -0.132, 0], "ti": [-0.106, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [0.342, 0.519, 0], "to": [0.106, -0.166, 0], "ti": [-0.077, 0.187, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [0.875, 0.317, 0], "to": [0.077, -0.187, 0], "ti": [0.267, 0.263, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [0.805, -0.6, 0], "to": [-0.267, -0.263, 0], "ti": [0.195, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [-0.725, -1.262, 0], "to": [-0.195, -0.077, 0], "ti": [-0.203, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [-0.365, -1.064, 0], "to": [0.203, 0.096, 0], "ti": [0.072, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [0.496, -0.684, 0], "to": [-0.072, 0.107, 0], "ti": [0.219, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [-0.797, -0.424, 0], "to": [-0.219, 0.057, 0], "ti": [-0.267, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [-0.819, -0.34, 0], "to": [0.267, 0.087, 0], "ti": [-0.28, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [0.803, 0.096, 0], "to": [0.28, -0.055, 0], "ti": [0.055, 0.155, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [0.861, -0.671, 0], "to": [-0.055, -0.155, 0], "ti": [0.165, -0.258, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [0.475, -0.834, 0], "to": [-0.165, 0.258, 0], "ti": [-0.061, -0.286, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-0.127, 0.875, 0], "to": [0.061, 0.286, 0], "ti": [-0.187, 0.119, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [0.843, 0.884, 0], "to": [0.187, -0.119, 0], "ti": [0.195, 0.092, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [0.992, 0.162, 0], "to": [-0.195, -0.092, 0], "ti": [0.142, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [-0.329, 0.33, 0], "to": [-0.142, -0.069, 0], "ti": [-0.254, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [0.141, -0.25, 0], "to": [0.254, -0.087, 0], "ti": [-0.158, -0.177, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [1.194, -0.189, 0], "to": [0.158, 0.177, 0], "ti": [0.116, -0.126, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [1.087, 0.813, 0], "to": [-0.116, 0.126, 0], "ti": [0.179, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [0.499, 0.569, 0], "to": [-0.179, -0.041, 0], "ti": [-0.078, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [0.015, 0.565, 0], "to": [0.078, -0.028, 0], "ti": [-0.201, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [0.964, 0.403, 0], "to": [0.201, 0.074, 0], "ti": [0.081, -0.139, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [1.22, 1.008, 0], "to": [-0.081, 0.139, 0], "ti": [0.37, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [0.481, 1.236, 0], "to": [-0.37, -0.019, 0], "ti": [0.136, 0.108, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [-1.002, 0.891, 0], "to": [-0.136, -0.108, 0], "ti": [-0.22, 0.174, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [-0.336, 0.586, 0], "to": [0.22, -0.174, 0], "ti": [-0.019, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [0.317, -0.15, 0], "to": [0.019, -0.012, 0], "ti": [0.168, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [-0.221, 0.516, 0], "to": [-0.168, -0.024, 0], "ti": [0.039, 0.225, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [-0.689, -0.295, 0], "to": [-0.039, -0.225, 0], "ti": [-0.044, 0.088, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [-0.456, -0.835, 0], "to": [0.044, -0.088, 0], "ti": [-0.01, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [-0.424, -0.823, 0], "to": [0.01, 0.005, 0], "ti": [-0.01, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [-0.394, -0.805, 0], "to": [0.01, 0.007, 0], "ti": [-0.009, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [-0.366, -0.783, 0], "to": [0.009, 0.007, 0], "ti": [-0.009, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [-0.339, -0.76, 0], "to": [0.009, 0.008, 0], "ti": [-0.008, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [-0.313, -0.736, 0], "to": [0.008, 0.008, 0], "ti": [-0.008, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [-0.288, -0.711, 0], "to": [0.008, 0.008, 0], "ti": [-0.008, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [-0.264, -0.686, 0], "to": [0.008, 0.008, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [-0.24, -0.661, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [-0.217, -0.635, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [-0.194, -0.609, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [-0.171, -0.583, 0], "to": [0.008, 0.009, 0], "ti": [-0.007, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [-0.148, -0.558, 0], "to": [0.007, 0.009, 0], "ti": [-0.007, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [-0.126, -0.532, 0], "to": [0.007, 0.009, 0], "ti": [-0.007, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [-0.104, -0.507, 0], "to": [0.007, 0.008, 0], "ti": [-0.007, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [-0.082, -0.481, 0], "to": [0.007, 0.008, 0], "ti": [-0.007, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [-0.06, -0.456, 0], "to": [0.007, 0.009, 0], "ti": [-0.007, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [-0.038, -0.43, 0], "to": [0.007, 0.009, 0], "ti": [-0.007, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [-0.016, -0.404, 0], "to": [0.007, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [0.006, -0.379, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [0.029, -0.353, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [0.052, -0.327, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [0.076, -0.301, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [0.1, -0.275, 0], "to": [0.008, 0.009, 0], "ti": [-0.008, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [0.125, -0.249, 0], "to": [0.008, 0.009, 0], "ti": [-0.009, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [0.151, -0.223, 0], "to": [0.009, 0.009, 0], "ti": [-0.009, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [0.177, -0.197, 0], "to": [0.009, 0.008, 0], "ti": [-0.009, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [0.205, -0.172, 0], "to": [0.009, 0.008, 0], "ti": [-0.01, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [0.234, -0.149, 0], "to": [0.01, 0.008, 0], "ti": [-0.01, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [0.264, -0.127, 0], "to": [0.01, 0.006, 0], "ti": [-0.011, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [0.296, -0.11, 0], "to": [0.011, 0.005, 0], "ti": [-0.055, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [0.329, -0.1, 0], "to": [0.055, -0.099, 0], "ti": [0.013, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [0.623, -0.706, 0], "to": [-0.013, 0.059, 0], "ti": [0.095, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [0.249, 0.252, 0], "to": [-0.095, 0.027, 0], "ti": [0.097, 0.259, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [0.051, -0.547, 0], "to": [-0.097, -0.259, 0], "ti": [0.076, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [-0.335, -1.299, 0], "to": [-0.076, 0.05, 0], "ti": [-0.076, -0.233, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [-0.404, -0.247, 0], "to": [0.076, 0.233, 0], "ti": [-0.194, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [0.123, 0.097, 0], "to": [0.194, -0.033, 0], "ti": [0.12, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [0.761, -0.443, 0], "to": [-0.12, 0.071, 0], "ti": [0.29, -0.283, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [-0.596, 0.526, 0], "to": [-0.29, 0.283, 0], "ti": [-0.233, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [-0.979, 1.255, 0], "to": [0.233, -0.084, 0], "ti": [-0.239, 0.31, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [0.802, 0.024, 0], "to": [0.239, -0.31, 0], "ti": [0.135, 0.121, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [0.456, -0.608, 0], "to": [-0.135, -0.121, 0], "ti": [0.099, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [-0.006, -0.701, 0], "to": [-0.099, 0.11, 0], "ti": [0.064, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [-0.135, 0.05, 0], "to": [-0.064, -0.002, 0], "ti": [-0.106, 0.199, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [-0.388, -0.714, 0], "to": [0.106, -0.199, 0], "ti": [-0.16, -0.188, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [0.504, -1.147, 0], "to": [0.16, 0.188, 0], "ti": [0.125, -0.271, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [0.57, 0.413, 0], "to": [-0.125, 0.271, 0], "ti": [0.015, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [-0.243, 0.477, 0], "to": [-0.015, -0.184, 0], "ti": [-0.142, 0.374, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [0.479, -0.689, 0], "to": [0.142, -0.374, 0], "ti": [-0.024, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [0.611, -1.765, 0], "to": [0.024, -0.046, 0], "ti": [0.131, -0.328, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [0.624, -0.963, 0], "to": [-0.131, 0.328, 0], "ti": [0.111, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [-0.177, 0.203, 0], "to": [-0.111, 0.229, 0], "ti": [-0.154, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [-0.042, 0.412, 0], "to": [0.154, 0.037, 0], "ti": [-0.009, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [0.745, 0.427, 0], "to": [0.009, 0.006, 0], "ti": [0.143, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [0.01, 0.447, 0], "to": [-0.143, -0.072, 0], "ti": [-0.075, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [-0.112, -0.004, 0], "to": [0.075, -0.009, 0], "ti": [-0.065, -0.165, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [0.457, 0.39, 0], "to": [0.065, 0.165, 0], "ti": [-0.12, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [0.277, 0.985, 0], "to": [0.12, -0.074, 0], "ti": [-0.102, 0.267, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [1.176, -0.053, 0], "to": [0.102, -0.267, 0], "ti": [0.302, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [0.887, -0.617, 0], "to": [-0.302, -0.078, 0], "ti": [0.145, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [-0.639, -0.518, 0], "to": [-0.145, -0.058, 0], "ti": [-0.166, 0.159, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [0.02, -0.968, 0], "to": [0.166, -0.159, 0], "ti": [-0.053, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [0.355, -1.473, 0], "to": [0.053, -0.02, 0], "ti": [-0.121, -0.222, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [0.337, -1.088, 0], "to": [0.121, 0.222, 0], "ti": [-0.06, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [1.082, -0.141, 0], "to": [0.06, 0.086, 0], "ti": [0.131, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [0.698, -0.569, 0], "to": [-0.131, 0.043, 0], "ti": [0.062, -0.312, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [0.296, 0.118, 0], "to": [-0.062, 0.312, 0], "ti": [-0.125, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [0.329, 1.301, 0], "to": [0.125, 0.179, 0], "ti": [-0.035, 0.185, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [1.044, 1.194, 0], "to": [0.035, -0.185, 0], "ti": [0.065, 0.393, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [0.537, 0.192, 0], "to": [-0.065, -0.393, 0], "ti": [0.058, 0.162, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [0.652, -1.161, 0], "to": [-0.058, -0.162, 0], "ti": [0.004, -0.231, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [0.19, -0.782, 0], "to": [-0.004, 0.231, 0], "ti": [0.05, -0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [0.626, 0.227, 0], "to": [-0.05, 0.167, 0], "ti": [0.186, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [-0.108, 0.221, 0], "to": [-0.186, -0.015, 0], "ti": [0.061, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [-0.491, 0.134, 0], "to": [-0.061, 0.053, 0], "ti": [-0.117, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [-0.472, 0.538, 0], "to": [0.117, 0.032, 0], "ti": [-0.096, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [0.209, 0.323, 0], "to": [0.096, 0.005, 0], "ti": [0.172, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [0.102, 0.57, 0], "to": [-0.172, 0.053, 0], "ti": [0.098, 0.131, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [-0.824, 0.644, 0], "to": [-0.098, -0.131, 0], "ti": [-0.119, 0.197, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [-0.486, -0.213, 0], "to": [0.119, -0.197, 0], "ti": [-0.031, -0.171, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [-0.113, -0.535, 0], "to": [0.031, 0.171, 0], "ti": [0.06, -0.316, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [-0.302, 0.813, 0], "to": [-0.06, 0.316, 0], "ti": [-0.025, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [-0.472, 1.358, 0], "to": [0.025, 0.062, 0], "ti": [-0.284, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [-0.154, 1.183, 0], "to": [0.284, -0.047, 0], "ti": [-0.218, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [1.229, 1.074, 0], "to": [0.218, -0.021, 0], "ti": [0.091, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [1.154, 1.055, 0], "to": [-0.091, -0.166, 0], "ti": [0.078, 0.21, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [0.681, 0.081, 0], "to": [-0.078, -0.21, 0], "ti": [0.138, 0.095, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [0.685, -0.208, 0], "to": [-0.138, -0.095, 0], "ti": [0.194, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [-0.147, -0.49, 0], "to": [-0.194, -0.067, 0], "ti": [-0.098, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [-0.479, -0.608, 0], "to": [0.098, 0.224, 0], "ti": [-0.153, -0.243, 0]}, {"t": 182, "s": [0.441, 0.852, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 80, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-88.719, -18.858], [18.858, -88.719], [19.281, -20.935], [58.862, 12.511], [20.574, 46.646], [-7.448, 35.042]], "o": [[88.719, 18.858], [-6.346, 29.857], [-38.012, 41.272], [-53.677, -11.409], [-13.432, -30.452], [18.858, -88.719]], "v": [[34.145, -160.64], [160.64, 34.145], [120.925, 111.161], [-34.145, 160.64], [-150.328, 66.156], [-160.64, -34.145]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274516582, 0.541176497936, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 7, "k": {"a": 0, "k": [0, 1, 0.851, 0.008, 0.394, 1, 0.851, 0.008, 0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173, 0.999, 1, 0.702, 0.173, 1, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [29, -159.5], "ix": 5}, "e": {"a": 0, "k": [-36.879, 140.394], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian17", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 337, "st": 0, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 0, "nm": "花朵脸 花瓣", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 181, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 182, "s": [10.215]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 183, "s": [10.406]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 184, "s": [10.567]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [10.694]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 186, "s": [10.782]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 187, "s": [10.831]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 188, "s": [10.84]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 189, "s": [10.813]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 190, "s": [10.752]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 191, "s": [10.662]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 192, "s": [10.549]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 193, "s": [10.42]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 194, "s": [10.281]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 195, "s": [10.139]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 196, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 197, "s": [9.87]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 198, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 213, "s": [-0.689]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 214, "s": [-1.305]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 215, "s": [-1.823]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 216, "s": [-2.23]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 217, "s": [-2.513]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 218, "s": [-2.67]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 219, "s": [-2.7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220, "s": [-2.612]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 221, "s": [-2.416]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 222, "s": [-2.128]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 223, "s": [-1.766]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [-1.351]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 225, "s": [-0.904]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [-0.447]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 227, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 228, "s": [0.418]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [0.791]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 230, "s": [1.106]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 231, "s": [1.352]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232, "s": [1.524]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [1.619]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [1.638]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 235, "s": [1.584]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 236, "s": [1.465]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [1.29]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [1.071]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 239, "s": [0.819]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 240, "s": [0.548]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [0.271]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 242, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 243, "s": [-0.254]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 244, "s": [-0.48]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [-0.671]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 246, "s": [-0.82]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [-0.925]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 248, "s": [-0.982]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [-0.993]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 250, "s": [-0.961]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 251, "s": [-0.889]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [-0.783]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [-0.65]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 254, "s": [-0.497]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [-0.333]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 256, "s": [-0.164]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 258, "s": [0.154]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 259, "s": [0.291]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 260, "s": [0.407]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 261, "s": [0.498]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [0.561]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 263, "s": [0.596]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 264, "s": [0.602]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 265, "s": [0.583]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [0.539]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 267, "s": [0.475]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [0.394]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 269, "s": [0.301]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 270, "s": [0.202]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 271, "s": [0.1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 272, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 273, "s": [-0.093]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 274, "s": [-0.177]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 275, "s": [-0.247]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 276, "s": [-0.302]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 277, "s": [-0.34]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 278, "s": [-0.361]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 279, "s": [-0.365]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 280, "s": [-0.353]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 281, "s": [-0.327]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 282, "s": [-0.288]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 283, "s": [-0.239]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 284, "s": [-0.183]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 285, "s": [-0.122]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 286, "s": [-0.06]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 287, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 288, "s": [0.057]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 289, "s": [0.107]}, {"t": 290, "s": [0.15]}], "ix": 10}, "p": {"a": 0, "k": [300, 300, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [300, 300, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "w": 600, "h": 600, "ip": 0, "op": 420, "st": 0, "bm": 0}]}, {"id": "comp_1", "nm": "花朵脸 右眼睛", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 168, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 172, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 250, "s": [100]}, {"t": 254, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [311.52, 138.969, 0], "to": [0.078, -0.011, 0], "ti": [-0.159, -0.095, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [311.987, 138.901, 0], "to": [0.159, 0.095, 0], "ti": [-0.065, -0.188, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [312.473, 139.536, 0], "to": [0.065, 0.188, 0], "ti": [0.077, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [312.375, 140.029, 0], "to": [-0.077, 0.12, 0], "ti": [0.121, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [312.009, 140.255, 0], "to": [-0.121, -0.003, 0], "ti": [0.077, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [311.652, 140.013, 0], "to": [-0.077, -0.073, 0], "ti": [0.003, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [311.549, 139.815, 0], "to": [-0.003, -0.057, 0], "ti": [-0.036, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [311.633, 139.672, 0], "to": [0.036, -0.049, 0], "ti": [-0.017, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [311.762, 139.518, 0], "to": [0.017, -0.061, 0], "ti": [0.036, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [311.732, 139.304, 0], "to": [-0.036, -0.049, 0], "ti": [0.081, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [311.546, 139.222, 0], "to": [-0.081, 0.02, 0], "ti": [0.084, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [311.247, 139.423, 0], "to": [-0.084, 0.037, 0], "ti": [0.028, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [311.043, 139.444, 0], "to": [-0.028, -0.036, 0], "ti": [-0.061, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [311.082, 139.207, 0], "to": [0.061, -0.031, 0], "ti": [-0.062, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [311.408, 139.26, 0], "to": [0.062, 0.077, 0], "ti": [0.039, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [311.452, 139.667, 0], "to": [-0.039, 0.075, 0], "ti": [0.049, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [311.171, 139.711, 0], "to": [-0.049, -0.075, 0], "ti": [-0.078, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [311.161, 139.216, 0], "to": [0.078, -0.084, 0], "ti": [-0.173, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [311.638, 139.206, 0], "to": [0.173, 0.112, 0], "ti": [-0.139, -0.161, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [312.201, 139.887, 0], "to": [0.139, 0.161, 0], "ti": [-0.012, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [312.475, 140.171, 0], "to": [0.012, -0.033, 0], "ti": [0.199, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [312.276, 139.689, 0], "to": [-0.199, -0.112, 0], "ti": [0.301, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [311.283, 139.5, 0], "to": [-0.301, 0.008, 0], "ti": [0.125, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [310.47, 139.739, 0], "to": [-0.125, 0.046, 0], "ti": [-0.08, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [310.531, 139.779, 0], "to": [0.08, 0.004, 0], "ti": [-0.176, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [310.953, 139.765, 0], "to": [0.176, 0.002, 0], "ti": [-0.163, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [311.584, 139.79, 0], "to": [0.163, 0.01, 0], "ti": [-0.006, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [311.928, 139.823, 0], "to": [0.006, -0.005, 0], "ti": [0.176, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [311.621, 139.759, 0], "to": [-0.176, -0.029, 0], "ti": [0.157, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [310.872, 139.649, 0], "to": [-0.157, -0.016, 0], "ti": [-0.02, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [310.68, 139.665, 0], "to": [0.02, -0.001, 0], "ti": [-0.117, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [310.991, 139.642, 0], "to": [0.117, -0.051, 0], "ti": [-0.089, 0.103, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [311.385, 139.356, 0], "to": [0.089, -0.103, 0], "ti": [-0.067, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [311.524, 139.021, 0], "to": [0.067, -0.06, 0], "ti": [-0.036, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [311.789, 138.997, 0], "to": [0.036, 0.058, 0], "ti": [0.082, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [311.739, 139.368, 0], "to": [-0.082, 0.116, 0], "ti": [0.14, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [311.297, 139.69, 0], "to": [-0.14, 0.103, 0], "ti": [0.048, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [310.9, 139.987, 0], "to": [-0.048, 0.038, 0], "ti": [-0.083, 0.085, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [311.008, 139.916, 0], "to": [0.083, -0.085, 0], "ti": [-0.147, 0.185, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [311.398, 139.478, 0], "to": [0.147, -0.185, 0], "ti": [-0.091, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [311.89, 138.804, 0], "to": [0.091, -0.132, 0], "ti": [0.016, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [311.943, 138.684, 0], "to": [-0.016, 0.06, 0], "ti": [-0.005, -0.207, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [311.791, 139.162, 0], "to": [0.005, 0.207, 0], "ti": [-0.092, -0.21, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [311.975, 139.928, 0], "to": [0.092, 0.21, 0], "ti": [-0.034, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [312.343, 140.422, 0], "to": [0.034, 0.11, 0], "ti": [0.148, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [312.181, 140.586, 0], "to": [-0.148, 0.026, 0], "ti": [0.183, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [311.453, 140.579, 0], "to": [-0.183, -0.056, 0], "ti": [0.026, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [311.083, 140.252, 0], "to": [-0.026, -0.091, 0], "ti": [-0.026, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [311.297, 140.033, 0], "to": [0.026, -0.059, 0], "ti": [0.047, 0.089, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [311.242, 139.896, 0], "to": [-0.047, -0.089, 0], "ti": [0.03, 0.168, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [311.017, 139.501, 0], "to": [-0.03, -0.168, 0], "ti": [-0.05, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [311.064, 138.885, 0], "to": [0.05, -0.166, 0], "ti": [-0.105, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [311.315, 138.507, 0], "to": [0.105, -0.033, 0], "ti": [-0.077, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [311.692, 138.689, 0], "to": [0.077, 0.066, 0], "ti": [0.074, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [311.775, 138.902, 0], "to": [-0.074, 0.069, 0], "ti": [0.186, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [311.25, 139.105, 0], "to": [-0.186, 0.037, 0], "ti": [0.062, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [310.656, 139.124, 0], "to": [-0.062, -0.024, 0], "ti": [-0.193, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [310.88, 138.959, 0], "to": [0.193, -0.087, 0], "ti": [-0.272, 0.097, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [311.812, 138.601, 0], "to": [0.272, -0.097, 0], "ti": [-0.087, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [312.511, 138.375, 0], "to": [0.087, -0.006, 0], "ti": [0.121, -0.159, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [312.332, 138.565, 0], "to": [-0.121, 0.159, 0], "ti": [0.141, -0.257, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [311.784, 139.328, 0], "to": [-0.141, 0.257, 0], "ti": [0.063, -0.155, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [311.488, 140.108, 0], "to": [-0.063, 0.155, 0], "ti": [0.099, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [311.404, 140.256, 0], "to": [-0.099, -0.032, 0], "ti": [0.152, 0.143, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [310.895, 139.913, 0], "to": [-0.152, -0.143, 0], "ti": [0.032, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [310.491, 139.399, 0], "to": [-0.032, -0.113, 0], "ti": [-0.129, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [310.704, 139.233, 0], "to": [0.129, -0.025, 0], "ti": [-0.163, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [311.267, 139.25, 0], "to": [0.163, 0.041, 0], "ti": [-0.063, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [311.685, 139.482, 0], "to": [0.063, 0.04, 0], "ti": [0.044, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [311.648, 139.489, 0], "to": [-0.044, -0.023, 0], "ti": [0.041, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [311.423, 139.345, 0], "to": [-0.041, 0.034, 0], "ti": [-0.02, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [311.401, 139.694, 0], "to": [0.02, 0.114, 0], "ti": [0.006, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [311.543, 140.03, 0], "to": [-0.006, -0.036, 0], "ti": [0.09, 0.163, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [311.364, 139.478, 0], "to": [-0.09, -0.163, 0], "ti": [0.075, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [311.001, 139.051, 0], "to": [-0.075, 0.007, 0], "ti": [0.009, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [310.914, 139.523, 0], "to": [-0.009, 0.103, 0], "ti": [-0.016, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [310.945, 139.668, 0], "to": [0.016, -0.029, 0], "ti": [-0.023, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [311.012, 139.347, 0], "to": [0.023, -0.078, 0], "ti": [-0.038, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [311.083, 139.202, 0], "to": [0.038, -0.006, 0], "ti": [-0.025, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [311.239, 139.309, 0], "to": [0.025, 0.113, 0], "ti": [0.031, -0.184, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [311.233, 139.883, 0], "to": [-0.031, 0.184, 0], "ti": [0.055, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [311.05, 140.412, 0], "to": [-0.055, 0.085, 0], "ti": [0.023, 0.106, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [310.901, 140.39, 0], "to": [-0.023, -0.106, 0], "ti": [0.182, 0.174, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [310.91, 139.773, 0], "to": [-0.182, -0.174, 0], "ti": [0.361, 0.071, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [309.809, 139.345, 0], "to": [-0.361, -0.071, 0], "ti": [0.351, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [308.742, 139.346, 0], "to": [-0.351, -0.023, 0], "ti": [0.349, 0.133, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [307.702, 139.206, 0], "to": [-0.349, -0.133, 0], "ti": [0.352, 0.188, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [306.65, 138.547, 0], "to": [-0.352, -0.188, 0], "ti": [0.361, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [305.591, 138.076, 0], "to": [-0.361, -0.037, 0], "ti": [0.369, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [304.483, 138.328, 0], "to": [-0.369, 0.055, 0], "ti": [0.351, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [303.376, 138.403, 0], "to": [-0.351, -0.105, 0], "ti": [0.318, 0.248, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [302.376, 137.695, 0], "to": [-0.318, -0.248, 0], "ti": [0.292, 0.16, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [301.468, 136.914, 0], "to": [-0.292, -0.16, 0], "ti": [0.267, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [300.624, 136.735, 0], "to": [-0.267, -0.091, 0], "ti": [0.316, 0.115, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [299.868, 136.367, 0], "to": [-0.316, -0.115, 0], "ti": [0.422, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [298.728, 136.046, 0], "to": [-0.422, -0.029, 0], "ti": [0.453, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [297.333, 136.19, 0], "to": [-0.453, 0.08, 0], "ti": [0.121, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [296.008, 136.529, 0], "to": [-0.121, 0.092, 0], "ti": [-0.183, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [296.605, 136.742, 0], "to": [0.183, 0.017, 0], "ti": [-0.135, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [297.104, 136.633, 0], "to": [0.135, -0.015, 0], "ti": [-0.116, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [297.413, 136.65, 0], "to": [0.116, 0.012, 0], "ti": [-0.152, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [297.802, 136.707, 0], "to": [0.152, -0.011, 0], "ti": [-0.16, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [298.325, 136.584, 0], "to": [0.16, -0.019, 0], "ti": [-0.168, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [298.763, 136.593, 0], "to": [0.168, 0.074, 0], "ti": [-0.223, -0.154, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [299.334, 137.027, 0], "to": [0.223, 0.154, 0], "ti": [-0.294, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [300.103, 137.515, 0], "to": [0.294, 0.114, 0], "ti": [-0.345, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [301.097, 137.711, 0], "to": [0.345, -0.018, 0], "ti": [-0.297, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [302.176, 137.407, 0], "to": [0.297, -0.063, 0], "ti": [-0.146, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [302.876, 137.333, 0], "to": [0.146, 0.057, 0], "ti": [-0.081, -0.138, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [303.053, 137.75, 0], "to": [0.081, 0.138, 0], "ti": [-0.194, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [303.363, 138.163, 0], "to": [0.194, 0.03, 0], "ti": [-0.282, 0.121, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [304.218, 137.932, 0], "to": [0.282, -0.121, 0], "ti": [-0.258, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [305.054, 137.435, 0], "to": [0.258, -0.083, 0], "ti": [-0.234, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [305.767, 137.432, 0], "to": [0.234, 0.097, 0], "ti": [-0.224, -0.202, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [306.46, 138.016, 0], "to": [0.224, 0.202, 0], "ti": [-0.234, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [307.11, 138.645, 0], "to": [0.234, 0.092, 0], "ti": [-0.236, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [307.862, 138.57, 0], "to": [0.236, -0.075, 0], "ti": [-0.17, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [308.525, 138.197, 0], "to": [0.17, -0.052, 0], "ti": [-0.167, -0.118, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [308.884, 138.256, 0], "to": [0.167, 0.118, 0], "ti": [-0.286, -0.19, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [309.527, 138.903, 0], "to": [0.286, 0.19, 0], "ti": [-0.328, -0.129, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [310.598, 139.394, 0], "to": [0.328, 0.129, 0], "ti": [-0.235, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [311.495, 139.674, 0], "to": [0.235, 0.032, 0], "ti": [-0.065, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [312.01, 139.584, 0], "to": [0.065, -0.039, 0], "ti": [0.053, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [311.884, 139.441, 0], "to": [-0.053, -0.014, 0], "ti": [0.043, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [311.692, 139.501, 0], "to": [-0.043, 0.013, 0], "ti": [-0.01, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [311.624, 139.518, 0], "to": [0.01, -0.004, 0], "ti": [-0.05, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [311.753, 139.478, 0], "to": [0.05, 0.011, 0], "ti": [-0.02, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [311.921, 139.586, 0], "to": [0.02, -0.018, 0], "ti": [0.011, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [311.875, 139.371, 0], "to": [-0.011, -0.114, 0], "ti": [-0.033, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [311.854, 138.901, 0], "to": [0.033, -0.076, 0], "ti": [-0.038, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [312.073, 138.918, 0], "to": [0.038, 0.097, 0], "ti": [0.048, -0.157, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [312.082, 139.481, 0], "to": [-0.048, 0.157, 0], "ti": [0.068, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [311.784, 139.859, 0], "to": [-0.068, 0.102, 0], "ti": [-0.004, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [311.672, 140.091, 0], "to": [0.004, -0.004, 0], "ti": [-0.033, 0.088, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [311.806, 139.837, 0], "to": [0.033, -0.088, 0], "ti": [-0.028, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [311.868, 139.561, 0], "to": [0.028, -0.022, 0], "ti": [-0.025, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [311.973, 139.702, 0], "to": [0.025, 0.087, 0], "ti": [0.045, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [312.015, 140.084, 0], "to": [-0.045, 0.053, 0], "ti": [0.176, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [311.704, 140.019, 0], "to": [-0.176, -0.077, 0], "ti": [0.169, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [310.957, 139.623, 0], "to": [-0.169, -0.099, 0], "ti": [0.007, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [310.691, 139.427, 0], "to": [-0.007, 0.001, 0], "ti": [-0.05, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [310.914, 139.628, 0], "to": [0.05, 0.019, 0], "ti": [-0.054, 0.127, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [310.989, 139.54, 0], "to": [0.054, -0.127, 0], "ti": [-0.137, 0.162, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [311.241, 138.867, 0], "to": [0.137, -0.162, 0], "ti": [-0.149, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [311.812, 138.57, 0], "to": [0.149, 0.034, 0], "ti": [-0.006, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [312.138, 139.07, 0], "to": [0.006, 0.143, 0], "ti": [0.1, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [311.85, 139.431, 0], "to": [-0.1, 0.006, 0], "ti": [0.09, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [311.54, 139.108, 0], "to": [-0.09, -0.05, 0], "ti": [0.032, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [311.313, 139.129, 0], "to": [-0.032, 0.029, 0], "ti": [-0.031, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [311.345, 139.279, 0], "to": [0.031, -0.006, 0], "ti": [-0.068, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [311.499, 139.095, 0], "to": [0.068, -0.034, 0], "ti": [-0.058, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [311.752, 139.074, 0], "to": [0.058, 0.023, 0], "ti": [0.028, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [311.848, 139.23, 0], "to": [-0.028, 0.043, 0], "ti": [0.114, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [311.586, 139.332, 0], "to": [-0.114, 0.052, 0], "ti": [0.048, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [311.161, 139.541, 0], "to": [-0.048, 0.044, 0], "ti": [-0.107, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [311.298, 139.599, 0], "to": [0.107, 0.023, 0], "ti": [-0.132, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [311.801, 139.681, 0], "to": [0.132, 0.065, 0], "ti": [-0.045, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [312.092, 139.991, 0], "to": [0.045, 0.02, 0], "ti": [0.017, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [312.069, 139.803, 0], "to": [-0.017, -0.144, 0], "ti": [0.108, 0.179, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [311.989, 139.128, 0], "to": [-0.108, -0.179, 0], "ti": [0.199, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [311.422, 138.728, 0], "to": [-0.199, -0.025, 0], "ti": [0.119, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [310.794, 138.979, 0], "to": [-0.119, 0.089, 0], "ti": [-0.046, -0.125, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [310.71, 139.261, 0], "to": [0.046, 0.125, 0], "ti": [-0.16, -0.156, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [311.072, 139.73, 0], "to": [0.16, 0.156, 0], "ti": [-0.149, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [311.668, 140.2, 0], "to": [0.149, 0.11, 0], "ti": [-0.096, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [311.963, 140.388, 0], "to": [0.096, -0.008, 0], "ti": [-0.105, 0.122, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [312.245, 140.153, 0], "to": [0.105, -0.122, 0], "ti": [-0.063, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [312.596, 139.654, 0], "to": [0.063, -0.134, 0], "ti": [0.124, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [312.624, 139.347, 0], "to": [-0.124, -0.091, 0], "ti": [0.222, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [311.85, 139.107, 0], "to": [-0.222, -0.01, 0], "ti": [0.029, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [311.291, 139.289, 0], "to": [-0.029, 0.088, 0], "ti": [-0.145, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [311.674, 139.638, 0], "to": [0.145, 0.112, 0], "ti": [-0.107, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [312.158, 139.96, 0], "to": [0.107, 0.082, 0], "ti": [-0.022, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [312.319, 140.132, 0], "to": [0.022, 0.05, 0], "ti": [0.066, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [312.292, 140.263, 0], "to": [-0.066, 0.026, 0], "ti": [0.167, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [311.921, 140.289, 0], "to": [-0.167, -0.013, 0], "ti": [0.169, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [311.29, 140.185, 0], "to": [-0.169, -0.048, 0], "ti": [0.085, 0.103, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [310.908, 140, 0], "to": [-0.085, -0.103, 0], "ti": [0.007, 0.111, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [310.777, 139.567, 0], "to": [-0.007, -0.111, 0], "ti": [-0.033, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [310.868, 139.334, 0], "to": [0.033, -0.007, 0], "ti": [-0.076, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [310.974, 139.524, 0], "to": [0.076, 0.068, 0], "ti": [-0.085, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [311.324, 139.744, 0], "to": [0.085, 0.012, 0], "ti": [0.002, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [311.486, 139.595, 0], "to": [-0.002, -0.079, 0], "ti": [0.073, 0.085, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [311.314, 139.272, 0], "to": [-0.073, -0.085, 0], "ti": [0.102, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [311.048, 139.087, 0], "to": [-0.102, -0.013, 0], "ti": [0.093, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [310.703, 139.197, 0], "to": [-0.093, 0.065, 0], "ti": [0.018, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [310.49, 139.475, 0], "to": [-0.018, 0.137, 0], "ti": [-0.04, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [310.594, 140.021, 0], "to": [0.04, 0.12, 0], "ti": [-0.037, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [310.728, 140.197, 0], "to": [0.037, 0.01, 0], "ti": [-0.115, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [310.813, 140.079, 0], "to": [0.115, -0.009, 0], "ti": [-0.166, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [311.42, 140.141, 0], "to": [0.166, -0.012, 0], "ti": [-0.021, 0.085, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [311.808, 140.009, 0], "to": [0.021, -0.085, 0], "ti": [0.082, 0.123, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [311.545, 139.632, 0], "to": [-0.082, -0.123, 0], "ti": [0.036, 0.13, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [311.316, 139.274, 0], "to": [-0.036, -0.13, 0], "ti": [0.006, 0.119, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [311.33, 138.854, 0], "to": [-0.006, -0.119, 0], "ti": [0.04, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [311.277, 138.561, 0], "to": [-0.04, 0.009, 0], "ti": [0.043, -0.133, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [311.09, 138.908, 0], "to": [-0.043, 0.133, 0], "ti": [0.003, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [311.021, 139.357, 0], "to": [-0.003, 0.058, 0], "ti": [-0.011, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [311.071, 139.254, 0], "to": [0.011, -0.025, 0], "ti": [-0.026, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [311.085, 139.206, 0], "to": [0.026, -0.011, 0], "ti": [-0.081, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [311.227, 139.19, 0], "to": [0.081, 0.005, 0], "ti": [-0.1, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [311.571, 139.237, 0], "to": [0.1, 0.046, 0], "ti": [0.007, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [311.824, 139.464, 0], "to": [-0.007, 0.006, 0], "ti": [0.123, 0.147, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [311.526, 139.276, 0], "to": [-0.123, -0.147, 0], "ti": [0.121, 0.198, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [311.087, 138.584, 0], "to": [-0.121, -0.198, 0], "ti": [0.049, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [310.8, 138.09, 0], "to": [-0.049, -0.012, 0], "ti": [0.001, -0.2, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [310.795, 138.513, 0], "to": [-0.001, 0.2, 0], "ti": [-0.03, -0.232, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [310.791, 139.288, 0], "to": [0.03, 0.232, 0], "ti": [-0.086, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [310.975, 139.907, 0], "to": [0.086, 0.039, 0], "ti": [-0.069, 0.154, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [311.304, 139.52, 0], "to": [0.069, -0.154, 0], "ti": [0.027, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [311.387, 138.983, 0], "to": [-0.027, -0.093, 0], "ti": [0.079, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [311.145, 138.963, 0], "to": [-0.079, 0.033, 0], "ti": [0.034, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [310.911, 139.18, 0], "to": [-0.034, -0.002, 0], "ti": [0.03, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [310.94, 138.949, 0], "to": [-0.03, -0.063, 0], "ti": [0.107, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271, "s": [310.729, 138.802, 0], "to": [-0.107, 0.018, 0], "ti": [0.083, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272, "s": [310.299, 139.057, 0], "to": [-0.083, 0.057, 0], "ti": [-0.086, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273, "s": [310.229, 139.146, 0], "to": [0.086, -0.028, 0], "ti": [-0.213, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274, "s": [310.815, 138.889, 0], "to": [0.213, -0.037, 0], "ti": [-0.126, -0.121, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [311.508, 138.925, 0], "to": [0.126, 0.121, 0], "ti": [-0.015, -0.249, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [311.574, 139.613, 0], "to": [0.015, 0.249, 0], "ti": [-0.051, -0.14, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [311.595, 140.417, 0], "to": [0.051, 0.14, 0], "ti": [-0.085, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278, "s": [311.878, 140.451, 0], "to": [0.085, -0.032, 0], "ti": [0.021, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279, "s": [312.104, 140.225, 0], "to": [-0.021, -0.033, 0], "ti": [0.152, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [311.754, 140.252, 0], "to": [-0.152, -0.008, 0], "ti": [0.105, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281, "s": [311.191, 140.176, 0], "to": [-0.105, -0.046, 0], "ti": [-0.015, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282, "s": [311.126, 139.974, 0], "to": [0.015, 0.011, 0], "ti": [-0.07, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [311.282, 140.24, 0], "to": [0.07, 0.091, 0], "ti": [-0.047, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [311.547, 140.518, 0], "to": [0.047, 0.005, 0], "ti": [0.074, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [311.565, 140.27, 0], "to": [-0.074, -0.114, 0], "ti": [0.168, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286, "s": [311.102, 139.837, 0], "to": [-0.168, -0.138, 0], "ti": [0.077, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 287, "s": [310.557, 139.44, 0], "to": [-0.077, -0.076, 0], "ti": [-0.119, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 288, "s": [310.638, 139.384, 0], "to": [0.119, 0.045, 0], "ti": [-0.187, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 289, "s": [311.273, 139.712, 0], "to": [0.187, 0.06, 0], "ti": [-0.077, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 290, "s": [311.759, 139.747, 0], "to": [0.077, -0.035, 0], "ti": [0.03, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 291, "s": [311.737, 139.501, 0], "to": [-0.03, -0.016, 0], "ti": [0.096, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 292, "s": [311.58, 139.652, 0], "to": [-0.096, 0.02, 0], "ti": [0.07, 0.005, 0]}, {"t": 293, "s": [311.161, 139.622, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [55.539, -116.576, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 140, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 154, "s": [66.529, 66.529, 100]}, {"t": 179, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 168, "s": [{"i": [[1.145, 2.213], [0.944, -1.061], [-1.151, -1.789], [-1.119, 0.956]], "o": [[-1.138, -2.199], [-1.323, 1.487], [1.914, 2.975], [1.423, -1.216]], "v": [[55.584, -118.562], [55.015, -116.363], [56.658, -115.005], [57.485, -117.01]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 186, "s": [{"i": [[5.534, 10.692], [4.562, -5.125], [-5.562, -8.644], [-5.408, 4.621]], "o": [[-5.5, -10.625], [-6.395, 7.184], [9.25, 14.375], [6.875, -5.875]], "v": [[52.312, -126], [49.562, -115.375], [57.5, -108.812], [61.5, -118.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 194.334, "s": [{"i": [[4.253, 5.597], [4.753, -4.028], [-4.685, -5.466], [-5.81, 3.847]], "o": [[-3.9, -5.132], [-7.337, 6.219], [5.527, 6.449], [7.54, -4.993]], "v": [[54.188, -122.375], [49.562, -115.375], [55.875, -111.188], [61.5, -118.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 202.666, "s": [{"i": [[5.534, 10.692], [4.562, -5.125], [-5.562, -8.644], [-5.408, 4.621]], "o": [[-5.5, -10.625], [-6.395, 7.184], [9.25, 14.375], [6.875, -5.875]], "v": [[52.312, -126], [49.562, -115.375], [57.5, -108.812], [61.5, -118.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 212.191, "s": [{"i": [[4.253, 5.597], [4.753, -4.028], [-4.685, -5.466], [-5.81, 3.847]], "o": [[-3.9, -5.132], [-7.337, 6.219], [5.527, 6.449], [7.54, -4.993]], "v": [[54.188, -122.375], [49.562, -115.375], [55.875, -111.188], [61.5, -118.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 222.475, "s": [{"i": [[5.534, 10.692], [4.562, -5.125], [-5.562, -8.644], [-5.408, 4.621]], "o": [[-5.5, -10.625], [-6.395, 7.184], [9.25, 14.375], [6.875, -5.875]], "v": [[52.312, -126], [49.562, -115.375], [57.5, -108.812], [61.5, -118.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 232, "s": [{"i": [[4.253, 5.597], [4.753, -4.028], [-4.685, -5.466], [-5.81, 3.847]], "o": [[-3.9, -5.132], [-7.337, 6.219], [5.527, 6.449], [7.54, -4.993]], "v": [[54.188, -122.375], [49.562, -115.375], [55.875, -111.188], [61.5, -118.5]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248.904, "s": [{"i": [[5.534, 10.692], [4.562, -5.125], [-5.562, -8.644], [-5.408, 4.621]], "o": [[-5.5, -10.625], [-6.395, 7.184], [9.25, 14.375], [6.875, -5.875]], "v": [[52.312, -126], [49.562, -115.375], [57.5, -108.812], [61.5, -118.5]], "c": true}]}, {"t": 255, "s": [{"i": [[1.145, 2.213], [0.944, -1.061], [-1.151, -1.789], [-1.119, 0.956]], "o": [[-1.138, -2.199], [-1.323, 1.487], [1.914, 2.975], [1.423, -1.216]], "v": [[48.772, -111.812], [48.202, -109.613], [49.845, -108.255], [50.673, -110.26]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 169, "s": [0]}, {"i": {"x": [0.715], "y": [49.389]}, "o": {"x": [0.303], "y": [0]}, "t": 178, "s": [100]}, {"i": {"x": [0.775], "y": [1]}, "o": {"x": [0.424], "y": [0.062]}, "t": 248, "s": [100]}, {"t": 254, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.747}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [309.766, 177.614, 0], "to": [0.019, 0.05, 0], "ti": [-0.128, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.124}, "t": 1, "s": [309.879, 177.914, 0], "to": [0.128, 0.08, 0], "ti": [-0.127, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.788}, "o": {"x": 0.167, "y": 0.275}, "t": 2, "s": [310.532, 178.095, 0], "to": [0.127, -0.007, 0], "ti": [0.049, 0.071, 0]}, {"i": {"x": 0.833, "y": 0.887}, "o": {"x": 0.167, "y": 0.137}, "t": 3, "s": [310.64, 177.874, 0], "to": [-0.049, -0.071, 0], "ti": [0.092, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.878}, "o": {"x": 0.167, "y": 0.315}, "t": 4, "s": [310.235, 177.666, 0], "to": [-0.092, -0.026, 0], "ti": [0.014, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.708}, "o": {"x": 0.167, "y": 0.263}, "t": 5, "s": [310.088, 177.717, 0], "to": [-0.014, 0.01, 0], "ti": [0.013, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.762}, "o": {"x": 0.167, "y": 0.117}, "t": 6, "s": [310.148, 177.726, 0], "to": [-0.013, -0.021, 0], "ti": [0.071, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.128}, "t": 7, "s": [310.01, 177.592, 0], "to": [-0.071, -0.055, 0], "ti": [0.046, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.768}, "o": {"x": 0.167, "y": 0.16}, "t": 8, "s": [309.722, 177.399, 0], "to": [-0.046, 0.031, 0], "ti": [-0.027, -0.176, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.13}, "t": 9, "s": [309.734, 177.779, 0], "to": [0.027, 0.176, 0], "ti": [-0.049, -0.19, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.202}, "t": 10, "s": [309.886, 178.455, 0], "to": [0.049, 0.19, 0], "ti": [0.012, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.764}, "o": {"x": 0.167, "y": 0.205}, "t": 11, "s": [310.029, 178.919, 0], "to": [-0.012, 0.038, 0], "ti": [0.107, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.129}, "t": 12, "s": [309.811, 178.681, 0], "to": [-0.107, -0.113, 0], "ti": [0.063, 0.111, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.286}, "t": 13, "s": [309.386, 178.24, 0], "to": [-0.063, -0.111, 0], "ti": [-0.046, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.171}, "t": 14, "s": [309.43, 178.016, 0], "to": [0.046, -0.036, 0], "ti": [-0.014, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.686}, "o": {"x": 0.167, "y": 0.191}, "t": 15, "s": [309.664, 178.027, 0], "to": [0.014, 0.019, 0], "ti": [0.07, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.114}, "t": 16, "s": [309.516, 178.131, 0], "to": [-0.07, 0.089, 0], "ti": [-0.026, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.153}, "t": 17, "s": [309.246, 178.559, 0], "to": [0.026, 0.143, 0], "ti": [-0.172, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.166}, "t": 18, "s": [309.674, 178.989, 0], "to": [0.172, 0.072, 0], "ti": [-0.067, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.183}, "t": 19, "s": [310.281, 178.99, 0], "to": [0.067, -0.076, 0], "ti": [0.12, 0.173, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.139}, "t": 20, "s": [310.077, 178.533, 0], "to": [-0.12, -0.173, 0], "ti": [0.08, 0.149, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.271}, "t": 21, "s": [309.562, 177.955, 0], "to": [-0.08, -0.149, 0], "ti": [-0.057, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.17}, "t": 22, "s": [309.596, 177.637, 0], "to": [0.057, -0.068, 0], "ti": [-0.067, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.243}, "t": 23, "s": [309.906, 177.549, 0], "to": [0.067, 0.007, 0], "ti": [-0.025, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.169}, "t": 24, "s": [310, 177.681, 0], "to": [0.025, 0.048, 0], "ti": [0.007, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.146}, "t": 25, "s": [310.056, 177.835, 0], "to": [-0.007, 0.058, 0], "ti": [0.049, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.172}, "t": 26, "s": [309.959, 178.028, 0], "to": [-0.049, 0.037, 0], "ti": [0.033, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.751}, "o": {"x": 0.167, "y": 0.184}, "t": 27, "s": [309.761, 178.059, 0], "to": [-0.033, -0.02, 0], "ti": [-0.029, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.125}, "t": 28, "s": [309.763, 177.909, 0], "to": [0.029, 0.023, 0], "ti": [-0.054, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.167, "y": 0.145}, "t": 29, "s": [309.938, 178.197, 0], "to": [0.054, 0.12, 0], "ti": [-0.035, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.648}, "o": {"x": 0.167, "y": 0.354}, "t": 30, "s": [310.089, 178.628, 0], "to": [0.035, 0.07, 0], "ti": [0.035, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.109}, "t": 31, "s": [310.149, 178.615, 0], "to": [-0.035, -0.062, 0], "ti": [0.062, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.196}, "t": 32, "s": [309.879, 178.253, 0], "to": [-0.062, -0.112, 0], "ti": [-0.034, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.161}, "t": 33, "s": [309.775, 177.945, 0], "to": [0.034, -0.08, 0], "ti": [-0.113, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.146}, "t": 34, "s": [310.086, 177.776, 0], "to": [0.113, -0.079, 0], "ti": [-0.097, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.787}, "o": {"x": 0.167, "y": 0.257}, "t": 35, "s": [310.452, 177.469, 0], "to": [0.097, -0.054, 0], "ti": [-0.067, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.883}, "o": {"x": 0.167, "y": 0.137}, "t": 36, "s": [310.666, 177.452, 0], "to": [0.067, 0.047, 0], "ti": [-0.018, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.66}, "o": {"x": 0.167, "y": 0.289}, "t": 37, "s": [310.855, 177.752, 0], "to": [0.018, 0.062, 0], "ti": [0.086, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.11}, "t": 38, "s": [310.774, 177.823, 0], "to": [-0.086, -0.004, 0], "ti": [0.112, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.228}, "t": 39, "s": [310.339, 177.729, 0], "to": [-0.112, -0.001, 0], "ti": [0.016, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.222}, "t": 40, "s": [310.102, 177.818, 0], "to": [-0.016, 0.007, 0], "ti": [-0.039, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.663}, "o": {"x": 0.167, "y": 0.163}, "t": 41, "s": [310.244, 177.774, 0], "to": [0.039, 0.011, 0], "ti": [0.005, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.111}, "t": 42, "s": [310.333, 177.882, 0], "to": [-0.005, 0.096, 0], "ti": [0.062, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.212}, "t": 43, "s": [310.214, 178.352, 0], "to": [-0.062, 0.105, 0], "ti": [0.102, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.147}, "t": 44, "s": [309.96, 178.511, 0], "to": [-0.102, -0.007, 0], "ti": [0.113, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.184}, "t": 45, "s": [309.602, 178.308, 0], "to": [-0.113, -0.02, 0], "ti": [0.04, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.766}, "o": {"x": 0.167, "y": 0.165}, "t": 46, "s": [309.28, 178.388, 0], "to": [-0.04, 0.065, 0], "ti": [-0.117, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.129}, "t": 47, "s": [309.361, 178.697, 0], "to": [0.117, 0.03, 0], "ti": [-0.185, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.177}, "t": 48, "s": [309.983, 178.57, 0], "to": [0.185, -0.07, 0], "ti": [-0.084, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.612}, "o": {"x": 0.167, "y": 0.421}, "t": 49, "s": [310.469, 178.277, 0], "to": [0.084, -0.047, 0], "ti": [0.077, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.106}, "t": 50, "s": [310.485, 178.29, 0], "to": [-0.077, 0.032, 0], "ti": [0.159, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.174}, "t": 51, "s": [310.004, 178.467, 0], "to": [-0.159, 0.035, 0], "ti": [0.084, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.309}, "t": 52, "s": [309.534, 178.503, 0], "to": [-0.084, 0.032, 0], "ti": [0.006, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.473}, "o": {"x": 0.167, "y": 0.228}, "t": 53, "s": [309.503, 178.656, 0], "to": [-0.006, 0.012, 0], "ti": [0.044, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.099}, "t": 54, "s": [309.495, 178.576, 0], "to": [-0.044, -0.091, 0], "ti": [0.036, 0.17, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.161}, "t": 55, "s": [309.236, 178.11, 0], "to": [-0.036, -0.17, 0], "ti": [-0.109, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.158}, "t": 56, "s": [309.277, 177.554, 0], "to": [0.109, -0.069, 0], "ti": [-0.174, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.164}, "t": 57, "s": [309.891, 177.693, 0], "to": [0.174, 0.107, 0], "ti": [-0.104, -0.181, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.173}, "t": 58, "s": [310.322, 178.196, 0], "to": [0.104, 0.181, 0], "ti": [-0.007, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.256}, "t": 59, "s": [310.512, 178.78, 0], "to": [0.007, 0.134, 0], "ti": [0.089, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.142}, "t": 60, "s": [310.361, 179, 0], "to": [-0.089, 0.008, 0], "ti": [0.139, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.153}, "t": 61, "s": [309.978, 178.828, 0], "to": [-0.139, -0.066, 0], "ti": [0.078, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.79}, "o": {"x": 0.167, "y": 0.283}, "t": 62, "s": [309.529, 178.601, 0], "to": [-0.078, -0.068, 0], "ti": [-0.044, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.138}, "t": 63, "s": [309.508, 178.417, 0], "to": [0.044, -0.053, 0], "ti": [-0.052, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.179}, "t": 64, "s": [309.793, 178.281, 0], "to": [0.052, -0.067, 0], "ti": [0.029, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 65, "s": [309.818, 178.012, 0], "to": [-0.029, -0.06, 0], "ti": [0.039, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.674}, "o": {"x": 0.167, "y": 0.194}, "t": 66, "s": [309.619, 177.92, 0], "to": [-0.039, 0.005, 0], "ti": [-0.067, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.112}, "t": 67, "s": [309.582, 178.043, 0], "to": [0.067, -0.018, 0], "ti": [-0.147, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [310.023, 177.812, 0], "to": [0.147, -0.074, 0], "ti": [-0.082, 0, 0]}, {"i": {"x": 0.833, "y": 0.7}, "o": {"x": 0.167, "y": 0.229}, "t": 69, "s": [310.467, 177.596, 0], "to": [0.082, 0, 0], "ti": [0.101, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.115}, "t": 70, "s": [310.516, 177.811, 0], "to": [-0.101, 0.09, 0], "ti": [0.188, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.202}, "t": 71, "s": [309.86, 178.135, 0], "to": [-0.188, 0.027, 0], "ti": [0.038, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.734}, "o": {"x": 0.167, "y": 0.181}, "t": 72, "s": [309.389, 177.973, 0], "to": [-0.038, -0.075, 0], "ti": [-0.175, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.121}, "t": 73, "s": [309.634, 177.684, 0], "to": [0.175, 0.041, 0], "ti": [-0.218, -0.18, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.192}, "t": 74, "s": [310.438, 178.217, 0], "to": [0.218, 0.18, 0], "ti": [-0.077, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.728}, "o": {"x": 0.167, "y": 0.298}, "t": 75, "s": [310.94, 178.764, 0], "to": [0.077, 0.056, 0], "ti": [0.104, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.12}, "t": 76, "s": [310.902, 178.552, 0], "to": [-0.104, -0.08, 0], "ti": [0.198, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.878}, "o": {"x": 0.167, "y": 0.163}, "t": 77, "s": [310.316, 178.285, 0], "to": [-0.198, 0.007, 0], "ti": [0.122, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.263}, "t": 78, "s": [309.716, 178.592, 0], "to": [-0.122, 0.097, 0], "ti": [0.062, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.172}, "t": 79, "s": [309.585, 178.869, 0], "to": [-0.062, 0.02, 0], "ti": [0.037, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.753}, "o": {"x": 0.167, "y": 0.184}, "t": 80, "s": [309.345, 178.713, 0], "to": [-0.037, -0.063, 0], "ti": [-0.083, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.126}, "t": 81, "s": [309.363, 178.494, 0], "to": [0.083, -0.04, 0], "ti": [-0.138, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.189}, "t": 82, "s": [309.844, 178.474, 0], "to": [0.138, 0.02, 0], "ti": [0.003, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [310.19, 178.614, 0], "to": [-0.003, 0.039, 0], "ti": [0.134, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.155}, "t": 84, "s": [309.828, 178.707, 0], "to": [-0.134, 0.017, 0], "ti": [0.086, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.692}, "o": {"x": 0.167, "y": 0.341}, "t": 85, "s": [309.386, 178.716, 0], "to": [-0.086, -0.012, 0], "ti": [-0.019, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.114}, "t": 86, "s": [309.314, 178.635, 0], "to": [0.019, -0.07, 0], "ti": [-0.061, 0.118, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.161}, "t": 87, "s": [309.502, 178.299, 0], "to": [0.061, -0.118, 0], "ti": [-0.086, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.176}, "t": 88, "s": [309.681, 177.929, 0], "to": [0.086, -0.084, 0], "ti": [-0.06, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.765}, "o": {"x": 0.167, "y": 0.195}, "t": 89, "s": [310.017, 177.796, 0], "to": [0.06, 0.019, 0], "ti": [0.067, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.129}, "t": 90, "s": [310.038, 178.042, 0], "to": [-0.067, 0.085, 0], "ti": [0.161, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.899}, "o": {"x": 0.167, "y": 0.154}, "t": 91, "s": [309.614, 178.305, 0], "to": [-0.161, 0.085, 0], "ti": [0.099, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.668}, "o": {"x": 0.167, "y": 0.477}, "t": 92, "s": [309.075, 178.551, 0], "to": [-0.099, 0.041, 0], "ti": [-0.033, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.733}, "o": {"x": 0.167, "y": 0.111}, "t": 93, "s": [309.022, 178.548, 0], "to": [0.033, -0.047, 0], "ti": [-0.131, 0.151, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.121}, "t": 94, "s": [309.273, 178.27, 0], "to": [0.131, -0.151, 0], "ti": [-0.182, 0.154, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.191}, "t": 95, "s": [309.806, 177.64, 0], "to": [0.182, -0.154, 0], "ti": [-0.123, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.773}, "o": {"x": 0.167, "y": 0.24}, "t": 96, "s": [310.364, 177.343, 0], "to": [0.123, -0.009, 0], "ti": [-0.016, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.132}, "t": 97, "s": [310.543, 177.586, 0], "to": [0.016, 0.137, 0], "ti": [0.01, -0.186, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.173}, "t": 98, "s": [310.458, 178.162, 0], "to": [-0.01, 0.186, 0], "ti": [-0.029, -0.133, 0]}, {"i": {"x": 0.833, "y": 0.752}, "o": {"x": 0.167, "y": 0.224}, "t": 99, "s": [310.483, 178.703, 0], "to": [0.029, 0.133, 0], "ti": [0.08, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.126}, "t": 100, "s": [310.63, 178.96, 0], "to": [-0.08, 0.042, 0], "ti": [0.225, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.148}, "t": 101, "s": [310.001, 178.953, 0], "to": [-0.225, -0.063, 0], "ti": [0.104, 0.119, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.26}, "t": 102, "s": [309.282, 178.58, 0], "to": [-0.104, -0.119, 0], "ti": [-0.022, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.242}, "t": 103, "s": [309.377, 178.238, 0], "to": [0.022, -0.026, 0], "ti": [0.014, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.665}, "o": {"x": 0.167, "y": 0.18}, "t": 104, "s": [309.414, 178.424, 0], "to": [-0.014, 0.039, 0], "ti": [-0.027, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.111}, "t": 105, "s": [309.291, 178.472, 0], "to": [0.027, -0.066, 0], "ti": [-0.127, 0.168, 0]}, {"i": {"x": 0.833, "y": 0.901}, "o": {"x": 0.167, "y": 0.143}, "t": 106, "s": [309.573, 178.027, 0], "to": [0.127, -0.168, 0], "ti": [-0.078, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.727}, "o": {"x": 0.167, "y": 0.528}, "t": 107, "s": [310.055, 177.464, 0], "to": [0.078, -0.096, 0], "ti": [0.041, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.12}, "t": 108, "s": [310.041, 177.449, 0], "to": [-0.041, 0.033, 0], "ti": [0.093, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.145}, "t": 109, "s": [309.81, 177.664, 0], "to": [-0.093, 0.081, 0], "ti": [0.069, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.234}, "t": 110, "s": [309.48, 177.932, 0], "to": [-0.069, 0.079, 0], "ti": [-0.03, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.73}, "o": {"x": 0.167, "y": 0.154}, "t": 111, "s": [309.393, 178.138, 0], "to": [0.03, 0.031, 0], "ti": [-0.133, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.12}, "t": 112, "s": [309.659, 178.116, 0], "to": [0.133, -0.057, 0], "ti": [-0.146, 0.103, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.197}, "t": 113, "s": [310.191, 177.796, 0], "to": [0.146, -0.103, 0], "ti": [-0.061, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.58}, "o": {"x": 0.167, "y": 0.413}, "t": 114, "s": [310.535, 177.498, 0], "to": [0.061, -0.052, 0], "ti": [-0.001, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.788}, "o": {"x": 0.167, "y": 0.104}, "t": 115, "s": [310.555, 177.481, 0], "to": [0.001, 0.075, 0], "ti": [0.029, -0.194, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.137}, "t": 116, "s": [310.543, 177.946, 0], "to": [-0.029, 0.194, 0], "ti": [0.072, -0.18, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.208}, "t": 117, "s": [310.382, 178.646, 0], "to": [-0.072, 0.18, 0], "ti": [0.122, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.161}, "t": 118, "s": [310.11, 179.026, 0], "to": [-0.122, 0.03, 0], "ti": [0.155, 0.131, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.141}, "t": 119, "s": [309.647, 178.823, 0], "to": [-0.155, -0.131, 0], "ti": [0.115, 0.156, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.227}, "t": 120, "s": [309.177, 178.239, 0], "to": [-0.115, -0.156, 0], "ti": [-0.056, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.147}, "t": 121, "s": [308.958, 177.885, 0], "to": [0.056, -0.057, 0], "ti": [-0.218, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.142}, "t": 122, "s": [309.513, 177.897, 0], "to": [0.218, 0.045, 0], "ti": [-0.142, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.774}, "o": {"x": 0.167, "y": 0.341}, "t": 123, "s": [310.267, 178.156, 0], "to": [0.142, 0.077, 0], "ti": [0.017, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.132}, "t": 124, "s": [310.367, 178.357, 0], "to": [-0.017, -0.032, 0], "ti": [0.031, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.727}, "o": {"x": 0.167, "y": 0.268}, "t": 125, "s": [310.168, 177.963, 0], "to": [-0.031, -0.094, 0], "ti": [-0.015, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.12}, "t": 126, "s": [310.181, 177.794, 0], "to": [0.015, 0.046, 0], "ti": [-0.041, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.774}, "o": {"x": 0.167, "y": 0.268}, "t": 127, "s": [310.259, 178.24, 0], "to": [0.041, 0.084, 0], "ti": [0.004, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.132}, "t": 128, "s": [310.426, 178.299, 0], "to": [-0.004, -0.035, 0], "ti": [0.12, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.135}, "t": 129, "s": [310.233, 178.027, 0], "to": [-0.12, -0.015, 0], "ti": [0.16, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.181}, "t": 130, "s": [309.704, 178.211, 0], "to": [-0.16, 0.065, 0], "ti": [0.033, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.183}, "t": 131, "s": [309.274, 178.42, 0], "to": [-0.033, -0.018, 0], "ti": [-0.11, 0.095, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.15}, "t": 132, "s": [309.505, 178.104, 0], "to": [0.11, -0.095, 0], "ti": [-0.101, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.79}, "o": {"x": 0.167, "y": 0.286}, "t": 133, "s": [309.934, 177.852, 0], "to": [0.101, -0.056, 0], "ti": [-0.01, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.768}, "o": {"x": 0.167, "y": 0.138}, "t": 134, "s": [310.108, 177.766, 0], "to": [0.01, 0.034, 0], "ti": [0.043, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.13}, "t": 135, "s": [309.996, 178.054, 0], "to": [-0.043, 0.137, 0], "ti": [0.041, -0.146, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.209}, "t": 136, "s": [309.848, 178.588, 0], "to": [-0.041, 0.146, 0], "ti": [0.073, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 137, "s": [309.75, 178.929, 0], "to": [-0.073, 0.012, 0], "ti": [0.121, 0.106, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.152}, "t": 138, "s": [309.412, 178.657, 0], "to": [-0.121, -0.106, 0], "ti": [0.053, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.788}, "o": {"x": 0.167, "y": 0.221}, "t": 139, "s": [309.027, 178.291, 0], "to": [-0.053, -0.013, 0], "ti": [-0.069, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.881}, "o": {"x": 0.167, "y": 0.137}, "t": 140, "s": [309.094, 178.581, 0], "to": [0.069, 0.107, 0], "ti": [-0.068, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.643}, "o": {"x": 0.167, "y": 0.276}, "t": 141, "s": [309.441, 178.93, 0], "to": [0.068, 0.031, 0], "ti": [-0.026, 0.143, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.109}, "t": 142, "s": [309.504, 178.765, 0], "to": [0.026, -0.143, 0], "ti": [-0.074, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.227}, "t": 143, "s": [309.598, 178.069, 0], "to": [0.074, -0.138, 0], "ti": [-0.085, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.152}, "t": 144, "s": [309.947, 177.939, 0], "to": [0.085, 0.053, 0], "ti": [0.039, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.181}, "t": 145, "s": [310.11, 178.389, 0], "to": [-0.039, 0.073, 0], "ti": [0.119, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.878}, "o": {"x": 0.167, "y": 0.149}, "t": 146, "s": [309.713, 178.378, 0], "to": [-0.119, -0.072, 0], "ti": [0.021, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.634}, "o": {"x": 0.167, "y": 0.261}, "t": 147, "s": [309.399, 177.954, 0], "to": [-0.021, -0.086, 0], "ti": [-0.173, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.108}, "t": 148, "s": [309.588, 177.862, 0], "to": [0.173, -0.031, 0], "ti": [-0.224, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.212}, "t": 149, "s": [310.435, 177.771, 0], "to": [0.224, -0.053, 0], "ti": [-0.051, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.726}, "o": {"x": 0.167, "y": 0.272}, "t": 150, "s": [310.934, 177.544, 0], "to": [0.051, -0.029, 0], "ti": [0.068, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.12}, "t": 151, "s": [310.74, 177.599, 0], "to": [-0.068, 0.094, 0], "ti": [0.081, -0.121, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.211}, "t": 152, "s": [310.525, 178.111, 0], "to": [-0.081, 0.121, 0], "ti": [0.12, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.143}, "t": 153, "s": [310.252, 178.324, 0], "to": [-0.12, -0.003, 0], "ti": [0.135, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.183}, "t": 154, "s": [309.808, 178.091, 0], "to": [-0.135, -0.009, 0], "ti": [0.061, -0.106, 0]}, {"i": {"x": 0.833, "y": 0.893}, "o": {"x": 0.167, "y": 0.159}, "t": 155, "s": [309.443, 178.269, 0], "to": [-0.061, 0.106, 0], "ti": [-0.005, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.605}, "o": {"x": 0.167, "y": 0.374}, "t": 156, "s": [309.444, 178.727, 0], "to": [0.005, 0.083, 0], "ti": [0.017, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.106}, "t": 157, "s": [309.472, 178.768, 0], "to": [-0.017, -0.073, 0], "ti": [0.041, 0.12, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.235}, "t": 158, "s": [309.34, 178.288, 0], "to": [-0.041, -0.12, 0], "ti": [0.032, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.208}, "t": 159, "s": [309.224, 178.045, 0], "to": [-0.032, -0.015, 0], "ti": [0.011, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.126}, "t": 160, "s": [309.149, 178.196, 0], "to": [-0.011, 0.082, 0], "ti": [-0.072, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.797}, "o": {"x": 0.167, "y": 0.151}, "t": 161, "s": [309.16, 178.539, 0], "to": [0.072, 0.052, 0], "ti": [-0.158, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.142}, "t": 162, "s": [309.583, 178.507, 0], "to": [0.158, -0.061, 0], "ti": [-0.112, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.757}, "o": {"x": 0.167, "y": 0.385}, "t": 163, "s": [310.108, 178.172, 0], "to": [0.112, -0.066, 0], "ti": [0.029, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.127}, "t": 164, "s": [310.252, 178.108, 0], "to": [-0.029, 0.004, 0], "ti": [0.095, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.712}, "o": {"x": 0.167, "y": 0.173}, "t": 165, "s": [309.935, 178.196, 0], "to": [-0.095, -0.01, 0], "ti": [0.103, 0.133, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.117}, "t": 166, "s": [309.68, 178.05, 0], "to": [-0.103, -0.133, 0], "ti": [0.079, 0.172, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.239}, "t": 167, "s": [309.315, 177.395, 0], "to": [-0.079, -0.172, 0], "ti": [-0.037, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.752}, "o": {"x": 0.167, "y": 0.164}, "t": 168, "s": [309.204, 177.019, 0], "to": [0.037, -0.031, 0], "ti": [-0.059, -0.167, 0]}, {"i": {"x": 0.833, "y": 0.878}, "o": {"x": 0.167, "y": 0.126}, "t": 169, "s": [309.538, 177.206, 0], "to": [0.059, 0.167, 0], "ti": [0.007, -0.196, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.264}, "t": 170, "s": [309.561, 178.022, 0], "to": [-0.007, 0.196, 0], "ti": [-0.041, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.143}, "t": 171, "s": [309.498, 178.384, 0], "to": [0.041, -0.01, 0], "ti": [-0.028, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.18}, "t": 172, "s": [309.809, 177.965, 0], "to": [0.028, -0.138, 0], "ti": [0.092, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.156}, "t": 173, "s": [309.664, 177.556, 0], "to": [-0.092, -0.018, 0], "ti": [0.034, -0.126, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [309.258, 177.855, 0], "to": [-0.034, 0.126, 0], "ti": [-0.128, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.156}, "t": 175, "s": [309.46, 178.315, 0], "to": [0.128, 0.104, 0], "ti": [-0.071, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.762}, "o": {"x": 0.167, "y": 0.247}, "t": 176, "s": [310.028, 178.48, 0], "to": [0.071, -0.011, 0], "ti": [0.101, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.128}, "t": 177, "s": [309.885, 178.249, 0], "to": [-0.101, -0.091, 0], "ti": [0.01, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.79}, "o": {"x": 0.167, "y": 0.187}, "t": 178, "s": [309.42, 177.935, 0], "to": [-0.01, -0.08, 0], "ti": [-0.18, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.893}, "o": {"x": 0.167, "y": 0.138}, "t": 179, "s": [309.824, 177.771, 0], "to": [0.18, -0.051, 0], "ti": [-0.113, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.378}, "t": 180, "s": [310.5, 177.63, 0], "to": [0.113, -0.048, 0], "ti": [0.021, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.16}, "t": 181, "s": [310.5, 177.483, 0], "to": [-0.021, 0.003, 0], "ti": [-0.002, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.75}, "o": {"x": 0.167, "y": 0.186}, "t": 182, "s": [310.372, 177.649, 0], "to": [0.002, 0.037, 0], "ti": [-0.023, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.125}, "t": 183, "s": [310.51, 177.704, 0], "to": [0.023, -0.048, 0], "ti": [-0.02, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.6}, "o": {"x": 0.167, "y": 0.23}, "t": 184, "s": [310.508, 177.359, 0], "to": [0.02, -0.051, 0], "ti": [-0.016, -0.13, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.105}, "t": 185, "s": [310.627, 177.396, 0], "to": [0.016, 0.13, 0], "ti": [0.066, -0.211, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.179}, "t": 186, "s": [310.604, 178.138, 0], "to": [-0.066, 0.211, 0], "ti": [0.051, -0.138, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.244}, "t": 187, "s": [310.228, 178.662, 0], "to": [-0.051, 0.138, 0], "ti": [-0.063, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.166}, "t": 188, "s": [310.3, 178.968, 0], "to": [0.063, 0.04, 0], "ti": [-0.017, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.795}, "o": {"x": 0.167, "y": 0.145}, "t": 189, "s": [310.607, 178.901, 0], "to": [0.017, -0.077, 0], "ti": [0.136, 0.108, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.141}, "t": 190, "s": [310.405, 178.506, 0], "to": [-0.136, -0.108, 0], "ti": [0.088, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.715}, "o": {"x": 0.167, "y": 0.329}, "t": 191, "s": [309.79, 178.256, 0], "to": [-0.088, -0.02, 0], "ti": [-0.101, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.118}, "t": 192, "s": [309.874, 178.388, 0], "to": [0.101, 0.045, 0], "ti": [-0.07, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.177}, "t": 193, "s": [310.395, 178.527, 0], "to": [0.07, -0.052, 0], "ti": [0.106, 0.168, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.135}, "t": 194, "s": [310.295, 178.076, 0], "to": [-0.106, -0.168, 0], "ti": [0.118, 0.108, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.384}, "t": 195, "s": [309.756, 177.519, 0], "to": [-0.118, -0.108, 0], "ti": [0.046, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.134}, "t": 196, "s": [309.587, 177.43, 0], "to": [-0.046, 0.041, 0], "ti": [-0.001, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.258}, "t": 197, "s": [309.482, 177.763, 0], "to": [0.001, 0.041, 0], "ti": [-0.101, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.104}, "t": 198, "s": [309.592, 177.675, 0], "to": [0.101, -0.094, 0], "ti": [-0.143, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.225}, "t": 199, "s": [310.09, 177.2, 0], "to": [0.143, -0.066, 0], "ti": [0.025, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.139}, "t": 200, "s": [310.448, 177.277, 0], "to": [-0.025, 0.065, 0], "ti": [0.201, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.154}, "t": 201, "s": [309.937, 177.592, 0], "to": [-0.201, 0.024, 0], "ti": [0.205, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.192}, "t": 202, "s": [309.245, 177.422, 0], "to": [-0.205, -0.021, 0], "ti": [0.072, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.172}, "t": 203, "s": [308.709, 177.465, 0], "to": [-0.072, 0.087, 0], "ti": [-0.106, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.16}, "t": 204, "s": [308.815, 177.944, 0], "to": [0.106, 0.1, 0], "ti": [-0.198, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.147}, "t": 205, "s": [309.345, 178.062, 0], "to": [0.198, -0.033, 0], "ti": [-0.198, 0.089, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.189}, "t": 206, "s": [310.002, 177.745, 0], "to": [0.198, -0.089, 0], "ti": [-0.125, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.707}, "o": {"x": 0.167, "y": 0.261}, "t": 207, "s": [310.534, 177.526, 0], "to": [0.125, -0.039, 0], "ti": [0.042, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.116}, "t": 208, "s": [310.754, 177.51, 0], "to": [-0.042, 0.078, 0], "ti": [0.176, -0.172, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.154}, "t": 209, "s": [310.279, 177.994, 0], "to": [-0.176, 0.172, 0], "ti": [0.085, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.732}, "o": {"x": 0.167, "y": 0.445}, "t": 210, "s": [309.699, 178.54, 0], "to": [-0.085, 0.105, 0], "ti": [-0.076, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.889}, "o": {"x": 0.167, "y": 0.121}, "t": 211, "s": [309.768, 178.626, 0], "to": [0.076, 0.036, 0], "ti": [-0.058, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.577}, "o": {"x": 0.167, "y": 0.339}, "t": 212, "s": [310.153, 178.756, 0], "to": [0.058, 0.009, 0], "ti": [0.05, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.104}, "t": 213, "s": [310.117, 178.677, 0], "to": [-0.05, -0.091, 0], "ti": [0.137, 0.157, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.145}, "t": 214, "s": [309.85, 178.21, 0], "to": [-0.137, -0.157, 0], "ti": [0.145, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.25}, "t": 215, "s": [309.297, 177.735, 0], "to": [-0.145, -0.061, 0], "ti": [0.02, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.165}, "t": 216, "s": [308.983, 177.846, 0], "to": [-0.02, 0.069, 0], "ti": [-0.097, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.163}, "t": 217, "s": [309.178, 178.146, 0], "to": [0.097, 0.053, 0], "ti": [-0.12, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.179}, "t": 218, "s": [309.563, 178.166, 0], "to": [0.12, 0.01, 0], "ti": [-0.093, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.176}, "t": 219, "s": [309.896, 178.208, 0], "to": [0.093, 0.04, 0], "ti": [-0.067, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.189}, "t": 220, "s": [310.12, 178.409, 0], "to": [0.067, 0.059, 0], "ti": [-0.075, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.145}, "t": 221, "s": [310.295, 178.561, 0], "to": [0.075, -0.002, 0], "ti": [-0.098, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.774}, "o": {"x": 0.167, "y": 0.151}, "t": 222, "s": [310.573, 178.397, 0], "to": [0.098, -0.062, 0], "ti": [0.046, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.132}, "t": 223, "s": [310.884, 178.19, 0], "to": [-0.046, -0.091, 0], "ti": [0.233, 0.089, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.152}, "t": 224, "s": [310.295, 177.85, 0], "to": [-0.233, -0.089, 0], "ti": [0.121, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.657}, "o": {"x": 0.167, "y": 0.343}, "t": 225, "s": [309.484, 177.656, 0], "to": [-0.121, -0.008, 0], "ti": [-0.138, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.878}, "o": {"x": 0.167, "y": 0.11}, "t": 226, "s": [309.567, 177.8, 0], "to": [0.138, 0.088, 0], "ti": [-0.143, -0.121, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.262}, "t": 227, "s": [310.31, 178.185, 0], "to": [0.143, 0.121, 0], "ti": [0.048, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.876}, "o": {"x": 0.167, "y": 0.158}, "t": 228, "s": [310.425, 178.527, 0], "to": [-0.048, 0.082, 0], "ti": [0.1, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.252}, "t": 229, "s": [310.02, 178.675, 0], "to": [-0.1, 0.039, 0], "ti": [0.063, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.75}, "o": {"x": 0.167, "y": 0.183}, "t": 230, "s": [309.823, 178.762, 0], "to": [-0.063, 0.013, 0], "ti": [0.089, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.125}, "t": 231, "s": [309.643, 178.754, 0], "to": [-0.089, -0.012, 0], "ti": [0.057, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.777}, "o": {"x": 0.167, "y": 0.251}, "t": 232, "s": [309.29, 178.691, 0], "to": [-0.057, -0.035, 0], "ti": [-0.051, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.133}, "t": 233, "s": [309.3, 178.541, 0], "to": [0.051, -0.032, 0], "ti": [-0.04, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.259}, "t": 234, "s": [309.594, 178.497, 0], "to": [0.04, 0.013, 0], "ti": [-0.012, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.531}, "o": {"x": 0.167, "y": 0.165}, "t": 235, "s": [309.542, 178.616, 0], "to": [0.012, 0.01, 0], "ti": [-0.101, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.101}, "t": 236, "s": [309.663, 178.56, 0], "to": [0.101, -0.086, 0], "ti": [-0.111, 0.147, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.204}, "t": 237, "s": [310.148, 178.099, 0], "to": [0.111, -0.147, 0], "ti": [-0.003, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.703}, "o": {"x": 0.167, "y": 0.269}, "t": 238, "s": [310.332, 177.679, 0], "to": [0.003, -0.073, 0], "ti": [0.1, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.116}, "t": 239, "s": [310.166, 177.664, 0], "to": [-0.1, 0.048, 0], "ti": [0.149, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.171}, "t": 240, "s": [309.73, 177.968, 0], "to": [-0.149, 0.086, 0], "ti": [0.103, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.198}, "t": 241, "s": [309.273, 178.178, 0], "to": [-0.103, 0.089, 0], "ti": [0.033, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.238}, "t": 242, "s": [309.11, 178.499, 0], "to": [-0.033, 0.085, 0], "ti": [0.036, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.149}, "t": 243, "s": [309.075, 178.686, 0], "to": [-0.036, 0.003, 0], "ti": [0.025, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.6}, "o": {"x": 0.167, "y": 0.371}, "t": 244, "s": [308.892, 178.52, 0], "to": [-0.025, -0.025, 0], "ti": [-0.05, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.105}, "t": 245, "s": [308.924, 178.538, 0], "to": [0.05, 0.008, 0], "ti": [-0.046, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.168}, "t": 246, "s": [309.193, 178.566, 0], "to": [0.046, -0.039, 0], "ti": [0.02, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.171}, "t": 247, "s": [309.2, 178.303, 0], "to": [-0.02, -0.078, 0], "ti": [-0.056, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.79}, "o": {"x": 0.167, "y": 0.126}, "t": 248, "s": [309.076, 178.099, 0], "to": [0.056, -0.068, 0], "ti": [-0.185, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.138}, "t": 249, "s": [309.533, 177.893, 0], "to": [0.185, -0.101, 0], "ti": [-0.148, 0.095, 0]}, {"i": {"x": 0.833, "y": 0.789}, "o": {"x": 0.167, "y": 0.297}, "t": 250, "s": [310.187, 177.492, 0], "to": [0.148, -0.095, 0], "ti": [0.029, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.138}, "t": 251, "s": [310.422, 177.322, 0], "to": [-0.029, 0.006, 0], "ti": [0.137, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.175}, "t": 252, "s": [310.014, 177.529, 0], "to": [-0.137, 0.043, 0], "ti": [0.108, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.787}, "o": {"x": 0.167, "y": 0.186}, "t": 253, "s": [309.601, 177.579, 0], "to": [-0.108, 0.044, 0], "ti": [-0.02, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.137}, "t": 254, "s": [309.369, 177.795, 0], "to": [0.02, 0.099, 0], "ti": [-0.177, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.145}, "t": 255, "s": [309.723, 178.172, 0], "to": [0.177, 0.072, 0], "ti": [-0.204, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.192}, "t": 256, "s": [310.432, 178.229, 0], "to": [0.204, 0.039, 0], "ti": [-0.03, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.735}, "o": {"x": 0.167, "y": 0.21}, "t": 257, "s": [310.949, 178.405, 0], "to": [0.03, 0.026, 0], "ti": [0.161, 0.081, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.122}, "t": 258, "s": [310.611, 178.387, 0], "to": [-0.161, -0.081, 0], "ti": [0.214, 0.189, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.154}, "t": 259, "s": [309.981, 177.916, 0], "to": [-0.214, -0.189, 0], "ti": [0.154, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.771}, "o": {"x": 0.167, "y": 0.284}, "t": 260, "s": [309.33, 177.255, 0], "to": [-0.154, -0.083, 0], "ti": [0.021, -0.136, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.131}, "t": 261, "s": [309.057, 177.416, 0], "to": [-0.021, 0.136, 0], "ti": [-0.019, -0.233, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.159}, "t": 262, "s": [309.203, 178.072, 0], "to": [0.019, 0.233, 0], "ti": [-0.011, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.62}, "o": {"x": 0.167, "y": 0.369}, "t": 263, "s": [309.169, 178.812, 0], "to": [0.011, 0.112, 0], "ti": [-0.084, 0.12, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.107}, "t": 264, "s": [309.269, 178.746, 0], "to": [0.084, -0.12, 0], "ti": [-0.147, 0.162, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.195}, "t": 265, "s": [309.672, 178.09, 0], "to": [0.147, -0.162, 0], "ti": [-0.052, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.761}, "o": {"x": 0.167, "y": 0.272}, "t": 266, "s": [310.151, 177.774, 0], "to": [0.052, -0.027, 0], "ti": [0.106, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.128}, "t": 267, "s": [309.987, 177.926, 0], "to": [-0.106, 0.035, 0], "ti": [0.136, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.19}, "t": 268, "s": [309.517, 177.986, 0], "to": [-0.136, -0.01, 0], "ti": [0.086, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.19}, "t": 269, "s": [309.172, 177.863, 0], "to": [-0.086, 0.017, 0], "ti": [0.044, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.155}, "t": 270, "s": [309.002, 178.086, 0], "to": [-0.044, 0.091, 0], "ti": [0.022, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.782}, "o": {"x": 0.167, "y": 0.163}, "t": 271, "s": [308.905, 178.407, 0], "to": [-0.022, -0.004, 0], "ti": [-0.049, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.135}, "t": 272, "s": [308.869, 178.065, 0], "to": [0.049, -0.134, 0], "ti": [-0.12, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.784}, "o": {"x": 0.167, "y": 0.191}, "t": 273, "s": [309.198, 177.604, 0], "to": [0.12, -0.053, 0], "ti": [-0.104, -0.135, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.136}, "t": 274, "s": [309.588, 177.746, 0], "to": [0.104, 0.135, 0], "ti": [-0.082, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.257}, "t": 275, "s": [309.822, 178.413, 0], "to": [0.082, 0.143, 0], "ti": [-0.093, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.152}, "t": 276, "s": [310.078, 178.604, 0], "to": [0.093, -0.013, 0], "ti": [-0.058, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.778}, "o": {"x": 0.167, "y": 0.211}, "t": 277, "s": [310.382, 178.336, 0], "to": [0.058, -0.005, 0], "ti": [0.034, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.134}, "t": 278, "s": [310.426, 178.574, 0], "to": [-0.034, 0.1, 0], "ti": [0.057, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.275}, "t": 279, "s": [310.177, 178.938, 0], "to": [-0.057, 0.037, 0], "ti": [0.047, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.146}, "t": 280, "s": [310.082, 178.796, 0], "to": [-0.047, -0.052, 0], "ti": [0.076, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.878}, "o": {"x": 0.167, "y": 0.161}, "t": 281, "s": [309.894, 178.623, 0], "to": [-0.076, -0.022, 0], "ti": [0.045, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.746}, "o": {"x": 0.167, "y": 0.265}, "t": 282, "s": [309.625, 178.667, 0], "to": [-0.045, -0.009, 0], "ti": [-0.042, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.124}, "t": 283, "s": [309.627, 178.568, 0], "to": [0.042, -0.027, 0], "ti": [-0.017, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.708}, "o": {"x": 0.167, "y": 0.207}, "t": 284, "s": [309.878, 178.505, 0], "to": [0.017, 0.001, 0], "ti": [0.097, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.117}, "t": 285, "s": [309.73, 178.575, 0], "to": [-0.097, 0.002, 0], "ti": [0.041, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.646}, "o": {"x": 0.167, "y": 0.247}, "t": 286, "s": [309.297, 178.518, 0], "to": [-0.041, -0.02, 0], "ti": [-0.148, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.109}, "t": 287, "s": [309.486, 178.456, 0], "to": [0.148, -0.04, 0], "ti": [-0.164, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.222}, "t": 288, "s": [310.186, 178.277, 0], "to": [0.164, -0.083, 0], "ti": [-0.019, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.718}, "o": {"x": 0.167, "y": 0.226}, "t": 289, "s": [310.471, 177.957, 0], "to": [0.019, -0.025, 0], "ti": [0.12, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.118}, "t": 290, "s": [310.301, 178.128, 0], "to": [-0.12, 0.065, 0], "ti": [0.197, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.153}, "t": 291, "s": [309.748, 178.345, 0], "to": [-0.197, -0.018, 0], "ti": [0.156, 0.14, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.181}, "t": 292, "s": [309.12, 178.018, 0], "to": [-0.156, -0.14, 0], "ti": [0.007, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.245}, "t": 293, "s": [308.812, 177.502, 0], "to": [-0.007, -0.101, 0], "ti": [-0.095, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.166}, "t": 294, "s": [309.08, 177.41, 0], "to": [0.095, -0.004, 0], "ti": [-0.058, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.217}, "t": 295, "s": [309.385, 177.477, 0], "to": [0.058, 0.042, 0], "ti": [-0.02, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.6}, "o": {"x": 0.167, "y": 0.219}, "t": 296, "s": [309.43, 177.661, 0], "to": [0.02, 0.019, 0], "ti": [-0.04, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.105}, "t": 297, "s": [309.503, 177.588, 0], "to": [0.04, -0.082, 0], "ti": [-0.115, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.155}, "t": 298, "s": [309.67, 177.172, 0], "to": [0.115, -0.061, 0], "ti": [-0.088, -0.009, 0]}, {"t": 299, "s": [310.195, 177.224, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [51.568, -84.164, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [144.017, 144.017, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [{"i": [[4.062, -5.809], [-0.488, -4.307], [-3.439, 1.749], [0.73, 3.23]], "o": [[-1.648, 2.357], [0.602, 5.309], [6.83, -3.474], [-1.278, -5.653]], "v": [[34.383, -100.269], [27.797, -93.376], [40.188, -89.031], [46.747, -102.061]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [{"i": [[0.806, -1.152], [-0.097, -0.854], [-0.682, 0.347], [0.145, 0.641]], "o": [[-0.327, 0.467], [0.119, 1.053], [1.355, -0.689], [-0.254, -1.121]], "v": [[42.631, -94.766], [41.325, -93.398], [43.783, -92.537], [45.084, -95.121]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [{"i": [[6.911, -9.884], [-0.831, -7.328], [-5.852, 2.977], [1.243, 5.496]], "o": [[-2.804, 4.01], [1.024, 9.032], [11.621, -5.911], [-2.175, -9.619]], "v": [[46.462, -89.937], [35.257, -78.208], [56.34, -70.816], [67.499, -92.986]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [{"i": [[6.911, -9.884], [-0.831, -7.328], [-5.852, 2.977], [1.243, 5.496]], "o": [[-2.804, 4.01], [1.024, 9.032], [11.621, -5.911], [-2.175, -9.619]], "v": [[46.462, -89.937], [35.257, -78.208], [56.34, -70.816], [67.499, -92.986]], "c": true}]}, {"t": 254, "s": [{"i": [[0.95, -1.359], [-0.114, -1.007], [-0.805, 0.409], [0.171, 0.756]], "o": [[-0.385, 0.551], [0.141, 1.242], [1.598, -0.813], [-0.299, -1.322]], "v": [[45.368, -83.126], [43.827, -81.513], [46.726, -80.497], [48.26, -83.545]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "右腮帮子高光", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -21.212, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [29.193, -63.251, 0], "to": [6.014, 1.978, 0], "ti": [-6.014, -1.978, 0]}, {"t": 92, "s": [65.278, -51.385, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-231.836, 85.133, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 56, "s": [29.874, 34.575, 100]}, {"t": 92, "s": [31.592, 41.622, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -78.553], [78.553, 0], [0, 78.553], [-78.553, 0]], "o": [[0, 78.553], [-78.553, 0], [0, -78.553], [78.553, 0]], "v": [[142.233, 0], [0, 142.233], [-142.233, 0], [0, -142.233]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-231.836, 85.133], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 331, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "右腮帮子描边", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2.433, -1.216, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -78.553], [78.553, 0], [0, 78.553], [-78.553, 0]], "o": [[0, 78.553], [-78.553, 0], [0, -78.553], [78.553, 0]], "v": [[142.233, 0], [0, 142.233], [-142.233, 0], [0, -142.233]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [84]}, {"t": 135, "s": [91]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [23]}, {"t": 135, "s": [25]}], "ix": 2}, "o": {"a": 0, "k": -180, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274509804, 0.541176470588, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [16.691]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [18.459]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 85, "s": [21.6]}, {"t": 103, "s": [16]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 331, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "右腮帮子", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [294.507, 305.59, 0], "to": [0, 3.667, 0], "ti": [-0.167, -4.792, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [294.507, 327.59, 0], "to": [0.167, 4.792, 0], "ti": [-4, -3.25, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 63, "s": [295.507, 334.34, 0], "to": [4, 3.25, 0], "ti": [-5.375, -2.208, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [318.507, 347.09, 0], "to": [5.375, 2.208, 0], "ti": [-1.625, -0.375, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [327.757, 347.59, 0], "to": [1.625, 0.375, 0], "ti": [-4.75, -2.958, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 75, "s": [328.257, 349.34, 0], "to": [4.75, 2.958, 0], "ti": [-4.667, -2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 108, "s": [356.257, 365.34, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [356.257, 365.34, 0], "to": [-9.333, -11.917, 0], "ti": [9.667, 17.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [300.257, 293.84, 0], "to": [-9.667, -17.667, 0], "ti": [0.333, 5.75, 0]}, {"t": 138, "s": [298.257, 259.34, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 56, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 58, "s": [20.553, 20.553, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 60, "s": [24.068, 21.08, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 63, "s": [26.178, 24.244, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 68, "s": [36.021, 33.911, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 70, "s": [39.536, 35.669, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 75, "s": [42.348, 39.184, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 108, "s": [56.761, 52.894, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 135, "s": [56.761, 52.894, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 136, "s": [28.99, 28.638, 100]}, {"t": 138, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -78.553], [78.553, 0], [0, 78.553], [-78.553, 0]], "o": [[0, 78.553], [-78.553, 0], [0, -78.553], [78.553, 0]], "v": [[142.233, 0], [0, 142.233], [-142.233, 0], [0, -142.233]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 1, 0.765, 0.02, 0.309, 1, 0.854, 0.218, 0.618, 1, 0.943, 0.417, 0.809, 1, 0.937, 0.354, 1, 1, 0.931, 0.292, 0, 1, 0.342, 1, 0.999, 1, 0.999, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-90.814, 79.982], "ix": 5}, "e": {"a": 0, "k": [241.591, -224.427], "ix": 6}, "t": 1, "nm": "jianbian2qweqw", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 331, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "右眼高光1", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-20.313, -13.514, 0], "to": [-0.017, 0.095, 0], "ti": [0.011, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.294}, "t": 1, "s": [-20.415, -12.942, 0], "to": [-0.011, 0.132, 0], "ti": [-0.002, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.147}, "t": 2, "s": [-20.378, -12.72, 0], "to": [0.002, -0.013, 0], "ti": [0.008, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.632}, "o": {"x": 0.167, "y": 0.268}, "t": 3, "s": [-20.403, -13.023, 0], "to": [-0.008, -0.067, 0], "ti": [-0.073, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.108}, "t": 4, "s": [-20.426, -13.12, 0], "to": [0.073, -0.001, 0], "ti": [-0.126, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.208}, "t": 5, "s": [-19.967, -13.027, 0], "to": [0.126, 0.006, 0], "ti": [0.005, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.155}, "t": 6, "s": [-19.669, -13.084, 0], "to": [-0.005, -0.03, 0], "ti": [0.141, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.139}, "t": 7, "s": [-19.999, -13.206, 0], "to": [-0.141, 0.003, 0], "ti": [0.057, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.266}, "t": 8, "s": [-20.514, -13.068, 0], "to": [-0.057, 0.041, 0], "ti": [-0.106, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.126}, "t": 9, "s": [-20.342, -12.957, 0], "to": [0.106, 0.007, 0], "ti": [-0.081, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.775}, "o": {"x": 0.167, "y": 0.195}, "t": 10, "s": [-19.875, -13.024, 0], "to": [0.081, 0.041, 0], "ti": [0.095, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.132}, "t": 11, "s": [-19.858, -12.711, 0], "to": [-0.095, 0.066, 0], "ti": [0.175, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.165}, "t": 12, "s": [-20.443, -12.63, 0], "to": [-0.175, -0.052, 0], "ti": [0.165, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.176}, "t": 13, "s": [-20.911, -13.023, 0], "to": [-0.165, -0.049, 0], "ti": [0.072, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 14, "s": [-21.433, -12.927, 0], "to": [-0.072, 0.107, 0], "ti": [-0.111, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.165}, "t": 15, "s": [-21.34, -12.379, 0], "to": [0.111, 0.096, 0], "ti": [-0.182, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.154}, "t": 16, "s": [-20.769, -12.35, 0], "to": [0.182, -0.072, 0], "ti": [-0.062, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.729}, "o": {"x": 0.167, "y": 0.328}, "t": 17, "s": [-20.246, -12.809, 0], "to": [0.062, -0.082, 0], "ti": [0.079, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.12}, "t": 18, "s": [-20.4, -12.843, 0], "to": [-0.079, 0.066, 0], "ti": [0.034, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.71}, "o": {"x": 0.167, "y": 0.24}, "t": 19, "s": [-20.718, -12.415, 0], "to": [-0.034, 0.103, 0], "ti": [-0.113, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.117}, "t": 20, "s": [-20.604, -12.224, 0], "to": [0.113, -0.038, 0], "ti": [-0.149, 0.139, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.192}, "t": 21, "s": [-20.038, -12.642, 0], "to": [0.149, -0.139, 0], "ti": [-0.022, 0.155, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.163}, "t": 22, "s": [-19.712, -13.06, 0], "to": [0.022, -0.155, 0], "ti": [0.128, 0.106, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.162}, "t": 23, "s": [-19.903, -13.571, 0], "to": [-0.128, -0.106, 0], "ti": [0.14, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.203}, "t": 24, "s": [-20.478, -13.697, 0], "to": [-0.14, 0.027, 0], "ti": [0.007, -0.131, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.146}, "t": 25, "s": [-20.742, -13.411, 0], "to": [-0.007, 0.131, 0], "ti": [-0.082, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.227}, "t": 26, "s": [-20.52, -12.908, 0], "to": [0.082, 0.111, 0], "ti": [-0.028, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.742}, "o": {"x": 0.167, "y": 0.21}, "t": 27, "s": [-20.25, -12.744, 0], "to": [0.028, -0.002, 0], "ti": [0.075, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.123}, "t": 28, "s": [-20.35, -12.918, 0], "to": [-0.075, -0.075, 0], "ti": [0.109, 0.071, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.191}, "t": 29, "s": [-20.698, -13.192, 0], "to": [-0.109, -0.071, 0], "ti": [0.078, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.671}, "o": {"x": 0.167, "y": 0.223}, "t": 30, "s": [-21.005, -13.344, 0], "to": [-0.078, -0.015, 0], "ti": [0.034, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.112}, "t": 31, "s": [-21.167, -13.285, 0], "to": [-0.034, 0.11, 0], "ti": [-0.054, -0.2, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.154}, "t": 32, "s": [-21.211, -12.687, 0], "to": [0.054, 0.2, 0], "ti": [-0.142, -0.101, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.202}, "t": 33, "s": [-20.841, -12.086, 0], "to": [0.142, 0.101, 0], "ti": [-0.155, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.15}, "t": 34, "s": [-20.36, -12.079, 0], "to": [0.155, -0.07, 0], "ti": [-0.069, 0.12, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.246}, "t": 35, "s": [-19.911, -12.505, 0], "to": [0.069, -0.12, 0], "ti": [0.059, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.765}, "o": {"x": 0.167, "y": 0.165}, "t": 36, "s": [-19.946, -12.798, 0], "to": [-0.059, -0.041, 0], "ti": [0.151, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.129}, "t": 37, "s": [-20.262, -12.748, 0], "to": [-0.151, 0.015, 0], "ti": [0.128, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.309}, "t": 38, "s": [-20.853, -12.707, 0], "to": [-0.128, -0.006, 0], "ti": [-0.021, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.764}, "o": {"x": 0.167, "y": 0.138}, "t": 39, "s": [-21.033, -12.787, 0], "to": [0.021, -0.033, 0], "ti": [-0.12, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.129}, "t": 40, "s": [-20.729, -12.905, 0], "to": [0.12, -0.093, 0], "ti": [-0.103, 0.123, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.222}, "t": 41, "s": [-20.313, -13.343, 0], "to": [0.103, -0.123, 0], "ti": [-0.04, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.271}, "t": 42, "s": [-20.114, -13.645, 0], "to": [0.04, -0.029, 0], "ti": [0.024, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.773}, "o": {"x": 0.167, "y": 0.135}, "t": 43, "s": [-20.074, -13.519, 0], "to": [-0.024, 0.048, 0], "ti": [0.094, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.132}, "t": 44, "s": [-20.255, -13.357, 0], "to": [-0.094, -0.009, 0], "ti": [0.112, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.171}, "t": 45, "s": [-20.639, -13.57, 0], "to": [-0.112, -0.087, 0], "ti": [0.09, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.205}, "t": 46, "s": [-20.926, -13.879, 0], "to": [-0.09, -0.073, 0], "ti": [-0.006, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.15}, "t": 47, "s": [-21.181, -14.008, 0], "to": [0.006, 0.014, 0], "ti": [-0.137, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.136}, "t": 48, "s": [-20.888, -13.794, 0], "to": [0.137, 0.072, 0], "ti": [-0.124, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.189}, "t": 49, "s": [-20.36, -13.574, 0], "to": [0.124, 0.1, 0], "ti": [0.016, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.149}, "t": 50, "s": [-20.143, -13.197, 0], "to": [-0.016, 0.143, 0], "ti": [0.088, -0.115, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.231}, "t": 51, "s": [-20.456, -12.718, 0], "to": [-0.088, 0.115, 0], "ti": [0.057, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.788}, "o": {"x": 0.167, "y": 0.135}, "t": 52, "s": [-20.674, -12.507, 0], "to": [-0.057, -0.049, 0], "ti": [0.047, 0.216, 0]}, {"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.167, "y": 0.137}, "t": 53, "s": [-20.796, -13.011, 0], "to": [-0.047, -0.216, 0], "ti": [0.048, 0.151, 0]}, {"i": {"x": 0.833, "y": 0.686}, "o": {"x": 0.167, "y": 0.35}, "t": 54, "s": [-20.958, -13.803, 0], "to": [-0.048, -0.151, 0], "ti": [-0.026, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.114}, "t": 55, "s": [-21.084, -13.918, 0], "to": [0.026, 0.086, 0], "ti": [-0.154, -0.156, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.164}, "t": 56, "s": [-20.8, -13.287, 0], "to": [0.154, 0.156, 0], "ti": [-0.148, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.201}, "t": 57, "s": [-20.158, -12.982, 0], "to": [0.148, -0.016, 0], "ti": [0.038, 0.133, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.151}, "t": 58, "s": [-19.91, -13.383, 0], "to": [-0.038, -0.133, 0], "ti": [0.127, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.229}, "t": 59, "s": [-20.388, -13.778, 0], "to": [-0.127, -0.039, 0], "ti": [-0.005, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.149}, "t": 60, "s": [-20.672, -13.618, 0], "to": [0.005, 0.081, 0], "ti": [-0.039, -0.161, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.143}, "t": 61, "s": [-20.36, -13.291, 0], "to": [0.039, 0.161, 0], "ti": [0.053, -0.154, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.227}, "t": 62, "s": [-20.437, -12.652, 0], "to": [-0.053, 0.154, 0], "ti": [-0.008, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.164}, "t": 63, "s": [-20.676, -12.367, 0], "to": [0.008, 0.003, 0], "ti": [-0.137, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.135}, "t": 64, "s": [-20.392, -12.635, 0], "to": [0.137, -0.099, 0], "ti": [-0.118, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.311}, "t": 65, "s": [-19.856, -12.963, 0], "to": [0.118, -0.04, 0], "ti": [0, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.686}, "o": {"x": 0.167, "y": 0.149}, "t": 66, "s": [-19.681, -12.872, 0], "to": [0, 0.049, 0], "ti": [0.164, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.113}, "t": 67, "s": [-19.859, -12.666, 0], "to": [-0.164, 0.051, 0], "ti": [0.239, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.186}, "t": 68, "s": [-20.666, -12.567, 0], "to": [-0.239, 0.049, 0], "ti": [0.08, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.778}, "o": {"x": 0.167, "y": 0.247}, "t": 69, "s": [-21.293, -12.372, 0], "to": [-0.08, 0.072, 0], "ti": [-0.098, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.134}, "t": 70, "s": [-21.147, -12.135, 0], "to": [0.098, -0.013, 0], "ti": [-0.054, 0.163, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.151}, "t": 71, "s": [-20.708, -12.451, 0], "to": [0.054, -0.163, 0], "ti": [0.072, 0.145, 0]}, {"i": {"x": 0.833, "y": 0.782}, "o": {"x": 0.167, "y": 0.226}, "t": 72, "s": [-20.82, -13.114, 0], "to": [-0.072, -0.145, 0], "ti": [-0.054, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.135}, "t": 73, "s": [-21.137, -13.322, 0], "to": [0.054, -0.021, 0], "ti": [-0.259, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.141}, "t": 74, "s": [-20.498, -13.243, 0], "to": [0.259, 0.045, 0], "ti": [-0.151, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.702}, "o": {"x": 0.167, "y": 0.389}, "t": 75, "s": [-19.585, -13.051, 0], "to": [0.151, 0.056, 0], "ti": [0.109, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.116}, "t": 76, "s": [-19.59, -12.91, 0], "to": [-0.109, 0.029, 0], "ti": [0.23, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.15}, "t": 77, "s": [-20.24, -12.877, 0], "to": [-0.23, -0.055, 0], "ti": [0.171, 0.128, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.215}, "t": 78, "s": [-20.969, -13.239, 0], "to": [-0.171, -0.128, 0], "ti": [-0.023, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.76}, "o": {"x": 0.167, "y": 0.177}, "t": 79, "s": [-21.266, -13.643, 0], "to": [0.023, -0.064, 0], "ti": [-0.196, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.128}, "t": 80, "s": [-20.831, -13.62, 0], "to": [0.196, 0.076, 0], "ti": [-0.16, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.347}, "t": 81, "s": [-20.089, -13.189, 0], "to": [0.16, 0.097, 0], "ti": [-0.042, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.189}, "t": 82, "s": [-19.869, -13.039, 0], "to": [0.042, -0.009, 0], "ti": [-0.002, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.192}, "t": 83, "s": [-19.835, -13.242, 0], "to": [0.002, -0.06, 0], "ti": [0.014, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.623}, "o": {"x": 0.167, "y": 0.148}, "t": 84, "s": [-19.857, -13.4, 0], "to": [-0.014, 0.007, 0], "ti": [0.059, -0.146, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.107}, "t": 85, "s": [-19.918, -13.199, 0], "to": [-0.059, 0.146, 0], "ti": [0.136, -0.138, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.192}, "t": 86, "s": [-20.213, -12.523, 0], "to": [-0.136, 0.138, 0], "ti": [0.123, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.163}, "t": 87, "s": [-20.733, -12.371, 0], "to": [-0.123, -0.062, 0], "ti": [-0.055, 0.165, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.15}, "t": 88, "s": [-20.954, -12.893, 0], "to": [0.055, -0.165, 0], "ti": [-0.142, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.188}, "t": 89, "s": [-20.403, -13.362, 0], "to": [0.142, -0.001, 0], "ti": [0.028, -0.171, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.15}, "t": 90, "s": [-20.101, -12.9, 0], "to": [-0.028, 0.171, 0], "ti": [0.111, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.268}, "t": 91, "s": [-20.569, -12.334, 0], "to": [-0.111, 0.063, 0], "ti": [-0.032, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.14}, "t": 92, "s": [-20.769, -12.524, 0], "to": [0.032, -0.08, 0], "ti": [-0.097, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.788}, "o": {"x": 0.167, "y": 0.216}, "t": 93, "s": [-20.377, -12.813, 0], "to": [0.097, -0.012, 0], "ti": [-0.052, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.137}, "t": 94, "s": [-20.186, -12.596, 0], "to": [0.052, -0.041, 0], "ti": [-0.029, 0.197, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.139}, "t": 95, "s": [-20.064, -13.056, 0], "to": [0.029, -0.197, 0], "ti": [0.006, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.784}, "o": {"x": 0.167, "y": 0.434}, "t": 96, "s": [-20.015, -13.779, 0], "to": [-0.006, -0.114, 0], "ti": [0.037, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.136}, "t": 97, "s": [-20.101, -13.739, 0], "to": [-0.037, 0.046, 0], "ti": [0.012, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.187}, "t": 98, "s": [-20.235, -13.503, 0], "to": [-0.012, 0.074, 0], "ti": [-0.03, -0.067, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.163}, "t": 99, "s": [-20.171, -13.296, 0], "to": [0.03, 0.067, 0], "ti": [0.023, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.789}, "o": {"x": 0.167, "y": 0.157}, "t": 100, "s": [-20.054, -13.103, 0], "to": [-0.023, 0.026, 0], "ti": [0.108, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.138}, "t": 101, "s": [-20.309, -13.141, 0], "to": [-0.108, 0.003, 0], "ti": [0.062, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.172}, "t": 102, "s": [-20.7, -13.086, 0], "to": [-0.062, 0.069, 0], "ti": [-0.065, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.777}, "o": {"x": 0.167, "y": 0.157}, "t": 103, "s": [-20.683, -12.726, 0], "to": [0.065, 0.082, 0], "ti": [-0.038, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.133}, "t": 104, "s": [-20.311, -12.594, 0], "to": [0.038, -0.094, 0], "ti": [0.113, 0.218, 0]}, {"i": {"x": 0.833, "y": 0.893}, "o": {"x": 0.167, "y": 0.157}, "t": 105, "s": [-20.457, -13.291, 0], "to": [-0.113, -0.218, 0], "ti": [0.093, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.743}, "o": {"x": 0.167, "y": 0.375}, "t": 106, "s": [-20.988, -13.905, 0], "to": [-0.093, -0.08, 0], "ti": [-0.06, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.123}, "t": 107, "s": [-21.017, -13.771, 0], "to": [0.06, 0.07, 0], "ti": [-0.063, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.233}, "t": 108, "s": [-20.628, -13.484, 0], "to": [0.063, 0.006, 0], "ti": [0.015, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.638}, "o": {"x": 0.167, "y": 0.245}, "t": 109, "s": [-20.642, -13.734, 0], "to": [-0.015, -0.051, 0], "ti": [-0.04, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.108}, "t": 110, "s": [-20.719, -13.793, 0], "to": [0.04, 0.047, 0], "ti": [-0.108, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.193}, "t": 111, "s": [-20.403, -13.454, 0], "to": [0.108, 0.061, 0], "ti": [-0.071, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.145}, "t": 112, "s": [-20.07, -13.424, 0], "to": [0.071, -0.072, 0], "ti": [-0.008, 0.11, 0]}, {"i": {"x": 0.833, "y": 0.78}, "o": {"x": 0.167, "y": 0.272}, "t": 113, "s": [-19.975, -13.889, 0], "to": [0.008, -0.11, 0], "ti": [0.003, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.761}, "o": {"x": 0.167, "y": 0.134}, "t": 114, "s": [-20.025, -14.082, 0], "to": [-0.003, 0.025, 0], "ti": [0.032, -0.159, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.128}, "t": 115, "s": [-19.994, -13.739, 0], "to": [-0.032, 0.159, 0], "ti": [0.077, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.247}, "t": 116, "s": [-20.214, -13.131, 0], "to": [-0.077, 0.137, 0], "ti": [0.039, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.711}, "o": {"x": 0.167, "y": 0.235}, "t": 117, "s": [-20.457, -12.919, 0], "to": [-0.039, 0.008, 0], "ti": [0.029, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.117}, "t": 118, "s": [-20.448, -13.086, 0], "to": [-0.029, -0.096, 0], "ti": [0.048, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.202}, "t": 119, "s": [-20.63, -13.495, 0], "to": [-0.048, -0.117, 0], "ti": [-0.017, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.2}, "t": 120, "s": [-20.738, -13.788, 0], "to": [0.017, -0.047, 0], "ti": [-0.063, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.155}, "t": 121, "s": [-20.527, -13.778, 0], "to": [0.063, 0.034, 0], "ti": [0.011, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.174}, "t": 122, "s": [-20.361, -13.582, 0], "to": [-0.011, 0.039, 0], "ti": [0.078, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.151}, "t": 123, "s": [-20.591, -13.546, 0], "to": [-0.078, -0.024, 0], "ti": [0.032, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.183}, "t": 124, "s": [-20.827, -13.725, 0], "to": [-0.032, -0.07, 0], "ti": [-0.041, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.177}, "t": 125, "s": [-20.783, -13.965, 0], "to": [0.041, -0.055, 0], "ti": [-0.036, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.542}, "o": {"x": 0.167, "y": 0.204}, "t": 126, "s": [-20.583, -14.057, 0], "to": [0.036, 0.006, 0], "ti": [0.03, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.102}, "t": 127, "s": [-20.569, -13.926, 0], "to": [-0.03, 0.134, 0], "ti": [0.077, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.787}, "o": {"x": 0.167, "y": 0.244}, "t": 128, "s": [-20.761, -13.256, 0], "to": [-0.077, 0.142, 0], "ti": [0.065, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.137}, "t": 129, "s": [-21.03, -13.074, 0], "to": [-0.065, -0.06, 0], "ti": [-0.014, 0.16, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.182}, "t": 130, "s": [-21.149, -13.615, 0], "to": [0.014, -0.16, 0], "ti": [-0.082, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.212}, "t": 131, "s": [-20.946, -14.035, 0], "to": [0.082, -0.061, 0], "ti": [-0.06, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.538}, "o": {"x": 0.167, "y": 0.287}, "t": 132, "s": [-20.656, -13.982, 0], "to": [0.06, 0.024, 0], "ti": [-0.045, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.102}, "t": 133, "s": [-20.584, -13.893, 0], "to": [0.045, 0.104, 0], "ti": [-0.062, -0.17, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.176}, "t": 134, "s": [-20.387, -13.356, 0], "to": [0.062, 0.17, 0], "ti": [-0.028, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.363}, "t": 135, "s": [-20.21, -12.875, 0], "to": [0.028, 0.061, 0], "ti": [-0.01, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.136}, "t": 136, "s": [-20.217, -12.99, 0], "to": [0.01, -0.058, 0], "ti": [-0.009, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.759}, "o": {"x": 0.167, "y": 0.226}, "t": 137, "s": [-20.148, -13.221, 0], "to": [0.009, -0.017, 0], "ti": [0.031, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.127}, "t": 138, "s": [-20.166, -13.09, 0], "to": [-0.031, 0.056, 0], "ti": [0.038, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.778}, "o": {"x": 0.167, "y": 0.246}, "t": 139, "s": [-20.336, -12.887, 0], "to": [-0.038, 0.017, 0], "ti": [-0.013, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.133}, "t": 140, "s": [-20.395, -12.985, 0], "to": [0.013, -0.047, 0], "ti": [-0.013, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.518}, "o": {"x": 0.167, "y": 0.24}, "t": 141, "s": [-20.26, -13.166, 0], "to": [0.013, -0.021, 0], "ti": [0.105, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.101}, "t": 142, "s": [-20.316, -13.109, 0], "to": [-0.105, 0.016, 0], "ti": [0.11, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.672}, "o": {"x": 0.167, "y": 0.31}, "t": 143, "s": [-20.891, -13.073, 0], "to": [-0.11, -0.012, 0], "ti": [-0.089, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.112}, "t": 144, "s": [-20.975, -13.178, 0], "to": [0.089, -0.027, 0], "ti": [-0.2, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.173}, "t": 145, "s": [-20.355, -13.237, 0], "to": [0.2, -0.009, 0], "ti": [-0.051, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.771}, "o": {"x": 0.167, "y": 0.244}, "t": 146, "s": [-19.775, -13.234, 0], "to": [0.051, 0.012, 0], "ti": [0.131, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.131}, "t": 147, "s": [-20.05, -13.163, 0], "to": [-0.131, 0.032, 0], "ti": [0.167, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.163}, "t": 148, "s": [-20.562, -13.044, 0], "to": [-0.167, 0.061, 0], "ti": [0.075, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.671}, "o": {"x": 0.167, "y": 0.423}, "t": 149, "s": [-21.052, -12.796, 0], "to": [-0.075, 0.033, 0], "ti": [-0.052, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.112}, "t": 150, "s": [-21.013, -12.845, 0], "to": [0.052, -0.056, 0], "ti": [-0.039, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.702}, "o": {"x": 0.167, "y": 0.309}, "t": 151, "s": [-20.738, -13.129, 0], "to": [0.039, -0.063, 0], "ti": [0.062, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.885}, "o": {"x": 0.167, "y": 0.116}, "t": 152, "s": [-20.781, -13.22, 0], "to": [-0.062, 0.016, 0], "ti": [0.074, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.773}, "o": {"x": 0.167, "y": 0.301}, "t": 153, "s": [-21.109, -13.034, 0], "to": [-0.074, 0.042, 0], "ti": [-0.022, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.708}, "o": {"x": 0.167, "y": 0.132}, "t": 154, "s": [-21.225, -12.965, 0], "to": [0.022, 0.007, 0], "ti": [-0.145, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.117}, "t": 155, "s": [-20.978, -12.994, 0], "to": [0.145, -0.008, 0], "ti": [-0.179, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.194}, "t": 156, "s": [-20.356, -13.013, 0], "to": [0.179, -0.015, 0], "ti": [-0.065, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.174}, "t": 157, "s": [-19.903, -13.085, 0], "to": [0.065, -0.079, 0], "ti": [0.085, 0.092, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.158}, "t": 158, "s": [-19.967, -13.486, 0], "to": [-0.085, -0.092, 0], "ti": [0.1, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.209}, "t": 159, "s": [-20.411, -13.636, 0], "to": [-0.1, 0.019, 0], "ti": [0.025, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.183}, "t": 160, "s": [-20.564, -13.375, 0], "to": [-0.025, 0.088, 0], "ti": [0, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.151}, "t": 161, "s": [-20.562, -13.11, 0], "to": [0, 0.099, 0], "ti": [-0.004, -0.133, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.142}, "t": 162, "s": [-20.563, -12.779, 0], "to": [0.004, 0.133, 0], "ti": [-0.049, -0.152, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.158}, "t": 163, "s": [-20.536, -12.311, 0], "to": [0.049, 0.152, 0], "ti": [-0.079, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.219}, "t": 164, "s": [-20.267, -11.866, 0], "to": [0.079, 0.04, 0], "ti": [-0.021, 0.109, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.142}, "t": 165, "s": [-20.06, -12.071, 0], "to": [0.021, -0.109, 0], "ti": [0.054, 0.162, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.149}, "t": 166, "s": [-20.141, -12.519, 0], "to": [-0.054, -0.162, 0], "ti": [0.086, 0.126, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.216}, "t": 167, "s": [-20.384, -13.043, 0], "to": [-0.086, -0.126, 0], "ti": [0.087, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.776}, "o": {"x": 0.167, "y": 0.204}, "t": 168, "s": [-20.658, -13.274, 0], "to": [-0.087, -0.035, 0], "ti": [0.111, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.133}, "t": 169, "s": [-20.904, -13.252, 0], "to": [-0.111, 0.011, 0], "ti": [0.084, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.737}, "o": {"x": 0.167, "y": 0.31}, "t": 170, "s": [-21.327, -13.21, 0], "to": [-0.084, -0.008, 0], "ti": [-0.036, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.767}, "o": {"x": 0.167, "y": 0.122}, "t": 171, "s": [-21.409, -13.298, 0], "to": [0.036, -0.038, 0], "ti": [-0.148, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.13}, "t": 172, "s": [-21.11, -13.436, 0], "to": [0.148, -0.006, 0], "ti": [-0.179, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.156}, "t": 173, "s": [-20.518, -13.337, 0], "to": [0.179, 0.098, 0], "ti": [-0.112, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.887}, "o": {"x": 0.167, "y": 0.22}, "t": 174, "s": [-20.038, -12.845, 0], "to": [0.112, 0.144, 0], "ti": [-0.029, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.555}, "o": {"x": 0.167, "y": 0.318}, "t": 175, "s": [-19.847, -12.471, 0], "to": [0.029, 0.051, 0], "ti": [0.083, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.103}, "t": 176, "s": [-19.865, -12.542, 0], "to": [-0.083, -0.084, 0], "ti": [0.15, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.193}, "t": 177, "s": [-20.348, -12.978, 0], "to": [-0.15, -0.114, 0], "ti": [0.02, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.763}, "o": {"x": 0.167, "y": 0.194}, "t": 178, "s": [-20.767, -13.228, 0], "to": [-0.02, -0.068, 0], "ti": [-0.155, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.128}, "t": 179, "s": [-20.469, -13.388, 0], "to": [0.155, 0.015, 0], "ti": [-0.122, -0.101, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.232}, "t": 180, "s": [-19.836, -13.136, 0], "to": [0.122, 0.101, 0], "ti": [0.012, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.213}, "t": 181, "s": [-19.737, -12.78, 0], "to": [-0.012, 0.032, 0], "ti": [0.075, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.136}, "t": 182, "s": [-19.907, -12.945, 0], "to": [-0.075, -0.072, 0], "ti": [0.083, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.212}, "t": 183, "s": [-20.184, -13.214, 0], "to": [-0.083, -0.031, 0], "ti": [0.035, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.142}, "t": 184, "s": [-20.407, -13.133, 0], "to": [-0.035, -0.045, 0], "ti": [-0.041, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.753}, "o": {"x": 0.167, "y": 0.183}, "t": 185, "s": [-20.396, -13.483, 0], "to": [0.041, -0.079, 0], "ti": [-0.066, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.126}, "t": 186, "s": [-20.161, -13.605, 0], "to": [0.066, 0.074, 0], "ti": [-0.083, -0.189, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.158}, "t": 187, "s": [-19.998, -13.04, 0], "to": [0.083, 0.189, 0], "ti": [-0.08, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.369}, "t": 188, "s": [-19.666, -12.471, 0], "to": [0.08, 0.093, 0], "ti": [0.007, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.708}, "o": {"x": 0.167, "y": 0.153}, "t": 189, "s": [-19.515, -12.485, 0], "to": [-0.007, -0.023, 0], "ti": [0.119, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.117}, "t": 190, "s": [-19.707, -12.607, 0], "to": [-0.119, -0.06, 0], "ti": [0.195, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.151}, "t": 191, "s": [-20.23, -12.847, 0], "to": [-0.195, -0.09, 0], "ti": [0.164, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.241}, "t": 192, "s": [-20.877, -13.148, 0], "to": [-0.164, -0.075, 0], "ti": [-0.006, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.159}, "t": 193, "s": [-21.211, -13.3, 0], "to": [0.006, -0.053, 0], "ti": [-0.184, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.887}, "o": {"x": 0.167, "y": 0.13}, "t": 194, "s": [-20.842, -13.464, 0], "to": [0.184, -0.026, 0], "ti": [-0.161, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.319}, "t": 195, "s": [-20.106, -13.457, 0], "to": [0.161, 0.019, 0], "ti": [-0.006, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.686}, "o": {"x": 0.167, "y": 0.188}, "t": 196, "s": [-19.875, -13.35, 0], "to": [0.006, 0.008, 0], "ti": [0.128, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.113}, "t": 197, "s": [-20.072, -13.408, 0], "to": [-0.128, -0.018, 0], "ti": [0.177, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.177}, "t": 198, "s": [-20.642, -13.46, 0], "to": [-0.177, 0.012, 0], "ti": [0.1, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.713}, "o": {"x": 0.167, "y": 0.195}, "t": 199, "s": [-21.133, -13.334, 0], "to": [-0.1, 0.073, 0], "ti": [-0.11, -0.139, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.117}, "t": 200, "s": [-21.245, -13.023, 0], "to": [0.11, 0.139, 0], "ti": [-0.254, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.177}, "t": 201, "s": [-20.47, -12.5, 0], "to": [0.254, 0.147, 0], "ti": [-0.12, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.756}, "o": {"x": 0.167, "y": 0.252}, "t": 202, "s": [-19.723, -12.142, 0], "to": [0.12, 0.002, 0], "ti": [0.112, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.126}, "t": 203, "s": [-19.747, -12.487, 0], "to": [-0.112, -0.134, 0], "ti": [0.143, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.295}, "t": 204, "s": [-20.392, -12.948, 0], "to": [-0.143, -0.047, 0], "ti": [0.032, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.753}, "o": {"x": 0.167, "y": 0.208}, "t": 205, "s": [-20.605, -12.769, 0], "to": [-0.032, 0.062, 0], "ti": [0.016, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.126}, "t": 206, "s": [-20.584, -12.574, 0], "to": [-0.016, -0.033, 0], "ti": [0.044, 0.137, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.158}, "t": 207, "s": [-20.704, -12.967, 0], "to": [-0.044, -0.137, 0], "ti": [-0.031, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.744}, "o": {"x": 0.167, "y": 0.186}, "t": 208, "s": [-20.851, -13.394, 0], "to": [0.031, -0.087, 0], "ti": [-0.179, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.124}, "t": 209, "s": [-20.517, -13.492, 0], "to": [0.179, 0.018, 0], "ti": [-0.169, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.755}, "o": {"x": 0.167, "y": 0.299}, "t": 210, "s": [-19.776, -13.288, 0], "to": [0.169, 0.044, 0], "ti": [0.051, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.126}, "t": 211, "s": [-19.506, -13.225, 0], "to": [-0.051, 0.007, 0], "ti": [0.157, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.213}, "t": 212, "s": [-20.082, -13.244, 0], "to": [-0.157, 0.002, 0], "ti": [-0.006, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.158}, "t": 213, "s": [-20.449, -13.21, 0], "to": [0.006, 0.018, 0], "ti": [-0.1, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.776}, "o": {"x": 0.167, "y": 0.177}, "t": 214, "s": [-20.043, -13.135, 0], "to": [0.1, 0.059, 0], "ti": [0.051, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.133}, "t": 215, "s": [-19.849, -12.855, 0], "to": [-0.051, 0.108, 0], "ti": [0.129, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.775}, "o": {"x": 0.167, "y": 0.248}, "t": 216, "s": [-20.35, -12.486, 0], "to": [-0.129, 0.08, 0], "ti": [-0.013, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.132}, "t": 217, "s": [-20.625, -12.376, 0], "to": [0.013, -0.05, 0], "ti": [-0.095, 0.13, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.186}, "t": 218, "s": [-20.271, -12.785, 0], "to": [0.095, -0.13, 0], "ti": [0.031, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.166}, "t": 219, "s": [-20.056, -13.159, 0], "to": [-0.031, -0.035, 0], "ti": [0.183, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.126}, "t": 220, "s": [-20.456, -12.993, 0], "to": [-0.183, 0.112, 0], "ti": [0.139, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.777}, "o": {"x": 0.167, "y": 0.392}, "t": 221, "s": [-21.153, -12.488, 0], "to": [-0.139, 0.111, 0], "ti": [-0.036, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.746}, "o": {"x": 0.167, "y": 0.133}, "t": 222, "s": [-21.289, -12.328, 0], "to": [0.036, 0.001, 0], "ti": [-0.191, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.124}, "t": 223, "s": [-20.935, -12.484, 0], "to": [0.191, -0.041, 0], "ti": [-0.2, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.211}, "t": 224, "s": [-20.145, -12.577, 0], "to": [0.2, -0.064, 0], "ti": [-0.029, 0.124, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 225, "s": [-19.735, -12.868, 0], "to": [0.029, -0.124, 0], "ti": [0.125, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 226, "s": [-19.969, -13.322, 0], "to": [-0.125, -0.094, 0], "ti": [0.093, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.166}, "t": 227, "s": [-20.484, -13.43, 0], "to": [-0.093, 0.072, 0], "ti": [-0.035, -0.14, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.196}, "t": 228, "s": [-20.528, -12.893, 0], "to": [0.035, 0.14, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 229, "s": [-20.276, -12.587, 0], "to": [0, -0.032, 0], "ti": [0.11, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.147}, "t": 230, "s": [-20.528, -13.086, 0], "to": [-0.11, -0.184, 0], "ti": [0.053, 0.102, 0]}, {"i": {"x": 0.833, "y": 0.758}, "o": {"x": 0.167, "y": 0.443}, "t": 231, "s": [-20.939, -13.693, 0], "to": [-0.053, -0.102, 0], "ti": [-0.068, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.127}, "t": 232, "s": [-20.846, -13.701, 0], "to": [0.068, 0.008, 0], "ti": [-0.062, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.746}, "o": {"x": 0.167, "y": 0.213}, "t": 233, "s": [-20.529, -13.644, 0], "to": [0.062, 0.04, 0], "ti": [0.007, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.124}, "t": 234, "s": [-20.472, -13.458, 0], "to": [-0.007, 0.1, 0], "ti": [0.003, -0.151, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.154}, "t": 235, "s": [-20.57, -13.044, 0], "to": [-0.003, 0.151, 0], "ti": [-0.05, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.232}, "t": 236, "s": [-20.49, -12.551, 0], "to": [0.05, 0.062, 0], "ti": [-0.061, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.145}, "t": 237, "s": [-20.268, -12.674, 0], "to": [0.061, -0.078, 0], "ti": [-0.081, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.169}, "t": 238, "s": [-20.127, -13.02, 0], "to": [0.081, -0.039, 0], "ti": [-0.108, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.899}, "o": {"x": 0.167, "y": 0.155}, "t": 239, "s": [-19.785, -12.91, 0], "to": [0.108, 0.07, 0], "ti": [-0.059, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.768}, "o": {"x": 0.167, "y": 0.465}, "t": 240, "s": [-19.48, -12.601, 0], "to": [0.059, 0.049, 0], "ti": [0.017, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.13}, "t": 241, "s": [-19.433, -12.616, 0], "to": [-0.017, -0.016, 0], "ti": [0.124, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.104}, "t": 242, "s": [-19.582, -12.696, 0], "to": [-0.124, -0.074, 0], "ti": [0.191, 0.157, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.156}, "t": 243, "s": [-20.178, -13.058, 0], "to": [-0.191, -0.157, 0], "ti": [0.079, 0.173, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.219}, "t": 244, "s": [-20.729, -13.638, 0], "to": [-0.079, -0.173, 0], "ti": [-0.067, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.162}, "t": 245, "s": [-20.654, -14.097, 0], "to": [0.067, -0.012, 0], "ti": [-0.036, -0.155, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.161}, "t": 246, "s": [-20.33, -13.709, 0], "to": [0.036, 0.155, 0], "ti": [0.018, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.797}, "o": {"x": 0.167, "y": 0.443}, "t": 247, "s": [-20.439, -13.168, 0], "to": [-0.018, 0.11, 0], "ti": [-0.013, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.141}, "t": 248, "s": [-20.44, -13.049, 0], "to": [0.013, -0.008, 0], "ti": [0.008, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.2}, "t": 249, "s": [-20.361, -13.214, 0], "to": [-0.008, -0.025, 0], "ti": [0.003, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.749}, "o": {"x": 0.167, "y": 0.16}, "t": 250, "s": [-20.488, -13.198, 0], "to": [-0.003, -0.01, 0], "ti": [-0.061, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.125}, "t": 251, "s": [-20.377, -13.276, 0], "to": [0.061, 0.007, 0], "ti": [-0.045, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.141}, "t": 252, "s": [-20.123, -13.157, 0], "to": [0.045, 0.09, 0], "ti": [-0.02, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.162}, "t": 253, "s": [-20.104, -12.737, 0], "to": [0.02, 0.142, 0], "ti": [-0.031, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.741}, "o": {"x": 0.167, "y": 0.368}, "t": 254, "s": [-20.004, -12.303, 0], "to": [0.031, 0.082, 0], "ti": [0.023, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.583}, "o": {"x": 0.167, "y": 0.123}, "t": 255, "s": [-19.918, -12.244, 0], "to": [-0.023, -0.016, 0], "ti": [0.175, 0.148, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.104}, "t": 256, "s": [-20.143, -12.401, 0], "to": [-0.175, -0.148, 0], "ti": [0.204, 0.238, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.197}, "t": 257, "s": [-20.967, -13.134, 0], "to": [-0.204, -0.238, 0], "ti": [0.014, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.719}, "o": {"x": 0.167, "y": 0.249}, "t": 258, "s": [-21.369, -13.828, 0], "to": [-0.014, -0.101, 0], "ti": [-0.112, -0.163, 0]}, {"i": {"x": 0.833, "y": 0.881}, "o": {"x": 0.167, "y": 0.118}, "t": 259, "s": [-21.052, -13.739, 0], "to": [0.112, 0.163, 0], "ti": [-0.072, -0.216, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.276}, "t": 260, "s": [-20.696, -12.849, 0], "to": [0.072, 0.216, 0], "ti": [-0.03, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.156}, "t": 261, "s": [-20.619, -12.446, 0], "to": [0.03, -0.01, 0], "ti": [-0.054, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.153}, "t": 262, "s": [-20.514, -12.911, 0], "to": [0.054, -0.166, 0], "ti": [0.006, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.768}, "o": {"x": 0.167, "y": 0.247}, "t": 263, "s": [-20.297, -13.441, 0], "to": [-0.006, -0.077, 0], "ti": [0.122, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.13}, "t": 264, "s": [-20.553, -13.375, 0], "to": [-0.122, 0.046, 0], "ti": [0.103, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.78}, "o": {"x": 0.167, "y": 0.23}, "t": 265, "s": [-21.028, -13.165, 0], "to": [-0.103, 0.075, 0], "ti": [-0.014, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.134}, "t": 266, "s": [-21.172, -12.927, 0], "to": [0.014, 0.111, 0], "ti": [-0.056, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.181}, "t": 267, "s": [-20.942, -12.502, 0], "to": [0.056, 0.137, 0], "ti": [-0.056, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.744}, "o": {"x": 0.167, "y": 0.21}, "t": 268, "s": [-20.835, -12.105, 0], "to": [0.056, 0.069, 0], "ti": [-0.03, 0.089, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.124}, "t": 269, "s": [-20.606, -12.085, 0], "to": [0.03, -0.089, 0], "ti": [0.059, 0.154, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.181}, "t": 270, "s": [-20.656, -12.641, 0], "to": [-0.059, -0.154, 0], "ti": [0.015, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.251}, "t": 271, "s": [-20.96, -13.01, 0], "to": [-0.015, -0.055, 0], "ti": [-0.098, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.136}, "t": 272, "s": [-20.745, -12.97, 0], "to": [0.098, 0.007, 0], "ti": [-0.04, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 273, "s": [-20.374, -12.966, 0], "to": [0.04, -0.048, 0], "ti": [0.053, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.179}, "t": 274, "s": [-20.503, -13.258, 0], "to": [-0.053, -0.084, 0], "ti": [0.047, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.226}, "t": 275, "s": [-20.692, -13.468, 0], "to": [-0.047, -0.014, 0], "ti": [0.029, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.644}, "o": {"x": 0.167, "y": 0.175}, "t": 276, "s": [-20.786, -13.343, 0], "to": [-0.029, 0.035, 0], "ti": [-0.068, 0, 0]}, {"i": {"x": 0.833, "y": 0.787}, "o": {"x": 0.167, "y": 0.109}, "t": 277, "s": [-20.865, -13.259, 0], "to": [0.068, 0, 0], "ti": [-0.206, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.137}, "t": 278, "s": [-20.376, -13.341, 0], "to": [0.206, -0.044, 0], "ti": [-0.153, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.33}, "t": 279, "s": [-19.626, -13.525, 0], "to": [0.153, -0.056, 0], "ti": [0.043, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.131}, "t": 280, "s": [-19.458, -13.676, 0], "to": [-0.043, -0.044, 0], "ti": [0.147, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.152}, "t": 281, "s": [-19.886, -13.79, 0], "to": [-0.147, 0.03, 0], "ti": [0.08, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.214}, "t": 282, "s": [-20.339, -13.497, 0], "to": [-0.08, 0.104, 0], "ti": [-0.032, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.15}, "t": 283, "s": [-20.365, -13.168, 0], "to": [0.032, -0.007, 0], "ti": [-0.097, 0.139, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.144}, "t": 284, "s": [-20.146, -13.54, 0], "to": [0.097, -0.139, 0], "ti": [-0.094, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.214}, "t": 285, "s": [-19.781, -13.999, 0], "to": [0.094, -0.033, 0], "ti": [0.028, -0.131, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.131}, "t": 286, "s": [-19.583, -13.737, 0], "to": [-0.028, 0.131, 0], "ti": [0.128, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.197}, "t": 287, "s": [-19.948, -13.211, 0], "to": [-0.128, 0.056, 0], "ti": [0.045, 0.103, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.17}, "t": 288, "s": [-20.352, -13.404, 0], "to": [-0.045, -0.103, 0], "ti": [-0.039, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.752}, "o": {"x": 0.167, "y": 0.18}, "t": 289, "s": [-20.219, -13.83, 0], "to": [0.039, -0.013, 0], "ti": [0.074, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.126}, "t": 290, "s": [-20.116, -13.48, 0], "to": [-0.074, 0.15, 0], "ti": [0.186, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.193}, "t": 291, "s": [-20.66, -12.931, 0], "to": [-0.186, 0.074, 0], "ti": [0.105, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.683}, "o": {"x": 0.167, "y": 0.433}, "t": 292, "s": [-21.233, -13.035, 0], "to": [-0.105, -0.026, 0], "ti": [-0.052, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.113}, "t": 293, "s": [-21.291, -13.086, 0], "to": [0.052, 0.016, 0], "ti": [-0.077, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.708}, "o": {"x": 0.167, "y": 0.296}, "t": 294, "s": [-20.922, -12.939, 0], "to": [0.077, 0.009, 0], "ti": [-0.028, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.117}, "t": 295, "s": [-20.829, -13.035, 0], "to": [0.028, -0.079, 0], "ti": [-0.068, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.166}, "t": 296, "s": [-20.755, -13.414, 0], "to": [0.068, -0.096, 0], "ti": [-0.106, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.18}, "t": 297, "s": [-20.424, -13.613, 0], "to": [0.106, -0.014, 0], "ti": [-0.074, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.153}, "t": 298, "s": [-20.118, -13.498, 0], "to": [0.074, 0.082, 0], "ti": [0.009, -0.106, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.185}, "t": 299, "s": [-19.979, -13.124, 0], "to": [-0.009, 0.106, 0], "ti": [0.058, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.249}, "t": 300, "s": [-20.171, -12.862, 0], "to": [-0.058, 0.041, 0], "ti": [-0.002, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.739}, "o": {"x": 0.167, "y": 0.163}, "t": 301, "s": [-20.327, -12.88, 0], "to": [0.002, -0.006, 0], "ti": [-0.072, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.122}, "t": 302, "s": [-20.162, -12.897, 0], "to": [0.072, 0.04, 0], "ti": [-0.045, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.75}, "o": {"x": 0.167, "y": 0.217}, "t": 303, "s": [-19.896, -12.64, 0], "to": [0.045, 0.077, 0], "ti": [0.035, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.125}, "t": 304, "s": [-19.89, -12.433, 0], "to": [-0.035, -0.034, 0], "ti": [0.065, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.17}, "t": 305, "s": [-20.107, -12.846, 0], "to": [-0.065, -0.138, 0], "ti": [0.047, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.739}, "o": {"x": 0.167, "y": 0.257}, "t": 306, "s": [-20.28, -13.261, 0], "to": [-0.047, -0.043, 0], "ti": [0.094, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.122}, "t": 307, "s": [-20.392, -13.104, 0], "to": [-0.094, 0.036, 0], "ti": [0.079, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.742}, "o": {"x": 0.167, "y": 0.239}, "t": 308, "s": [-20.845, -13.045, 0], "to": [-0.079, -0.023, 0], "ti": [-0.08, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.123}, "t": 309, "s": [-20.867, -13.244, 0], "to": [0.08, -0.051, 0], "ti": [-0.132, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.175}, "t": 310, "s": [-20.363, -13.349, 0], "to": [0.132, -0.077, 0], "ti": [-0.025, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.219}, "t": 311, "s": [-20.072, -13.705, 0], "to": [0.025, -0.099, 0], "ti": [0.013, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.682}, "o": {"x": 0.167, "y": 0.221}, "t": 312, "s": [-20.212, -13.942, 0], "to": [-0.013, -0.016, 0], "ti": [-0.085, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.113}, "t": 313, "s": [-20.153, -13.799, 0], "to": [0.085, 0.055, 0], "ti": [-0.102, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.266}, "t": 314, "s": [-19.702, -13.614, 0], "to": [0.102, 0.054, 0], "ti": [0.005, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.163}, "t": 315, "s": [-19.54, -13.474, 0], "to": [-0.005, 0.043, 0], "ti": [0.046, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.747}, "o": {"x": 0.167, "y": 0.267}, "t": 316, "s": [-19.733, -13.358, 0], "to": [-0.046, 0.011, 0], "ti": [0.045, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.723}, "o": {"x": 0.167, "y": 0.124}, "t": 317, "s": [-19.815, -13.409, 0], "to": [-0.045, 0.008, 0], "ti": [0.089, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.876}, "o": {"x": 0.167, "y": 0.119}, "t": 318, "s": [-20.001, -13.309, 0], "to": [-0.089, 0.075, 0], "ti": [0.046, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.684}, "o": {"x": 0.167, "y": 0.252}, "t": 319, "s": [-20.351, -12.957, 0], "to": [-0.046, 0.085, 0], "ti": [-0.096, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.113}, "t": 320, "s": [-20.277, -12.8, 0], "to": [0.096, -0.051, 0], "ti": [-0.112, 0.155, 0]}, {"i": {"x": 0.833, "y": 0.881}, "o": {"x": 0.167, "y": 0.197}, "t": 321, "s": [-19.776, -13.261, 0], "to": [0.112, -0.155, 0], "ti": [-0.024, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.279}, "t": 322, "s": [-19.602, -13.728, 0], "to": [0.024, -0.112, 0], "ti": [-0.003, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.143}, "t": 323, "s": [-19.63, -13.934, 0], "to": [0.003, 0.015, 0], "ti": [-0.028, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.146}, "t": 324, "s": [-19.582, -13.636, 0], "to": [0.028, 0.113, 0], "ti": [-0.018, -0.13, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.166}, "t": 325, "s": [-19.463, -13.256, 0], "to": [0.018, 0.13, 0], "ti": [0.037, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.765}, "o": {"x": 0.167, "y": 0.214}, "t": 326, "s": [-19.475, -12.857, 0], "to": [-0.037, 0.05, 0], "ti": [0.075, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.789}, "o": {"x": 0.167, "y": 0.129}, "t": 327, "s": [-19.686, -12.958, 0], "to": [-0.075, -0.082, 0], "ti": [0.148, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.138}, "t": 328, "s": [-19.927, -13.352, 0], "to": [-0.148, -0.117, 0], "ti": [0.168, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.243}, "t": 329, "s": [-20.571, -13.659, 0], "to": [-0.168, -0.046, 0], "ti": [0.061, -0.005, 0]}, {"t": 330, "s": [-20.938, -13.629, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [78.828, 82.571, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -11.947], [11.947, 0], [0, 11.947], [-11.947, 0]], "o": [[0, 11.947], [-11.947, 0], [0, -11.947], [11.947, 0]], "v": [[21.631, 0], [0, 21.631], [-21.631, 0], [0, -21.631]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 331, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "右黑眼珠", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.75, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [102.523, 102.033, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -18.714], [18.714, 0], [0, 18.714], [-18.714, 0]], "o": [[0, 18.714], [-18.714, 0], [0, -18.714], [18.714, 0]], "v": [[33.885, 0], [0, 33.885], [-33.885, 0], [0, -33.885]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.086274512112, 0.023529412225, 0.007843137719, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 331, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "右瞳孔", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [6.816]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [16.055]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 138, "s": [10.926]}, {"t": 267, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [278.491, 218.891, 0], "to": [-0.625, 5.833, 0], "ti": [0.625, -8.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [274.741, 253.891, 0], "to": [-0.625, 8.167, 0], "ti": [0.417, -4.25, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 63, "s": [274.741, 267.891, 0], "to": [-0.417, 4.25, 0], "ti": [0.5, 8.417, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [272.241, 279.391, 0], "to": [-0.5, -8.417, 0], "ti": [0.417, 18.625, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [271.741, 217.391, 0], "to": [-0.417, -18.625, 0], "ti": [0.375, -0.958, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 138, "s": [269.741, 167.641, 0], "to": [-0.375, 0.958, 0], "ti": [-0.333, -9.958, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 144, "s": [269.491, 223.141, 0], "to": [0.333, 9.958, 0], "ti": [-2.333, 12.625, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 149, "s": [271.741, 227.391, 0], "to": [0.995, -5.382, 0], "ti": [-1.903, 14.504, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 154, "s": [278.883, 171, 0], "to": [2.561, -19.52, 0], "ti": [-1.697, 5.761, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 161, "s": [283.491, 147.391, 0], "to": [2.958, -10.042, 0], "ti": [-3.083, -2.958, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 177, "s": [301.991, 165.141, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 250, "s": [301.991, 165.141, 0], "to": [-3.917, 8.958, 0], "ti": [3.917, -8.958, 0]}, {"t": 267, "s": [278.491, 218.891, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [39.634, 49.181, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 60, "s": [34.799, 43.181, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 63, "s": [28.105, 29.987, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 68, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 135, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 136, "s": [31.957, 44.093, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 138, "s": [43.374, 49.558, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 144, "s": [31.957, 45.292, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 149, "s": [37.525, 47.495, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 177, "s": [87.996, 87.996, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 250, "s": [87.996, 87.996, 100]}, {"t": 267, "s": [39.634, 49.181, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -25.311], [25.311, 0], [0, 25.311], [-25.311, 0]], "o": [[0, 25.311], [-25.311, 0], [0, -25.311], [25.311, 0]], "v": [[45.83, 0], [0, 45.83], [-45.83, 0], [0, -45.83]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.337254911661, 0.141176477075, 0.074509806931, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 331, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "右眼白", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [325.243, 216.432, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [112, 112, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.827, "y": 0}, "t": 57, "s": [{"i": [[0, -34.313], [34.313, 0], [0, 34.313], [-34.313, 0]], "o": [[0, 34.313], [-34.313, 0], [0, -34.313], [34.313, 0]], "v": [[62.129, 0], [0, 62.129], [-62.129, 0], [0, -62.129]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-3.432, -12.72], [37.171, -3.028], [2.426, 24.615], [-26.535, 1.206]], "o": [[4.172, 15.463], [-32.253, 2.627], [-2.017, -20.466], [33.823, -1.538]], "v": [[31.549, 28.125], [-7.478, 69.16], [-65.812, 37.277], [-17.522, -2.866]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 63, "s": [{"i": [[-6.499, -8.044], [14.736, -3.988], [6.025, 13.77], [-26.11, 2.369]], "o": [[6.181, 7.65], [-6.132, 1.659], [-4.271, -9.76], [13.751, -1.248]], "v": [[3.535, 38.393], [-3.125, 62.576], [-65.031, 55.804], [-31.808, 27.38]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 68, "s": [{"i": [[-8.658, -5.656], [14.962, 5.789], [2.748, -1.761], [-13.627, 7.16]], "o": [[8.636, 5.641], [-11.098, -4.294], [-2.708, 1.735], [5.475, -2.877]], "v": [[-13.987, 56.696], [-18.75, 58.111], [-56.884, 63.393], [-47.768, 57.179]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 135, "s": [{"i": [[-12.234, -5.743], [10.274, 3.892], [3.147, -0.865], [-15.853, 9.716]], "o": [[9.338, 4.383], [-11.128, -4.215], [-0.365, 0.396], [4.694, -2.877]], "v": [[-21.465, 72.21], [-24.107, 73.29], [-53.424, 76.116], [-52.679, 74.701]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 136, "s": [{"i": [[-16.141, -23.823], [15.408, -5.149], [13.366, 9.264], [-19.19, 15.538]], "o": [[13.97, 20.619], [-15.562, 5.2], [-16.659, -11.546], [13.548, -10.969]], "v": [[7.442, -13.839], [-4.687, 32.218], [-56.102, 29.911], [-54.241, -20.388]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 138, "s": [{"i": [[-31.096, -0.162], [6.255, -44.821], [27.091, 2.739], [-2.364, 25.571]], "o": [[28.503, 0.149], [-4.235, 30.343], [-39.454, -3.99], [3.578, -38.703]], "v": [[-15.996, -111.161], [31.696, -34.076], [-19.942, 22.322], [-69.866, -49.406]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 144, "s": [{"i": [[-34.977, -5.201], [3.801, -30.372], [33.3, 4.9], [-0.502, 20.115]], "o": [[39.44, 5.865], [-3.804, 30.394], [-40.543, -5.966], [0.899, -36.024]], "v": [[-1.487, -57.366], [49.107, 15.924], [-16.147, 67.857], [-70.536, 2.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 149, "s": [{"i": [[-35.352, -0.845], [7.819, -34.613], [33.564, 2.963], [-3.565, 21.565]], "o": [[39.886, 0.954], [-7.348, 32.528], [-45.005, -3.973], [5.287, -31.982]], "v": [[1.638, -45.759], [59.375, 22.174], [-13.022, 65.625], [-68.527, 2.826]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [{"i": [[-31.147, 1.304], [3.173, -33.541], [30.526, 0.551], [-1.495, 24.585]], "o": [[38.146, -2.019], [-2.808, 32.988], [-43.639, -0.419], [2.459, -32.081]], "v": [[-7.5, -96.134], [52.113, -31.685], [-8.134, 19.896], [-66.766, -41.384]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 161, "s": [{"i": [[-24.846, 4.525], [-3.788, -31.934], [25.975, -3.064], [1.607, 29.111]], "o": [[35.538, -6.473], [3.995, 33.678], [-41.592, 4.906], [-1.779, -32.229]], "v": [[-21.576, -120.313], [40.848, -61.085], [-1.192, 2.679], [-64.509, -56.326]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 177, "s": [{"i": [[-26.388, 2.722], [-7.891, -24.318], [27.948, -3.372], [-0.007, 25.152]], "o": [[25.377, -2.618], [10.497, 32.352], [-41.213, 4.972], [0.006, -23.078]], "v": [[-21.13, -91.741], [39.732, -49.032], [5.951, 10.045], [-59.375, -46.058]], "c": true}]}, {"i": {"x": 0.833, "y": 0.775}, "o": {"x": 0.167, "y": 0}, "t": 250, "s": [{"i": [[-26.388, 2.722], [-7.891, -24.318], [27.948, -3.372], [-0.007, 25.152]], "o": [[25.377, -2.618], [10.497, 32.352], [-41.213, 4.972], [0.006, -23.078]], "v": [[-21.13, -91.741], [39.732, -49.032], [5.951, 10.045], [-59.375, -46.058]], "c": true}]}, {"i": {"x": 0.833, "y": 0.775}, "o": {"x": 0.167, "y": 0.225}, "t": 254.436, "s": [{"i": [[-17.844, -9.087], [2.796, -20.704], [21.628, 7.114], [-14.09, 22.347]], "o": [[21.888, 12.118], [-3.007, 22.823], [-37.747, -10.074], [12.867, -18.747]], "v": [[-0.9, -71.209], [28.907, -18.744], [-21.175, 7.233], [-53.984, -58.893]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.225}, "t": 257.391, "s": [{"i": [[-13.29, -15.661], [13.058, -12.247], [17.357, 13.442], [-22.77, 15.17]], "o": [[12.781, 15.714], [-13.307, 15.344], [-20.143, -15.353], [23.436, -15.187]], "v": [[20.198, -46.203], [20.01, 6.146], [-43.467, 4.923], [-45.081, -54.395]], "c": true}]}, {"t": 267, "s": [{"i": [[0, -34.313], [34.313, 0], [0, 34.313], [-34.313, 0]], "o": [[0, 34.313], [-34.313, 0], [0, -34.313], [34.313, 0]], "v": [[62.129, 0], [0, 62.129], [-62.129, 0], [0, -62.129]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.729411780834, 0.415686279535, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 331, "st": 0, "bm": 0}]}, {"id": "comp_2", "nm": "花朵脸 嘴巴 ", "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 9", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 141, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 146, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 153, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 252, "s": [100]}, {"t": 257, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[2.124, -0.897], [-1.552, -1.07], [-0.054, 2.943]], "o": [[-2.408, 1.017], [1.635, 1.128], [0.025, -1.355]], "v": [[-10.842, 69.457], [-11.323, 76.361], [-6.775, 71.491]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 186, "s": [{"i": [[9.922, -4.189], [-7.25, -5], [-0.25, 13.75]], "o": [[-11.25, 4.75], [7.64, 5.269], [0.115, -6.328]], "v": [[-7.75, 78.5], [-10, 110.75], [11.25, 88]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 247, "s": [{"i": [[9.922, -4.189], [-7.25, -5], [-0.25, 13.75]], "o": [[-11.25, 4.75], [7.64, 5.269], [0.115, -6.328]], "v": [[-7.75, 78.5], [-10, 110.75], [11.25, 88]], "c": true}]}, {"t": 260, "s": [{"i": [[2.124, -0.897], [-1.552, -1.07], [-0.054, 2.943]], "o": [[-2.408, 1.017], [1.635, 1.128], [0.025, -1.355]], "v": [[-10.842, 69.457], [-11.323, 76.361], [-6.775, 71.491]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 88, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [45.172, 104.342], "ix": 5}, "e": {"a": 0, "k": [-21.961, 91.283], "ix": 6}, "t": 1, "nm": "jianbian10", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 390, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "形状图层 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [242.998, 326.574, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-13.002, 70.574, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 138, "s": [{"i": [[1.558, 0], [0.167, -1.352], [-5.398, 0], [0.056, 1.09]], "o": [[-1.224, 0], [-0.182, 1.477], [5.285, 0], [-0.076, -1.491]], "v": [[-12.999, 68.528], [-22.068, 69.797], [-12.832, 72.625], [-3.931, 69.866]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 146, "s": [{"i": [[3.5, 0], [2.125, -12.5], [-27.625, 0], [1.375, 11.125]], "o": [[-2.75, 0], [-2.244, 13.2], [27, 0], [-1.658, -13.412]], "v": [[-14, 50.375], [-43.75, 62.5], [-13.75, 108.875], [15, 62.5]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 178, "s": [{"i": [[18.25, 0], [0, -16.75], [-29, 0], [0, 11.21]], "o": [[-18, 0], [0, 22.363], [33, 0], [0, -16.75]], "v": [[-14, 50.125], [-53.5, 69.25], [-13.75, 123.625], [25.625, 67.75]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 246.656, "s": [{"i": [[18.25, 0], [0, -16.75], [-29, 0], [0, 11.21]], "o": [[-18, 0], [0, 22.363], [33, 0], [0, -16.75]], "v": [[-14, 50.125], [-53.5, 69.25], [-13.75, 123.625], [25.625, 67.75]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 256.531, "s": [{"i": [[3.5, 0], [2.125, -12.5], [-27.625, 0], [1.375, 11.125]], "o": [[-2.75, 0], [-2.244, 13.2], [27, 0], [-1.658, -13.412]], "v": [[-14, 50.375], [-43.75, 62.5], [-13.75, 108.875], [15, 62.5]], "c": true}]}, {"t": 259, "s": [{"i": [[1.558, 0], [0.167, -1.352], [-5.398, 0], [0.056, 1.09]], "o": [[-1.224, 0], [-0.182, 1.477], [5.285, 0], [-0.076, -1.491]], "v": [[-12.999, 68.528], [-22.068, 69.797], [-12.832, 72.625], [-3.931, 69.866]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.764705882353, 0.192156862745, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 136, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 139, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 258.076, "s": [100]}, {"t": 259, "s": [0]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "形状图层 5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 115, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 140, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 142, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 253.754, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 260.162, "s": [0]}, {"t": 262.087890625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [242.064, 355.663, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-13.936, 83.163, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [148.63, 143.822, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 139, "s": [{"i": [[0.321, 0.474], [0, 0], [0.392, -0.321], [-0.875, 0.027]], "o": [[-0.318, -0.471], [0, 0], [-0.481, 0.394], [0.942, -0.029]], "v": [[-12.365, 62.219], [-13.394, 62.146], [-14.363, 62.186], [-13.394, 63.843]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 143, "s": [{"i": [[2.603, 3.85], [0, 0], [3.18, -2.606], [-7.104, 0.217]], "o": [[-2.584, -3.823], [0, 0], [-3.905, 3.2], [7.644, -0.233]], "v": [[-5.228, 63.947], [-13.58, 63.35], [-21.444, 63.676], [-13.58, 77.125]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 148, "s": [{"i": [[4.241, 6.273], [0, 0], [5.181, -4.245], [-11.575, 0.353]], "o": [[-4.211, -6.228], [0, 0], [-6.362, 5.213], [12.454, -0.38]], "v": [[-0.407, 66.42], [-14.015, 65.448], [-26.827, 65.978], [-14.015, 87.891]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 168, "s": [{"i": [[6, 8.875], [0, 0], [7.329, -6.006], [-16.375, 0.5]], "o": [[-5.957, -8.811], [0, 0], [-9, 7.375], [17.619, -0.538]], "v": [[5.375, 70.125], [-13.875, 68.75], [-32, 69.5], [-13.875, 100.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 246.039, "s": [{"i": [[6, 8.875], [0, 0], [7.329, -6.006], [-16.375, 0.5]], "o": [[-5.957, -8.811], [0, 0], [-9, 7.375], [17.619, -0.538]], "v": [[5.375, 70.125], [-13.875, 68.75], [-32, 69.5], [-13.875, 100.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 252.211, "s": [{"i": [[4.241, 6.273], [0, 0], [5.181, -4.245], [-11.575, 0.353]], "o": [[-4.211, -6.228], [0, 0], [-6.362, 5.213], [12.454, -0.38]], "v": [[-0.407, 66.42], [-14.015, 65.448], [-26.827, 65.978], [-14.015, 87.891]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [{"i": [[3.102, 4.589], [0, 0], [3.789, -3.105], [-8.466, 0.259]], "o": [[-3.08, -4.556], [0, 0], [-4.653, 3.813], [9.109, -0.278]], "v": [[-3.76, 64.7], [-13.712, 63.989], [-23.084, 64.377], [-13.677, 80.234]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [{"i": [[2.786, 4.121], [0, 0], [3.403, -2.789], [-7.604, 0.232]], "o": [[-2.766, -4.092], [0, 0], [-4.179, 3.425], [8.181, -0.25]], "v": [[-4.69, 64.223], [-13.629, 63.584], [-22.045, 63.933], [-13.615, 73.113]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [{"i": [[-0.361, 1.232], [0, 0], [2.495, -3.337], [-7.201, 0.22]], "o": [[1.315, -4.489], [0, 0], [-0.997, 1.333], [7.748, -0.237]], "v": [[-5.124, 64], [-13.59, 63.396], [-21.561, 63.726], [-13.531, 68.002]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 260, "s": [{"i": [[2.248, 3.325], [0, 0], [2.746, -2.25], [-6.135, 0.187]], "o": [[-2.232, -3.301], [0, 0], [-3.372, 2.763], [6.601, -0.202]], "v": [[-6.339, 63.678], [-13.551, 63.163], [-20.341, 63.444], [-13.131, 66.801]], "c": true}]}, {"t": 261.4453125, "s": [{"i": [[0.321, 0.474], [0, 0], [0.392, -0.321], [-0.875, 0.027]], "o": [[-0.318, -0.471], [0, 0], [-0.481, 0.394], [0.942, -0.029]], "v": [[-12.365, 62.219], [-13.394, 62.146], [-14.363, 62.186], [-13.394, 63.843]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.877, 0, 0, 0.5, 0.939, 0.154, 0.154, 1, 1, 0.307, 0.307, 0, 1, 0.342, 1, 0.999, 1, 0.999, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-12.783, 64.229], "ix": 5}, "e": {"a": 0, "k": [-12.051, 97.517], "ix": 6}, "t": 1, "nm": "jianbian1hhhj", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "形状图层 8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 143, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 146, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 252.52, "s": [100]}, {"t": 259.51953125, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 248.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 116, "s": [{"i": [[0, 0], [-2.84, -1.569], [-1.428, 1.428], [0, 0]], "o": [[0, 0], [2.84, 1.569], [1.457, -1.457], [0, 0]], "v": [[-19.754, 67.237], [-18.035, 72.431], [-7.984, 72.207], [-6.938, 67.125]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 145, "s": [{"i": [[0, 0], [-5.365, -2.965], [-2.697, 2.697], [0, 0]], "o": [[0, 0], [5.365, 2.965], [2.753, -2.753], [0, 0]], "v": [[-26.363, 62.672], [-23.116, 72.483], [-4.128, 72.06], [-2.151, 62.46]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 161, "s": [{"i": [[0, 0], [-9.5, -5.25], [-4.776, 4.776], [0, 0]], "o": [[0, 0], [9.5, 5.25], [4.875, -4.875], [0, 0]], "v": [[-35.375, 60], [-29.625, 77.375], [4, 76.625], [7.5, 59.625]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 248.199, "s": [{"i": [[0, 0], [-9.5, -5.25], [-4.776, 4.776], [0, 0]], "o": [[0, 0], [9.5, 5.25], [4.875, -4.875], [0, 0]], "v": [[-35.375, 60], [-29.625, 77.375], [4, 76.625], [7.5, 59.625]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 253.139, "s": [{"i": [[0, 0], [-5.365, -2.965], [-2.697, 2.697], [0, 0]], "o": [[0, 0], [5.365, 2.965], [2.753, -2.753], [0, 0]], "v": [[-26.363, 62.672], [-23.116, 72.483], [-4.128, 72.06], [-2.151, 62.46]], "c": true}]}, {"t": 262.087890625, "s": [{"i": [[0, 0], [-2.84, -1.569], [-1.428, 1.428], [0, 0]], "o": [[0, 0], [2.84, 1.569], [1.457, -1.457], [0, 0]], "v": [[-19.754, 67.237], [-18.035, 72.431], [-7.984, 72.207], [-6.938, 67.125]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 357, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "形状图层 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 139, "s": [100]}, {"t": 142, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [254.216, 329.516, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-23.909, 73.516, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [{"i": [[0, 0], [-0.719, -1.062], [0, 0]], "o": [[0, 0], [0.562, 1.25], [0, 0]], "v": [[-24.125, 66.094], [-22.25, 68.438], [-21.281, 71.031]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 71, "s": [{"i": [[0, 0], [-0.656, -6], [0, 0]], "o": [[0, 0], [0.839, 7.675], [0, 0]], "v": [[-26.469, 62.281], [-21.469, 72.5], [-24.5, 84.75]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[0, 0], [-0.302, -11.751], [0, 0]], "o": [[0, 0], [0.219, 8.5], [0, 0]], "v": [[-30.219, 54.531], [-21.469, 72.5], [-26.75, 89]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [-0.302, -11.751], [0, 0]], "o": [[0, 0], [0.219, 8.5], [0, 0]], "v": [[-30.219, 54.531], [-21.469, 72.5], [-26.75, 89]], "c": false}]}, {"t": 140, "s": [{"i": [[0, 0], [-0.719, -1.062], [0, 0]], "o": [[0, 0], [0.562, 1.25], [0, 0]], "v": [[-24.125, 66.094], [-22.25, 68.438], [-21.281, 71.031]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.764705882353, 0.192156862745, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 13, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [0]}, {"t": 66, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 363, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 139, "s": [100]}, {"t": 142, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [232.091, 329.516, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-23.909, 73.516, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [{"i": [[0, 0], [-0.719, -1.062], [0, 0]], "o": [[0, 0], [0.562, 1.25], [0, 0]], "v": [[-24.125, 66.094], [-22.25, 68.438], [-21.281, 71.031]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 71, "s": [{"i": [[0, 0], [-0.656, -6], [0, 0]], "o": [[0, 0], [0.839, 7.675], [0, 0]], "v": [[-26.469, 62.281], [-21.469, 72.5], [-24.5, 84.75]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[0, 0], [-0.302, -11.751], [0, 0]], "o": [[0, 0], [0.219, 8.5], [0, 0]], "v": [[-30.219, 54.531], [-21.469, 72.5], [-26.75, 89]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [-0.302, -11.751], [0, 0]], "o": [[0, 0], [0.219, 8.5], [0, 0]], "v": [[-30.219, 54.531], [-21.469, 72.5], [-26.75, 89]], "c": false}]}, {"t": 140, "s": [{"i": [[0, 0], [-0.719, -1.062], [0, 0]], "o": [[0, 0], [0.562, 1.25], [0, 0]], "v": [[-24.125, 66.094], [-22.25, 68.438], [-21.281, 71.031]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.764705882353, 0.192156862745, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 13, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [0]}, {"t": 66, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 363, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "形状图层 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [242.998, 326.574, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-13.002, 70.574, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 138, "s": [{"i": [[1.558, 0], [0.167, -1.352], [-5.398, 0], [0.056, 1.09]], "o": [[-1.224, 0], [-0.182, 1.477], [5.285, 0], [-0.076, -1.491]], "v": [[-12.999, 68.528], [-22.068, 69.797], [-12.832, 72.625], [-3.931, 69.866]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 146, "s": [{"i": [[3.5, 0], [2.125, -12.5], [-27.625, 0], [1.375, 11.125]], "o": [[-2.75, 0], [-2.244, 13.2], [27, 0], [-1.658, -13.412]], "v": [[-14, 50.375], [-43.75, 62.5], [-13.75, 108.875], [15, 62.5]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 178, "s": [{"i": [[18.25, 0], [0, -16.75], [-29, 0], [0, 11.21]], "o": [[-18, 0], [0, 22.363], [33, 0], [0, -16.75]], "v": [[-14, 50.125], [-53.5, 69.25], [-13.75, 123.625], [25.625, 67.75]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 246.656, "s": [{"i": [[18.25, 0], [0, -16.75], [-29, 0], [0, 11.21]], "o": [[-18, 0], [0, 22.363], [33, 0], [0, -16.75]], "v": [[-14, 50.125], [-53.5, 69.25], [-13.75, 123.625], [25.625, 67.75]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 256.531, "s": [{"i": [[3.5, 0], [2.125, -12.5], [-27.625, 0], [1.375, 11.125]], "o": [[-2.75, 0], [-2.244, 13.2], [27, 0], [-1.658, -13.412]], "v": [[-14, 50.375], [-43.75, 62.5], [-13.75, 108.875], [15, 62.5]], "c": true}]}, {"t": 259, "s": [{"i": [[1.558, 0], [0.167, -1.352], [-5.398, 0], [0.056, 1.09]], "o": [[-1.224, 0], [-0.182, 1.477], [5.285, 0], [-0.076, -1.491]], "v": [[-12.999, 68.528], [-22.068, 69.797], [-12.832, 72.625], [-3.931, 69.866]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.764705882353, 0.192156862745, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.407, 0, 0, 0.5, 0.703, 0.154, 0.154, 1, 1, 0.307, 0.307, 0, 1, 0.342, 1, 0.999, 1, 0.999, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-10.875, 51.625], "ix": 5}, "e": {"a": 0, "k": [-12.5, 200], "ix": 6}, "t": 1, "nm": "jianbian12", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 136, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 139, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 258.852, "s": [100]}, {"t": 260.13671875, "s": [0]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 247.322, "s": [{"i": [[18.25, 0], [0, -16.75], [-29, 0], [0, 11.21]], "o": [[-18, 0], [0, 22.363], [33, 0], [0, -16.75]], "v": [[-14, 50.125], [-53.5, 69.25], [-13.75, 123.625], [25.625, 67.75]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 267.865, "s": [{"i": [[3.5, 0], [2.125, -12.5], [-27.625, 0], [1.375, 11.125]], "o": [[-2.75, 0], [-2.244, 13.2], [27, 0], [-1.658, -13.412]], "v": [[-14, 50.375], [-43.75, 62.5], [-13.75, 108.875], [15, 62.5]], "c": true}]}, {"t": 273, "s": [{"i": [[1.558, 0], [0.167, -1.352], [-5.398, 0], [0.056, 1.09]], "o": [[-1.224, 0], [-0.182, 1.477], [5.285, 0], [-0.076, -1.491]], "v": [[-12.999, 68.528], [-22.068, 69.797], [-12.832, 72.625], [-3.931, 69.866]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 370, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "嘴巴 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 134, "s": [100]}, {"i": {"x": [0.806], "y": [0.919]}, "o": {"x": [0.326], "y": [0]}, "t": 139, "s": [0]}, {"i": {"x": [0.696], "y": [1]}, "o": {"x": [0.362], "y": [0.234]}, "t": 268, "s": [100]}, {"t": 270, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": -17.002, "ix": 10}, "p": {"a": 0, "k": [243.119, 327.591, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-11.205, 22.155, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[0, 0], [11.582, 4.172], [0, 0], [6.024, 2.88], [0, 0]], "o": [[-1.77, 4.572], [-8.873, -3.196], [-8.094, 7.004], [-10.527, -5.034], [0, 0]], "v": [[32.004, 5.964], [10.413, 14.704], [-1.433, -0.505], [-23.921, 2.873], [-31.673, -15.839]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 65, "s": [{"i": [[0, 0], [5.166, 1.861], [0, 0], [2.687, 1.285], [0, 0]], "o": [[-0.79, 2.039], [-3.958, -1.426], [-3.61, 3.125], [-4.696, -2.246], [0, 0]], "v": [[-4.776, 38.157], [-14.408, 42.056], [-19.692, 35.271], [-29.724, 36.778], [-33.182, 28.431]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 67, "s": [{"i": [[0, 0], [3.971, 1.024], [0, 0], [2.687, 1.285], [0, 0]], "o": [[-0.79, 2.039], [-2.826, -0.729], [0, 0], [-4.696, -2.246], [0, 0]], "v": [[-4.776, 38.157], [-13.543, 41.934], [-20.762, 39.853], [-29.724, 36.778], [-33.182, 28.431]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 129, "s": [{"i": [[0, 0], [3.971, 1.024], [0, 0], [2.687, 1.285], [0, 0]], "o": [[-0.79, 2.039], [-2.826, -0.729], [0, 0], [-4.696, -2.246], [0, 0]], "v": [[-4.776, 38.157], [-13.543, 41.934], [-20.762, 39.853], [-29.724, 36.778], [-33.182, 28.431]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 139, "s": [{"i": [[0, 0], [3.463, 0.703], [0, 0], [3.515, 1.424], [0, 0]], "o": [[-3.267, 0.525], [-2.86, -0.581], [0, 0], [-2.77, -1.122], [0, 0]], "v": [[-3.803, 42.37], [-13.543, 41.934], [-19.598, 40.374], [-27.581, 37.709], [-34.29, 33.497]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 261.039, "s": [{"i": [[0, 0], [3.463, 0.703], [0, 0], [3.515, 1.424], [0, 0]], "o": [[-3.267, 0.525], [-2.86, -0.581], [0, 0], [-2.77, -1.122], [0, 0]], "v": [[-3.803, 42.37], [-13.543, 41.934], [-19.598, 40.374], [-27.581, 37.709], [-34.29, 33.497]], "c": false}]}, {"t": 271, "s": [{"i": [[0, 0], [11.582, 4.172], [0, 0], [6.024, 2.88], [0, 0]], "o": [[-1.77, 4.572], [-8.873, -3.196], [-8.094, 7.004], [-10.527, -5.034], [0, 0]], "v": [[32.004, 5.964], [10.413, 14.704], [-1.433, -0.505], [-23.921, 2.873], [-31.673, -15.839]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.764705882353, 0.192156862745, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 20, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [59.247, 59.247], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 438, "st": 0, "bm": 0}]}, {"id": "comp_3", "nm": "花朵脸 花瓣", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "绿花瓣高光", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-49.899, -31.343, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-46.407, 27.377]], "o": [[0, 0], [35.972, -21.221]], "v": [[-24.505, 37.404], [24.505, -37.404]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.694117665291, 1, 0.662745118141, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.951, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "绿花瓣描边", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 70, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-26.941, -12.522, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.902, "s": [{"i": [[0, 0], [13.954, 3.678]], "o": [[-9.724, -9.507], [0, 0]], "v": [[17.759, 9.889], [-17.759, -9.889]], "c": false}]}, {"t": 149, "s": [{"i": [[0, 0], [11.55, -1.887]], "o": [[-3.467, -13.164], [0, 0]], "v": [[30.259, 17.389], [-17.759, -9.889]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.247058823705, 0.701960802078, 0.835294127464, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-20备份", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "绿花瓣", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [291.434, 297.144, 0], "to": [-0.472, -0.302, 0], "ti": [0.948, 0.599, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [288.601, 295.329, 0], "to": [-0.948, -0.599, 0], "ti": [0.957, 0.586, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [285.744, 293.552, 0], "to": [-0.957, -0.586, 0], "ti": [0.963, 0.575, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [282.862, 291.811, 0], "to": [-0.963, -0.575, 0], "ti": [0.968, 0.566, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [279.966, 290.102, 0], "to": [-0.968, -0.566, 0], "ti": [0.972, 0.559, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [277.057, 288.417, 0], "to": [-0.972, -0.559, 0], "ti": [0.976, 0.554, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [274.133, 286.747, 0], "to": [-0.976, -0.554, 0], "ti": [0.978, 0.55, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [271.199, 285.091, 0], "to": [-0.978, -0.55, 0], "ti": [0.979, 0.546, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [268.262, 283.448, 0], "to": [-0.979, -0.546, 0], "ti": [0.981, 0.542, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [265.322, 281.817, 0], "to": [-0.981, -0.542, 0], "ti": [0.983, 0.54, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [262.379, 280.195, 0], "to": [-0.983, -0.54, 0], "ti": [1.806, 0.949, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [259.427, 278.577, 0], "to": [-1.806, -0.949, 0], "ti": [3.794, 1.848, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [251.541, 274.5, 0], "to": [-3.794, -1.848, 0], "ti": [4.931, 2.289, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [236.66, 267.49, 0], "to": [-4.931, -2.289, 0], "ti": [3.458, 1.526, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [221.955, 260.769, 0], "to": [-3.458, -1.526, 0], "ti": [2.442, 1.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [215.91, 258.335, 0], "to": [-2.442, -1.075, 0], "ti": [2.672, 1.247, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [207.303, 254.32, 0], "to": [-2.672, -1.247, 0], "ti": [2.265, 1.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [199.878, 250.853, 0], "to": [-2.265, -1.058, 0], "ti": [1.84, 0.861, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [193.71, 247.97, 0], "to": [-1.84, -0.861, 0], "ti": [1.409, 0.661, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [188.837, 245.688, 0], "to": [-1.409, -0.661, 0], "ti": [0.983, 0.463, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [185.258, 244.006, 0], "to": [-0.983, -0.463, 0], "ti": [0.574, 0.273, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [182.938, 242.909, 0], "to": [-0.574, -0.273, 0], "ti": [0.189, 0.095, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [181.816, 242.367, 0], "to": [-0.189, -0.095, 0], "ti": [-0.162, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [181.803, 242.341, 0], "to": [0.162, 0.069, 0], "ti": [-0.476, -0.214, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [182.791, 242.779, 0], "to": [0.476, 0.214, 0], "ti": [-0.746, -0.339, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [184.656, 243.624, 0], "to": [0.746, 0.339, 0], "ti": [-0.97, -0.443, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [187.264, 244.814, 0], "to": [0.97, 0.443, 0], "ti": [-1.147, -0.525, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [190.474, 246.283, 0], "to": [1.147, 0.525, 0], "ti": [-1.276, -0.585, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [194.143, 247.966, 0], "to": [1.276, 0.585, 0], "ti": [-1.359, -0.624, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [198.13, 249.795, 0], "to": [1.359, 0.624, 0], "ti": [-1.398, -0.642, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [202.299, 251.709, 0], "to": [1.398, 0.642, 0], "ti": [-1.397, -0.641, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [206.521, 253.648, 0], "to": [1.397, 0.641, 0], "ti": [-1.359, -0.624, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [210.68, 255.558, 0], "to": [1.359, 0.624, 0], "ti": [-1.288, -0.591, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [214.672, 257.39, 0], "to": [1.288, 0.591, 0], "ti": [-1.19, -0.545, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [218.407, 259.103, 0], "to": [1.19, 0.545, 0], "ti": [-1.069, -0.489, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [221.809, 260.661, 0], "to": [1.069, 0.489, 0], "ti": [-0.931, -0.425, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [224.821, 262.038, 0], "to": [0.931, 0.425, 0], "ti": [-0.782, -0.356, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [227.397, 263.213, 0], "to": [0.782, 0.356, 0], "ti": [-0.625, -0.283, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [229.511, 264.173, 0], "to": [0.625, 0.283, 0], "ti": [-0.467, -0.21, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [231.149, 264.912, 0], "to": [0.467, 0.21, 0], "ti": [-0.31, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [232.311, 265.431, 0], "to": [0.31, 0.137, 0], "ti": [-0.159, -0.067, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [233.009, 265.734, 0], "to": [0.159, 0.067, 0], "ti": [-0.018, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [233.267, 265.833, 0], "to": [0.018, 0.001, 0], "ti": [0.111, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [233.118, 265.742, 0], "to": [-0.111, -0.059, 0], "ti": [0.226, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [232.6, 265.481, 0], "to": [-0.226, -0.112, 0], "ti": [0.326, 0.158, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [231.759, 265.07, 0], "to": [-0.326, -0.158, 0], "ti": [0.408, 0.196, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [230.645, 264.532, 0], "to": [-0.408, -0.196, 0], "ti": [0.473, 0.226, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [229.311, 263.892, 0], "to": [-0.473, -0.226, 0], "ti": [0.52, 0.248, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [227.808, 263.174, 0], "to": [-0.52, -0.248, 0], "ti": [0.551, 0.263, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [226.188, 262.402, 0], "to": [-0.551, -0.263, 0], "ti": [0.565, 0.269, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [224.502, 261.599, 0], "to": [-0.565, -0.269, 0], "ti": [0.565, 0.269, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [222.796, 260.787, 0], "to": [-0.565, -0.269, 0], "ti": [0.551, 0.262, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [221.113, 259.985, 0], "to": [-0.551, -0.262, 0], "ti": [0.525, 0.25, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [219.491, 259.212, 0], "to": [-0.525, -0.25, 0], "ti": [0.489, 0.234, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [217.963, 258.482, 0], "to": [-0.489, -0.234, 0], "ti": [0.445, 0.213, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [216.557, 257.809, 0], "to": [-0.445, -0.213, 0], "ti": [0.394, 0.19, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [215.295, 257.202, 0], "to": [-0.394, -0.19, 0], "ti": [0.339, 0.164, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [214.192, 256.669, 0], "to": [-0.339, -0.164, 0], "ti": [0.282, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [213.258, 256.215, 0], "to": [-0.282, -0.138, 0], "ti": [0.224, 0.111, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [212.5, 255.842, 0], "to": [-0.224, -0.111, 0], "ti": [0.166, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [211.917, 255.551, 0], "to": [-0.166, -0.084, 0], "ti": [0.11, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [211.505, 255.339, 0], "to": [-0.11, -0.058, 0], "ti": [0.058, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [211.256, 255.202, 0], "to": [-0.058, -0.034, 0], "ti": [-0.001, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [211.157, 255.136, 0], "to": [0.001, -0.004, 0], "ti": [-0.081, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [211.264, 255.178, 0], "to": [0.081, 0.04, 0], "ti": [-0.144, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [211.645, 255.376, 0], "to": [0.144, 0.074, 0], "ti": [-0.174, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [212.128, 255.621, 0], "to": [0.174, 0.088, 0], "ti": [-0.198, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [212.692, 255.904, 0], "to": [0.198, 0.099, 0], "ti": [-0.216, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [213.317, 256.215, 0], "to": [0.216, 0.107, 0], "ti": [-0.227, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [213.985, 256.545, 0], "to": [0.227, 0.112, 0], "ti": [-0.232, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [214.676, 256.887, 0], "to": [0.232, 0.114, 0], "ti": [-0.231, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [215.375, 257.232, 0], "to": [0.231, 0.114, 0], "ti": [-0.226, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [216.064, 257.572, 0], "to": [0.226, 0.112, 0], "ti": [-0.216, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [216.731, 257.902, 0], "to": [0.216, 0.107, 0], "ti": [-0.203, -0.101, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [217.363, 258.216, 0], "to": [0.203, 0.101, 0], "ti": [-0.187, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [217.951, 258.509, 0], "to": [0.187, 0.094, 0], "ti": [-0.169, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [218.486, 258.778, 0], "to": [0.169, 0.085, 0], "ti": [-0.149, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [218.963, 259.02, 0], "to": [0.149, 0.076, 0], "ti": [-0.127, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [219.378, 259.233, 0], "to": [0.127, 0.066, 0], "ti": [-0.106, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [219.728, 259.417, 0], "to": [0.106, 0.056, 0], "ti": [-0.085, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [220.013, 259.57, 0], "to": [0.085, 0.046, 0], "ti": [-0.064, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [220.236, 259.694, 0], "to": [0.064, 0.037, 0], "ti": [-0.045, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [220.399, 259.791, 0], "to": [0.045, 0.028, 0], "ti": [-0.028, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [220.507, 259.861, 0], "to": [0.028, 0.02, 0], "ti": [-0.012, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [220.565, 259.909, 0], "to": [0.012, 0.012, 0], "ti": [0.001, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [220.579, 259.936, 0], "to": [-0.001, 0.006, 0], "ti": [0.013, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [220.556, 259.946, 0], "to": [-0.013, 0.001, 0], "ti": [0.021, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [220.503, 259.942, 0], "to": [-0.021, -0.003, 0], "ti": [0.028, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [220.427, 259.928, 0], "to": [-0.028, -0.006, 0], "ti": [0.032, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [220.336, 259.906, 0], "to": [-0.032, -0.008, 0], "ti": [0.034, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [220.235, 259.88, 0], "to": [-0.034, -0.009, 0], "ti": [0.034, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [220.131, 259.852, 0], "to": [-0.034, -0.009, 0], "ti": [0.032, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [220.031, 259.826, 0], "to": [-0.032, -0.008, 0], "ti": [0.029, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [219.939, 259.804, 0], "to": [-0.029, -0.006, 0], "ti": [0.024, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [219.86, 259.788, 0], "to": [-0.024, -0.004, 0], "ti": [0.018, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [219.797, 259.78, 0], "to": [-0.018, -0.001, 0], "ti": [0.011, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [219.754, 259.781, 0], "to": [-0.011, 0.002, 0], "ti": [0.003, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [219.733, 259.791, 0], "to": [-0.003, 0.005, 0], "ti": [-0.005, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [219.735, 259.813, 0], "to": [0.005, 0.009, 0], "ti": [-0.012, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [219.76, 259.846, 0], "to": [0.012, 0.013, 0], "ti": [-0.02, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [219.81, 259.889, 0], "to": [0.02, 0.016, 0], "ti": [-0.028, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [219.882, 259.943, 0], "to": [0.028, 0.02, 0], "ti": [-0.035, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [219.975, 260.007, 0], "to": [0.035, 0.023, 0], "ti": [-0.041, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [220.089, 260.081, 0], "to": [0.041, 0.026, 0], "ti": [-0.047, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [220.221, 260.162, 0], "to": [0.047, 0.029, 0], "ti": [-0.052, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [220.369, 260.252, 0], "to": [0.052, 0.031, 0], "ti": [-0.056, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [220.531, 260.347, 0], "to": [0.056, 0.033, 0], "ti": [-0.059, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [220.704, 260.449, 0], "to": [0.059, 0.034, 0], "ti": [-0.062, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [220.887, 260.554, 0], "to": [0.062, 0.036, 0], "ti": [-0.064, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [221.076, 260.663, 0], "to": [0.064, 0.036, 0], "ti": [-0.064, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [221.268, 260.773, 0], "to": [0.064, 0.037, 0], "ti": [-0.064, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [221.462, 260.884, 0], "to": [0.064, 0.037, 0], "ti": [-0.079, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [221.654, 260.994, 0], "to": [0.079, 0.043, 0], "ti": [0.318, 0.403, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [221.934, 261.144, 0], "to": [-0.318, -0.403, 0], "ti": [0.751, 0.856, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [219.749, 258.576, 0], "to": [-0.751, -0.856, 0], "ti": [0.775, 0.858, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [217.427, 256.008, 0], "to": [-0.775, -0.858, 0], "ti": [0.777, 0.861, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [215.096, 253.43, 0], "to": [-0.777, -0.861, 0], "ti": [0.777, 0.867, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [212.764, 250.84, 0], "to": [-0.777, -0.867, 0], "ti": [0.775, 0.875, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [210.435, 248.229, 0], "to": [-0.775, -0.875, 0], "ti": [0.769, 0.884, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [208.115, 245.591, 0], "to": [-0.769, -0.884, 0], "ti": [0.758, 0.9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [205.823, 242.925, 0], "to": [-0.758, -0.9, 0], "ti": [0.73, 0.928, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [203.567, 240.193, 0], "to": [-0.73, -0.928, 0], "ti": [0.313, 0.761, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [201.444, 237.355, 0], "to": [-0.313, -0.761, 0], "ti": [-2.999, -1.482, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [201.69, 235.629, 0], "to": [2.999, 1.482, 0], "ti": [-5.805, -3.62, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [219.441, 246.247, 0], "to": [5.805, 3.62, 0], "ti": [-5.616, -3.714, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [236.521, 257.346, 0], "to": [5.616, 3.714, 0], "ti": [-5.469, -3.723, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [253.135, 268.53, 0], "to": [5.469, 3.723, 0], "ti": [-5.338, -3.706, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [269.338, 279.687, 0], "to": [5.338, 3.706, 0], "ti": [-3.683, -2.909, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [285.164, 290.767, 0], "to": [3.683, 2.909, 0], "ti": [-2.822, -2.307, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [291.434, 297.144, 0], "to": [2.822, 2.307, 0], "ti": [-3.338, -2.337, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [302.094, 304.607, 0], "to": [3.338, 2.337, 0], "ti": [-2.889, -2.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [311.465, 311.167, 0], "to": [2.889, 2.022, 0], "ti": [-2.407, -1.685, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [319.427, 316.742, 0], "to": [2.407, 1.685, 0], "ti": [-1.91, -1.337, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [325.909, 321.28, 0], "to": [1.91, 1.337, 0], "ti": [-1.41, -0.987, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [330.885, 324.763, 0], "to": [1.41, 0.987, 0], "ti": [-0.922, -0.645, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [334.369, 327.203, 0], "to": [0.922, 0.645, 0], "ti": [-0.456, -0.319, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [336.414, 328.634, 0], "to": [0.456, 0.319, 0], "ti": [-0.023, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [337.104, 329.117, 0], "to": [0.023, 0.016, 0], "ti": [0.37, 0.259, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [336.55, 328.729, 0], "to": [-0.37, -0.259, 0], "ti": [0.716, 0.501, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [334.885, 327.563, 0], "to": [-0.716, -0.501, 0], "ti": [1.01, 0.707, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [332.257, 325.724, 0], "to": [-1.01, -0.707, 0], "ti": [1.25, 0.875, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271, "s": [328.825, 323.321, 0], "to": [-1.25, -0.875, 0], "ti": [1.435, 1.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272, "s": [324.756, 320.473, 0], "to": [-1.435, -1.005, 0], "ti": [1.566, 1.096, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273, "s": [320.214, 317.293, 0], "to": [-1.566, -1.096, 0], "ti": [1.643, 1.15, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274, "s": [315.362, 313.896, 0], "to": [-1.643, -1.15, 0], "ti": [1.671, 1.17, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [310.354, 310.39, 0], "to": [-1.671, -1.17, 0], "ti": [1.653, 1.157, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [305.335, 306.876, 0], "to": [-1.653, -1.157, 0], "ti": [1.594, 1.116, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [300.434, 303.445, 0], "to": [-1.594, -1.116, 0], "ti": [1.5, 1.05, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278, "s": [295.768, 300.178, 0], "to": [-1.5, -1.05, 0], "ti": [1.376, 0.963, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279, "s": [291.434, 297.144, 0], "to": [-1.376, -0.963, 0], "ti": [1.228, 0.86, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [287.512, 294.398, 0], "to": [-1.228, -0.86, 0], "ti": [1.063, 0.744, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281, "s": [284.065, 291.985, 0], "to": [-1.063, -0.744, 0], "ti": [0.886, 0.62, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282, "s": [281.136, 289.934, 0], "to": [-0.886, -0.62, 0], "ti": [0.703, 0.492, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [278.751, 288.265, 0], "to": [-0.703, -0.492, 0], "ti": [0.519, 0.363, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [276.921, 286.983, 0], "to": [-0.519, -0.363, 0], "ti": [0.339, 0.237, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [275.639, 286.086, 0], "to": [-0.339, -0.237, 0], "ti": [0.168, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286, "s": [274.886, 285.559, 0], "to": [-0.168, -0.117, 0], "ti": [0.042, 0.03, 0]}, {"t": 287, "s": [274.633, 285.382, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [60.5, 26.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [33.201, 2.798], [15.143, -37.546], [-57.043, -15.034], [-31.586, 24.356]], "o": [[-18.159, -43.895], [-49.802, -4.197], [-15.143, 37.546], [38.029, 10.022], [0, 0]], "v": [[93.453, -26.242], [16.413, -96.282], [-85.416, -27.325], [-41.54, 92.237], [62.883, 70.736]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.247058823705, 0.835294127464, 0.486966192722, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 1, 0.647, 0.5, 0.637, 0.937, 0.773, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [20, 29], "ix": 5}, "e": {"a": 0, "k": [-90.934, -63.644], "ix": 6}, "t": 1, "nm": "jianbian16", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-15", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "蓝花瓣高光", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-11.443, -65.979, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-47.452, -25.524]], "o": [[0, 0], [36.782, 19.785]], "v": [[-44.688, 2.928], [44.688, 6.117]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.580392181873, 0.96862745285, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.951, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "蓝花瓣高光", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50.653, -50.103, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.484], [3.484, 0], [0, 3.484], [-3.484, 0]], "o": [[0, 3.484], [-3.484, 0], [0, -3.484], [3.484, 0]], "v": [[6.308, 0], [0, 6.308], [-6.308, 0], [0, -6.308]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.580392181873, 0.96862745285, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "蓝花瓣描边", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 70, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [4.204, -18.691, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.944, 12.65]], "o": [[-0.196, -13.598], [0, 0]], "v": [[-5.057, 19.686], [5.065, -19.686]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.313725501299, 0.54509806633, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-20备份-2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "蓝花瓣", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [311.826, 239.941, 0], "to": [0.013, -0.064, 0], "ti": [-0.155, 0.481, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [311.904, 239.558, 0], "to": [0.155, -0.481, 0], "ti": [-0.371, 0.793, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [312.758, 237.056, 0], "to": [0.371, -0.793, 0], "ti": [-0.502, 0.722, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [314.13, 234.799, 0], "to": [0.502, -0.722, 0], "ti": [-0.551, 0.69, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [315.772, 232.725, 0], "to": [0.551, -0.69, 0], "ti": [-0.533, 0.703, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [317.435, 230.661, 0], "to": [0.533, -0.703, 0], "ti": [-0.48, 0.738, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [318.968, 228.506, 0], "to": [0.48, -0.738, 0], "ti": [-0.418, 0.777, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [320.317, 226.231, 0], "to": [0.418, -0.777, 0], "ti": [-0.356, 0.808, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [321.475, 223.844, 0], "to": [0.356, -0.808, 0], "ti": [-0.3, 0.829, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [322.453, 221.383, 0], "to": [0.3, -0.829, 0], "ti": [-0.252, 0.844, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [323.274, 218.872, 0], "to": [0.252, -0.844, 0], "ti": [-0.212, 0.857, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [323.964, 216.319, 0], "to": [0.212, -0.857, 0], "ti": [-0.403, 2.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [324.544, 213.73, 0], "to": [0.403, -2.331, 0], "ti": [-0.678, 4.928, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [326.382, 202.333, 0], "to": [0.678, -4.928, 0], "ti": [-0.712, 6.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [328.612, 184.162, 0], "to": [0.712, -6.023, 0], "ti": [-0.502, 4.442, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [330.652, 166.195, 0], "to": [0.502, -4.442, 0], "ti": [-0.411, 3.251, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [331.625, 157.511, 0], "to": [0.411, -3.251, 0], "ti": [-0.462, 3.352, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [333.116, 146.69, 0], "to": [0.462, -3.352, 0], "ti": [-0.39, 2.826, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [334.398, 137.4, 0], "to": [0.39, -2.826, 0], "ti": [-0.315, 2.279, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [335.457, 129.732, 0], "to": [0.315, -2.279, 0], "ti": [-0.239, 1.727, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [336.288, 123.725, 0], "to": [0.239, -1.727, 0], "ti": [-0.165, 1.183, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [336.892, 119.372, 0], "to": [0.165, -1.183, 0], "ti": [-0.093, 0.663, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [337.277, 116.624, 0], "to": [0.093, -0.663, 0], "ti": [-0.027, 0.176, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [337.453, 115.395, 0], "to": [0.027, -0.176, 0], "ti": [0.034, -0.268, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [337.436, 115.569, 0], "to": [-0.034, 0.268, 0], "ti": [0.088, -0.662, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [337.247, 117.004, 0], "to": [-0.088, 0.662, 0], "ti": [0.135, -0.999, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [336.907, 119.539, 0], "to": [-0.135, 0.999, 0], "ti": [0.173, -1.278, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [336.44, 122.999, 0], "to": [-0.173, 1.278, 0], "ti": [0.203, -1.495, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [335.871, 127.205, 0], "to": [-0.203, 1.495, 0], "ti": [0.224, -1.652, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [335.225, 131.971, 0], "to": [-0.224, 1.652, 0], "ti": [0.238, -1.751, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [334.527, 137.118, 0], "to": [-0.238, 1.751, 0], "ti": [0.243, -1.793, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [333.8, 142.474, 0], "to": [-0.243, 1.793, 0], "ti": [0.242, -1.784, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [333.067, 147.876, 0], "to": [-0.242, 1.784, 0], "ti": [0.234, -1.728, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [332.347, 153.176, 0], "to": [-0.234, 1.728, 0], "ti": [0.221, -1.632, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [331.66, 158.244, 0], "to": [-0.221, 1.632, 0], "ti": [0.203, -1.501, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [331.02, 162.967, 0], "to": [-0.203, 1.501, 0], "ti": [0.182, -1.343, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [330.44, 167.252, 0], "to": [-0.182, 1.343, 0], "ti": [0.157, -1.164, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [329.93, 171.026, 0], "to": [-0.157, 1.164, 0], "ti": [0.131, -0.971, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [329.498, 174.236, 0], "to": [-0.131, 0.971, 0], "ti": [0.103, -0.77, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [329.147, 176.85, 0], "to": [-0.103, 0.77, 0], "ti": [0.075, -0.566, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [328.88, 178.854, 0], "to": [-0.075, 0.566, 0], "ti": [0.048, -0.366, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [328.696, 180.248, 0], "to": [-0.048, 0.366, 0], "ti": [0.021, -0.175, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [328.594, 181.052, 0], "to": [-0.021, 0.175, 0], "ti": [-0.003, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [328.568, 181.298, 0], "to": [0.003, -0.004, 0], "ti": [-0.026, 0.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [328.613, 181.027, 0], "to": [0.026, -0.167, 0], "ti": [-0.045, 0.312, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [328.721, 180.293, 0], "to": [0.045, -0.312, 0], "ti": [-0.062, 0.436, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [328.885, 179.154, 0], "to": [0.062, -0.436, 0], "ti": [-0.076, 0.538, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [329.095, 177.676, 0], "to": [0.076, -0.538, 0], "ti": [-0.087, 0.618, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [329.343, 175.924, 0], "to": [0.087, -0.618, 0], "ti": [-0.095, 0.676, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [329.619, 173.966, 0], "to": [0.095, -0.676, 0], "ti": [-0.1, 0.712, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [329.914, 171.867, 0], "to": [0.1, -0.712, 0], "ti": [-0.102, 0.728, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [330.22, 169.693, 0], "to": [0.102, -0.728, 0], "ti": [-0.102, 0.724, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [330.528, 167.501, 0], "to": [0.102, -0.724, 0], "ti": [-0.099, 0.704, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [330.831, 165.346, 0], "to": [0.099, -0.704, 0], "ti": [-0.094, 0.669, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [331.122, 163.276, 0], "to": [0.094, -0.669, 0], "ti": [-0.088, 0.621, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [331.396, 161.333, 0], "to": [0.088, -0.621, 0], "ti": [-0.08, 0.563, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [331.648, 159.551, 0], "to": [0.08, -0.563, 0], "ti": [-0.071, 0.497, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [331.874, 157.956, 0], "to": [0.071, -0.497, 0], "ti": [-0.061, 0.426, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [332.072, 156.567, 0], "to": [0.061, -0.426, 0], "ti": [-0.051, 0.352, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [332.24, 155.398, 0], "to": [0.051, -0.352, 0], "ti": [-0.041, 0.277, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [332.378, 154.454, 0], "to": [0.041, -0.277, 0], "ti": [-0.03, 0.204, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [332.484, 153.734, 0], "to": [0.03, -0.204, 0], "ti": [-0.021, 0.133, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [332.56, 153.232, 0], "to": [0.021, -0.133, 0], "ti": [-0.008, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [332.609, 152.935, 0], "to": [0.008, -0.048, 0], "ti": [0.01, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [332.609, 152.946, 0], "to": [-0.01, 0.063, 0], "ti": [0.023, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [332.551, 153.312, 0], "to": [-0.023, 0.147, 0], "ti": [0.029, -0.192, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [332.473, 153.826, 0], "to": [-0.029, 0.192, 0], "ti": [0.034, -0.23, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [332.378, 154.465, 0], "to": [-0.034, 0.23, 0], "ti": [0.038, -0.259, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [332.269, 155.205, 0], "to": [-0.038, 0.259, 0], "ti": [0.041, -0.28, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [332.149, 156.02, 0], "to": [-0.041, 0.28, 0], "ti": [0.043, -0.294, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [332.023, 156.887, 0], "to": [-0.043, 0.294, 0], "ti": [0.044, -0.3, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [331.893, 157.782, 0], "to": [-0.044, 0.3, 0], "ti": [0.043, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [331.761, 158.684, 0], "to": [-0.043, 0.298, 0], "ti": [0.042, -0.291, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [331.632, 159.573, 0], "to": [-0.042, 0.291, 0], "ti": [0.041, -0.278, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [331.507, 160.43, 0], "to": [-0.041, 0.278, 0], "ti": [0.038, -0.26, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [331.388, 161.241, 0], "to": [-0.038, 0.26, 0], "ti": [0.035, -0.239, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [331.277, 161.993, 0], "to": [-0.035, 0.239, 0], "ti": [0.032, -0.215, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [331.176, 162.675, 0], "to": [-0.032, 0.215, 0], "ti": [0.028, -0.188, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [331.085, 163.281, 0], "to": [-0.028, 0.188, 0], "ti": [0.025, -0.161, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [331.006, 163.805, 0], "to": [-0.025, 0.161, 0], "ti": [0.021, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [330.938, 164.247, 0], "to": [-0.021, 0.134, 0], "ti": [0.017, -0.106, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [330.881, 164.607, 0], "to": [-0.017, 0.106, 0], "ti": [0.014, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [330.835, 164.886, 0], "to": [-0.014, 0.08, 0], "ti": [0.01, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [330.8, 165.089, 0], "to": [-0.01, 0.056, 0], "ti": [0.007, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [330.774, 165.223, 0], "to": [-0.007, 0.034, 0], "ti": [0.005, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [330.756, 165.294, 0], "to": [-0.005, 0.014, 0], "ti": [0.002, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [330.747, 165.31, 0], "to": [-0.002, -0.002, 0], "ti": [0, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [330.743, 165.28, 0], "to": [0, -0.016, 0], "ti": [-0.001, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [330.745, 165.214, 0], "to": [0.001, -0.027, 0], "ti": [-0.002, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [330.75, 165.119, 0], "to": [0.002, -0.035, 0], "ti": [-0.003, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [330.758, 165.006, 0], "to": [0.003, -0.039, 0], "ti": [-0.003, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [330.767, 164.883, 0], "to": [0.003, -0.042, 0], "ti": [-0.003, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [330.777, 164.757, 0], "to": [0.003, -0.041, 0], "ti": [-0.003, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [330.786, 164.636, 0], "to": [0.003, -0.038, 0], "ti": [-0.002, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [330.793, 164.527, 0], "to": [0.002, -0.034, 0], "ti": [-0.001, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [330.798, 164.435, 0], "to": [0.001, -0.027, 0], "ti": [0, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [330.8, 164.365, 0], "to": [0, -0.019, 0], "ti": [0.001, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [330.799, 164.32, 0], "to": [-0.001, -0.01, 0], "ti": [0.002, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [330.794, 164.303, 0], "to": [-0.002, -0.001, 0], "ti": [0.004, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [330.784, 164.316, 0], "to": [-0.004, 0.009, 0], "ti": [0.005, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [330.771, 164.359, 0], "to": [-0.005, 0.019, 0], "ti": [0.007, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [330.753, 164.432, 0], "to": [-0.007, 0.029, 0], "ti": [0.008, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [330.732, 164.535, 0], "to": [-0.008, 0.039, 0], "ti": [0.009, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [330.706, 164.666, 0], "to": [-0.009, 0.048, 0], "ti": [0.01, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [330.677, 164.822, 0], "to": [-0.01, 0.056, 0], "ti": [0.011, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [330.645, 165.002, 0], "to": [-0.011, 0.063, 0], "ti": [0.012, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [330.61, 165.201, 0], "to": [-0.012, 0.069, 0], "ti": [0.013, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [330.573, 165.418, 0], "to": [-0.013, 0.074, 0], "ti": [0.013, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [330.534, 165.648, 0], "to": [-0.013, 0.078, 0], "ti": [0.014, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [330.493, 165.888, 0], "to": [-0.014, 0.081, 0], "ti": [0.014, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [330.452, 166.135, 0], "to": [-0.014, 0.083, 0], "ti": [0.021, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [330.41, 166.386, 0], "to": [-0.021, 0.134, 0], "ti": [-0.212, 0.431, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [330.326, 166.941, 0], "to": [0.212, -0.431, 0], "ti": [-0.455, 1.18, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [331.684, 163.798, 0], "to": [0.455, -1.18, 0], "ti": [-0.454, 1.313, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [333.054, 159.863, 0], "to": [0.454, -1.313, 0], "ti": [-0.451, 1.32, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [334.405, 155.918, 0], "to": [0.451, -1.32, 0], "ti": [-0.452, 1.326, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [335.759, 151.944, 0], "to": [0.452, -1.326, 0], "ti": [-0.454, 1.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [337.116, 147.963, 0], "to": [0.454, -1.329, 0], "ti": [-0.459, 1.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [338.485, 143.971, 0], "to": [0.459, -1.331, 0], "ti": [-0.47, 1.334, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [339.873, 139.979, 0], "to": [0.47, -1.334, 0], "ti": [-0.492, 1.334, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [341.304, 135.964, 0], "to": [0.492, -1.334, 0], "ti": [-0.411, 0.731, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [342.823, 131.978, 0], "to": [0.411, -0.731, 0], "ti": [0.704, -3.47, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [343.768, 131.581, 0], "to": [-0.704, 3.47, 0], "ti": [1.823, -6.98, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [338.601, 152.796, 0], "to": [-1.823, 6.98, 0], "ti": [1.945, -6.814, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [332.831, 173.461, 0], "to": [-1.945, 6.814, 0], "ti": [1.968, -6.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [326.933, 193.68, 0], "to": [-1.968, 6.667, 0], "ti": [1.958, -6.525, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [321.025, 213.463, 0], "to": [-1.958, 6.525, 0], "ti": [1.533, -4.413, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [315.187, 232.83, 0], "to": [-1.533, 4.413, 0], "ti": [1.131, -3.361, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [311.826, 239.941, 0], "to": [-1.131, 3.361, 0], "ti": [1.073, -4.089, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [308.401, 252.997, 0], "to": [-1.073, 4.089, 0], "ti": [0.928, -3.538, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [305.39, 264.474, 0], "to": [-0.928, 3.538, 0], "ti": [0.774, -2.948, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [302.832, 274.226, 0], "to": [-0.774, 2.948, 0], "ti": [0.614, -2.339, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [300.749, 282.165, 0], "to": [-0.614, 2.339, 0], "ti": [0.453, -1.727, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [299.15, 288.259, 0], "to": [-0.453, 1.727, 0], "ti": [0.296, -1.129, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [298.03, 292.527, 0], "to": [-0.296, 1.129, 0], "ti": [0.146, -0.558, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [297.373, 295.031, 0], "to": [-0.146, 0.558, 0], "ti": [0.007, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [297.152, 295.876, 0], "to": [-0.007, 0.028, 0], "ti": [-0.119, 0.453, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [297.33, 295.198, 0], "to": [0.119, -0.453, 0], "ti": [-0.23, 0.876, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [297.865, 293.158, 0], "to": [0.23, -0.876, 0], "ti": [-0.324, 1.237, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [298.709, 289.939, 0], "to": [0.324, -1.237, 0], "ti": [-0.402, 1.531, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271, "s": [299.812, 285.737, 0], "to": [0.402, -1.531, 0], "ti": [-0.461, 1.758, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272, "s": [301.119, 280.753, 0], "to": [0.461, -1.758, 0], "ti": [-0.503, 1.918, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273, "s": [302.579, 275.19, 0], "to": [0.503, -1.918, 0], "ti": [-0.528, 2.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274, "s": [304.138, 269.247, 0], "to": [0.528, -2.013, 0], "ti": [-0.537, 2.047, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [305.747, 263.114, 0], "to": [0.537, -2.047, 0], "ti": [-0.531, 2.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [307.359, 256.966, 0], "to": [0.531, -2.025, 0], "ti": [-0.512, 1.953, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [308.934, 250.964, 0], "to": [0.512, -1.953, 0], "ti": [-0.482, 1.837, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278, "s": [310.433, 245.249, 0], "to": [0.482, -1.837, 0], "ti": [-0.442, 1.685, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279, "s": [311.826, 239.941, 0], "to": [0.442, -1.685, 0], "ti": [-0.395, 1.504, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [313.086, 235.137, 0], "to": [0.395, -1.504, 0], "ti": [-0.341, 1.302, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281, "s": [314.194, 230.915, 0], "to": [0.341, -1.302, 0], "ti": [-0.285, 1.085, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282, "s": [315.135, 227.328, 0], "to": [0.285, -1.085, 0], "ti": [-0.226, 0.86, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [315.901, 224.407, 0], "to": [0.226, -0.86, 0], "ti": [-0.167, 0.635, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [316.489, 222.165, 0], "to": [0.167, -0.635, 0], "ti": [-0.109, 0.415, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [316.901, 220.595, 0], "to": [0.109, -0.415, 0], "ti": [-0.054, 0.205, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286, "s": [317.143, 219.674, 0], "to": [0.054, -0.205, 0], "ti": [-0.014, 0.052, 0]}, {"t": 287, "s": [317.224, 219.363, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.439, "s": [{"i": [[0, 0], [17.522, 35.247], [38.838, -5.214], [-14.902, -62.408], [-33.329, -16.4]], "o": [[45.361, -37.539], [-26.283, -52.871], [-38.838, 5.214], [9.935, 41.605], [0, 0]], "v": [[57.952, 80.572], [99.711, -28.607], [-32.589, -90.339], [-104.24, 4.344], [-39.344, 91.352]], "c": true}]}, {"t": 146.21875, "s": [{"i": [[0, 0], [17.522, 35.247], [38.838, -5.214], [-14.902, -62.408], [-33.329, -16.4]], "o": [[45.361, -37.539], [-26.283, -52.871], [-38.838, 5.214], [9.935, 41.605], [0, 0]], "v": [[57.952, 80.572], [99.711, -28.607], [-32.589, -90.339], [-104.24, 4.344], [-39.344, 91.352]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.313725501299, 0.54509806633, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 0.694, 1, 0.5, 0.637, 0.784, 0.949, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [-7, 23], "ix": 5}, "e": {"a": 0, "k": [16.174, -91.441], "ix": 6}, "t": 1, "nm": "jianbian18", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-15备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "红花瓣高光", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-47.584, 29.554, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [12.402, 56.575]], "o": [[0, 0], [-8.943, -40.796]], "v": [[29.625, 32.177], [-29.625, -32.177]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.951, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "红花瓣描边", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 70, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-28.104, 12.667, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [13.706, -4.516]], "o": [[-9.131, 10.078], [0, 0]], "v": [[17.127, -10.946], [-17.127, 10.946]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.75686275959, 0.270588248968, 0.270588248968, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-20备份-4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "红花瓣", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [238.826, 355.275, 0], "to": [-0.319, 0.274, 0], "ti": [0.613, -0.572, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [236.915, 356.918, 0], "to": [-0.613, 0.572, 0], "ti": [0.573, -0.611, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [235.151, 358.708, 0], "to": [-0.573, 0.611, 0], "ti": [0.555, -0.629, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [233.479, 360.586, 0], "to": [-0.555, 0.629, 0], "ti": [0.558, -0.626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [231.821, 362.479, 0], "to": [-0.558, 0.626, 0], "ti": [0.573, -0.612, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [230.129, 364.343, 0], "to": [-0.573, 0.612, 0], "ti": [0.59, -0.595, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [228.386, 366.154, 0], "to": [-0.59, 0.595, 0], "ti": [0.608, -0.579, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [226.586, 367.915, 0], "to": [-0.608, 0.579, 0], "ti": [0.622, -0.562, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [224.737, 369.627, 0], "to": [-0.622, 0.562, 0], "ti": [0.634, -0.547, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [222.851, 371.288, 0], "to": [-0.634, 0.547, 0], "ti": [0.646, -0.535, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [220.931, 372.909, 0], "to": [-0.646, 0.535, 0], "ti": [1.433, -1.106, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [218.977, 374.497, 0], "to": [-1.433, 1.106, 0], "ti": [3.342, -2.415, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [212.331, 379.542, 0], "to": [-3.342, 2.415, 0], "ti": [4.459, -3.1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [198.925, 388.985, 0], "to": [-4.459, 3.1, 0], "ti": [3.369, -2.305, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [185.574, 398.139, 0], "to": [-3.369, 2.305, 0], "ti": [2.482, -1.74, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [178.708, 402.813, 0], "to": [-2.482, 1.74, 0], "ti": [2.492, -1.791, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [170.682, 408.582, 0], "to": [-2.492, 1.791, 0], "ti": [2.113, -1.519, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [163.758, 413.559, 0], "to": [-2.113, 1.519, 0], "ti": [1.716, -1.234, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [158.007, 417.695, 0], "to": [-1.716, 1.234, 0], "ti": [1.313, -0.945, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [153.463, 420.964, 0], "to": [-1.313, 0.945, 0], "ti": [0.916, -0.66, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [150.126, 423.367, 0], "to": [-0.916, 0.66, 0], "ti": [0.534, -0.386, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [147.964, 424.925, 0], "to": [-0.534, 0.386, 0], "ti": [0.176, -0.129, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [146.92, 425.682, 0], "to": [-0.176, 0.129, 0], "ti": [-0.152, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [146.909, 425.696, 0], "to": [0.152, -0.107, 0], "ti": [-0.444, 0.317, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [147.833, 425.04, 0], "to": [0.444, -0.317, 0], "ti": [-0.696, 0.498, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [149.575, 423.796, 0], "to": [0.696, -0.498, 0], "ti": [-0.905, 0.648, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [152.01, 422.055, 0], "to": [0.905, -0.648, 0], "ti": [-1.07, 0.766, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [155.007, 419.91, 0], "to": [1.07, -0.766, 0], "ti": [-1.191, 0.853, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [158.432, 417.458, 0], "to": [1.191, -0.853, 0], "ti": [-1.269, 0.909, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [162.153, 414.793, 0], "to": [1.269, -0.909, 0], "ti": [-1.305, 0.935, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [166.044, 412.007, 0], "to": [1.305, -0.935, 0], "ti": [-1.304, 0.934, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [169.985, 409.184, 0], "to": [1.304, -0.934, 0], "ti": [-1.268, 0.908, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [173.867, 406.404, 0], "to": [1.268, -0.908, 0], "ti": [-1.202, 0.861, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [177.593, 403.736, 0], "to": [1.202, -0.861, 0], "ti": [-1.11, 0.795, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [181.079, 401.24, 0], "to": [1.11, -0.795, 0], "ti": [-0.998, 0.714, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [184.256, 398.966, 0], "to": [0.998, -0.714, 0], "ti": [-0.87, 0.622, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [187.067, 396.955, 0], "to": [0.87, -0.622, 0], "ti": [-0.73, 0.522, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [189.473, 395.234, 0], "to": [0.73, -0.522, 0], "ti": [-0.584, 0.417, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [191.448, 393.824, 0], "to": [0.584, -0.417, 0], "ti": [-0.436, 0.311, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [192.978, 392.732, 0], "to": [0.436, -0.311, 0], "ti": [-0.29, 0.206, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [194.064, 391.959, 0], "to": [0.29, -0.206, 0], "ti": [-0.15, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [194.718, 391.496, 0], "to": [0.15, -0.105, 0], "ti": [-0.018, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [194.961, 391.329, 0], "to": [0.018, -0.01, 0], "ti": [0.103, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [194.824, 391.434, 0], "to": [-0.103, 0.076, 0], "ti": [0.211, -0.153, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [194.343, 391.787, 0], "to": [-0.211, 0.153, 0], "ti": [0.303, -0.22, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [193.561, 392.355, 0], "to": [-0.303, 0.22, 0], "ti": [0.38, -0.275, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [192.524, 393.106, 0], "to": [-0.38, 0.275, 0], "ti": [0.441, -0.319, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [191.281, 394.006, 0], "to": [-0.441, 0.319, 0], "ti": [0.485, -0.35, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [189.88, 395.018, 0], "to": [-0.485, 0.35, 0], "ti": [0.513, -0.371, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [188.371, 396.108, 0], "to": [-0.513, 0.371, 0], "ti": [0.527, -0.38, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [186.799, 397.243, 0], "to": [-0.527, 0.38, 0], "ti": [0.526, -0.38, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [185.21, 398.391, 0], "to": [-0.526, 0.38, 0], "ti": [0.513, -0.371, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [183.642, 399.524, 0], "to": [-0.513, 0.371, 0], "ti": [0.489, -0.353, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [182.131, 400.615, 0], "to": [-0.489, 0.353, 0], "ti": [0.455, -0.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [180.708, 401.643, 0], "to": [-0.455, 0.329, 0], "ti": [0.414, -0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [179.4, 402.59, 0], "to": [-0.414, 0.299, 0], "ti": [0.367, -0.266, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [178.225, 403.44, 0], "to": [-0.367, 0.266, 0], "ti": [0.316, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [177.199, 404.184, 0], "to": [-0.316, 0.229, 0], "ti": [0.262, -0.19, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [176.331, 404.813, 0], "to": [-0.262, 0.19, 0], "ti": [0.208, -0.151, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [175.627, 405.326, 0], "to": [-0.208, 0.151, 0], "ti": [0.154, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [175.085, 405.721, 0], "to": [-0.154, 0.113, 0], "ti": [0.102, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [174.703, 406.003, 0], "to": [-0.102, 0.076, 0], "ti": [0.054, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [174.472, 406.176, 0], "to": [-0.054, 0.041, 0], "ti": [0.009, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [174.381, 406.248, 0], "to": [-0.009, 0.009, 0], "ti": [-0.03, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [174.417, 406.229, 0], "to": [0.03, -0.02, 0], "ti": [-0.075, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [174.563, 406.131, 0], "to": [0.075, -0.053, 0], "ti": [-0.138, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [174.87, 405.913, 0], "to": [0.138, -0.1, 0], "ti": [-0.184, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [175.392, 405.531, 0], "to": [0.184, -0.134, 0], "ti": [-0.2, 0.146, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [175.973, 405.107, 0], "to": [0.2, -0.146, 0], "ti": [-0.211, 0.153, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [176.593, 404.655, 0], "to": [0.211, -0.153, 0], "ti": [-0.215, 0.157, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [177.236, 404.187, 0], "to": [0.215, -0.157, 0], "ti": [-0.215, 0.157, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [177.886, 403.713, 0], "to": [0.215, -0.157, 0], "ti": [-0.21, 0.153, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [178.528, 403.246, 0], "to": [0.21, -0.153, 0], "ti": [-0.202, 0.147, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [179.149, 402.793, 0], "to": [0.202, -0.147, 0], "ti": [-0.189, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [179.737, 402.364, 0], "to": [0.189, -0.138, 0], "ti": [-0.174, 0.127, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [180.284, 401.965, 0], "to": [0.174, -0.127, 0], "ti": [-0.157, 0.115, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [180.782, 401.6, 0], "to": [0.157, -0.115, 0], "ti": [-0.138, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [181.225, 401.275, 0], "to": [0.138, -0.101, 0], "ti": [-0.118, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [181.61, 400.992, 0], "to": [0.118, -0.087, 0], "ti": [-0.098, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [181.934, 400.752, 0], "to": [0.098, -0.073, 0], "ti": [-0.078, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [182.198, 400.556, 0], "to": [0.078, -0.059, 0], "ti": [-0.059, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [182.404, 400.401, 0], "to": [0.059, -0.045, 0], "ti": [-0.041, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [182.554, 400.287, 0], "to": [0.041, -0.032, 0], "ti": [-0.025, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [182.652, 400.21, 0], "to": [0.025, -0.02, 0], "ti": [-0.01, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [182.703, 400.166, 0], "to": [0.01, -0.01, 0], "ti": [0.002, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [182.714, 400.151, 0], "to": [-0.002, -0.001, 0], "ti": [0.013, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [182.69, 400.162, 0], "to": [-0.013, 0.007, 0], "ti": [0.021, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [182.638, 400.192, 0], "to": [-0.021, 0.013, 0], "ti": [0.027, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [182.565, 400.238, 0], "to": [-0.027, 0.017, 0], "ti": [0.031, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [182.477, 400.294, 0], "to": [-0.031, 0.02, 0], "ti": [0.032, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [182.381, 400.357, 0], "to": [-0.032, 0.021, 0], "ti": [0.032, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [182.282, 400.421, 0], "to": [-0.032, 0.021, 0], "ti": [0.031, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [182.186, 400.482, 0], "to": [-0.031, 0.02, 0], "ti": [0.027, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [182.099, 400.539, 0], "to": [-0.027, 0.017, 0], "ti": [0.023, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [182.023, 400.586, 0], "to": [-0.023, 0.014, 0], "ti": [0.017, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [181.963, 400.623, 0], "to": [-0.017, 0.01, 0], "ti": [0.011, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [181.92, 400.646, 0], "to": [-0.011, 0.005, 0], "ti": [0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [181.898, 400.655, 0], "to": [-0.004, 0, 0], "ti": [-0.004, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [181.898, 400.648, 0], "to": [0.004, -0.005, 0], "ti": [-0.011, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [181.92, 400.626, 0], "to": [0.011, -0.01, 0], "ti": [-0.018, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [181.963, 400.588, 0], "to": [0.018, -0.015, 0], "ti": [-0.025, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [182.028, 400.534, 0], "to": [0.025, -0.02, 0], "ti": [-0.032, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [182.114, 400.466, 0], "to": [0.032, -0.025, 0], "ti": [-0.038, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [182.219, 400.384, 0], "to": [0.038, -0.029, 0], "ti": [-0.043, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [182.34, 400.29, 0], "to": [0.043, -0.033, 0], "ti": [-0.048, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [182.477, 400.185, 0], "to": [0.048, -0.036, 0], "ti": [-0.051, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [182.626, 400.071, 0], "to": [0.051, -0.039, 0], "ti": [-0.055, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [182.786, 399.949, 0], "to": [0.055, -0.041, 0], "ti": [-0.057, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [182.953, 399.822, 0], "to": [0.057, -0.043, 0], "ti": [-0.058, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [183.126, 399.691, 0], "to": [0.058, -0.044, 0], "ti": [-0.059, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [183.302, 399.558, 0], "to": [0.059, -0.044, 0], "ti": [-0.059, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [183.479, 399.424, 0], "to": [0.059, -0.044, 0], "ti": [-0.058, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [183.655, 399.291, 0], "to": [0.058, -0.044, 0], "ti": [-0.057, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [183.828, 399.16, 0], "to": [0.057, -0.043, 0], "ti": [-0.055, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [183.996, 399.033, 0], "to": [0.055, -0.042, 0], "ti": [-0.053, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [184.159, 398.909, 0], "to": [0.053, -0.04, 0], "ti": [-0.028, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [184.315, 398.79, 0], "to": [0.028, -0.022, 0], "ti": [0.487, -0.23, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [184.326, 398.775, 0], "to": [-0.487, 0.23, 0], "ti": [1.056, -0.552, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [181.39, 400.169, 0], "to": [-1.056, 0.552, 0], "ti": [1.133, -0.643, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [177.988, 402.088, 0], "to": [-1.133, 0.643, 0], "ti": [1.135, -0.649, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [174.593, 404.026, 0], "to": [-1.135, 0.649, 0], "ti": [1.139, -0.652, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [171.18, 405.981, 0], "to": [-1.139, 0.652, 0], "ti": [1.142, -0.652, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [167.761, 407.938, 0], "to": [-1.142, 0.652, 0], "ti": [1.146, -0.65, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [164.327, 409.895, 0], "to": [-1.146, 0.65, 0], "ti": [1.156, -0.646, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [160.882, 411.84, 0], "to": [-1.156, 0.646, 0], "ti": [1.17, -0.633, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [157.394, 413.773, 0], "to": [-1.17, 0.633, 0], "ti": [0.763, -0.274, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [153.864, 415.636, 0], "to": [-0.763, 0.274, 0], "ti": [-2.547, 2.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [152.816, 415.417, 0], "to": [2.547, -2.049, 0], "ti": [-5.43, 3.942, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [169.143, 403.342, 0], "to": [5.43, -3.942, 0], "ti": [-5.379, 3.807, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [185.396, 391.765, 0], "to": [5.379, -3.807, 0], "ti": [-5.295, 3.708, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [201.419, 380.502, 0], "to": [5.295, -3.708, 0], "ti": [-5.197, 3.62, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [217.163, 369.518, 0], "to": [5.197, -3.62, 0], "ti": [-3.611, 2.374, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [232.6, 358.782, 0], "to": [3.611, -2.374, 0], "ti": [-2.731, 1.809, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [238.826, 355.275, 0], "to": [2.731, -1.809, 0], "ti": [-3.181, 2.3, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [248.983, 347.93, 0], "to": [3.181, -2.3, 0], "ti": [-2.752, 1.99, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [257.912, 341.473, 0], "to": [2.752, -1.99, 0], "ti": [-2.294, 1.659, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [265.498, 335.987, 0], "to": [2.294, -1.659, 0], "ti": [-1.82, 1.316, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [271.674, 331.521, 0], "to": [1.82, -1.316, 0], "ti": [-1.343, 0.972, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [276.415, 328.093, 0], "to": [1.343, -0.972, 0], "ti": [-0.878, 0.635, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [279.735, 325.692, 0], "to": [0.878, -0.635, 0], "ti": [-0.434, 0.314, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [281.684, 324.283, 0], "to": [0.434, -0.314, 0], "ti": [-0.022, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271, "s": [282.341, 323.807, 0], "to": [0.022, -0.016, 0], "ti": [0.352, -0.255, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272, "s": [281.813, 324.189, 0], "to": [-0.352, 0.255, 0], "ti": [0.682, -0.493, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273, "s": [280.227, 325.337, 0], "to": [-0.682, 0.493, 0], "ti": [0.962, -0.696, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274, "s": [277.723, 327.147, 0], "to": [-0.962, 0.696, 0], "ti": [1.191, -0.861, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [274.453, 329.512, 0], "to": [-1.191, 0.861, 0], "ti": [1.367, -0.989, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [270.576, 332.315, 0], "to": [-1.367, 0.989, 0], "ti": [1.492, -1.079, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [266.248, 335.445, 0], "to": [-1.492, 1.079, 0], "ti": [1.566, -1.132, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278, "s": [261.625, 338.788, 0], "to": [-1.566, 1.132, 0], "ti": [1.592, -1.151, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279, "s": [256.854, 342.239, 0], "to": [-1.592, 1.151, 0], "ti": [1.575, -1.139, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [252.071, 345.697, 0], "to": [-1.575, 1.139, 0], "ti": [1.519, -1.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281, "s": [247.402, 349.074, 0], "to": [-1.519, 1.099, 0], "ti": [1.429, -1.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282, "s": [242.956, 352.289, 0], "to": [-1.429, 1.034, 0], "ti": [1.311, -0.948, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [238.826, 355.275, 0], "to": [-1.311, 0.948, 0], "ti": [1.17, -0.846, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [235.09, 357.977, 0], "to": [-1.17, 0.846, 0], "ti": [1.013, -0.732, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [231.805, 360.352, 0], "to": [-1.013, 0.732, 0], "ti": [0.844, -0.61, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286, "s": [229.014, 362.37, 0], "to": [-0.844, 0.61, 0], "ti": [0.379, -0.274, 0]}, {"t": 287, "s": [226.742, 364.013, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141.586, "s": [{"i": [[0, 0], [28.463, -9.466], [23.509, 44.475], [-71.856, 10.934], [-17.205, -16.305]], "o": [[-15.534, 52.119], [-42.694, 14.199], [-23.509, -44.475], [47.904, -7.289], [0, 0]], "v": [[99.096, -3.218], [33.101, 89.16], [-86.412, 36.518], [-33.434, -90.03], [64.229, -76.507]], "c": true}]}, {"t": 144.365234375, "s": [{"i": [[0, 0], [28.463, -9.466], [23.509, 44.475], [-71.856, 10.934], [-17.205, -16.305]], "o": [[-15.534, 52.119], [-42.694, 14.199], [-23.509, -44.475], [47.904, -7.289], [0, 0]], "v": [[99.096, -3.218], [33.101, 89.16], [-86.412, 36.518], [-33.434, -90.03], [64.229, -76.507]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.75686275959, 0.270588248968, 0.270588248968, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.165, 0.165, 0.5, 1, 0.52, 0.531, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [10, 6], "ix": 5}, "e": {"a": 0, "k": [-98.326, 101.725], "ix": 6}, "t": 1, "nm": "jianbian20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-15备份-4", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "粉花瓣高光", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [33.481, 50.166, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [57.855, -2.694]], "o": [[0, 0], [-41.72, 1.943]], "v": [[36.707, -23.781], [-36.707, 23.781]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.951, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "粉花瓣描边", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 70, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [21.172, 31.622, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-5.461, -13.358]], "o": [[10.691, 8.405], [0, 0]], "v": [[-12.114, -16.322], [12.114, 16.322]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.890196084976, 0.329411774874, 0.074509806931, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-20备份-3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "粉花瓣", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [316.073, 372.121, 0], "to": [0.028, 0.067, 0], "ti": [-0.219, -0.523, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [316.239, 372.521, 0], "to": [0.219, 0.523, 0], "ti": [-0.385, -0.911, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [317.386, 375.257, 0], "to": [0.385, 0.911, 0], "ti": [-0.388, -0.91, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [318.547, 377.988, 0], "to": [0.388, 0.91, 0], "ti": [-0.389, -0.91, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [319.714, 380.717, 0], "to": [0.389, 0.91, 0], "ti": [-0.389, -0.91, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [320.882, 383.446, 0], "to": [0.389, 0.91, 0], "ti": [-0.388, -0.909, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [322.047, 386.174, 0], "to": [0.388, 0.909, 0], "ti": [-0.386, -0.909, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [323.208, 388.902, 0], "to": [0.386, 0.909, 0], "ti": [-0.385, -0.91, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [324.364, 391.63, 0], "to": [0.385, 0.91, 0], "ti": [-0.383, -0.91, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [325.515, 394.36, 0], "to": [0.383, 0.91, 0], "ti": [-0.381, -0.911, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [326.661, 397.091, 0], "to": [0.381, 0.911, 0], "ti": [-0.38, -0.913, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [327.802, 399.827, 0], "to": [0.38, 0.913, 0], "ti": [-0.85, -1.938, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [328.94, 402.568, 0], "to": [0.85, 1.938, 0], "ti": [-1.768, -3.688, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [332.9, 411.454, 0], "to": [1.768, 3.688, 0], "ti": [-2.223, -4.362, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [339.547, 424.699, 0], "to": [2.223, 4.362, 0], "ti": [-1.538, -2.931, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [346.238, 437.629, 0], "to": [1.538, 2.931, 0], "ti": [-1.035, -2.051, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [348.774, 442.285, 0], "to": [1.035, 2.051, 0], "ti": [-1.137, -2.37, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [352.446, 449.934, 0], "to": [1.137, 2.37, 0], "ti": [-0.959, -1.999, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [355.598, 456.502, 0], "to": [0.959, 1.999, 0], "ti": [-0.773, -1.613, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [358.2, 461.926, 0], "to": [0.773, 1.613, 0], "ti": [-0.586, -1.223, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [360.238, 466.178, 0], "to": [0.586, 1.223, 0], "ti": [-0.401, -0.839, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [361.714, 469.262, 0], "to": [0.401, 0.839, 0], "ti": [-0.224, -0.472, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [362.645, 471.213, 0], "to": [0.224, 0.472, 0], "ti": [-0.059, -0.128, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [363.06, 472.093, 0], "to": [0.059, 0.128, 0], "ti": [0.092, 0.185, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [362.999, 471.982, 0], "to": [-0.092, -0.185, 0], "ti": [0.225, 0.463, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [362.509, 470.982, 0], "to": [-0.225, -0.463, 0], "ti": [0.34, 0.701, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [361.646, 469.205, 0], "to": [-0.34, -0.701, 0], "ti": [0.435, 0.898, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [360.469, 466.775, 0], "to": [-0.435, -0.898, 0], "ti": [0.508, 1.051, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [359.039, 463.82, 0], "to": [-0.508, -1.051, 0], "ti": [0.562, 1.162, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [357.418, 460.468, 0], "to": [-0.562, -1.162, 0], "ti": [0.595, 1.231, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [355.668, 456.848, 0], "to": [-0.595, -1.231, 0], "ti": [0.61, 1.261, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [353.847, 453.081, 0], "to": [-0.61, -1.261, 0], "ti": [0.606, 1.255, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [352.011, 449.281, 0], "to": [-0.606, -1.255, 0], "ti": [0.588, 1.215, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [350.209, 445.553, 0], "to": [-0.588, -1.215, 0], "ti": [0.555, 1.147, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [348.486, 441.989, 0], "to": [-0.555, -1.147, 0], "ti": [0.511, 1.055, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [346.88, 438.668, 0], "to": [-0.511, -1.055, 0], "ti": [0.457, 0.944, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [345.422, 435.657, 0], "to": [-0.457, -0.944, 0], "ti": [0.396, 0.817, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [344.139, 433.006, 0], "to": [-0.396, -0.817, 0], "ti": [0.33, 0.681, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [343.046, 430.752, 0], "to": [-0.33, -0.681, 0], "ti": [0.262, 0.539, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [342.156, 428.92, 0], "to": [-0.262, -0.539, 0], "ti": [0.193, 0.395, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [341.474, 427.519, 0], "to": [-0.193, -0.395, 0], "ti": [0.125, 0.254, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [340.998, 426.547, 0], "to": [-0.125, -0.254, 0], "ti": [0.06, 0.119, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [340.723, 425.992, 0], "to": [-0.06, -0.119, 0], "ti": [-0.001, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [340.637, 425.831, 0], "to": [0.001, 0.007, 0], "ti": [-0.056, -0.122, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [340.727, 426.034, 0], "to": [0.056, 0.122, 0], "ti": [-0.105, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [340.974, 426.565, 0], "to": [0.105, 0.224, 0], "ti": [-0.147, -0.312, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [341.358, 427.381, 0], "to": [0.147, 0.312, 0], "ti": [-0.182, -0.384, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [341.858, 428.436, 0], "to": [0.182, 0.384, 0], "ti": [-0.209, -0.44, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [342.451, 429.685, 0], "to": [0.209, 0.44, 0], "ti": [-0.229, -0.481, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [343.114, 431.079, 0], "to": [0.229, 0.481, 0], "ti": [-0.241, -0.507, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [343.824, 432.572, 0], "to": [0.241, 0.507, 0], "ti": [-0.246, -0.518, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [344.561, 434.119, 0], "to": [0.246, 0.518, 0], "ti": [-0.245, -0.515, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [345.303, 435.678, 0], "to": [0.245, 0.515, 0], "ti": [-0.238, -0.501, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [346.032, 437.211, 0], "to": [0.238, 0.501, 0], "ti": [-0.226, -0.476, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [346.733, 438.684, 0], "to": [0.226, 0.476, 0], "ti": [-0.21, -0.442, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [347.39, 440.067, 0], "to": [0.21, 0.442, 0], "ti": [-0.19, -0.401, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [347.993, 441.338, 0], "to": [0.19, 0.401, 0], "ti": [-0.168, -0.355, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [348.533, 442.476, 0], "to": [0.168, 0.355, 0], "ti": [-0.144, -0.305, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [349.002, 443.468, 0], "to": [0.144, 0.305, 0], "ti": [-0.119, -0.253, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [349.397, 444.305, 0], "to": [0.119, 0.253, 0], "ti": [-0.093, -0.2, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [349.715, 444.984, 0], "to": [0.093, 0.2, 0], "ti": [-0.068, -0.148, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [349.958, 445.504, 0], "to": [0.068, 0.148, 0], "ti": [-0.044, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [350.126, 445.871, 0], "to": [0.044, 0.098, 0], "ti": [-0.016, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [350.225, 446.093, 0], "to": [0.016, 0.036, 0], "ti": [0.021, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [350.22, 446.088, 0], "to": [-0.021, -0.046, 0], "ti": [0.049, 0.108, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [350.098, 445.818, 0], "to": [-0.049, -0.108, 0], "ti": [0.065, 0.14, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [349.926, 445.442, 0], "to": [-0.065, -0.14, 0], "ti": [0.077, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [349.711, 444.979, 0], "to": [-0.077, -0.166, 0], "ti": [0.087, 0.187, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [349.462, 444.445, 0], "to": [-0.087, -0.187, 0], "ti": [0.094, 0.202, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [349.188, 443.857, 0], "to": [-0.094, -0.202, 0], "ti": [0.099, 0.211, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [348.895, 443.233, 0], "to": [-0.099, -0.211, 0], "ti": [0.101, 0.215, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [348.594, 442.589, 0], "to": [-0.101, -0.215, 0], "ti": [0.101, 0.215, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [348.29, 441.94, 0], "to": [-0.101, -0.215, 0], "ti": [0.098, 0.21, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [347.99, 441.301, 0], "to": [-0.098, -0.21, 0], "ti": [0.094, 0.2, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [347.701, 440.683, 0], "to": [-0.094, -0.2, 0], "ti": [0.088, 0.188, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [347.428, 440.099, 0], "to": [-0.088, -0.188, 0], "ti": [0.08, 0.173, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [347.175, 439.556, 0], "to": [-0.08, -0.173, 0], "ti": [0.072, 0.156, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [346.945, 439.062, 0], "to": [-0.072, -0.156, 0], "ti": [0.063, 0.137, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [346.742, 438.622, 0], "to": [-0.063, -0.137, 0], "ti": [0.054, 0.118, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [346.566, 438.24, 0], "to": [-0.054, -0.118, 0], "ti": [0.045, 0.098, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [346.418, 437.915, 0], "to": [-0.045, -0.098, 0], "ti": [0.035, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [346.298, 437.65, 0], "to": [-0.035, -0.079, 0], "ti": [0.027, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [346.206, 437.44, 0], "to": [-0.027, -0.061, 0], "ti": [0.018, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [346.139, 437.284, 0], "to": [-0.018, -0.044, 0], "ti": [0.011, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [346.095, 437.178, 0], "to": [-0.011, -0.028, 0], "ti": [0.004, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [346.074, 437.116, 0], "to": [-0.004, -0.014, 0], "ti": [-0.001, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [346.07, 437.092, 0], "to": [0.001, -0.002, 0], "ti": [-0.006, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [346.083, 437.101, 0], "to": [0.006, 0.007, 0], "ti": [-0.01, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [346.107, 437.135, 0], "to": [0.01, 0.015, 0], "ti": [-0.012, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [346.141, 437.19, 0], "to": [0.012, 0.02, 0], "ti": [-0.014, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [346.182, 437.257, 0], "to": [0.014, 0.024, 0], "ti": [-0.015, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [346.226, 437.332, 0], "to": [0.015, 0.025, 0], "ti": [-0.015, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [346.271, 437.408, 0], "to": [0.015, 0.025, 0], "ti": [-0.014, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [346.314, 437.481, 0], "to": [0.014, 0.023, 0], "ti": [-0.012, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [346.354, 437.546, 0], "to": [0.012, 0.02, 0], "ti": [-0.01, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [346.387, 437.599, 0], "to": [0.01, 0.015, 0], "ti": [-0.007, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [346.413, 437.636, 0], "to": [0.007, 0.009, 0], "ti": [-0.004, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [346.431, 437.655, 0], "to": [0.004, 0.003, 0], "ti": [-0.001, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [346.438, 437.655, 0], "to": [0.001, -0.004, 0], "ti": [0.002, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [346.436, 437.633, 0], "to": [-0.002, -0.011, 0], "ti": [0.006, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [346.424, 437.591, 0], "to": [-0.006, -0.018, 0], "ti": [0.009, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [346.401, 437.526, 0], "to": [-0.009, -0.025, 0], "ti": [0.012, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [346.368, 437.442, 0], "to": [-0.012, -0.032, 0], "ti": [0.016, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [346.326, 437.337, 0], "to": [-0.016, -0.038, 0], "ti": [0.018, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [346.275, 437.214, 0], "to": [-0.018, -0.044, 0], "ti": [0.021, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [346.216, 437.076, 0], "to": [-0.021, -0.049, 0], "ti": [0.023, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [346.151, 436.922, 0], "to": [-0.023, -0.053, 0], "ti": [0.025, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [346.08, 436.757, 0], "to": [-0.025, -0.057, 0], "ti": [0.026, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [346.004, 436.583, 0], "to": [-0.026, -0.059, 0], "ti": [0.027, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [345.924, 436.401, 0], "to": [-0.027, -0.061, 0], "ti": [0.027, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [345.843, 436.214, 0], "to": [-0.027, -0.063, 0], "ti": [0.045, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [345.76, 436.025, 0], "to": [-0.045, -0.099, 0], "ti": [0.043, -0.49, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [345.573, 435.621, 0], "to": [-0.043, 0.49, 0], "ti": [-0.053, -1.208, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [345.502, 438.963, 0], "to": [0.053, 1.208, 0], "ti": [-0.135, -1.302, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [345.89, 442.872, 0], "to": [0.135, 1.302, 0], "ti": [-0.144, -1.305, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [346.315, 446.773, 0], "to": [0.144, 1.305, 0], "ti": [-0.146, -1.31, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [346.753, 450.7, 0], "to": [0.146, 1.31, 0], "ti": [-0.143, -1.313, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [347.189, 454.632, 0], "to": [0.143, 1.313, 0], "ti": [-0.137, -1.316, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [347.613, 458.577, 0], "to": [0.137, 1.316, 0], "ti": [-0.123, -1.324, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [348.008, 462.53, 0], "to": [0.123, 1.324, 0], "ti": [-0.089, -1.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [348.35, 466.519, 0], "to": [0.089, 1.331, 0], "ti": [0.152, -0.876, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [348.542, 470.517, 0], "to": [-0.152, 0.876, 0], "ti": [1.334, 2.971, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [347.439, 471.775, 0], "to": [-1.334, -2.971, 0], "ti": [2.195, 6.334, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [340.54, 452.689, 0], "to": [-2.195, -6.334, 0], "ti": [2.042, 6.256, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [334.269, 433.77, 0], "to": [-2.042, -6.256, 0], "ti": [1.957, 6.147, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [328.287, 415.153, 0], "to": [-1.957, -6.147, 0], "ti": [1.892, 6.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [322.526, 396.885, 0], "to": [-1.892, -6.028, 0], "ti": [1.075, 4.127, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [316.933, 378.986, 0], "to": [-1.075, -4.127, 0], "ti": [0.813, 3.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [316.073, 372.121, 0], "to": [-0.813, -3.114, 0], "ti": [1.259, 3.701, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [312.055, 360.302, 0], "to": [-1.259, -3.701, 0], "ti": [1.089, 3.203, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [308.522, 349.913, 0], "to": [-1.089, -3.203, 0], "ti": [0.908, 2.669, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [305.52, 341.086, 0], "to": [-0.908, -2.669, 0], "ti": [0.72, 2.117, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [303.077, 333.899, 0], "to": [-0.72, -2.117, 0], "ti": [0.532, 1.563, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [301.201, 328.382, 0], "to": [-0.532, -1.563, 0], "ti": [0.347, 1.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [299.887, 324.519, 0], "to": [-0.347, -1.022, 0], "ti": [0.172, 0.505, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [299.116, 322.252, 0], "to": [-0.172, -0.505, 0], "ti": [0.009, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [298.856, 321.487, 0], "to": [-0.009, -0.025, 0], "ti": [-0.139, -0.41, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [299.065, 322.101, 0], "to": [0.139, 0.41, 0], "ti": [-0.27, -0.793, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [299.693, 323.948, 0], "to": [0.27, 0.793, 0], "ti": [-0.381, -1.12, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [300.684, 326.861, 0], "to": [0.381, 1.12, 0], "ti": [-0.471, -1.386, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271, "s": [301.977, 330.665, 0], "to": [0.471, 1.386, 0], "ti": [-0.541, -1.591, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272, "s": [303.511, 335.177, 0], "to": [0.541, 1.591, 0], "ti": [-0.59, -1.736, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273, "s": [305.223, 340.213, 0], "to": [0.59, 1.736, 0], "ti": [-0.62, -1.822, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274, "s": [307.053, 345.592, 0], "to": [0.62, 1.822, 0], "ti": [-0.63, -1.853, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [308.941, 351.144, 0], "to": [0.63, 1.853, 0], "ti": [-0.623, -1.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [310.833, 356.709, 0], "to": [0.623, 1.833, 0], "ti": [-0.601, -1.768, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [312.68, 362.142, 0], "to": [0.601, 1.768, 0], "ti": [-0.566, -1.663, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278, "s": [314.439, 367.316, 0], "to": [0.566, 1.663, 0], "ti": [-0.519, -1.526, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279, "s": [316.073, 372.121, 0], "to": [0.519, 1.526, 0], "ti": [-0.463, -1.362, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [317.552, 376.469, 0], "to": [0.463, 1.362, 0], "ti": [-0.401, -1.178, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281, "s": [318.851, 380.291, 0], "to": [0.401, 1.178, 0], "ti": [-0.334, -0.982, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282, "s": [319.956, 383.539, 0], "to": [0.334, 0.982, 0], "ti": [-0.265, -0.779, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [320.855, 386.182, 0], "to": [0.265, 0.779, 0], "ti": [-0.196, -0.575, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [321.545, 388.212, 0], "to": [0.196, 0.575, 0], "ti": [-0.128, -0.376, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [322.028, 389.633, 0], "to": [0.128, 0.376, 0], "ti": [-0.063, -0.186, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286, "s": [322.312, 390.467, 0], "to": [0.063, 0.186, 0], "ti": [-0.016, -0.047, 0]}, {"t": 287, "s": [322.407, 390.749, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.439, "s": [{"i": [[0, 0], [-20.1, -22.265], [-42.376, 27.112], [57.133, 44.929], [23.006, -5.707]], "o": [[-12.172, 53.006], [30.15, 33.397], [42.376, -27.112], [-38.089, -29.953], [0, 0]], "v": [[-91.194, -47.401], [-79.302, 65.504], [50.633, 78.604], [66.908, -57.617], [-24.735, -93.985]], "c": true}]}, {"t": 146.21875, "s": [{"i": [[0, 0], [-20.1, -22.265], [-42.376, 27.112], [57.133, 44.929], [23.006, -5.707]], "o": [[-12.172, 53.006], [30.15, 33.397], [42.376, -27.112], [-38.089, -29.953], [0, 0]], "v": [[-91.194, -47.401], [-79.302, 65.504], [50.633, 78.604], [66.908, -57.617], [-24.735, -93.985]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.890196084976, 0.329411774874, 0.074509806931, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.188, 0.667, 0.5, 1, 0.531, 0.782, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [-17, -14], "ix": 5}, "e": {"a": 0, "k": [73.927, 119.379], "ix": 6}, "t": 1, "nm": "jianbian22", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-15备份-3", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "紫花瓣高光", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.903, 13.543, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.91, -1.916], [1.916, 2.91], [-2.91, 1.916], [-1.916, -2.91]], "o": [[-2.91, 1.916], [-1.916, -2.91], [2.91, -1.916], [1.916, 2.91]], "v": [[3.469, 5.268], [-5.268, 3.469], [-3.469, -5.268], [5.268, -3.469]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254905701, 0.51372551918, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "紫花瓣高光", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [42.172, -42.262, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.577, -57.807]], "o": [[0, 0], [2.579, 41.685]], "v": [[-24.338, -36.34], [24.338, 36.34]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.937254905701, 0.51372551918, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.951, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "紫花瓣描边", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 70, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [28.44, -0.045, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-13.954, -3.678]], "o": [[13.147, -3.48], [0, 0]], "v": [[-20.326, 1.12], [20.326, 1.417]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.717647075653, 0.301960796118, 0.949019610882, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-20", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "紫花瓣", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [364.243, 301.082, 0], "to": [0.445, 0, 0], "ti": [-0.89, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [366.915, 301.082, 0], "to": [0.89, 0, 0], "ti": [-0.89, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [369.584, 301.082, 0], "to": [0.89, 0, 0], "ti": [-0.89, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [372.255, 301.082, 0], "to": [0.89, 0, 0], "ti": [-0.89, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [374.925, 301.082, 0], "to": [0.89, 0, 0], "ti": [-0.889, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [377.596, 301.082, 0], "to": [0.889, 0, 0], "ti": [-0.89, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [380.262, 301.082, 0], "to": [0.89, 0, 0], "ti": [-0.891, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [382.936, 301.082, 0], "to": [0.891, 0, 0], "ti": [-0.89, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [385.609, 301.082, 0], "to": [0.89, 0, 0], "ti": [-0.889, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [388.277, 301.082, 0], "to": [0.889, 0, 0], "ti": [-0.89, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [390.943, 301.082, 0], "to": [0.89, 0, 0], "ti": [-1.864, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [393.615, 301.082, 0], "to": [1.864, 0.049, 0], "ti": [-4.182, -0.356, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [402.128, 301.377, 0], "to": [4.182, 0.356, 0], "ti": [-5.499, -0.689, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [418.707, 303.22, 0], "to": [5.499, 0.689, 0], "ti": [-3.964, -0.529, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [435.125, 305.51, 0], "to": [3.964, 0.529, 0], "ti": [-2.122, -0.221, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [442.494, 306.393, 0], "to": [2.122, 0.221, 0], "ti": [-1.668, -0.138, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [447.859, 306.837, 0], "to": [1.668, 0.138, 0], "ti": [-1.418, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [452.499, 307.219, 0], "to": [1.418, 0.116, 0], "ti": [-1.156, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [456.366, 307.535, 0], "to": [1.156, 0.094, 0], "ti": [-0.891, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [459.438, 307.784, 0], "to": [0.891, 0.072, 0], "ti": [-0.63, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [461.715, 307.965, 0], "to": [0.63, 0.049, 0], "ti": [-0.378, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [463.218, 308.08, 0], "to": [0.378, 0.028, 0], "ti": [-0.142, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [463.986, 308.133, 0], "to": [0.142, 0.008, 0], "ti": [0.074, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [464.072, 308.128, 0], "to": [-0.074, -0.01, 0], "ti": [0.266, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [463.543, 308.07, 0], "to": [-0.266, -0.027, 0], "ti": [0.432, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [462.475, 307.967, 0], "to": [-0.432, -0.041, 0], "ti": [0.57, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [460.95, 307.825, 0], "to": [-0.57, -0.053, 0], "ti": [0.678, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [459.056, 307.651, 0], "to": [-0.678, -0.062, 0], "ti": [0.758, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [456.88, 307.454, 0], "to": [-0.758, -0.069, 0], "ti": [0.809, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [454.509, 307.24, 0], "to": [-0.809, -0.073, 0], "ti": [0.833, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [452.027, 307.016, 0], "to": [-0.833, -0.075, 0], "ti": [0.832, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [449.511, 306.79, 0], "to": [-0.832, -0.075, 0], "ti": [0.808, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [447.034, 306.567, 0], "to": [-0.808, -0.073, 0], "ti": [0.765, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [444.661, 306.352, 0], "to": [-0.765, -0.069, 0], "ti": [0.705, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [442.445, 306.151, 0], "to": [-0.705, -0.064, 0], "ti": [0.63, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [440.433, 305.968, 0], "to": [-0.63, -0.058, 0], "ti": [0.546, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [438.662, 305.804, 0], "to": [-0.546, -0.051, 0], "ti": [0.454, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [437.158, 305.664, 0], "to": [-0.454, -0.043, 0], "ti": [0.358, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [435.938, 305.547, 0], "to": [-0.358, -0.035, 0], "ti": [0.26, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [435.011, 305.456, 0], "to": [-0.26, -0.026, 0], "ti": [0.164, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [434.377, 305.389, 0], "to": [-0.164, -0.018, 0], "ti": [0.072, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [434.027, 305.347, 0], "to": [-0.072, -0.01, 0], "ti": [-0.015, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [433.947, 305.327, 0], "to": [0.015, -0.003, 0], "ti": [-0.095, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [434.119, 305.329, 0], "to": [0.095, 0.004, 0], "ti": [-0.166, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [434.516, 305.35, 0], "to": [0.166, 0.01, 0], "ti": [-0.226, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [435.112, 305.388, 0], "to": [0.226, 0.015, 0], "ti": [-0.277, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [435.875, 305.441, 0], "to": [0.277, 0.019, 0], "ti": [-0.317, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [436.774, 305.505, 0], "to": [0.317, 0.023, 0], "ti": [-0.346, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [437.776, 305.577, 0], "to": [0.346, 0.025, 0], "ti": [-0.365, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [438.85, 305.656, 0], "to": [0.365, 0.027, 0], "ti": [-0.373, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [439.964, 305.738, 0], "to": [0.373, 0.028, 0], "ti": [-0.373, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [441.09, 305.822, 0], "to": [0.373, 0.028, 0], "ti": [-0.364, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [442.202, 305.904, 0], "to": [0.364, 0.027, 0], "ti": [-0.348, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [443.277, 305.982, 0], "to": [0.348, 0.025, 0], "ti": [-0.326, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [444.293, 306.056, 0], "to": [0.326, 0.024, 0], "ti": [-0.299, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [445.235, 306.124, 0], "to": [0.299, 0.021, 0], "ti": [-0.268, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [446.088, 306.184, 0], "to": [0.268, 0.019, 0], "ti": [-0.235, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [446.844, 306.236, 0], "to": [0.235, 0.016, 0], "ti": [-0.199, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [447.496, 306.279, 0], "to": [0.199, 0.013, 0], "ti": [-0.164, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [448.04, 306.312, 0], "to": [0.164, 0.01, 0], "ti": [-0.128, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [448.477, 306.337, 0], "to": [0.128, 0.007, 0], "ti": [-0.094, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [448.81, 306.352, 0], "to": [0.094, 0.004, 0], "ti": [-0.062, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [449.042, 306.359, 0], "to": [0.062, 0.001, 0], "ti": [-0.033, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [449.182, 306.359, 0], "to": [0.033, -0.001, 0], "ti": [-0.007, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [449.239, 306.351, 0], "to": [0.007, -0.004, 0], "ti": [0.056, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [449.222, 306.337, 0], "to": [-0.056, -0.006, 0], "ti": [0.124, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [448.902, 306.318, 0], "to": [-0.124, -0.007, 0], "ti": [0.148, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [448.477, 306.294, 0], "to": [-0.148, -0.008, 0], "ti": [0.159, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [448.014, 306.268, 0], "to": [-0.159, -0.009, 0], "ti": [0.165, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [447.526, 306.239, 0], "to": [-0.165, -0.01, 0], "ti": [0.168, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [447.023, 306.208, 0], "to": [-0.168, -0.01, 0], "ti": [0.168, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [446.515, 306.178, 0], "to": [-0.168, -0.01, 0], "ti": [0.165, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [446.013, 306.147, 0], "to": [-0.165, -0.01, 0], "ti": [0.159, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [445.524, 306.118, 0], "to": [-0.159, -0.009, 0], "ti": [0.151, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [445.057, 306.091, 0], "to": [-0.151, -0.009, 0], "ti": [0.141, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [444.616, 306.066, 0], "to": [-0.141, -0.008, 0], "ti": [0.13, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [444.208, 306.044, 0], "to": [-0.13, -0.007, 0], "ti": [0.118, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [443.835, 306.025, 0], "to": [-0.118, -0.006, 0], "ti": [0.105, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [443.502, 306.01, 0], "to": [-0.105, -0.005, 0], "ti": [0.091, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [443.208, 305.997, 0], "to": [-0.091, -0.004, 0], "ti": [0.078, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [442.954, 305.988, 0], "to": [-0.078, -0.002, 0], "ti": [0.066, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [442.738, 305.982, 0], "to": [-0.066, -0.001, 0], "ti": [0.054, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [442.56, 305.98, 0], "to": [-0.054, 0, 0], "ti": [0.043, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [442.415, 305.98, 0], "to": [-0.043, 0.001, 0], "ti": [0.033, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [442.302, 305.983, 0], "to": [-0.033, 0.001, 0], "ti": [0.025, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [442.215, 305.988, 0], "to": [-0.025, 0.002, 0], "ti": [0.018, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [442.151, 305.995, 0], "to": [-0.018, 0.003, 0], "ti": [0.013, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [442.106, 306.004, 0], "to": [-0.013, 0.003, 0], "ti": [0.009, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [442.075, 306.014, 0], "to": [-0.009, 0.003, 0], "ti": [0.006, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [442.053, 306.024, 0], "to": [-0.006, 0.004, 0], "ti": [0.005, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [442.036, 306.035, 0], "to": [-0.005, 0.004, 0], "ti": [0.005, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [442.021, 306.047, 0], "to": [-0.005, 0.004, 0], "ti": [0.007, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [442.004, 306.058, 0], "to": [-0.007, 0.004, 0], "ti": [0.009, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [441.982, 306.068, 0], "to": [-0.009, 0.003, 0], "ti": [0.012, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [441.952, 306.078, 0], "to": [-0.012, 0.003, 0], "ti": [0.015, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [441.911, 306.088, 0], "to": [-0.015, 0.003, 0], "ti": [0.02, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [441.859, 306.096, 0], "to": [-0.02, 0.003, 0], "ti": [0.024, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [441.793, 306.103, 0], "to": [-0.024, 0.002, 0], "ti": [0.029, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [441.713, 306.109, 0], "to": [-0.029, 0.002, 0], "ti": [0.034, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [441.619, 306.113, 0], "to": [-0.034, 0.001, 0], "ti": [0.039, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [441.51, 306.116, 0], "to": [-0.039, 0.001, 0], "ti": [0.043, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [441.387, 306.118, 0], "to": [-0.043, 0.001, 0], "ti": [0.048, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [441.251, 306.119, 0], "to": [-0.048, 0, 0], "ti": [0.052, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [441.102, 306.119, 0], "to": [-0.052, 0, 0], "ti": [0.055, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [440.942, 306.118, 0], "to": [-0.055, 0, 0], "ti": [0.058, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [440.772, 306.116, 0], "to": [-0.058, -0.001, 0], "ti": [0.061, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [440.594, 306.114, 0], "to": [-0.061, -0.001, 0], "ti": [0.063, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [440.408, 306.111, 0], "to": [-0.063, -0.001, 0], "ti": [0.064, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [440.218, 306.107, 0], "to": [-0.064, -0.001, 0], "ti": [0.065, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [440.024, 306.103, 0], "to": [-0.065, -0.001, 0], "ti": [0.065, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [439.829, 306.099, 0], "to": [-0.065, -0.001, 0], "ti": [0.065, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [439.632, 306.095, 0], "to": [-0.065, -0.001, 0], "ti": [0.065, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [439.437, 306.091, 0], "to": [-0.065, -0.001, 0], "ti": [0.064, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [439.243, 306.087, 0], "to": [-0.064, -0.001, 0], "ti": [0.063, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [439.052, 306.083, 0], "to": [-0.063, -0.001, 0], "ti": [0.052, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [438.865, 306.08, 0], "to": [-0.052, 0, 0], "ti": [-0.392, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [438.743, 306.082, 0], "to": [0.392, 0.039, 0], "ti": [-0.972, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [441.218, 306.312, 0], "to": [0.972, 0.069, 0], "ti": [-1.12, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [444.577, 306.497, 0], "to": [1.12, 0.06, 0], "ti": [-1.123, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [447.94, 306.675, 0], "to": [1.123, 0.059, 0], "ti": [-1.126, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [451.317, 306.852, 0], "to": [1.126, 0.059, 0], "ti": [-1.128, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [454.697, 307.029, 0], "to": [1.128, 0.06, 0], "ti": [-1.129, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [458.085, 307.211, 0], "to": [1.129, 0.062, 0], "ti": [-1.133, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [461.469, 307.399, 0], "to": [1.133, 0.065, 0], "ti": [-1.136, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [464.883, 307.602, 0], "to": [1.136, 0.075, 0], "ti": [-0.384, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [468.287, 307.847, 0], "to": [0.384, 0.08, 0], "ti": [3.254, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [467.187, 308.082, 0], "to": [-3.254, -0.077, 0], "ti": [6.112, 0.308, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [448.764, 307.387, 0], "to": [-6.112, -0.308, 0], "ti": [6.053, 0.408, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [430.516, 306.237, 0], "to": [-6.053, -0.408, 0], "ti": [5.994, 0.442, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [412.447, 304.941, 0], "to": [-5.994, -0.442, 0], "ti": [5.936, 0.453, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [394.555, 303.582, 0], "to": [-5.936, -0.453, 0], "ti": [5.052, 0.417, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [376.833, 302.221, 0], "to": [-5.052, -0.417, 0], "ti": [3.124, 0.236, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [364.243, 301.082, 0], "to": [-3.124, -0.236, 0], "ti": [1.927, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [358.09, 300.804, 0], "to": [-1.927, -0.087, 0], "ti": [1.667, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [352.682, 300.56, 0], "to": [-1.667, -0.075, 0], "ti": [1.389, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [348.086, 300.353, 0], "to": [-1.389, -0.063, 0], "ti": [1.102, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [344.345, 300.184, 0], "to": [-1.102, -0.05, 0], "ti": [0.814, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [341.473, 300.054, 0], "to": [-0.814, -0.037, 0], "ti": [0.532, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [339.462, 299.964, 0], "to": [-0.532, -0.024, 0], "ti": [0.263, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [338.281, 299.91, 0], "to": [-0.263, -0.012, 0], "ti": [0.013, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [337.883, 299.892, 0], "to": [-0.013, -0.001, 0], "ti": [-0.214, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271, "s": [338.203, 299.907, 0], "to": [0.214, 0.01, 0], "ti": [-0.413, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272, "s": [339.164, 299.95, 0], "to": [0.413, 0.019, 0], "ti": [-0.583, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273, "s": [340.681, 300.019, 0], "to": [0.583, 0.026, 0], "ti": [-0.722, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274, "s": [342.661, 300.108, 0], "to": [0.722, 0.033, 0], "ti": [-0.828, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [345.01, 300.214, 0], "to": [0.828, 0.037, 0], "ti": [-0.904, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [347.631, 300.332, 0], "to": [0.904, 0.041, 0], "ti": [-0.949, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [350.432, 300.459, 0], "to": [0.949, 0.043, 0], "ti": [-0.965, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278, "s": [353.323, 300.589, 0], "to": [0.965, 0.044, 0], "ti": [-0.954, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279, "s": [356.22, 300.72, 0], "to": [0.954, 0.043, 0], "ti": [-0.92, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [359.048, 300.847, 0], "to": [0.92, 0.042, 0], "ti": [-0.866, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281, "s": [361.741, 300.969, 0], "to": [0.866, 0.039, 0], "ti": [-0.794, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282, "s": [364.243, 301.082, 0], "to": [0.794, 0.036, 0], "ti": [-0.709, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [366.506, 301.184, 0], "to": [0.709, 0.032, 0], "ti": [-0.613, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [368.496, 301.274, 0], "to": [0.613, 0.028, 0], "ti": [-0.511, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [370.187, 301.35, 0], "to": [0.511, 0.023, 0], "ti": [-0.405, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286, "s": [371.563, 301.412, 0], "to": [0.405, 0.018, 0], "ti": [-0.176, -0.008, 0]}, {"t": 287, "s": [372.62, 301.46, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141.586, "s": [{"i": [[0, 0], [-30.268, -13.929], [5.329, -40.133], [57.043, 15.034], [15.479, 36.76]], "o": [[37.435, -29.244], [45.401, 20.894], [-5.329, 40.133], [-38.029, -10.022], [0, 0]], "v": [[-68.009, -71.182], [33.545, -94.154], [88.16, 16.034], [-8.944, 98.44], [-89.206, 28.266]], "c": true}]}, {"t": 144.365234375, "s": [{"i": [[0, 0], [-30.268, -13.929], [5.329, -40.133], [57.043, 15.034], [15.479, 36.76]], "o": [[37.435, -29.244], [45.401, 20.894], [-5.329, 40.133], [-38.029, -10.022], [0, 0]], "v": [[-68.009, -71.182], [33.545, -94.154], [88.16, 16.034], [-8.944, 98.44], [-89.206, 28.266]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.717647075653, 0.301960796118, 0.949019610882, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 11.731, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.808, 0.184, 1, 0.5, 0.904, 0.529, 0.949, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [-45, 167], "ix": 5}, "e": {"a": 0, "k": [53.257, -147.082], "ix": 6}, "t": 1, "nm": "jianbian24", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-15备份-2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "花朵脸 内", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [300, 300, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 58, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 126, "s": [85, 85, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 186, "s": [97, 97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 243, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 251, "s": [90, 90, 100]}, {"t": 258, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 600, "h": 600, "ip": 0, "op": 420, "st": 0, "bm": 0}], "markers": []}