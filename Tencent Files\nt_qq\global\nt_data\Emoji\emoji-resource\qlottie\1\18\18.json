{"v": "5.7.8", "fr": 60, "ip": 0, "op": 150, "w": 512, "h": 512, "nm": "鞭炮", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.855, 39.339, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-61.145, 0.339, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.286, -2.4]], "o": [[0, 0], [0, 0]], "v": [[-68.569, 8.794], [-71.998, 12.395]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-50.293, -11.718], [-61.98, 1.319]], "c": false}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4.032, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON><PERSON><PERSON>", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [95.971, 32.636, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-25.029, -6.364, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.39, -33.723], [0, 0], [27.366, 3.311], [0, 0]], "o": [[0, 0], [-4.071, -23.695], [0, 0], [10.441, 0.845]], "v": [[1.145, 9.652], [-4.048, 16.789], [-51.204, -23.72], [-45.31, -29.517]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.988, 0.675, 0.208, 1, 0.988, 0.655, 0.2], "ix": 9}}, "s": {"a": 0, "k": [-47.367, -29.076], "ix": 5}, "e": {"a": 0, "k": [-1.851, 11.686], "ix": 6}, "t": 1, "nm": "sajnknkfknasng", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-57", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Oval", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [37.457, 100.772, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-83.543, 61.772, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.163, -1.167], [2.124, 0.126], [3.274, 1.518], [7.383, 7.411], [2.981, 6.614], [0.126, 2.513], [-1.176, 1.18], [-2.085, -0.121], [-3.214, -1.504], [-7.403, -7.432], [-3.058, -6.645], [-0.147, -2.496]], "o": [[-1.161, 1.166], [-2.486, -0.148], [-6.619, -3.07], [-7.336, -7.364], [-1.491, -3.307], [-0.109, -2.166], [1.156, -1.16], [2.441, 0.142], [6.543, 3.062], [7.383, 7.412], [1.513, 3.288], [0.126, 2.135]], "v": [[-57.353, 88.221], [-62.392, 89.674], [-71.128, 87.114], [-92.91, 71.02], [-108.706, 49.287], [-111.187, 40.457], [-109.688, 35.324], [-104.722, 33.869], [-96.148, 36.4], [-74.488, 52.515], [-58.455, 74.382], [-55.904, 83.156]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.729411780834, 0.035294119269, 0.035294119269, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.16, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.113725490868, 0.058823529631, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Oval", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [51.085, 86.037, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-69.915, 47.037, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.36, -9.286], [0, 0], [30.529, 0.826], [0, 0], [-14.217, -17.021]], "o": [[0, 0], [1.769, -13.539], [0, 0], [14.114, -1.476], [6.027, 7.216]], "v": [[-39.534, 65.554], [-47.694, 76.769], [-100.296, 24.548], [-93.111, 17.483], [-50.615, 40.801]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.861, 0.527, 0.137, 1, 0.733, 0.361, 0.059], "ix": 9}}, "s": {"a": 0, "k": [-98.743, 17.377], "ix": 5}, "e": {"a": 0, "k": [-39.436, 71.215], "ix": 6}, "t": 1, "nm": "sjznkmdkawng", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-55", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Mask 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.66, 67.636, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-53.34, 28.636, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-8.655, -6.482], [-2.767, -12.216]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [11.202, -0.534], [8.655, 6.482], [0, 0]], "v": [[5.603, 3.526], [-55.614, 87.646], [-56.587, 88.984], [-93.675, 71.782], [-110.453, 34.562], [-110.565, 34.68], [-110.582, 34.663], [-41.317, -33.443], [-11.531, -24.521], [5.603, 3.526]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-23.662, 24.181], "ix": 5}, "e": {"a": 0, "k": [-9.912, 34.947], "ix": 6}, "t": 1, "nm": "mux<PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Mask 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.66, 67.636, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-53.34, 28.636, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-8.655, -6.482], [-2.767, -12.216]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [11.202, -0.534], [8.655, 6.482], [0, 0]], "v": [[5.603, 3.526], [-55.614, 87.646], [-56.587, 88.984], [-93.675, 71.782], [-110.453, 34.562], [-110.565, 34.68], [-110.582, 34.663], [-41.317, -33.443], [-11.531, -24.521], [5.603, 3.526]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 0.998, 0.7, 0.339, 1, 0.996, 0.769, 0.482, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-64.294, 6.283], "ix": 5}, "e": {"a": 0, "k": [-76.709, -5.948], "ix": 6}, "t": 1, "nm": "zjndjfnasnhgs", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Mask 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.277, 67.639, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-53.723, 28.639, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-2.14, -0.036], [-7.767, -5.817], [-3.061, -10.105], [1.609, -2.211]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [1.526, -1.5], [9.818, 0.167], [7.596, 5.689], [0.793, 2.617], [0, 0]], "v": [[3.01, 7.089], [-55.614, 87.646], [-56.587, 88.984], [-93.675, 71.782], [-110.453, 34.562], [-110.565, 34.68], [-110.582, 34.663], [-43.633, -31.165], [-37.909, -33.496], [-11.531, -24.521], [4.454, -0.83], [3.194, 6.836]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.859, 0.192, 0.106, 0.242, 0.843, 0.184, 0.1, 0.485, 0.827, 0.176, 0.094, 0.742, 0.792, 0.159, 0.076, 1, 0.757, 0.141, 0.059, 0, 0, 0.242, 0.465, 0.485, 0.93, 0.742, 0.965, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-61.202, 15.578], "ix": 5}, "e": {"a": 0, "k": [-80.126, -0.213], "ix": 6}, "t": 1, "nm": "sakjkngaszjg", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Mask 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.277, 67.639, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-53.723, 28.639, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-2.14, -0.036], [-7.767, -5.817], [-3.061, -10.105], [1.609, -2.211]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [1.526, -1.5], [9.818, 0.167], [7.596, 5.689], [0.793, 2.617], [0, 0]], "v": [[3.01, 7.089], [-55.614, 87.646], [-56.587, 88.984], [-93.675, 71.782], [-110.453, 34.562], [-110.565, 34.68], [-110.582, 34.663], [-43.633, -31.165], [-37.909, -33.496], [-11.531, -24.521], [4.454, -0.83], [3.194, 6.836]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.859, 0.192, 0.106, 0.379, 0.859, 0.192, 0.106, 0.758, 0.859, 0.192, 0.106, 0.879, 0.859, 0.192, 0.106, 1, 0.859, 0.192, 0.106, 0, 0, 0.379, 0.465, 0.758, 0.93, 0.879, 0.965, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-28.226, 24.798], "ix": 5}, "e": {"a": 0, "k": [-14.928, 36.252], "ix": 6}, "t": 1, "nm": "sjkanmznmn", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.277, 67.639, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-53.723, 28.639, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-2.14, -0.036], [-7.767, -5.817], [-3.061, -10.105], [1.609, -2.211]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [1.526, -1.5], [9.818, 0.167], [7.596, 5.689], [0.793, 2.617], [0, 0]], "v": [[3.01, 7.089], [-55.614, 87.646], [-56.587, 88.984], [-93.675, 71.782], [-110.453, 34.562], [-110.565, 34.68], [-110.582, 34.663], [-43.633, -31.165], [-37.909, -33.496], [-11.531, -24.521], [4.454, -0.83], [3.194, 6.836]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.992, 0.2, 0.094, 0.5, 0.99, 0.255, 0.122, 1, 0.988, 0.31, 0.149], "ix": 9}}, "s": {"a": 0, "k": [-93.521, 19.631], "ix": 5}, "e": {"a": 0, "k": [-22.824, 34.766], "ix": 6}, "t": 1, "nm": "smnsnsawe", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [69.234, 38.129, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [30.234, -65.871, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.516, -2.157]], "o": [[0, 0], [0, 0]], "v": [[39.067, -58.901], [42.842, -55.665]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[17.627, -76.078], [31.257, -65.089]], "c": false}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4.032, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON><PERSON><PERSON>", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [34.173, 37.707, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-4.827, -66.293, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-33.829, 5.8], [0, 0], [3.788, -27.304], [0, 0]], "o": [[0, 0], [-23.762, 3.657], [0, 0], [1.028, -10.424]], "v": [[11.638, -92.172], [18.683, -86.855], [-22.642, -40.413], [-28.336, -46.408]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.967, 0.61, 0.153, 1, 0.945, 0.525, 0.09], "ix": 9}}, "s": {"a": 0, "k": [17.916, -88.139], "ix": 5}, "e": {"a": 0, "k": [-31.482, -38.498], "ix": 6}, "t": 1, "nm": "wewrwr", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-85", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Oval", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [101.272, 97.414, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [62.272, -6.586, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.147, -1.183], [0.163, -2.121], [1.575, -3.247], [7.539, -7.252], [6.665, -2.865], [2.515, -0.082], [1.16, 1.196], [-0.158, 2.083], [-1.56, 3.187], [-7.56, 7.272], [-6.698, 2.942], [-2.499, 0.103]], "o": [[1.145, 1.181], [-0.191, 2.483], [-3.185, 6.565], [-7.491, 7.206], [-3.332, 1.433], [-2.167, 0.071], [-1.14, -1.176], [0.185, -2.438], [3.176, -6.488], [7.54, -7.253], [3.314, -1.456], [2.137, -0.088]], "v": [[89.174, -32.312], [90.539, -27.248], [87.826, -18.558], [71.355, 2.94], [49.35, 18.354], [40.478, 20.681], [35.372, 19.092], [34.003, 14.101], [36.683, 5.573], [53.174, -15.802], [75.318, -31.451], [84.135, -33.849]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.729411780834, 0.035294119269, 0.035294119269, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.16, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.113725490868, 0.058823529631, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Oval", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [86.796, 83.495, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [47.796, -20.505, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-9.309, 1.198], [0, 0], [1.359, -30.509], [0, 0], [-17.267, 13.918]], "o": [[0, 0], [-13.506, -2.005], [0, 0], [-1.229, -14.138], [7.32, -5.9]], "v": [[66.821, -50.523], [77.892, -42.169], [24.761, 9.514], [17.822, 2.207], [41.878, -39.876]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.951, 0.553, 0.108, 1, 0.914, 0.412, 0], "ix": 9}}, "s": {"a": 0, "k": [69.562, -52.384], "ix": 5}, "e": {"a": 0, "k": [22.562, 6.75], "ix": 6}, "t": 1, "nm": "waerwwraws", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "awsfg", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Mask 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [68.719, 66.516, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29.719, -37.484, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.385, -0.397], [15.29, -14.709], [5.434, 5.605], [0, 0], [0, 0], [0, 0], [-6.632, 8.541], [-12.263, 2.553]], "o": [[0, 0], [0.494, 0.269], [5.434, 5.605], [-15.29, 14.709], [0, 0], [0, 0], [0, 0], [-0.339, -11.21], [6.632, -8.541], [0, 0]], "v": [[5.591, -96.736], [88.63, -34.061], [89.95, -33.064], [72.103, 3.718], [34.596, 19.844], [34.712, 19.958], [34.695, 19.975], [-32.191, -50.469], [-22.751, -80.094], [5.591, -96.736]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 0.998, 0.7, 0.339, 1, 0.996, 0.769, 0.482, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [44.192, -52.511], "ix": 5}, "e": {"a": 0, "k": [53.842, -66.42], "ix": 6}, "t": 1, "nm": "sanweix", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "path-78", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [68.721, 66.818, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29.721, -37.182, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-10.054, 2.896], [-1.719, -0.367], [-1.389, -1.108], [0, 0], [0, 0], [-0.385, -0.398], [15.29, -14.709], [5.434, 5.605], [0, 0], [0, 0], [0, 0], [-0.02, 1.461], [-6.168, 7.943]], "o": [[1.484, -0.428], [1.146, 0.244], [0, 0], [0, 0], [0.495, 0.269], [5.434, 5.605], [-15.29, 14.709], [0, 0], [0, 0], [0, 0], [-1.006, -1.06], [0.138, -10.252], [5.779, -7.442]], "v": [[0.998, -95.602], [5.491, -95.969], [9.294, -93.941], [9.294, -93.941], [88.627, -34.062], [89.95, -33.064], [72.103, 3.718], [34.596, 19.844], [34.711, 19.957], [34.695, 19.975], [-30.673, -48.869], [-32.211, -52.801], [-22.751, -80.094]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [16.85, -19.769], "ix": 5}, "e": {"a": 0, "k": [3.966, -6.464], "ix": 6}, "t": 1, "nm": "zmnmnjab", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-76", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "path-77", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [68.721, 66.818, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29.721, -37.182, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-10.054, 2.896], [-1.719, -0.367], [-1.389, -1.108], [0, 0], [0, 0], [-0.385, -0.398], [15.29, -14.709], [5.434, 5.605], [0, 0], [0, 0], [0, 0], [-0.02, 1.461], [-6.168, 7.943]], "o": [[1.484, -0.428], [1.146, 0.244], [0, 0], [0, 0], [0.495, 0.269], [5.434, 5.605], [-15.29, 14.709], [0, 0], [0, 0], [0, 0], [-1.006, -1.06], [0.138, -10.252], [5.779, -7.442]], "v": [[0.998, -95.602], [5.491, -95.969], [9.294, -93.941], [9.294, -93.941], [88.627, -34.062], [89.95, -33.064], [72.103, 3.718], [34.596, 19.844], [34.711, 19.957], [34.695, 19.975], [-30.673, -48.869], [-32.211, -52.801], [-22.751, -80.094]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.859, 0.192, 0.106, 0.379, 0.812, 0.163, 0.078, 0.758, 0.765, 0.133, 0.051, 0.879, 0.739, 0.124, 0.043, 1, 0.714, 0.114, 0.035, 0, 0, 0.379, 0.465, 0.758, 0.93, 0.879, 0.965, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [19.999, -53.259], "ix": 5}, "e": {"a": 0, "k": [37.162, -95.705], "ix": 6}, "t": 1, "nm": "j<PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Mask 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [68.719, 66.516, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29.719, -37.484, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.385, -0.397], [15.29, -14.709], [5.434, 5.605], [0, 0], [0, 0], [0, 0], [-6.632, 8.541], [-12.263, 2.553]], "o": [[0, 0], [0.494, 0.269], [5.434, 5.605], [-15.29, 14.709], [0, 0], [0, 0], [0, 0], [-0.339, -11.21], [6.632, -8.541], [0, 0]], "v": [[5.591, -96.736], [88.63, -34.061], [89.95, -33.064], [72.103, 3.718], [34.596, 19.844], [34.712, 19.958], [34.695, 19.975], [-32.191, -50.469], [-22.751, -80.094], [5.591, -96.736]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.859, 0.192, 0.106, 0.379, 0.859, 0.192, 0.106, 0.758, 0.859, 0.192, 0.106, 0.879, 0.859, 0.192, 0.106, 1, 0.859, 0.192, 0.106, 0, 0, 0.379, 0.465, 0.758, 0.93, 0.879, 0.965, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [25.518, -36.443], "ix": 5}, "e": {"a": 0, "k": [10.639, 2.203], "ix": 6}, "t": 1, "nm": "sknnansngh", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "path-76", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [68.721, 66.818, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29.721, -37.182, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-10.054, 2.896], [-1.719, -0.367], [-1.389, -1.108], [0, 0], [0, 0], [-0.385, -0.398], [15.29, -14.709], [5.434, 5.605], [0, 0], [0, 0], [0, 0], [-0.02, 1.461], [-6.168, 7.943]], "o": [[1.484, -0.428], [1.146, 0.244], [0, 0], [0, 0], [0.495, 0.269], [5.434, 5.605], [-15.29, 14.709], [0, 0], [0, 0], [0, 0], [-1.006, -1.06], [0.138, -10.252], [5.779, -7.442]], "v": [[0.998, -95.602], [5.491, -95.969], [9.294, -93.941], [9.294, -93.941], [88.627, -34.062], [89.95, -33.064], [72.103, 3.718], [34.596, 19.844], [34.711, 19.957], [34.695, 19.975], [-30.673, -48.869], [-32.211, -52.801], [-22.751, -80.094]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.992, 0.2, 0.094, 0.5, 0.99, 0.255, 0.122, 1, 0.988, 0.31, 0.149], "ix": 9}}, "s": {"a": 0, "k": [10.254, -4.393], "ix": 5}, "e": {"a": 0, "k": [29.123, -78.729], "ix": 6}, "t": 1, "nm": "zksknngawgh", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.645, 44.537, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-72.355, -114.463, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.286, -2.4]], "o": [[0, 0], [0, 0]], "v": [[-79.778, -106.007], [-83.207, -102.407]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-61.503, -126.519], [-73.189, -113.482]], "c": false}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4.032, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON><PERSON><PERSON>", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [95.761, 37.834, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-36.239, -121.166, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.39, -33.723], [0, 0], [27.366, 3.311], [0, 0]], "o": [[0, 0], [-4.071, -23.695], [0, 0], [10.441, 0.845]], "v": [[-10.064, -105.149], [-15.258, -98.012], [-62.413, -138.521], [-56.519, -144.319]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.988, 0.675, 0.208, 1, 0.988, 0.655, 0.2], "ix": 9}}, "s": {"a": 0, "k": [-61.65, -142.078], "ix": 5}, "e": {"a": 0, "k": [-11.294, -104.718], "ix": 6}, "t": 1, "nm": "g<PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-46", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Oval", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [37.248, 105.971, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-94.752, -53.029, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.163, -1.167], [2.124, 0.126], [3.274, 1.518], [7.383, 7.411], [2.981, 6.614], [0.126, 2.513], [-1.176, 1.18], [-2.085, -0.121], [-3.214, -1.504], [-7.403, -7.432], [-3.058, -6.645], [-0.147, -2.496]], "o": [[-1.161, 1.166], [-2.486, -0.148], [-6.619, -3.07], [-7.336, -7.364], [-1.491, -3.307], [-0.109, -2.166], [1.156, -1.16], [2.441, 0.142], [6.543, 3.062], [7.383, 7.412], [1.513, 3.288], [0.126, 2.135]], "v": [[-68.562, -26.58], [-73.601, -25.127], [-82.337, -27.688], [-104.119, -43.782], [-119.915, -65.515], [-122.397, -74.344], [-120.897, -79.477], [-115.931, -80.933], [-107.357, -78.402], [-85.698, -62.287], [-69.665, -40.419], [-67.113, -31.645]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.729411780834, 0.035294119269, 0.035294119269, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.16, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.113725490868, 0.058823529631, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Oval", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50.875, 91.236, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-81.125, -67.764, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.36, -9.286], [0, 0], [30.529, 0.826], [0, 0], [-14.217, -17.021]], "o": [[0, 0], [1.769, -13.539], [0, 0], [14.114, -1.476], [6.027, 7.216]], "v": [[-50.744, -49.248], [-58.903, -38.033], [-111.506, -90.254], [-104.321, -97.319], [-61.825, -74.001]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.988, 0.675, 0.208, 1, 0.988, 0.655, 0.2], "ix": 9}}, "s": {"a": 0, "k": [-105.982, -95.284], "ix": 5}, "e": {"a": 0, "k": [-53.814, -41.886], "ix": 6}, "t": 1, "nm": "<PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-43", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Mask 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.45, 72.838, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-64.55, -86.162, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-2.208, -0.042], [-7.739, -5.796], [-2.767, -12.216]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [1.575, -1.548], [9.775, 0.186], [8.655, 6.482], [0, 0]], "v": [[-5.607, -111.275], [-66.823, -27.155], [-67.797, -25.818], [-104.885, -43.02], [-121.662, -80.24], [-121.775, -80.122], [-121.792, -80.139], [-54.919, -145.892], [-49.012, -148.296], [-22.74, -139.323], [-5.607, -111.275]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-56.552, -70.711], "ix": 5}, "e": {"a": 0, "k": [-37.893, -53.781], "ix": 6}, "t": 1, "nm": "jen<PERSON>n", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-38", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Mask 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.45, 72.838, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-64.55, -86.162, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-2.208, -0.042], [-7.739, -5.796], [-2.767, -12.216]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [1.575, -1.548], [9.775, 0.186], [8.655, 6.482], [0, 0]], "v": [[-5.607, -111.275], [-66.823, -27.155], [-67.797, -25.818], [-104.885, -43.02], [-121.662, -80.24], [-121.775, -80.122], [-121.792, -80.139], [-54.919, -145.892], [-49.012, -148.296], [-22.74, -139.323], [-5.607, -111.275]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-85.501, -93.031], "ix": 5}, "e": {"a": 0, "k": [-99.381, -106.198], "ix": 6}, "t": 1, "nm": "wkwet", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-38", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Mask 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.45, 72.838, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-64.55, -86.162, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.39, -0.391], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-2.208, -0.042], [-7.739, -5.796], [-2.767, -12.216]], "o": [[0, 0], [-0.26, 0.498], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [1.575, -1.548], [9.775, 0.186], [8.655, 6.482], [0, 0]], "v": [[-5.607, -111.275], [-66.823, -27.155], [-67.797, -25.818], [-104.885, -43.02], [-121.662, -80.24], [-121.775, -80.122], [-121.792, -80.139], [-54.919, -145.892], [-49.012, -148.296], [-22.74, -139.323], [-5.607, -111.275]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.859, 0.192, 0.106, 0.231, 0.859, 0.192, 0.106, 0.462, 0.859, 0.192, 0.106, 0.731, 0.859, 0.176, 0.088, 1, 0.859, 0.161, 0.071, 0, 0, 0.231, 0.471, 0.462, 0.943, 0.731, 0.971, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-70.902, -99.082], "ix": 5}, "e": {"a": 0, "k": [-53.887, -21.137], "ix": 6}, "t": 1, "nm": "wanesnrt", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-35", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.075, 72.842, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-64.925, -86.158, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.101, -9.672], [0.188, -1.544], [1.301, -1.896], [0, 0], [0, 0], [0.391, -0.392], [14.973, 15.031], [-5.51, 5.531], [0, 0], [0, 0], [0, 0], [-0.689, 0.294], [-1.288, -0.035], [-7.633, -5.716]], "o": [[0.542, 1.691], [-0.126, 1.03], [0, 0], [0, 0], [-0.26, 0.5], [-5.51, 5.531], [-14.973, -15.031], [0, 0], [0, 0], [0, 0], [1.063, -0.967], [1.034, -0.441], [9.613, 0.26], [7.367, 5.517]], "v": [[-7.038, -116.539], [-6.417, -111.609], [-8.557, -107.221], [-8.557, -107.221], [-66.821, -27.158], [-67.797, -25.818], [-104.885, -43.02], [-121.662, -80.24], [-121.774, -80.123], [-121.792, -80.139], [-55.131, -145.683], [-52.503, -147.575], [-48.609, -148.287], [-22.74, -139.323]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.992, 0.2, 0.094, 0.5, 0.99, 0.255, 0.122, 1, 0.988, 0.31, 0.149], "ix": 9}}, "s": {"a": 0, "k": [-61.279, -18.584], "ix": 5}, "e": {"a": 0, "k": [-38.705, -141.316], "ix": 6}, "t": 1, "nm": "weahrr", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-31", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [76.931, 43.996, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [30.431, -179.504, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.192, -2.486]], "o": [[0, 0], [0, 0]], "v": [[38.208, -171.372], [41.495, -167.642]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[19.367, -191.365], [31.335, -178.587]], "c": false}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4.032, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON><PERSON><PERSON>", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "金色1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [42.317, 38.606, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-4.183, -184.894, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-34.307, 1.036], [0, 0], [7.552, -26.511], [0, 0]], "o": [[0, 0], [-24.04, 0.314], [0, 0], [2.468, -10.18]], "v": [[15.676, -208.136], [21.913, -201.89], [-25.474, -161.652], [-30.279, -168.381]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.988, 0.675, 0.208, 1, 0.988, 0.655, 0.2], "ix": 9}}, "s": {"a": 0, "k": [21.173, -206.802], "ix": 5}, "e": {"a": 0, "k": [-30.519, -163.743], "ix": 6}, "t": 1, "nm": "<PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-74", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "底部", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100.408, 107.177, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [53.908, -116.323, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.971, -1.331], [0.457, -2.078], [2.012, -2.996], [8.475, -6.133], [6.998, -1.91], [2.502, 0.268], [0.982, 1.346], [-0.446, 2.04], [-1.989, 2.939], [-8.498, 6.149], [-7.042, 1.981], [-2.489, -0.245]], "o": [[0.97, 1.329], [-0.535, 2.432], [-4.068, 6.057], [-8.421, 6.093], [-3.499, 0.955], [-2.156, -0.231], [-0.965, -1.323], [0.522, -2.389], [4.048, -5.983], [8.476, -6.133], [3.484, -0.98], [2.128, 0.21]], "v": [[84.127, -138.068], [84.773, -132.864], [80.878, -124.636], [61.574, -105.639], [37.638, -93.438], [28.529, -92.368], [23.694, -94.652], [23.033, -99.785], [26.874, -107.857], [46.179, -126.729], [70.285, -139.144], [79.351, -140.291]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.729411780834, 0.035294119269, 0.035294119269, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.16, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.113725490868, 0.058823529631, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Oval", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "金色2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [87.995, 91.085, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41.495, -132.415, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-9.385, -0.109], [0, 0], [5.591, -30.023], [0, 0], [-19.036, 11.379]], "o": [[0, 0], [-13.096, -3.866], [0, 0], [0.75, -14.171], [8.069, -4.824]], "v": [[64.525, -159.213], [74.326, -149.4], [14.519, -105.614], [8.665, -113.815], [38.344, -152.141]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.988, 0.675, 0.208, 1, 0.988, 0.655, 0.2], "ix": 9}}, "s": {"a": 0, "k": [72.9, -158.938], "ix": 5}, "e": {"a": 0, "k": [7.379, -107.578], "ix": 6}, "t": 1, "nm": "jianbian2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-71", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "path-63", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [72.91, 71.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [26.41, -152.25, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-0.233, 1.49], [-7.192, 6.987], [-11.152, 1.249]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [-0.876, -1.227], [1.576, -10.094], [7.137, -6.933], [2.916, -0.439]], "v": [[12.308, -211.509], [83.831, -139.875], [84.999, -138.704], [62.207, -104.764], [22.821, -94.016], [22.92, -93.886], [22.901, -93.872], [-32.217, -171.098], [-33.212, -175.341], [-20.06, -200.962], [7.375, -213.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [34.993, -175.135], "ix": 5}, "e": {"a": 0, "k": [43.969, -184.871], "ix": 6}, "t": 1, "nm": "jianbian4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-61", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "path-62", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [72.91, 71.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [26.41, -152.25, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-0.233, 1.49], [-7.192, 6.987], [-11.152, 1.249]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [-0.876, -1.227], [1.576, -10.094], [7.137, -6.933], [2.916, -0.439]], "v": [[12.308, -211.509], [83.831, -139.875], [84.999, -138.704], [62.207, -104.764], [22.821, -94.016], [22.92, -93.886], [22.901, -93.872], [-32.217, -171.098], [-33.212, -175.341], [-20.06, -200.962], [7.375, -213.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0.856, -151.586], "ix": 5}, "e": {"a": 0, "k": [-14.446, -139.738], "ix": 6}, "t": 1, "nm": "jianbian7", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-61", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "path-61", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [72.91, 71.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [26.41, -152.25, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-0.233, 1.49], [-7.192, 6.987], [-11.152, 1.249]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [-0.876, -1.227], [1.576, -10.094], [7.137, -6.933], [2.916, -0.439]], "v": [[12.308, -211.509], [83.831, -139.875], [84.999, -138.704], [62.207, -104.764], [22.821, -94.016], [22.92, -93.886], [22.901, -93.872], [-32.217, -171.098], [-33.212, -175.341], [-20.06, -200.962], [7.375, -213.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.859, 0.192, 0.106, 0.454, 0.829, 0.198, 0.116, 0.907, 0.8, 0.204, 0.125, 0, 0, 0.454, 0.5, 0.907, 1], "ix": 9}}, "s": {"a": 0, "k": [24.822, -160.621], "ix": 5}, "e": {"a": 0, "k": [20.34, -116.9], "ix": 6}, "t": 1, "nm": "jianbian8", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-61", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Mask 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [72.91, 71.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [26.41, -152.25, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-0.233, 1.49], [-7.192, 6.987], [-11.152, 1.249]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [-0.876, -1.227], [1.576, -10.094], [7.137, -6.933], [2.916, -0.439]], "v": [[12.308, -211.509], [83.831, -139.875], [84.999, -138.704], [62.207, -104.764], [22.821, -94.016], [22.92, -93.886], [22.901, -93.872], [-32.217, -171.098], [-33.212, -175.341], [-20.06, -200.962], [7.375, -213.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.992, 0.2, 0.094, 0.5, 0.99, 0.255, 0.122, 1, 0.988, 0.31, 0.149], "ix": 9}}, "s": {"a": 0, "k": [1.375, -122.15], "ix": 5}, "e": {"a": 0, "k": [46.486, -181.799], "ix": 6}, "t": 1, "nm": "jianbiamn89", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-61", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [73.005, 71.412, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [26.505, -152.088, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-9.635, 1.645], [-1.791, -0.666], [-1.121, -1.076], [0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [0.225, 1.174], [-0.314, 1.537], [-6.634, 6.444]], "o": [[2.036, -0.348], [1.194, 0.444], [0, 0], [0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [-0.845, -1.176], [-0.338, -1.762], [1.856, -9.088], [6.403, -6.22]], "v": [[3.998, -212.761], [10.236, -212.386], [13.708, -210.106], [13.708, -210.106], [83.831, -139.875], [84.999, -138.704], [62.207, -104.764], [22.821, -94.016], [22.919, -93.887], [22.901, -93.872], [-31.284, -169.791], [-32.89, -173.317], [-32.794, -177.664], [-20.06, -200.962]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941176474094, 0.105882354081, 0.247058823705, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-59", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [78.275, 47.647, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [36.275, 53.647, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.26, -4.505]], "o": [[0, 0], [0, 0]], "v": [[33.08, 50.268], [39.471, 57.026]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4.032, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON><PERSON><PERSON>", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.792, 33.773, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-6.208, 39.773, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-34.307, 1.036], [0, 0], [7.552, -26.511], [0, 0]], "o": [[0, 0], [-24.04, 0.314], [0, 0], [2.468, -10.18]], "v": [[13.651, 16.531], [19.888, 22.777], [-27.499, 63.015], [-32.303, 56.287]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.89, 0.631, 0.016, 0.068, 0.876, 0.606, 0.075, 0.136, 0.863, 0.58, 0.133, 0.19, 0.929, 0.716, 0.237, 0.244, 0.996, 0.851, 0.341, 0.285, 0.996, 0.851, 0.341, 0.326, 0.996, 0.851, 0.341, 0.419, 0.965, 0.739, 0.235, 0.512, 0.933, 0.627, 0.129, 0.756, 0.771, 0.445, 0.065, 1, 0.608, 0.263, 0], "ix": 9}}, "s": {"a": 0, "k": [19.445, 19.106], "ix": 5}, "e": {"a": 0, "k": [-29.15, 59.962], "ix": 6}, "t": 1, "nm": "szkmfkannsa", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-28", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Oval", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [93.883, 102.345, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [51.883, 108.345, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.971, -1.331], [0.457, -2.078], [2.012, -2.996], [8.475, -6.133], [6.998, -1.91], [2.502, 0.268], [0.982, 1.346], [-0.446, 2.04], [-1.989, 2.939], [-8.498, 6.149], [-7.042, 1.981], [-2.489, -0.245]], "o": [[0.97, 1.329], [-0.535, 2.432], [-4.068, 6.057], [-8.421, 6.093], [-3.499, 0.955], [-2.156, -0.231], [-0.965, -1.323], [0.522, -2.389], [4.048, -5.983], [8.476, -6.133], [3.484, -0.98], [2.128, 0.21]], "v": [[82.102, 86.6], [82.749, 91.804], [78.853, 100.032], [59.55, 119.028], [35.613, 131.23], [26.504, 132.299], [21.669, 130.015], [21.008, 124.883], [24.849, 116.811], [44.154, 97.938], [68.261, 85.524], [77.326, 84.376]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.729411780834, 0.035294119269, 0.035294119269, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.16, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.113725490868, 0.058823529631, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Oval", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [81.471, 86.253, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [39.471, 92.253, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-9.385, -0.109], [0, 0], [5.591, -30.023], [0, 0], [-19.036, 11.379]], "o": [[0, 0], [-13.096, -3.866], [0, 0], [0.75, -14.171], [8.069, -4.824]], "v": [[62.501, 65.455], [72.301, 75.268], [12.495, 119.054], [6.64, 110.852], [36.319, 72.527]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.988, 0.675, 0.208, 1, 0.988, 0.655, 0.2], "ix": 9}}, "s": {"a": 0, "k": [66.431, 65.741], "ix": 5}, "e": {"a": 0, "k": [10.585, 114.496], "ix": 6}, "t": 1, "nm": "awneankng", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-25", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Mask 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.261, 66.316, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [24.261, 72.316, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-7.756, 7.535], [-12.499, 0.822]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [1.225, -11.148], [7.756, -7.535], [0, 0]], "v": [[8.298, 11.17], [81.806, 84.792], [82.975, 85.963], [60.183, 119.903], [20.797, 130.652], [20.895, 130.781], [20.876, 130.796], [-35.556, 51.729], [-22.084, 23.705], [8.298, 11.17]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-9.747, 67.179], "ix": 5}, "e": {"a": 0, "k": [-23.603, 78.284], "ix": 6}, "t": 1, "nm": "zxngzmdng", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Mask 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.261, 66.316, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [24.261, 72.316, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-7.756, 7.535], [-12.499, 0.822]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [1.225, -11.148], [7.756, -7.535], [0, 0]], "v": [[8.298, 11.17], [81.806, 84.792], [82.975, 85.963], [60.183, 119.903], [20.797, 130.652], [20.895, 130.781], [20.876, 130.796], [-35.556, 51.729], [-22.084, 23.705], [8.298, 11.17]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 0.998, 0.7, 0.339, 1, 0.996, 0.769, 0.482, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [14.541, 40.482], "ix": 5}, "e": {"a": 0, "k": [32.056, 24.804], "ix": 6}, "t": 1, "nm": "awoijgasg", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Mask 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.261, 66.316, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [24.261, 72.316, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-7.756, 7.535], [-12.499, 0.822]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [1.225, -11.148], [7.756, -7.535], [0, 0]], "v": [[8.298, 11.17], [81.806, 84.792], [82.975, 85.963], [60.183, 119.903], [20.797, 130.652], [20.895, 130.781], [20.876, 130.796], [-35.556, 51.729], [-22.084, 23.705], [8.298, 11.17]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 7, "k": {"a": 0, "k": [0, 0.647, 0.122, 0.051, 0.234, 0.682, 0.124, 0.051, 0.468, 0.718, 0.125, 0.051, 0.734, 0.859, 0.296, 0.275, 1, 1, 0.467, 0.498, 1, 0.88, 0.306, 0.284, 1, 0.761, 0.145, 0.071, 0, 1, 0.234, 1, 0.468, 1, 0.734, 0.5, 1, 0, 1, 0, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-27.062, 16.783], "ix": 5}, "e": {"a": 0, "k": [24.76, 37.923], "ix": 6}, "t": 1, "nm": "awjnejnagsg", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Mask 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.261, 66.316, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [24.261, 72.316, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-7.756, 7.535], [-12.499, 0.822]], "o": [[0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [1.225, -11.148], [7.756, -7.535], [0, 0]], "v": [[8.298, 11.17], [81.806, 84.792], [82.975, 85.963], [60.183, 119.903], [20.797, 130.652], [20.895, 130.781], [20.876, 130.796], [-35.556, 51.729], [-22.084, 23.705], [8.298, 11.17]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.859, 0.192, 0.106, 0.379, 0.859, 0.192, 0.106, 0.758, 0.859, 0.192, 0.106, 0.879, 0.859, 0.192, 0.106, 1, 0.859, 0.192, 0.106, 0, 0, 0.379, 0.465, 0.758, 0.93, 0.879, 0.965, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [30.338, 85.483], "ix": 5}, "e": {"a": 0, "k": [10.954, 118.562], "ix": 6}, "t": 1, "nm": "waejjsnmnz", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "path-18", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.261, 66.51, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [24.261, 72.51, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-10.321, 1.479], [-1.385, -0.424], [-1.336, -1.027], [0, 0], [0, 0], [-0.326, -0.446], [17.188, -12.438], [4.601, 6.307], [0, 0], [0, 0], [0, 0], [-7.756, 7.535]], "o": [[1.556, -0.223], [0.923, 0.283], [0, 0], [0, 0], [0.452, 0.335], [4.601, 6.307], [-17.189, 12.437], [0, 0], [0, 0], [0, 0], [1.225, -11.148], [6.74, -6.547]], "v": [[3.507, 11.666], [7.696, 11.996], [11.084, 13.961], [11.084, 13.961], [81.806, 84.792], [82.975, 85.963], [60.183, 119.903], [20.797, 130.652], [20.895, 130.781], [20.876, 130.796], [-35.556, 51.729], [-22.084, 23.705]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.992, 0.2, 0.094, 0.5, 0.99, 0.255, 0.122, 1, 0.988, 0.31, 0.149], "ix": 9}}, "s": {"a": 0, "k": [2.146, 106.383], "ix": 5}, "e": {"a": 0, "k": [34.383, 39.201], "ix": 6}, "t": 1, "nm": "aiwnjntamnsng", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-18", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50.338, 114.391, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-63.662, 152.391, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[18.051, 29.193], [0, 0], [-26.735, 6.716], [0, 0]], "o": [[0, 0], [12.292, 20.662], [0, 0], [-10.05, 2.952]], "v": [[-94.078, 145.347], [-91.787, 136.823], [-33.246, 157.742], [-36.671, 165.267]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.996, 0.827, 0.384, 0.068, 0.992, 0.759, 0.3, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.988, 0.675, 0.208, 1, 0.988, 0.655, 0.2], "ix": 9}}, "s": {"a": 0, "k": [-96.151, 141.055], "ix": 5}, "e": {"a": 0, "k": [-35.873, 164.416], "ix": 6}, "t": 1, "nm": "weatsscxb", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-16", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Oval", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [80.291, 28.324, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-33.709, 66.324, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.624, 1.409], [-1.885, 0.598], [-3.536, -0.24], [-9.495, -4.249], [-5.124, -5.077], [-1.001, -2.261], [0.632, -1.427], [1.848, -0.589], [3.475, 0.248], [9.521, 4.261], [5.208, 5.079], [1.014, 2.238]], "o": [[0.623, -1.406], [2.332, -0.74], [7.239, 0.491], [9.432, 4.222], [2.531, 2.508], [0.818, 1.848], [-0.619, 1.398], [-2.288, 0.729], [-7.166, -0.511], [-9.495, -4.25], [-2.545, -2.482], [-0.821, -1.812]], "v": [[-67.227, 51.208], [-63.308, 48.283], [-54.394, 47.561], [-28.438, 54.744], [-6.031, 69.285], [-0.641, 76.507], [-0.201, 81.487], [-4.052, 84.389], [-12.802, 85.079], [-38.652, 77.833], [-61.331, 63.248], [-66.766, 56.103]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.600000023842, 0.0941176489, 0.0941176489, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.024, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.066666670144, 0.031372550875, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Oval", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [73.577, 48.525, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-40.423, 86.525, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.598, 8.182], [0, 0], [-28.797, 10.169], [0, 0], [19.372, 10.796]], "o": [[0, 0], [3.2, 13.274], [0, 0], [-12.648, 6.436], [-8.212, -4.576]], "v": [[-76.133, 78.581], [-72.535, 65.187], [-4.712, 95.088], [-8.888, 104.258], [-56.918, 97.719]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 11, "k": {"a": 0, "k": [0, 0.89, 0.631, 0.016, 0.068, 0.939, 0.661, 0.116, 0.136, 0.988, 0.69, 0.216, 0.19, 0.992, 0.841, 0.278, 0.244, 0.996, 0.992, 0.341, 0.285, 0.996, 0.992, 0.341, 0.326, 0.996, 0.992, 0.341, 0.419, 0.992, 0.843, 0.278, 0.512, 0.988, 0.694, 0.216, 0.756, 0.798, 0.478, 0.108, 1, 0.608, 0.263, 0], "ix": 9}}, "s": {"a": 0, "k": [-75.795, 75.972], "ix": 5}, "e": {"a": 0, "k": [-5.811, 100.865], "ix": 6}, "t": 1, "nm": "ijsnzfnmagnj", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-13", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Mask 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.808, 72.207, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-47.192, 110.207, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.224, 0.505], [-19.366, -8.667], [3.161, -7.138], [0, 0], [0, 0], [0, 0], [10.403, 2.95], [6.961, 10.413]], "o": [[0, 0], [0.064, -0.559], [3.162, -7.138], [19.365, 8.667], [0, 0], [0, 0], [0, 0], [-10.267, 4.513], [-10.403, -2.95], [0, 0]], "v": [[-96.044, 152.664], [-69.039, 52.193], [-68.609, 50.596], [-27.821, 53.364], [1.181, 82.099], [1.244, 81.949], [1.266, 81.958], [-38.992, 170.363], [-69.997, 172.708], [-96.044, 152.664]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-29.4, 121.409], "ix": 5}, "e": {"a": 0, "k": [-15.016, 128.207], "ix": 6}, "t": 1, "nm": "awkjnawmn", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Mask 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.808, 72.207, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-47.192, 110.207, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.224, 0.505], [-19.366, -8.667], [3.161, -7.138], [0, 0], [0, 0], [0, 0], [10.403, 2.95], [6.961, 10.413]], "o": [[0, 0], [0.064, -0.559], [3.162, -7.138], [19.365, 8.667], [0, 0], [0, 0], [0, 0], [-10.267, 4.513], [-10.403, -2.95], [0, 0]], "v": [[-96.044, 152.664], [-69.039, 52.193], [-68.609, 50.596], [-27.821, 53.364], [1.181, 82.099], [1.244, 81.949], [1.266, 81.958], [-38.992, 170.363], [-69.997, 172.708], [-96.044, 152.664]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.631, 0.196, 0.5, 1, 0.733, 0.42, 1, 1, 0.835, 0.643, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-68.756, 106.868], "ix": 5}, "e": {"a": 0, "k": [-87.388, 102.433], "ix": 6}, "t": 1, "nm": "zsmnfmnf", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Mask 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.808, 72.207, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-47.192, 110.207, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.224, 0.505], [-19.366, -8.667], [3.161, -7.138], [0, 0], [0, 0], [0, 0], [10.403, 2.95], [6.961, 10.413]], "o": [[0, 0], [0.064, -0.559], [3.162, -7.138], [19.365, 8.667], [0, 0], [0, 0], [0, 0], [-10.267, 4.513], [-10.403, -2.95], [0, 0]], "v": [[-96.044, 152.664], [-69.039, 52.193], [-68.609, 50.596], [-27.821, 53.364], [1.181, 82.099], [1.244, 81.949], [1.266, 81.958], [-38.992, 170.363], [-69.997, 172.708], [-96.044, 152.664]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 7, "k": {"a": 0, "k": [0, 0.71, 0.11, 0.031, 0.234, 0.725, 0.122, 0.045, 0.468, 0.741, 0.133, 0.059, 0.734, 0.871, 0.3, 0.278, 1, 1, 0.467, 0.498, 1, 0.88, 0.306, 0.284, 1, 0.761, 0.145, 0.071, 0, 1, 0.234, 1, 0.468, 1, 0.734, 0.5, 1, 0, 1, 0, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-38.197, 62.633], "ix": 5}, "e": {"a": 0, "k": [-55.492, 143.295], "ix": 6}, "t": 1, "nm": "waojemkwrg", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Mask", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67.271, 72.207, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-46.729, 110.207, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6.374, 7.962], [0.294, 1.536], [-0.507, 2.254], [0, 0], [0, 0], [-0.224, 0.505], [-19.366, -8.667], [3.161, -7.138], [0, 0], [0, 0], [0, 0], [0.594, -0.544], [1.187, -0.422], [9.202, 2.609]], "o": [[-1.09, -1.361], [-0.196, -1.024], [0, 0], [0, 0], [0.064, -0.559], [3.162, -7.138], [19.365, 8.667], [0, 0], [0, 0], [0, 0], [-0.762, 1.56], [-0.891, 0.816], [-9.095, 3.23], [-8.884, -2.519]], "v": [[-92.885, 156.986], [-95.037, 152.101], [-94.571, 147.183], [-94.571, 147.183], [-69.039, 52.193], [-68.609, 50.596], [-27.821, 53.364], [1.181, 82.099], [1.244, 81.949], [1.266, 81.958], [-37.311, 166.67], [-39.345, 169.826], [-42.551, 171.777], [-69.997, 172.708]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.992, 0.2, 0.094, 0.5, 0.99, 0.255, 0.122, 1, 0.988, 0.31, 0.149], "ix": 9}}, "s": {"a": 0, "k": [-7.391, 97.43], "ix": 5}, "e": {"a": 0, "k": [-80.691, 102.582], "ix": 6}, "t": 1, "nm": "znksmenakjwn", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "path-6", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}]}, {"id": "comp_6", "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12.5, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [100]}, {"t": 27.5, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [486, 500, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-12.68, 5.072], [0.5, 5]], "o": [[0, 0], [2.5, -1], [-1.339, -13.387]], "v": [[102.25, 241.5], [99.75, 242.25], [101, 233]], "c": true}, "ix": 2}, "nm": "路径 12", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [11, 0.5], [0, 0], [0, 0], [0, 0], [0, 0], [-47, 12]], "o": [[0, 0], [-11, -0.5], [0, 0], [0, 0], [0, 0], [0, 0], [47, -12]], "v": [[231.5, -59], [188, -58], [115, -21.5], [110.5, -15], [82, 0.5], [147, 27], [157.5, -31]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [11, 0.5], [0, 0], [0, 0], [0, 0], [0, 0], [-47, 12]], "o": [[0, 0], [-11, -0.5], [0, 0], [0, 0], [0, 0], [0, 0], [47, -12]], "v": [[279, -39.5], [235.5, -38.5], [213, -28.5], [208.5, -22], [180, -6.5], [207.5, -1], [237, -25.5]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [11, 0.5], [0, 0], [0, 0], [0, 0], [0, 0], [-23.75, 4.875]], "o": [[0, 0], [-11, -0.5], [0, 0], [0, 0], [0, 0], [0, 0], [47.517, -9.754]], "v": [[265.25, -35.75], [235.5, -38.5], [224.375, -33.375], [219.875, -26.875], [206.125, -14.125], [237.375, -11.25], [237, -25.5]], "c": true}]}], "ix": 2}, "nm": "路径 11", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[24, 0], [-12, -1]], "o": [[-24, 0], [12, 1]], "v": [[93.5, 53.5], [115.5, 63]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[24, 0], [-12, -1]], "o": [[-24, 0], [12, 1]], "v": [[136.5, 79], [147.5, 83]], "c": true}]}, {"t": 25, "s": [{"i": [[24, 0], [-12, -1]], "o": [[-24, 0], [12, 1]], "v": [[147, 83], [147.5, 83]], "c": true}]}], "ix": 2}, "nm": "路径 10", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-26.5, -2]], "o": [[0, 0], [26.5, 2]], "v": [[134.5, 90], [183, 128.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-26.5, -2]], "o": [[0, 0], [26.5, 2]], "v": [[212.5, 159], [236, 176.5]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-26.5, -2]], "o": [[0, 0], [26.5, 2]], "v": [[237, 178], [236, 176.5]], "c": true}]}], "ix": 2}, "nm": "路径 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[20.5, 1.5], [14.5, -4], [0, 0]], "o": [[-20.5, -1.5], [-14.5, 4], [0, 0]], "v": [[184, 73.5], [203.5, 92], [249.5, 109.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[20.5, 1.5], [14.5, -3.5], [0, 0]], "o": [[-20.5, -1.5], [-14.622, 3.529], [0, 0]], "v": [[260, 100.5], [279.5, 119], [300.5, 125.5]], "c": true}]}, {"t": 25, "s": [{"i": [[20.5, 1.5], [14.5, -3.5], [0, 0]], "o": [[-20.5, -1.5], [-14.622, 3.529], [0, 0]], "v": [[293, 118], [312.5, 136.5], [300.5, 125.5]], "c": true}]}], "ix": 2}, "nm": "路径 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[23, 10], [0, 0]], "o": [[-23, -10], [0, 0]], "v": [[189, 230], [260, 316]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[23, 10], [0, 0]], "o": [[-23, -10], [0, 0]], "v": [[223, 274], [249, 307]], "c": true}]}, {"t": 25, "s": [{"i": [[23, 10], [0, 0]], "o": [[-23, -10], [0, 0]], "v": [[247, 305], [249, 307]], "c": true}]}], "ix": 2}, "nm": "路径 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [9, 22]], "o": [[0, 0], [-9, -22]], "v": [[281, 214], [369, 276]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [14.649, 18.719]], "o": [[0, 0], [-18, -23]], "v": [[329, 254], [369, 276]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [14.649, 18.719]], "o": [[0, 0], [-18, -23]], "v": [[371.5, 279.5], [369, 276]], "c": true}]}], "ix": 2}, "nm": "路径 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [14, 24]], "o": [[0, 0], [-14, -24]], "v": [[352, 203], [394, 218]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [14.295, 23.825]], "o": [[0, 0], [-6, -10]], "v": [[364, 208], [394, 218]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [14.295, 23.825]], "o": [[0, 0], [-6, -10]], "v": [[395.5, 219.5], [394, 218]], "c": true}]}], "ix": 2}, "nm": "路径 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.5, 26], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.5, -26], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-180, -218], [-149.5, -145.5], [-156, -143.5], [-137, -101.5], [-148.5, -86.5], [-103, -49.5], [-122.5, -121], [-101, -105], [-123, -156.5], [-90, -128], [-126.5, -182.5], [-130, -173]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-20.754, -15.565], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [6, 4.5], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-219.5, -232.5], [-210, -210], [-219.5, -203.5], [-208.5, -194.5], [-198, -184.5], [-180, -165.5], [-174, -182], [-152, -176], [-173.75, -196.75], [-178.75, -200.75], [-183, -204.875], [-187.75, -208.25]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.25, 2.375], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.802, -7.223], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-261.5, -264.5], [-249.125, -247.875], [-258.125, -242.75], [-257.125, -241.75], [-256.875, -240.75], [-238.875, -221.75], [-236.25, -231], [-233.75, -242.5], [-236, -245.75], [-241, -249.75], [-245.25, -253.875], [-250, -257.25]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 9, "ty": "sh", "ix": 10, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-2.5, -10], [0, 0], [0, 0], [0, 0], [-11.5, -4.5], [0, 0], [-1.5, 0.5], [-20, 5.5], [-63, 0.5]], "o": [[0, 0], [2.5, 10], [0, 0], [0, 0], [0, 0], [11.5, 4.5], [0, 0], [1.5, -0.5], [20, -5.5], [63, -0.5]], "v": [[-185, -18], [-145, 14], [-155, 23], [-164.5, 37], [-249, 76], [-137, 77.5], [-109, 57.5], [-177, 54.5], [-120.5, 28], [-114.5, -6.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-0.856, -10.272], [0, 0], [0, 0], [0, 0], [-12.099, 2.475], [0, 0], [-1.5, 0.5], [-20, 5.5], [-61.836, 12.066]], "o": [[0, 0], [0.25, 3], [0, 0], [0, 0], [0, 0], [11, -2.25], [0, 0], [1.5, -0.5], [20, -5.5], [20.5, -4]], "v": [[-251.5, 3], [-246, 14], [-262.5, 18], [-267, 26.5], [-308, 39.5], [-272.5, 38.75], [-243, 35.5], [-226.5, 33.5], [-200.5, 27.5], [-201, 6.75]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-7, -5.5], [0, 0], [0, 0], [0, 0], [-12.349, 0], [0, 0], [-1.5, 0.5], [-20, 5.5], [-19.122, 6.039]], "o": [[0, 0], [2.367, 1.86], [0, 0], [0, 0], [0, 0], [4.5, 0], [0, 0], [1.5, -0.5], [20, -5.5], [14.25, -4.5]], "v": [[-250.75, 9.25], [-257, 11.75], [-273.5, 15.75], [-278, 24.25], [-314, 36.5], [-290.75, 34.25], [-281.375, 33.125], [-275, 33], [-267.25, 29.625], [-249.75, 15.5]], "c": true}]}], "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 10, "ty": "sh", "ix": 11, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [7, -24.5]], "o": [[0, 0], [-7, 24.5]], "v": [[-69, 78.5], [-124, 124]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [7, -24.5]], "o": [[0, 0], [-7, 24.5]], "v": [[-140, 137.5], [-171, 161]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [7, -7]], "o": [[0, 0], [-18.017, 18.017]], "v": [[-167.5, 159.5], [-171, 161]], "c": true}]}], "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 11, "ty": "sh", "ix": 12, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-12.542, 11.628]], "o": [[0, 0], [48, -44.5]], "v": [[-74, 125.5], [-153, 264.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-11.828, 12.353]], "o": [[0, 0], [45, -47]], "v": [[-122, 214], [-170.5, 288.5]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-11.828, 12.353]], "o": [[0, 0], [45, -47]], "v": [[-168, 286], [-170.5, 288.5]], "c": true}]}], "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 12, "ty": "sh", "ix": 13, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-11.5, 0.5]], "o": [[0, 0], [11.5, -0.5]], "v": [[43, 101], [71, 155.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-3.25, 1.25]], "o": [[0, 0], [5.155, -1.983]], "v": [[94.5, 165.25], [106, 185.5]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-3.25, 1.25]], "o": [[0, 0], [5.155, -1.983]], "v": [[106.25, 185], [106, 185.5]], "c": true}]}], "ix": 2}, "nm": "路径 13", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 13, "ty": "sh", "ix": 14, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-10.25, 10.75]], "o": [[0, 0], [10.25, -10.75]], "v": [[13, 172.75], [19, 191]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-10.25, 10.75]], "o": [[0, 0], [10.25, -10.75]], "v": [[24, 213.75], [27.5, 222.5]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-10.25, 10.75]], "o": [[0, 0], [10.25, -10.75]], "v": [[27.25, 222.875], [27.5, 222.5]], "c": true}]}], "ix": 2}, "nm": "路径 14", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 14, "ty": "sh", "ix": 15, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[4, 1], [1.75, -8.5], [-12.75, 3]], "o": [[-4, -1], [-1.75, 8.5], [12.75, -3]], "v": [[-13.75, 120.25], [-26.5, 130.5], [-10.25, 135]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[4, 1], [2.919, -8.173], [-7.266, 10.898]], "o": [[-4, -1], [-1.25, 3.5], [2.5, -3.75]], "v": [[-17, 124], [-24, 130.75], [-14.25, 133.5]], "c": true}]}, {"t": 25, "s": [{"i": [[1.167, 0.584], [2.919, -8.173], [-3.483, 12.627]], "o": [[0, 0.125], [-1.25, 3.5], [1, -3.625]], "v": [[-17, 124], [-19.5, 132.375], [-18.375, 130]], "c": true}]}], "ix": 2}, "nm": "路径 15", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 15, "ty": "sh", "ix": 16, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-345, -80], [-373, -80], [-368, -62], [-273, -47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-352, -80.5], [-373, -80], [-357, -70.5], [-337.5, -72]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-425.5, -86], [-430.5, -83], [-430.5, -76], [-429, -82]], "c": true}]}], "ix": 2}, "nm": "路径 16", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 16, "ty": "sh", "ix": 17, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-15, 7]], "o": [[0, 0], [15, -7]], "v": [[0, 305], [-4, 373]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-15, 7]], "o": [[0, 0], [15, -7]], "v": [[-1, 366], [-3, 405]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-2.75, 3]], "o": [[0, 0], [3.263, -3.559]], "v": [[-2, 404.75], [-3, 405]], "c": true}]}], "ix": 2}, "nm": "路径 17", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 17, "ty": "sh", "ix": 18, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-20, 10]], "o": [[0, 0], [20, -10]], "v": [[-246, 322.5], [-268, 346]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-20, 10]], "o": [[0, 0], [20, -10]], "v": [[-276, 360], [-288, 370.5]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-20, 10]], "o": [[0, 0], [20, -10]], "v": [[-272.5, 363], [-288, 370.5]], "c": true}]}], "ix": 2}, "nm": "路径 18", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 18, "ty": "sh", "ix": 19, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-21.5, 3.5]], "o": [[0, 0], [21.5, -3.5]], "v": [[-321.5, 251.5], [-341, 265.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-6.25, 0.25]], "o": [[0, 0], [21.766, -0.871]], "v": [[-354.75, 268.75], [-362.5, 275]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-6.25, 0.25]], "o": [[0, 0], [21.766, -0.871]], "v": [[-358, 274.5], [-362.5, 275]], "c": true}]}], "ix": 2}, "nm": "路径 19", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 19, "ty": "sh", "ix": 20, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[8, 5.5], [-12, -0.5]], "o": [[-8, -5.5], [12, 0.5]], "v": [[-58, 103.5], [-64.5, 115.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[8, 5.5], [-4.125, 0.75]], "o": [[-8, -5.5], [3.862, -0.702]], "v": [[-75.375, 140.625], [-79, 147.5]], "c": true}]}, {"t": 25, "s": [{"i": [[8, 5.5], [-4.125, 0.75]], "o": [[-8, -5.5], [3.862, -0.702]], "v": [[-75.625, 147.875], [-79, 147.5]], "c": true}]}], "ix": 2}, "nm": "路径 20", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 20, "ty": "sh", "ix": 21, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-20, 6.5]], "o": [[0, 0], [20, -6.5]], "v": [[-260, 279.5], [-334.5, 347]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-20, 6.5]], "o": [[0, 0], [20, -6.5]], "v": [[-309, 324], [-334.5, 347]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [-20, 6.5]], "o": [[0, 0], [20, -6.5]], "v": [[-331.75, 346], [-334.5, 347]], "c": true}]}], "ix": 2}, "nm": "路径 21", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 21, "ty": "sh", "ix": 22, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[317, -360], [302, -337], [330, -351]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[326, -359], [311, -336], [330, -351]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[326, -359], [328, -353.75], [330, -351]], "c": true}]}], "ix": 2}, "nm": "路径 22", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 22, "ty": "sh", "ix": 23, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[20, 8], [0, 0]], "o": [[-20, -8], [0, 0]], "v": [[373, -98], [289, -70]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[20, 8], [0, 0]], "o": [[-20, -8], [0, 0]], "v": [[428, -96], [391, -83]], "c": true}]}, {"t": 25, "s": [{"i": [[20, 8], [0, 0]], "o": [[-20, -8], [0, 0]], "v": [[428, -96], [427.75, -95.5]], "c": true}]}], "ix": 2}, "nm": "路径 23", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 23, "ty": "sh", "ix": 24, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[180, -105], [119, -76], [161, -79], [162, -66], [234, -86], [190, -93]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[277, -151], [259, -130], [284, -133], [285, -120], [343, -148], [287, -139]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[308.75, -150.5], [296.75, -134], [306.25, -137.5], [307, -131.75], [337, -143.5], [310.5, -140.75]], "c": true}]}], "ix": 2}, "nm": "路径 24", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 24, "ty": "sh", "ix": 25, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[180, -105], [119, -76], [161, -79], [162, -66], [234, -86], [190, -93]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[277, -151], [259, -130], [284, -133], [285, -120], [343, -148], [287, -139]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[308.75, -150.5], [296.75, -134], [306.25, -137.5], [307, -131.75], [337, -143.5], [310.5, -140.75]], "c": true}]}], "ix": 2}, "nm": "路径 25", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 25, "ty": "sh", "ix": 26, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[-11, 35], [48, -76]], "o": [[11, -35], [-48, 76]], "v": [[270, -374], [191, -276]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[-16, 29], [33, -50]], "o": [[17.723, -32.123], [-49.515, 75.022]], "v": [[270, -374], [237, -341]], "c": true}]}, {"t": 25, "s": [{"i": [[-16, 29], [-0.25, -0.25]], "o": [[17.723, -32.123], [-1.5, 5.5]], "v": [[270, -374], [276.5, -398.25]], "c": true}]}], "ix": 2}, "nm": "路径 26", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 26, "ty": "sh", "ix": 27, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-13, 5]], "o": [[0, 0], [13, -5]], "v": [[31.5, -333], [26.5, -307]], "c": true}]}, {"t": 17.5, "s": [{"i": [[0, 0], [-13, 5]], "o": [[0, 0], [13, -5]], "v": [[42.5, -378], [36.5, -360]], "c": true}]}], "ix": 2}, "nm": "路径 27", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 27, "ty": "sh", "ix": 28, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-15, -8], [0, 0], [32.5, -15.5]], "o": [[0, 0], [46.5, -43.5], [0, 0], [-32.5, 15.5]], "v": [[25.5, -272.5], [8, -191.5], [42.5, -113], [80.5, -171]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [-15, -8], [0, 0], [26.75, -15.75]], "o": [[0, 0], [27, -25.5], [0, 0], [-31.028, 18.269]], "v": [[58, -276.25], [51, -238.25], [58.25, -225.75], [84.75, -268.25]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [10, -16.5], [0, 0], [26.458, -16.236]], "o": [[0, 0], [7, -6.75], [0, 0], [-11, 6.75]], "v": [[119.75, -315.5], [102.5, -290.75], [113.25, -293.25], [122.25, -308.75]], "c": true}]}], "ix": 2}, "nm": "路径 28", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 28, "ty": "sh", "ix": 29, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [18, 7.5], [0, 0], [-4.5, -13.5]], "o": [[0, 0], [-18, -7.5], [0, 0], [4.5, 13.5]], "v": [[-21.5, -161], [-35.5, -146.5], [-53, -156], [-34, -116.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[0, 0], [18, 7.5], [0, 0], [-12.493, -6.814]], "o": [[0, 0], [-18, -7.5], [0, 0], [8.25, 4.5]], "v": [[-46.75, -266.25], [-60.75, -251.75], [-78.25, -261.25], [-61.5, -243.25]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [18, 7.5], [0, 0], [-12.493, -6.814]], "o": [[0, 0], [-18, -7.5], [0, 0], [8.25, 4.5]], "v": [[-57.25, -263.75], [-60.75, -251.75], [-67.75, -261.75], [-57.25, -249.5]], "c": true}]}], "ix": 2}, "nm": "路径 29", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 29, "ty": "sh", "ix": 30, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-17, -5.5]], "o": [[0, 0], [17, 5.5]], "v": [[-71, -133.5], [-57, -105]], "c": true}]}, {"t": 17.5, "s": [{"i": [[0, 0], [-7.75, -5.75]], "o": [[0, 0], [4.922, 3.652]], "v": [[-98.5, -198.5], [-92.25, -184.75]], "c": true}]}], "ix": 2}, "nm": "路径 30", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.741176486015, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 31, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "形状图层 4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [100]}, {"t": 11.0009765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [506, 499, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "s", "pt": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.25, -5.875], [6.375, -7.375], [5.375, -9], [4, -8.75], [2.626, -8.772], [0.875, -8.25], [-0.75, -7.5], [-2, -6.625], [-2.375, -4.875], [-2.466, -3.538], [-2.25, -1.375], [-1.316, -0.494], [0.125, -0.75], [1.5, -0.5], [3.25, -0.125], [4.25, -1.125], [4.5, -0.875], [4.78, 0.4], [5.661, 1.898], [5.982, 2.366], [7.382, 2.558], [9.009, 4.708], [9.13, 4.876], [9.277, 5.081], [10.214, 6.384], [10.288, 6.488], [10.443, 6.704], [10.604, 6.927], [10.922, 7.37], [7.636, 1.516], [6.875, 0.5], [7, -1.5], [7.75, -2.625], [8.75, -4.625]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, -1], [3.73, -9.072], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.735, 0.49], [-3.877, 9.431], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.5, -72], [0, -43], [-13, -50.5], [-12, -33.5], [-25.499, -39.022], [-34, -42.5], [-17, -16.5], [-27.5, -19], [-24, -5.5], [-30.466, 13.087], [-38.5, 32], [-37.316, 31.381], [-16.5, 20.5], [-26, 42.5], [-0.5, 22.5], [1.5, 39.5], [8.5, 24.5], [10.405, 27.15], [11.661, 28.898], [12.357, 29.866], [14.382, 32.683], [14.759, 33.208], [14.88, 33.376], [15.027, 33.581], [15.964, 34.884], [16.038, 34.988], [16.193, 35.204], [16.354, 35.427], [16.672, 35.87], [17.136, 36.516], [20, 40.5], [16, 0.5], [47.5, -25.5], [8.5, -33]], "c": true}]}, {"t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, -1], [11.204, 5.938], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [96.756, 35.774], [0, 0], [112.714, -41.964], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.735, 0.49], [-11.647, -6.173], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.213, -6.364], [0, 0], [-112.714, 41.964], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[12.5, -179.5], [-25.5, -81], [-59, -127], [-34.5, -65], [-80.64, -122.752], [-114.5, -154.5], [-105.5, -107], [-135.5, -110.5], [-64, -28.5], [-85.273, -39.648], [-109, -52.25], [-122.237, -36.766], [-67, -9], [-122.5, 16.5], [-61, 14], [-134, 49.5], [-83.5, 50.5], [-81.588, 65.525], [-27.492, 43.168], [-40.735, 98.596], [7.049, 59.38], [6.311, 100.875], [31.4, 64.629], [37.539, 117.474], [40.803, 67.795], [270.263, 216.927], [51.187, 34.057], [54.744, 0.726], [111.899, -31.227], [113.214, -57.464], [117.5, -143], [41, -67], [83.5, -163.5], [25, -61]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0.75, 0.5], [0, 0]], "o": [[-3.25, 0.75], [0, 0]], "v": [[22, 28.5], [16, 32]], "c": true}]}, {"t": 10, "s": [{"i": [[20, 20], [0, 0]], "o": [[-20, -20], [0, 0]], "v": [[254, 258], [224, 262]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.849785539216, 0.849785539216, 0.798092890721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 6", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "fl", "c": {"a": 0, "k": [0.849785539216, 0.849785539216, 0.798092890721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-6, 8]], "o": [[0, 0], [8.736, -11.648]], "v": [[-9, -47], [-3, -39]], "c": true}]}, {"t": 10, "s": [{"i": [[0, 0], [4.16, 68.225]], "o": [[0, 0], [-5, -82]], "v": [[-56, -362], [-38, -299]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 5", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-5.5, -4.5], [6.293, 1.18]], "o": [[6.119, 5.006], [-8, -1.5]], "v": [[-27, 9], [-29, -9]], "c": true}]}, {"t": 8.9990234375, "s": [{"i": [[-37.691, -28.33], [12.459, 16.236]], "o": [[41.025, 30.836], [-12.8, -16.3]], "v": [[-293.888, -121.67], [-241.281, -108.344]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 4", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "fl", "c": {"a": 0, "k": [0.849785539216, 0.849785539216, 0.798092890721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 3", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, -1], [3.73, -9.072], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.735, 0.49], [-3.877, 9.431], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5, -72.5], [-0.5, -43.5], [-13.5, -51], [-12.5, -34], [-25.999, -39.522], [-34.5, -43], [-17.5, -17], [-28, -19.5], [-24.5, -6], [-30.966, 12.587], [-39, 31.5], [-37.816, 30.881], [-17, 20], [-26.5, 42], [-1, 22], [0, 0], [0, 0], [9.905, 26.65], [11.161, 28.398], [11.857, 29.366], [13.882, 32.183], [14.259, 32.708], [14.38, 32.876], [14.527, 33.081], [15.464, 34.384], [15.538, 34.488], [15.693, 34.704], [15.854, 34.927], [16.172, 35.37], [16.636, 36.016], [0, 0], [0, 0], [0, 0], [0, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, -1], [3.73, -9.072], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.735, 0.49], [-3.877, 9.431], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5, -72.5], [-0.5, -43.5], [-13.5, -51], [-12.5, -34], [-25.999, -39.522], [-34.5, -43], [-17.5, -17], [-28, -19.5], [-24.5, -6], [-30.966, 12.587], [-39, 31.5], [-37.816, 30.881], [-17, 20], [-26.5, 42], [-1, 22], [1, 39], [8, 24], [9.905, 26.65], [11.161, 28.398], [11.857, 29.366], [13.882, 32.183], [14.259, 32.708], [14.38, 32.876], [14.527, 33.081], [15.464, 34.384], [15.538, 34.488], [15.693, 34.704], [15.854, 34.927], [16.172, 35.37], [16.636, 36.016], [19.5, 40], [15.5, 0], [47, -26], [8, -33.5]], "c": true}]}, {"t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, -1], [11.204, 5.938], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [96.756, 35.774], [0, 0], [112.714, -41.964], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.735, 0.49], [-11.647, -6.173], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.213, -6.364], [0, 0], [-112.714, 41.964], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[12, -180], [-26, -81.5], [-59.5, -127.5], [-35, -65.5], [-81.14, -123.252], [-115, -155], [-106, -107.5], [-136, -111], [-64.5, -29], [-85.773, -40.148], [-109.5, -52.75], [-122.737, -37.266], [-67.5, -9.5], [-123, 16], [-61.5, 13.5], [-134.5, 49], [-84, 50], [-82.088, 65.025], [-27.992, 42.668], [-41.235, 98.096], [6.549, 58.88], [5.811, 100.375], [30.9, 64.129], [37.04, 116.974], [40.303, 67.295], [269.763, 216.427], [50.687, 33.557], [54.244, 0.226], [111.399, -31.727], [112.714, -57.964], [117, -143.5], [40.5, -67.5], [83, -164], [24.5, -61.5]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.849785539216, 0.849785539216, 0.798092890721, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 2.5, "s": [0, 0]}, {"t": 5, "s": [100, 100]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 2", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [5.508, -5.326], [7.644, -3.239], [8.456, -0.556], [-1.455, -6.839], [0, 0], [-16.581, 3.309], [-1.365, 0.067], [-0.215, 0.009], [-0.383, 0.013], [-0.316, 0.008], [-0.564, -0.024], [-0.057, -0.005], [0, 0], [-0.008, -0.001], [0, 0], [0, 0], [-0.004, -0.002], [1.59, 2.133], [1.304, 2.641], [0.784, 2.2], [0.208, 0.645], [0.251, 0.893], [0.052, 0.19], [0.097, 0.376], [0.055, 0.22], [0.055, 0.228], [0.06, 0.263], [0.124, 0.6], [0.063, 0.332], [0.076, 0.435], [0.075, 0.474]], "o": [[0, 0], [-4.015, 3.883], [-5.944, 2.519], [7.481, 8.215], [3.504, 16.471], [0, 0], [0.468, -0.093], [0.208, -0.01], [0.379, -0.016], [0.326, -0.011], [1.311, -0.035], [0.088, 0.004], [0.021, 0.002], [0, 0], [0.008, 0.001], [0, 0], [0, 0], [-2.018, -1.809], [-1.769, -2.372], [-1.041, -2.108], [-0.228, -0.641], [-0.288, -0.889], [-0.053, -0.19], [-0.103, -0.376], [-0.057, -0.221], [-0.057, -0.228], [-0.063, -0.264], [-0.138, -0.603], [-0.033, -0.161], [-0.083, -0.438], [-0.083, -0.479], [-2.251, -14.202]], "v": [[19.25, -43.25], [11.371, -33.37], [-5.97, -21.956], [-27.5, -17], [-14.892, 5.794], [-20.75, 33.5], [27.344, 17.835], [30.358, 17.588], [30.993, 17.559], [32.142, 17.514], [33.107, 17.485], [36.148, 17.462], [36.367, 17.476], [36.424, 17.482], [36.451, 17.486], [36.472, 17.491], [36.479, 17.493], [36.5, 17.5], [31.1, 11.562], [26.508, 4.008], [23.779, -2.472], [23.124, -4.4], [22.316, -7.075], [22.158, -7.644], [21.859, -8.773], [21.692, -9.435], [21.525, -10.119], [21.34, -10.909], [20.947, -12.713], [20.655, -14.194], [20.417, -15.504], [20.179, -16.933]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[7.443, 5.487], [-5.606, 44.014], [-7.008, 48.994], [10.051, -0.66], [40.385, 28.767], [8.864, -5.026], [79.92, 66.061], [345.992, 131.631], [-3.176, -2.922], [19.611, -3.547], [-0.897, -7.654], [130.06, -69.327], [31.824, -23.639], [-74.976, 46.813], [18.624, -17.587], [-39.339, 30.143], [-41.259, 51.576], [-0.01, -0.006], [-46.896, 42.706], [-12.462, 84.607], [-9.905, 17.292], [-2.891, 21.432], [-13.212, 34.222], [-3.016, 7.46], [-6.512, 15.993], [-2.127, 18.092], [-0.83, 16.258], [-22.56, -4.676], [80.079, 62.449], [0.897, 31.098], [-29.036, 56.741], [2.32, 20.718]], "o": [[-7.443, -5.487], [-29.606, 43.014], [-39.36, 55.445], [-1.628, 35.817], [-71.24, -50.746], [-8.864, 5.026], [3.584, 4.728], [-12.78, -3.895], [10.511, 9.669], [-16.688, 3.018], [4.037, 34.46], [58.06, 11.673], [-15.405, 11.443], [22.872, -14.28], [-25.258, 23.852], [90.859, -69.619], [204.607, -255.77], [-3.241, -2.906], [60.4, -55.003], [2.911, -19.761], [4.125, -19.579], [4.478, -33.188], [2.797, -7.244], [6.015, -14.876], [5.494, -13.492], [1.704, -14.49], [0.998, -19.542], [194.018, 40.211], [15.079, -8.551], [-12.301, -21.449], [31.928, -62.392], [-106.706, 72.264]], "v": [[249.987, -356.355], [148.343, -247.119], [57.008, -129.994], [5.5, -184], [-48.672, -116.057], [-207.803, -301.517], [-146.92, -129.061], [-260.905, -78.123], [-254.152, -64.055], [-248.26, -43.74], [-308.752, -46.294], [-129.06, 56.327], [-312.21, 219.656], [-157.644, 144.528], [-232.659, 202.675], [-277.701, 274.882], [-226.993, 303.788], [-59.5, 94.5], [-66.979, 138.336], [-2.034, 207.919], [19.115, 125.252], [63.522, 164.189], [65.457, 91.335], [84.185, 101.266], [306.663, 295.02], [242.945, 207.347], [322.71, 243.095], [255.614, 171.982], [73.394, 24.99], [311.423, -75.042], [39.387, -31.566], [255.39, -269.633]], "c": true}]}, {"t": 10, "s": [{"i": [[16.338, 4.548], [27.394, -26.986], [-7.008, 48.994], [10.517, 0.596], [40.385, 28.767], [0.969, -26.079], [79.92, 66.061], [345.519, 141.342], [0.109, -4.314], [19.532, 0.758], [7.875, -7.215], [266.06, -75.327], [410.824, -227.639], [-149.584, 108.165], [-24.711, 15.512], [-39.128, 12.658], [-41.259, 51.576], [-0.01, -0.006], [-53.36, 34.289], [14.177, -279.077], [-15.116, 71.748], [-2.891, 21.432], [-13.211, 34.222], [-3.016, 7.46], [-6.512, 15.993], [-2.127, 18.092], [-0.83, 16.258], [-22.56, -4.676], [80.079, 62.449], [-3.778, 21.73], [16.254, 75.022], [4.746, 14.827]], "o": [[-16.338, -4.548], [-29.606, 43.014], [-39.36, 55.445], [-10.517, -0.596], [-71.24, -50.746], [-0.969, 26.079], [3.584, 4.728], [-11.464, -0.167], [-0.192, 7.604], [-16.946, -0.657], [-25.583, 23.438], [-41.309, 31.34], [-16.785, 9.301], [21.85, -15.8], [29.423, -18.469], [89.333, -28.9], [204.607, -255.77], [-3.241, -2.906], [26.979, -17.336], [-3.229, 63.572], [4.125, -19.579], [4.478, -33.189], [2.797, -7.245], [191.315, 97.734], [5.494, -13.492], [1.704, -14.49], [0.998, -19.542], [194.018, 40.211], [15.079, -8.551], [3.778, -21.73], [-22.342, -98.662], [-175.566, 122.361]], "v": [[281.25, -389.75], [153.606, -255.014], [73.008, -150.994], [28.5, -288], [-48.672, -116.057], [-236.75, -344.5], [-192.92, -158.061], [-331.519, -90.842], [-320.599, -67.783], [-346.945, -61.284], [-403.928, -58.136], [-231.06, 85.327], [-334.14, 237.639], [-208.416, 176.335], [-92.773, 95.658], [-311.262, 301.707], [-261.555, 328.113], [-59.5, 94.5], [-66.979, 138.336], [-24.841, 264.498], [19.116, 125.252], [63.522, 164.189], [65.457, 91.335], [84.185, 101.266], [329.032, 317.827], [265.313, 230.155], [345.079, 265.902], [277.982, 194.789], [83.921, 49.551], [390.958, -91.428], [91.641, -32.04], [310.654, -339.027]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 2, "k": {"a": 0, "k": [0, 1, 1, 1, 1, 0, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [-9.594, 20.117], "ix": 5}, "e": {"a": 0, "k": [17.758, -114.98], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "渐变填充 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 1, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 5, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 1, 0.996, 1, 0.261, 0.996, 0.998, 0.661, 0.521, 0.992, 1, 0.322, 0.761, 0.996, 0.871, 0.161, 1, 1, 0.741, 0], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [4.766, -4.387], "to": [-4.333, 0], "ti": [4.333, 0]}, {"t": 10, "s": [-21.234, -4.387]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [5.535, -37.602], "to": [1.874, -28.075], "ti": [-1.874, 28.075]}, {"t": 10, "s": [16.781, -206.051]}], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "weresgsdhfhj", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1.581, -7.021], "ix": 2}, "a": {"a": 0, "k": [1.581, -7.021], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 161, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Path-18-Copy-8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [100]}, {"t": 36.99951171875, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [501.173, 490.498, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-18.827, 16.498, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [136, 136, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[0, 0], [1.5, -3.5], [0, 0], [-4, 6.375]], "o": [[0, 0], [6.5, 3.75], [0, 0], [-3.5, -3.75]], "v": [[-41.75, 27.5], [-51.5, 37], [-41.875, 43.625], [-36.375, 31.125]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16.001, "s": [{"i": [[0, 0], [-1.5, -12.5], [0, 0], [-10, 4.75]], "o": [[0, 0], [6.5, 3.75], [0, 0], [-3.5, -3.75]], "v": [[-160.5, 151], [-181, 173], [-165.5, 180.75], [-152.5, 156.25]], "c": true}]}, {"t": 33.9990234375, "s": [{"i": [[0, 0], [-1.5, -12.5], [0, 0], [-10, 4.75]], "o": [[0, 0], [6.5, 3.75], [0, 0], [-3.5, -3.75]], "v": [[-197.5, 236.5], [-218, 258.5], [-202.5, 266.25], [-189.5, 241.75]], "c": true}]}], "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.929, 0.275, 0.157, 0.5, 0.965, 0.371, 0.325, 1, 1, 0.467, 0.494], "ix": 9}}, "s": {"a": 0, "k": [158.394, 15.619], "ix": 5}, "e": {"a": 0, "k": [172.844, 6.865], "ix": 6}, "t": 1, "nm": "wquhjakgn", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-43.379, 34.997], "ix": 2}, "a": {"a": 0, "k": [-43.379, 34.997], "ix": 1}, "s": {"a": 0, "k": [120, 120], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [100]}, {"t": 35, "s": [0]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1.001, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[16.566, 19.386], [18.116, 19.773], [18.094, 18.983]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[159.316, 14.636], [174.053, 14.023], [172.594, 5.858]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13.999, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[208.316, 18.136], [214.053, 20.523], [213.844, 15.858]], "c": true}]}, {"t": 36.0009765625, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[220.316, 54.136], [226.053, 56.523], [225.844, 51.858]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.929, 0.275, 0.157, 0.5, 0.965, 0.371, 0.325, 1, 1, 0.467, 0.494], "ix": 9}}, "s": {"a": 0, "k": [158.394, 15.619], "ix": 5}, "e": {"a": 0, "k": [172.844, 6.865], "ix": 6}, "t": 1, "nm": "wqoiiqj", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [210.162, 21.645], "ix": 2}, "a": {"a": 0, "k": [210.162, 21.645], "ix": 1}, "s": {"a": 0, "k": [140, 140], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Path-18-Copy-8", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-1.392, 7.027], [-0.833, -3.301], [6.611, -1.898]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6.001, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[43.358, -142.223], [43.917, -176.176], [67.861, -169.148]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[63.358, -190.973], [64.917, -205.676], [73.611, -199.648]], "c": true}]}, {"t": 36.0009765625, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[125.858, -205.973], [136.417, -211.176], [138.111, -202.148]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.929, 0.275, 0.157, 0.5, 0.965, 0.371, 0.325, 1, 1, 0.467, 0.494], "ix": 9}}, "s": {"a": 0, "k": [46.488, -140.927], "ix": 5}, "e": {"a": 0, "k": [56.09, -179.023], "ix": 6}, "t": 1, "nm": "<PERSON><PERSON>r<PERSON>qoiwit<PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [72.487, -198.765], "ix": 2}, "a": {"a": 0, "k": [72.487, -198.765], "ix": 1}, "s": {"a": 0, "k": [120, 120], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Path-18-Copy-7", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-8.8, 21.763], [-15.452, 28.608], [-10.187, 32.11]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8.999, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-81.8, 114.263], [-111.452, 140.358], [-91.187, 160.86]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16.001, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-87.3, 143.763], [-105.452, 159.358], [-93.187, 165.86]], "c": true}]}, {"t": 38.9990234375, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-72.8, 223.263], [-78.452, 241.858], [-64.187, 245.36]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.929, 0.275, 0.157, 0.5, 0.965, 0.371, 0.325, 1, 1, 0.467, 0.494], "ix": 9}}, "s": {"a": 0, "k": [-97.307, 151.453], "ix": 5}, "e": {"a": 0, "k": [-80.291, 107.357], "ix": 6}, "t": 1, "nm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-97.611, 151.664], "ix": 2}, "a": {"a": 0, "k": [-97.787, 152.072], "ix": 1}, "s": {"a": 0, "k": [140, 140], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Path-18-<PERSON><PERSON>-6", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.838, 24.843], [8.403, 25.881], [9.71, 31.323], [15.206, 28.948]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6.001, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[92.463, 140.718], [87.403, 153.256], [108.71, 193.448], [129.331, 168.448]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[135.963, 172.718], [127.903, 182.756], [142.71, 201.948], [151.831, 183.448]], "c": true}]}, {"t": 38.9990234375, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[163.963, 257.718], [155.903, 267.756], [170.71, 286.948], [179.831, 268.448]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.929, 0.275, 0.157, 0.5, 0.965, 0.371, 0.325, 1, 1, 0.467, 0.494], "ix": 9}}, "s": {"a": 0, "k": [122.074, 183.57], "ix": 5}, "e": {"a": 0, "k": [85.984, 145.383], "ix": 6}, "t": 1, "nm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [141.326, 189.781], "ix": 2}, "a": {"a": 0, "k": [141.326, 189.781], "ix": 1}, "s": {"a": 0, "k": [140, 140], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21.001, "s": [100]}, {"t": 28.00048828125, "s": [0]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Path-18-Copy-5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1.001, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-11.268, 10.928], [-19.747, 5.925], [-23.247, 13.297]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-140.268, -53.822], [-167.997, -80.575], [-181.997, -59.953]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13.999, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-175.018, -75.072], [-189.997, -93.325], [-195.747, -81.453]], "c": true}]}, {"t": 36.0009765625, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-228.768, -85.072], [-241.997, -82.575], [-237.497, -73.703]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.929, 0.275, 0.157, 0.5, 0.965, 0.371, 0.325, 1, 1, 0.467, 0.494], "ix": 9}}, "s": {"a": 0, "k": [-165.797, -77.35], "ix": 5}, "e": {"a": 0, "k": [-130.277, -47.902], "ix": 6}, "t": 1, "nm": "waejnwnnnhg", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-189.343, -84.6], "ix": 2}, "a": {"a": 0, "k": [-189.343, -84.6], "ix": 1}, "s": {"a": 0, "k": [120, 120], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Path-18-Copy-4", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "鞭炮4", "parent": 6, "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [61]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [61]}, {"i": {"x": [0.833], "y": [1.565]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [61]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}, "t": 110, "s": [61]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [54.222]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [47.444]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [40.667]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [33.889]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [27.111]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [20.333]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [13.556]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [6.778]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.155]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 120, "s": [-7.91]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 121, "s": [-14.476]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 122, "s": [-19.57]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 123, "s": [-23.147]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 124, "s": [-25.235]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 125, "s": [-25.925]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 126, "s": [-25.362]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 127, "s": [-23.726]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 128, "s": [-21.226]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 129, "s": [-18.082]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 130, "s": [-14.515]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 131, "s": [-10.74]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 132, "s": [-6.953]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 133, "s": [-3.325]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 135, "s": [2.91]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 136, "s": [5.325]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 137, "s": [7.2]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 138, "s": [8.515]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 139, "s": [9.283]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 140, "s": [9.537]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 141, "s": [9.33]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 142, "s": [8.728]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 143, "s": [7.809]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 144, "s": [6.652]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 145, "s": [5.34]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 146, "s": [3.951]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 147, "s": [2.558]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 148, "s": [1.223]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 149, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 150, "s": [-1.07]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 151, "s": [-1.959]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 152, "s": [-2.649]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 153, "s": [-3.133]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 154, "s": [-3.415]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 155, "s": [-3.509]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 156, "s": [-3.432]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 157, "s": [-3.211]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 158, "s": [-2.873]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 159, "s": [-2.447]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 160, "s": [-1.964]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 161, "s": [-1.454]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 162, "s": [-0.941]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 163, "s": [-0.45]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 165, "s": [0.394]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 166, "s": [0.721]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 167, "s": [0.974]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 168, "s": [1.152]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 169, "s": [1.256]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 170, "s": [1.291]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 171, "s": [1.263]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 172, "s": [1.181]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 173, "s": [1.057]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 174, "s": [0.9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 175, "s": [0.723]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 176, "s": [0.535]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 177, "s": [0.346]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.174]}, "t": 178, "s": [0.166]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.014]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 181, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 184, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 185, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 186, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 188, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 190, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 191, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 192, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 193, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 194, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 195, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 196, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 197, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 198, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 199, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 201, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 202, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 203, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 204, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 205, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 206, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 207, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 208, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 209, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 210, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 211, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 212, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 213, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 214, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 215, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 216, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 217, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 218, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 219, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 220, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 221, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 222, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 223, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 224, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 225, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 226, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 227, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 228, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 229, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 230, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 231, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 232, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 233, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 234, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 235, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 236, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 237, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 238, "s": [0]}, {"t": 239, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [-32.5, 188.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [108.5, 15.563, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 137, "h": 133, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "鞭炮3", "parent": 6, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [-55]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [-55]}, {"i": {"x": [0.833], "y": [0.491]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [-55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}, "t": 110, "s": [-55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [-48.889]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [-42.778]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [-36.667]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [-30.556]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [-24.444]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [-18.333]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [-12.222]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [-6.111]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.155]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 120, "s": [7.132]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 121, "s": [13.052]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 122, "s": [17.645]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 123, "s": [20.871]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 124, "s": [22.753]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 125, "s": [23.375]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 126, "s": [22.867]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 127, "s": [21.393]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 128, "s": [19.138]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 129, "s": [16.303]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 130, "s": [13.088]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 131, "s": [9.684]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 132, "s": [6.269]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 133, "s": [2.998]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 135, "s": [-2.624]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 136, "s": [-4.802]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 137, "s": [-6.491]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 138, "s": [-7.678]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 139, "s": [-8.37]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 140, "s": [-8.599]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 141, "s": [-8.412]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 142, "s": [-7.87]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 143, "s": [-7.041]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 144, "s": [-5.998]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 145, "s": [-4.815]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 146, "s": [-3.563]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 147, "s": [-2.306]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 148, "s": [-1.103]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 149, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 150, "s": [0.965]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 151, "s": [1.766]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 152, "s": [2.388]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 153, "s": [2.825]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 154, "s": [3.079]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 155, "s": [3.164]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 156, "s": [3.095]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 157, "s": [2.895]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 158, "s": [2.59]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 159, "s": [2.206]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 160, "s": [1.771]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 161, "s": [1.311]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 162, "s": [0.848]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 163, "s": [0.406]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 165, "s": [-0.355]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 166, "s": [-0.65]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 167, "s": [-0.879]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 168, "s": [-1.039]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 169, "s": [-1.133]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 170, "s": [-1.164]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 171, "s": [-1.138]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 172, "s": [-1.065]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 173, "s": [-0.953]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 174, "s": [-0.812]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 175, "s": [-0.652]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 176, "s": [-0.482]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 177, "s": [-0.312]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.174]}, "t": 178, "s": [-0.149]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.012]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 181, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 184, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 185, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 186, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 188, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 190, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 191, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 192, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 193, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 194, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 195, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 196, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 197, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 198, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 199, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 201, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 202, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 203, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 204, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 205, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 206, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 207, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 208, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 209, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 210, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 211, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 212, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 213, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 214, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 215, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 216, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 217, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 218, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 219, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 220, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 221, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 222, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 223, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 224, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 225, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 226, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 227, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 228, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 229, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 230, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 231, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 232, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 233, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 234, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 235, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 236, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 237, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 238, "s": [0]}, {"t": 239, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [-36.5, 144, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [19.938, 24.188, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 138, "h": 134, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "鞭炮2", "parent": 6, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [63]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [63]}, {"i": {"x": [0.833], "y": [1.583]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [63]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}, "t": 110, "s": [63]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [56]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [49]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [42]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [35]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [14]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [7]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.155]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 120, "s": [-8.169]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 121, "s": [-14.951]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 122, "s": [-20.212]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 123, "s": [-23.906]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 124, "s": [-26.062]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 125, "s": [-26.776]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 126, "s": [-26.193]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 127, "s": [-24.504]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 128, "s": [-21.922]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 129, "s": [-18.675]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 130, "s": [-14.991]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 131, "s": [-11.093]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 132, "s": [-7.181]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 133, "s": [-3.434]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 135, "s": [3.005]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 136, "s": [5.5]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 137, "s": [7.436]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 138, "s": [8.795]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 139, "s": [9.588]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 140, "s": [9.85]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 141, "s": [9.636]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 142, "s": [9.015]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 143, "s": [8.065]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 144, "s": [6.87]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 145, "s": [5.515]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 146, "s": [4.081]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 147, "s": [2.642]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 148, "s": [1.263]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 149, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 150, "s": [-1.106]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 151, "s": [-2.023]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 152, "s": [-2.735]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 153, "s": [-3.235]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 154, "s": [-3.527]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 155, "s": [-3.624]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 156, "s": [-3.545]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 157, "s": [-3.316]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 158, "s": [-2.967]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 159, "s": [-2.527]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 160, "s": [-2.029]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 161, "s": [-1.501]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 162, "s": [-0.972]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 163, "s": [-0.465]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 165, "s": [0.407]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 166, "s": [0.744]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 167, "s": [1.006]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 168, "s": [1.19]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 169, "s": [1.298]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 170, "s": [1.333]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 171, "s": [1.304]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 172, "s": [1.22]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 173, "s": [1.091]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 174, "s": [0.93]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 175, "s": [0.746]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 176, "s": [0.552]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 177, "s": [0.358]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.174]}, "t": 178, "s": [0.171]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.014]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 181, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 184, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 185, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 186, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 188, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 190, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 191, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 192, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 193, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 194, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 195, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 196, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 197, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 198, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 199, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 201, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 202, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 203, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 204, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 205, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 206, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 207, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 208, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 209, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 210, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 211, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 212, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 213, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 214, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 215, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 216, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 217, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 218, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 219, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 220, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 221, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 222, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 223, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 224, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 225, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 226, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 227, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 228, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 229, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 230, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 231, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 232, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 233, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 234, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 235, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 236, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 237, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 238, "s": [0]}, {"t": 239, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [-42, 96.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [107.25, 20, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 142, "h": 140, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "鞭炮1", "parent": 6, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [-44]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [-44]}, {"i": {"x": [0.833], "y": [0.593]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [-44]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}, "t": 110, "s": [-44]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [-39.111]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [-34.222]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [-29.333]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [-24.444]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [-19.556]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [-14.667]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [-9.778]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [-4.889]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.155]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 120, "s": [5.705]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 121, "s": [10.442]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 122, "s": [14.116]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 123, "s": [16.696]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 124, "s": [18.202]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 125, "s": [18.7]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 126, "s": [18.294]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 127, "s": [17.114]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 128, "s": [15.311]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 129, "s": [13.043]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 130, "s": [10.47]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 131, "s": [7.747]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 132, "s": [5.015]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 133, "s": [2.398]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 135, "s": [-2.099]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 136, "s": [-3.841]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 137, "s": [-5.193]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 138, "s": [-6.142]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 139, "s": [-6.696]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 140, "s": [-6.879]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 141, "s": [-6.73]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 142, "s": [-6.296]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 143, "s": [-5.632]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 144, "s": [-4.798]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 145, "s": [-3.852]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 146, "s": [-2.85]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 147, "s": [-1.845]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 148, "s": [-0.882]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 149, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 150, "s": [0.772]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 151, "s": [1.413]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 152, "s": [1.91]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 153, "s": [2.26]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 154, "s": [2.463]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 155, "s": [2.531]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 156, "s": [2.476]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 157, "s": [2.316]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 158, "s": [2.072]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 159, "s": [1.765]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 160, "s": [1.417]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 161, "s": [1.048]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 162, "s": [0.679]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 163, "s": [0.325]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 165, "s": [-0.284]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 166, "s": [-0.52]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 167, "s": [-0.703]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 168, "s": [-0.831]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 169, "s": [-0.906]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 170, "s": [-0.931]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 171, "s": [-0.911]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 172, "s": [-0.852]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 173, "s": [-0.762]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 174, "s": [-0.649]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 175, "s": [-0.521]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 176, "s": [-0.386]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 177, "s": [-0.25]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.174]}, "t": 178, "s": [-0.119]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.01]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 181, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 184, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 185, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 186, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 188, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 190, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 191, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 192, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 193, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 194, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 195, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 196, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 197, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 198, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 199, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 201, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 202, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 203, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 204, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 205, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 206, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 207, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 208, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 209, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 210, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 211, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 212, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 213, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 214, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 215, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 216, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 217, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 218, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 219, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 220, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 221, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 222, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 223, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 224, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 225, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 226, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 227, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 228, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 229, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 230, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 231, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 232, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 233, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 234, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 235, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 236, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 237, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 238, "s": [0]}, {"t": 239, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [-34.75, 44.996, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [31.875, 20.625, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 140, "h": 140, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "鞭炮5", "parent": 6, "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [-39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [-39]}, {"i": {"x": [0.833], "y": [0.639]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [-39]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}, "t": 110, "s": [-39]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [-34.667]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [-30.333]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [-26]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [-21.667]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [-17.333]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [-13]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [-8.667]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [-4.333]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.155]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 120, "s": [5.057]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 121, "s": [9.255]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 122, "s": [12.512]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 123, "s": [14.799]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 124, "s": [16.134]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 125, "s": [16.575]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 126, "s": [16.215]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 127, "s": [15.169]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 128, "s": [13.571]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 129, "s": [11.56]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 130, "s": [9.28]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 131, "s": [6.867]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 132, "s": [4.445]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 133, "s": [2.126]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 135, "s": [-1.86]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 136, "s": [-3.405]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 137, "s": [-4.603]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 138, "s": [-5.444]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 139, "s": [-5.935]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 140, "s": [-6.098]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 141, "s": [-5.965]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 142, "s": [-5.58]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 143, "s": [-4.992]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 144, "s": [-4.253]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 145, "s": [-3.414]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 146, "s": [-2.526]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 147, "s": [-1.635]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 148, "s": [-0.782]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 149, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 150, "s": [0.684]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 151, "s": [1.253]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 152, "s": [1.693]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 153, "s": [2.003]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 154, "s": [2.183]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 155, "s": [2.243]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 156, "s": [2.194]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 157, "s": [2.053]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 158, "s": [1.837]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 159, "s": [1.565]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 160, "s": [1.256]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 161, "s": [0.929]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 162, "s": [0.602]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 163, "s": [0.288]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 165, "s": [-0.252]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 166, "s": [-0.461]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 167, "s": [-0.623]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 168, "s": [-0.737]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 169, "s": [-0.803]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 170, "s": [-0.825]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 171, "s": [-0.807]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 172, "s": [-0.755]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 173, "s": [-0.676]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 174, "s": [-0.576]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 175, "s": [-0.462]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 176, "s": [-0.342]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 177, "s": [-0.221]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.174]}, "t": 178, "s": [-0.106]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.009]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 181, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 184, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 185, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 186, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 188, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 190, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 191, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 192, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 193, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 194, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 195, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 196, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 197, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 198, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 199, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 201, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 202, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 203, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 204, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 205, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 206, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 207, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 208, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 209, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 210, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 211, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 212, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 213, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 214, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 215, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 216, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 217, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 218, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 219, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 220, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 221, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 222, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 223, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 224, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 225, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 226, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 227, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 228, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 229, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 230, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 231, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 232, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 233, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 234, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 235, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 236, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 237, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 238, "s": [0]}, {"t": 239, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [-68.5, 292.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-15.625, 99.375, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 130, "h": 135, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "线", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 75, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 83, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 85, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 86, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 89, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 91, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 92, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 93, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 94, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [24]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [24]}, {"i": {"x": [0.833], "y": [1.069]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [24]}, {"i": {"x": [0.833], "y": [0.693]}, "o": {"x": [0.167], "y": [0.083]}, "t": 110, "s": [24]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.114]}, "t": 111, "s": [23.177]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.141]}, "t": 112, "s": [20.971]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.154]}, "t": 113, "s": [17.778]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.163]}, "t": 114, "s": [13.992]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.171]}, "t": 115, "s": [10.008]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.182]}, "t": 116, "s": [6.222]}, {"i": {"x": [0.833], "y": [0.886]}, "o": {"x": [0.167], "y": [0.204]}, "t": 117, "s": [3.029]}, {"i": {"x": [0.833], "y": [0.712]}, "o": {"x": [0.167], "y": [0.307]}, "t": 118, "s": [0.823]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.117]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 120, "s": [-2.024]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 121, "s": [-3.704]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 122, "s": [-5.008]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 123, "s": [-5.923]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 124, "s": [-6.457]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 125, "s": [-6.634]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 126, "s": [-6.489]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 127, "s": [-6.071]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 128, "s": [-5.431]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 129, "s": [-4.627]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 130, "s": [-3.714]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 131, "s": [-2.748]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 132, "s": [-1.779]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.174]}, "t": 133, "s": [-0.851]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.184]}, "t": 135, "s": [0.745]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.191]}, "t": 136, "s": [1.363]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.202]}, "t": 137, "s": [1.842]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.226]}, "t": 138, "s": [2.179]}, {"i": {"x": [0.833], "y": [0.985]}, "o": {"x": [0.167], "y": [0.335]}, "t": 139, "s": [2.375]}, {"i": {"x": [0.833], "y": [0.675]}, "o": {"x": [0.167], "y": [-0.019]}, "t": 140, "s": [2.44]}, {"i": {"x": [0.833], "y": [0.789]}, "o": {"x": [0.167], "y": [0.112]}, "t": 141, "s": [2.387]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}, "t": 142, "s": [2.233]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}, "t": 143, "s": [1.998]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}, "t": 144, "s": [1.702]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}, "t": 145, "s": [1.366]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}, "t": 146, "s": [1.011]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}, "t": 147, "s": [0.654]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.174]}, "t": 148, "s": [0.313]}, {"t": 149, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 110, "s": [284, -348, 0], "to": [0, 59, 0], "ti": [0, -59, 0]}, {"t": 119, "s": [284, 6, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-9.25, -10.75]], "o": [[0, 0], [0, 0], [7.5, 10]], "v": [[-41.5, 10], [-41.5, 250], [-32.25, 308.75]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.482352942228, 0.011764706112, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "鞭炮6", "parent": 6, "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [46]}, {"t": 119, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.2;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [-48.75, 261.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [81.687, 29.313, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 139, "h": 143, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "爆炸无烟", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [294, 140, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 63, "op": 123, "st": 63, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "爆炸无烟", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [225.5, 239, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 52, "op": 112, "st": 52, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "爆炸无烟", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [222, 264, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 41, "op": 101, "st": 41, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "爆炸无烟", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [233, 303, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 31, "op": 91, "st": 31, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "爆炸无烟", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [350, 325, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 21, "op": 81, "st": 21, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 0, "nm": "爆炸无烟", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [259, 401, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 9, "op": 69, "st": 9, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 0, "nm": "鞭炮4", "parent": 21, "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 31, "s": [100]}, {"t": 32, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [0]}, {"t": 31, "s": [-32]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 21, "s": [-34, 190.5, 0], "to": [-0.667, 11.333, 0], "ti": [0.667, -11.333, 0]}, {"t": 31, "s": [-38, 258.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [106.625, 17.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 137, "h": 133, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 0, "nm": "鞭炮3", "parent": 21, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 41, "s": [100]}, {"t": 42, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 31, "s": [0]}, {"t": 41, "s": [-12]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 31, "s": [-36.25, 142.75, 0], "to": [0, 15.333, 0], "ti": [0, -15.333, 0]}, {"t": 41, "s": [-36.25, 234.75, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [20.25, 22.625, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 138, "h": 134, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 0, "nm": "鞭炮2", "parent": 21, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [100]}, {"t": 53, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [13]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 41, "s": [0]}, {"t": 52, "s": [-25]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.8;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 41, "s": [-44, 99.75, 0], "to": [0, 15.5, 0], "ti": [0, -15.5, 0]}, {"t": 52, "s": [-44, 192.75, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [104.75, 23.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 142, "h": 140, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "鞭炮1", "parent": 21, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [100]}, {"t": 65, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9, "s": [-4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [-19]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-4]}, {"t": 54, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.8;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.734, "y": 1}, "o": {"x": 0.448, "y": 0}, "t": 12, "s": [-36.75, 45.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.177, "y": 0}, "t": 21, "s": [-45.75, 45.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.323, "y": 0.323}, "o": {"x": 0.448, "y": 0.448}, "t": 29, "s": [-36.75, 45.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 54, "s": [-36.75, 45.5, 0], "to": [0, 24.5, 0], "ti": [0, -10.667, 0]}, {"t": 64, "s": [-36.75, 109.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29.375, 21.25, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 140, "h": 140, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 0, "nm": "鞭炮5", "parent": 21, "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [100]}, {"t": 22, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [0]}, {"t": 21, "s": [-23]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 12, "s": [-34.75, 228, 0], "to": [0.667, 14.333, 0], "ti": [-0.667, -14.333, 0]}, {"t": 21, "s": [-30.75, 314, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [26.562, 18.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 130, "h": 135, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "形状图层 3", "parent": 21, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [100]}, {"t": 79, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [-25]}, {"t": 27, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-28.125, 314, 0], "to": [-7.018, -0.608, 0], "ti": [-4.91, 24.79, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [-36.164, 255.72, 0], "to": [1.53, -7.725, 0], "ti": [0.35, 8.786, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [-17.228, 245.927, 0], "to": [-44.56, -54.645, 0], "ti": [4.301, 44.293, 0]}, {"t": 77, "s": [-42.625, 10.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.625, 65, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.183, 76.206], [-4.5, 71.375], [0.625, 78.5], [2.125, 69.75], [9.375, 74.625], [8.375, 68.25], [15, 67.5], [6.891, 61.318]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-8.105, 72.342], [-4.499, 69.528], [-1.798, 76.874], [0.951, 69.159], [8.181, 76.077], [7.183, 66.625], [12.183, 66.451], [6.891, 61.318]], "c": true}]}, {"t": 140, "s": [{"i": [[4.276, -3.715], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-4.598, 3.994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.938, 60.25], [-5.638, 75.561], [-1.72, 70.355], [5.402, 80.832], [4.023, 68.406], [10.289, 73.801], [8.77, 66.442], [12.183, 66.451], [6.891, 61.318]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.988, 0.478, 0.5, 1, 0.892, 0.4, 1, 1, 0.796, 0.322], "ix": 9}}, "s": {"a": 0, "k": [6.659, 74.287], "ix": 5}, "e": {"a": 0, "k": [2.032, 62.099], "ix": 6}, "t": 1, "nm": "awnemnwn", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "引线", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [-12]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [-12]}, {"t": 85, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [241.5, 17, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-42.5, 11, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [-9.25, -10.75]], "o": [[0, 0], [0, 0], [7.5, 10]], "v": [[-41.5, 10], [-41.5, 250], [-32.25, 308.75]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [-39.91, -53.537], [-9.25, -10.75]], "o": [[0, 0], [30.677, 37.631], [7.5, 10]], "v": [[-41.5, 10], [-18.892, 237.426], [-32.25, 308.75]], "c": false}]}, {"t": 76, "s": [{"i": [[0, 0], [0, 0], [-9.25, -10.75]], "o": [[0, 0], [0, 0], [7.5, 10]], "v": [[-41.5, 10], [-41.5, 250], [-32.25, 308.75]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 76, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.482352942228, 0.011764706112, 0.219607844949, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "鞭炮6", "parent": 21, "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [100]}, {"t": 10, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 9, "s": [-14]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 0, "s": [-47.75, 257.5, 0], "to": [0, 14.5, 0], "ti": [0, -14.5, 0]}, {"t": 9, "s": [-47.75, 344.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [82.937, 24, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 139, "h": 143, "ip": 0, "op": 240, "st": 0, "bm": 0}], "markers": []}