// ==UserScript==
// @name         豆瓣 Emby 媒体检查与快捷链接
// @namespace    http://tampermonkey.net/
// @version      4.2
// @description  在豆瓣电影页面检查您的 Emby 服务器中是否存在该媒体。
// <AUTHOR> & Optimized
// @match        https://movie.douban.com/subject/*
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @connect      embyHost
// @key          提醒：使用此脚本前，请务必修改下方代码中的 embyHost 和 embyApiKey 为您自己的 Emby 服务器地址和API密钥。
// @key          同时，您可能需要根据您的 embyHost 修改上面一行 "@connect embyHost" 中的 "embyHost" 为您的实际主机名 (例如 @connect my.emby.server.com 或 @connect *************)，以便油猴插件授权网络请求。
// ==/UserScript==

(function () {
    'use strict';

    // ==================== 配置区域 ====================

    // --- 用户配置 ---
    // 请务必将此处 "填写1" 修改为您的 Emby 服务器地址，例如 "http://localhost:8096" 或 "https://your.emby.server"
    const embyHost = "";
    // 请务必将此处 "填写2" 修改为您的 Emby API Key
    const embyApiKey = "";

    // --- 站点配置 ---
    const SITES_CONFIG = {
        mteam: { name: '馒头', emoji: '🥟', url: 'https://kp.m-team.cc/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        audiences: { name: '观众', emoji: '👥', url: 'https://audiences.me/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        hhanclub: { name: '憨憨', emoji: '😊', url: 'https://hhanclub.top/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        hdsky: { name: '天空', emoji: '☁️', url: 'https://hdsky.me/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        chdbits: { name: '彩虹岛', emoji: '🌈', url: 'https://chdbits.co/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        ttg: { name: '听听歌', emoji: '🎵', url: 'https://totheglory.im/browse.php?c=M&search_field={title}', searchType: 'title' },
        ourbits: { name: '我堡', emoji: '🏰', url: 'https://ourbits.club/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        hddolby: { name: '杜比', emoji: '🔊', url: 'https://www.hddolby.com/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        ssd: { name: '春天', emoji: '🌸', url: 'https://springsunday.net/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        ubits: { name: '你堡', emoji: '🏯', url: 'https://ubits.club/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' }
    };

    // --- 系统配置 (淡雅配色方案) ---
    const CONFIG = {
        cacheTTL: 10 * 60 * 1000,
        requestTimeout: 15000,
        maxRetries: 3,
        retryDelay: 1000,
        colors: {
            background: 'rgba(255, 255, 255, 0.65)',
            border: 'rgba(200, 200, 220, 0.3)',
            success: 'rgba(16, 185, 129, 0.9)',
            successHover: 'rgba(5, 150, 105, 1)',
            warning: 'rgba(245, 158, 11, 0.9)',
            warningHover: 'rgba(217, 119, 6, 1)',
            error: 'rgba(239, 68, 68, 0.85)',
            errorHover: 'rgba(220, 38, 38, 1)',
            loading: 'rgba(148, 163, 184, 0.8)',
            primary: 'rgba(59, 130, 246, 0.9)',
            primaryHover: 'rgba(37, 99, 235, 1)',
            secondary: 'rgba(139, 92, 246, 0.85)',
            secondaryHover: 'rgba(124, 58, 237, 1)',
            textLight: '#ffffff',
            shadow: 'rgba(0, 0, 0, 0.05)',
            shadowHover: 'rgba(0, 0, 0, 0.15)'
        },
        spacing: { sm: '8px', md: '12px', lg: '16px' },
        borderRadius: { lg: '16px', xl: '20px' },
        fontSize: { sm: '13px', md: '14px' }
    };

    // ==================== 样式定义 ====================

    function injectStyles() {
        GM_addStyle(`
            /* 主容器 - iOS风格毛玻璃效果 */
            #emby-script-container {
                background: ${CONFIG.colors.background};
                backdrop-filter: blur(16px);
                -webkit-backdrop-filter: blur(16px);
                border: 1px solid ${CONFIG.colors.border};
                border-radius: ${CONFIG.borderRadius.xl};
                padding: ${CONFIG.spacing.lg};
                margin: ${CONFIG.spacing.lg} 0;
                box-shadow: 0 8px 32px 0 ${CONFIG.colors.shadow};
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            /* 按钮栏 */
            #emby-script-button-bar {
                display: flex;
                flex-wrap: wrap;
                gap: ${CONFIG.spacing.md};
                align-items: center;
            }

            /* 统一按钮样式 - 圆润矩形 */
            .es-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 10px 16px;
                border-radius: ${CONFIG.borderRadius.lg};
                color: ${CONFIG.colors.textLight} !important;
                text-decoration: none !important;
                font-size: ${CONFIG.fontSize.md};
                font-weight: 500;
                transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
                border: none;
                cursor: pointer;
                gap: 8px;
                white-space: nowrap;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .es-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 16px ${CONFIG.colors.shadowHover};
                color: ${CONFIG.colors.textLight} !important;
            }
            .es-button:active { transform: translateY(0); }

            /* 状态按钮样式 */
            .es-button.status-success { background: ${CONFIG.colors.success}; }
            .es-button.status-success:hover { background: ${CONFIG.colors.successHover}; }
            .es-button.status-warning { background: ${CONFIG.colors.warning}; }
            .es-button.status-warning:hover { background: ${CONFIG.colors.warningHover}; }
            .es-button.status-error { background: ${CONFIG.colors.error}; }
            .es-button.status-error:hover { background: ${CONFIG.colors.errorHover}; }
            .es-button.status-loading { background: ${CONFIG.colors.loading}; cursor: default; }

            /* 外部链接/PT站点按钮 */
            .es-button.external { background: ${CONFIG.colors.primary}; }
            .es-button.external:hover { background: ${CONFIG.colors.primaryHover}; }
            .es-button.pt-site { background: ${CONFIG.colors.secondary}; flex-shrink: 0; } /* flex-shrink:0 防止按钮在flex容器中被压缩 */
            .es-button.pt-site:hover { background: ${CONFIG.colors.secondaryHover}; }

            /* PT站点容器 - 优化为横向滚动 */
            .pt-sites-container {
                display: flex;
                gap: ${CONFIG.spacing.md};
                max-height: 0;
                overflow-x: auto;
                overflow-y: hidden;
                transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                opacity: 0;
                margin-top: 0;
                padding: 0 2px; /* 给滚动条留出一点空间 */
                scrollbar-width: thin;
                scrollbar-color: ${CONFIG.colors.border} transparent;
            }
            /* 美化滚动条 (Chrome/Safari) */
            .pt-sites-container::-webkit-scrollbar { height: 6px; }
            .pt-sites-container::-webkit-scrollbar-track { background: transparent; }
            .pt-sites-container::-webkit-scrollbar-thumb { background-color: ${CONFIG.colors.border}; border-radius: 10px; border: 2px solid transparent; background-clip: content-box; }

            .pt-sites-container.expanded {
                max-height: 70px; /* 固定高度，刚好容纳一行按钮和内外边距 */
                opacity: 1;
                margin-top: ${CONFIG.spacing.lg};
                padding-top: ${CONFIG.spacing.md};
                padding-bottom: ${CONFIG.spacing.md};
                border-top: 1px solid ${CONFIG.colors.border};
            }

            /* 切换图标动画 */
            .toggle-icon {
                transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                display: inline-block;
            }
            .toggle-icon.rotated { transform: rotate(180deg); }

            /* 加载动画 */
            .loading-spinner {
                width: 16px; height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 50%; border-top-color: ${CONFIG.colors.textLight};
                animation: spin 1s linear infinite;
            }
            @keyframes spin { to { transform: rotate(360deg); } }
        `);
    }

    // ==================== 工具函数 ====================
    function validateConfig() {
        if (!embyHost || embyHost.trim() === "" || embyHost.includes("填写") || !embyApiKey || embyApiKey.trim() === "" || embyApiKey.includes("填写")) {
            console.error('[Emby Script] 配置未完成，请检查embyHost和embyApiKey。');
            return false;
        }
        return true;
    }

    function requestEmby(url) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: "GET", url, timeout: CONFIG.requestTimeout,
                onload: res => (res.status >= 200 && res.status < 300) ? resolve(res.responseText) : reject(new Error(`HTTP ${res.status}: ${res.statusText}`)),
                onerror: () => reject(new Error('网络请求失败')),
                ontimeout: () => reject(new Error('请求超时'))
            });
        });
    }

    async function fetchWithRetry(url) {
        for (let attempt = 1; attempt <= CONFIG.maxRetries; attempt++) {
            try {
                return JSON.parse(await requestEmby(url));
            } catch (error) {
                console.warn(`[Emby Script] 请求失败 (${attempt}/${CONFIG.maxRetries}):`, error.message);
                if (attempt === CONFIG.maxRetries) throw error;
                await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay * Math.pow(2, attempt - 1)));
            }
        }
    }

    // ==================== 数据提取与处理 ====================
    function extractDoubanInfo() {
        const douban_id = window.location.href.match(/subject\/(\d+)/)?.[1] || '';
        let unititle = document.querySelector("#content > h1 > span:nth-child(1)")?.textContent.trim() || document.title.replace(/（.*/, "").replace(/\s*-\s*豆瓣.*/, "").trim();
        let imdb_id = document.querySelector("#info")?.textContent.match(/IMDb:\s*(tt\d+)/i)?.[1] || "";
        return { imdb_id, unititle, douban_id };
    }

    // ==================== 缓存管理 ====================
    const CacheManager = {
        get(key) {
            try {
                const data = JSON.parse(sessionStorage.getItem(key));
                if (data && Date.now() - data.timestamp < CONFIG.cacheTTL) {
                    return data.value;
                }
            } catch (e) {}
            return null;
        },
        set(key, value) {
            try {
                sessionStorage.setItem(key, JSON.stringify({ value: value, timestamp: Date.now() }));
            } catch (e) {}
        },
    };


    // ==================== UI 生成与渲染 ====================
    function generateSiteLinks(movieInfo) {
        let ptLinksHTML = '', externalLinksHTML = '', ptToggleButtonHTML = '';
        if (movieInfo.unititle) {
            externalLinksHTML += `<a href="https://www.themoviedb.org/search?q=${encodeURIComponent(movieInfo.unititle)}" target="_blank" class="es-button external" title="在TMDB中搜索">🎬 TMDB</a>`;
            const embySearchUrl = `${embyHost}/web/index.html#!/search.html?q=${encodeURIComponent(movieInfo.unititle)}`;
            externalLinksHTML += `<a href="${embySearchUrl}" target="_blank" class="es-button external" title="在Emby中搜索">🔍 Emby搜索</a>`;
        }
        const ptLinks = Object.values(SITES_CONFIG).map(site => {
            let url = site.url;
            if (site.searchType === 'imdb' && movieInfo.imdb_id) url = url.replace('{imdb}', movieInfo.imdb_id);
            else if (site.searchType === 'title' && movieInfo.unititle) url = url.replace('{title}', encodeURIComponent(movieInfo.unititle));
            else return null;
            return `<a href="${url}" target="_blank" class="es-button pt-site" title="在${site.name}中搜索">${site.emoji} ${site.name}</a>`;
        }).filter(Boolean);
        if (ptLinks.length > 0) {
            ptLinksHTML = ptLinks.join('');
            ptToggleButtonHTML = `<button id="pt-toggle-btn" class="es-button external" title="展开/收起PT站点"><span class="toggle-icon" id="toggle-icon">▼</span> PT站点 (${ptLinks.length})</button>`;
        }
        return { externalLinksHTML, ptToggleButtonHTML, ptLinksHTML };
    }

    function insertUI(movieInfo) {
        document.getElementById('emby-script-container')?.remove();
        const { externalLinksHTML, ptToggleButtonHTML, ptLinksHTML } = generateSiteLinks(movieInfo);
        const containerHTML = `<div id="emby-script-container"><div id="emby-script-button-bar"><div id="emby-status-container"></div>${externalLinksHTML}${ptToggleButtonHTML}</div><div class="pt-sites-container" id="pt-sites-container">${ptLinksHTML}</div></div>`;
        const h1 = document.querySelector("#content h1");
        if(h1) {
            h1.insertAdjacentHTML('afterend', containerHTML);
        }
        document.getElementById('pt-toggle-btn')?.addEventListener('click', () => {
            document.getElementById('pt-sites-container')?.classList.toggle('expanded');
            document.getElementById('toggle-icon')?.classList.toggle('rotated');
        });
    }

    function showLoadingStatus() {
        updateStatus(`<div class="es-button status-loading"><span class="loading-spinner"></span>正在检查...</div>`);
    }

    function updateStatus(html) {
        const container = document.getElementById('emby-status-container');
        if (container) {
            container.innerHTML = html;
        }
    }

    // ==================== Emby 媒体检查 ====================
    async function searchEmbyByImdb(imdb_id) {
        const url = `${embyHost}/emby/Items?Recursive=True&AnyProviderIdEquals=imdb.${imdb_id}&api_key=${embyApiKey}`;
        const response = await fetchWithRetry(url);
        if (response?.TotalRecordCount > 0 && response.Items?.length > 0) {
            const item = response.Items[0];
            const jumpUrl = `${embyHost}/web/index.html#!/item?id=${item.Id}&serverId=${item.ServerId}`;
            return { html: `<a href="${jumpUrl}" target="_blank" class="es-button status-success" title="媒体已入库: ${item.Name} (点击跳转)">✨ 媒体已入库</a>`, status: 'found' };
        }
        return { status: 'not_found' };
    }

    async function searchEmbyByTitle(title) {
        const url = `${embyHost}/emby/Items?IncludeItemTypes=Movie,Series&Recursive=True&api_key=${embyApiKey}&SearchTerm=${encodeURIComponent(title)}`;
        const response = await fetchWithRetry(url);
        if (response?.TotalRecordCount > 0 && response.Items?.length > 0) {
            const item = response.Items[0];
            const jumpUrl = `${embyHost}/web/index.html#!/item?id=${item.Id}&serverId=${item.ServerId}`;
            return { html: `<a href="${jumpUrl}" target="_blank" class="es-button status-warning" title="疑似存在: ${item.Name} (点击跳转)">? 疑似存在</a>`, status: 'maybe_found' };
        }
        return { html: `<div class="es-button status-error" title="媒体库中暂无此影片">📭 暂未入库</div>`, status: 'not_found' };
    }

    async function checkEmby(movieInfo) {
        const cacheKey = `emby_script_${movieInfo.imdb_id || movieInfo.unititle}`;
        const cachedResult = CacheManager.get(cacheKey);
        if (cachedResult) {
            return cachedResult;
        }
        try {
            if (movieInfo.imdb_id) {
                const result = await searchEmbyByImdb(movieInfo.imdb_id);
                if (result.status === 'found') {
                    CacheManager.set(cacheKey, result);
                    return result;
                }
            }
            if (movieInfo.unititle) {
                const result = await searchEmbyByTitle(movieInfo.unititle);
                CacheManager.set(cacheKey, result);
                return result;
            }
            const noDataResult = { html: `<div class="es-button status-error" title="无法获取有效的影片信息">❓ 信息不足</div>`, status: 'no_data' };
            CacheManager.set(cacheKey, noDataResult);
            return noDataResult;
        } catch (error) {
            console.error('[Emby Script] Emby查询失败:', error);
            return { html: `<div class="es-button status-error" title="网络连接或服务器出现问题: ${error.message}">❌ 连接失败</div>`, status: 'error' };
        }
    }

    // ==================== 主程序入口 ====================
    async function main() {
        if (!window.location.href.includes('movie.douban.com/subject/')) return;
        await new Promise(resolve => { if (document.readyState === 'complete') resolve(); else window.addEventListener('load', resolve, { once: true }); });
        await new Promise(resolve => setTimeout(resolve, 500));

        try {
            console.log('[Emby Script] v4.2 初始化...');
            injectStyles();
            const movieInfo = extractDoubanInfo();
            insertUI(movieInfo);

            if (!validateConfig()) {
                updateStatus(`<div class="es-button status-error" title="请在脚本中配置Emby服务器地址和API密钥">⚙️ 配置未完成</div>`);
                return;
            }

            if (!movieInfo.unititle && !movieInfo.imdb_id) {
                updateStatus(`<div class="es-button status-error" title="无法从页面获取有效的影片信息">❓ 信息不足</div>`);
                return;
            }

            showLoadingStatus();
            const embyResult = await checkEmby(movieInfo);
            updateStatus(embyResult.html);

            console.log('[Emby Script] 初始化完成');
        } catch (error) {
            console.error('[Emby Script] 初始化过程中发生严重错误:', error);
            updateStatus(`<div class="es-button status-error" title="脚本初始化失败: ${error.message}">❌ 初始化失败</div>`);
        }
    }

    main();
})();