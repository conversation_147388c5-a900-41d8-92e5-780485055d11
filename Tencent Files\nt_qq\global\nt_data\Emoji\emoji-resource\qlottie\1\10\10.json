{"v": "5.7.8", "fr": 60, "ip": 0, "op": 179, "w": 512, "h": 512, "nm": "庆祝修改后", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "line4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [268, 268, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-24.823, -26.375], [-23.717, -2.79], [-20.926, 64.174], [-62.779, 13.951], [0, 0]], "o": [[0, 0], [22.322, 23.717], [29.392, 3.458], [20.926, -64.174], [62.779, -13.951], [0, 0]], "v": [[-62.082, 84.403], [-4.883, 53.711], [29.995, 98.354], [89.984, 35.575], [186.245, -76.033], [229.493, -71.847]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.229461669922, 0.871826171875, 0.928344726563, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0]}, {"t": 105, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [75]}, {"t": 116, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "line4-shadow", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [268, 275, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\n$bm_rt = transform.position;"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-24.823, -26.375], [-23.717, -2.79], [-20.926, 64.174], [-62.779, 13.951], [0, 0]], "o": [[0, 0], [22.322, 23.717], [29.392, 3.458], [20.926, -64.174], [62.779, -13.951], [0, 0]], "v": [[-62.082, 84.403], [-4.883, 53.711], [29.995, 98.354], [89.984, 35.575], [186.245, -76.033], [229.493, -71.847]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.136047363281, 0.760864257813, 0.815856933594, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0]}, {"t": 105, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [75]}, {"t": 116, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "line3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -20.407, "ix": 10}, "p": {"a": 0, "k": [307.84, 286.371, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.926, 16.722], [-6.975, 41.853], [-46.415, -21.842], [-37.668, 0], [-64.174, -13.951], [0, 0]], "o": [[-27.902, -29.297], [14.565, -87.391], [23.717, 11.161], [37.667, 0], [64.174, 13.951], [0, 0]], "v": [[-145.787, -60.687], [-177.875, -162.529], [-7.673, -218.332], [63.477, -176.48], [162.529, -197.406], [225.308, -115.095]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [66]}, {"t": 107, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 87, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.928370098039, 0.229475776822, 0.589907537722, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "line3-shadow", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -20.407, "ix": 10}, "p": {"a": 0, "k": [307.84, 293.371, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.926, 16.722], [-6.975, 41.853], [-46.415, -21.842], [-37.668, 0], [-64.174, -13.951], [0, 0]], "o": [[-27.902, -29.297], [14.565, -87.391], [23.717, 11.161], [37.667, 0], [64.174, 13.951], [0, 0]], "v": [[-145.787, -60.687], [-177.875, -162.529], [-7.673, -218.332], [63.477, -176.48], [162.529, -197.406], [225.308, -115.095]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [66]}, {"t": 107, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 87, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.832977294922, 0.120483398438, 0.487945556641, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "line2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-18.298, -34.765], [6.145, -49.158], [23.717, 54.409], [82.997, 59.283], [-20.926, 51.619]], "o": [[0, 0], [13.951, 26.507], [-6.975, 55.804], [-42.59, -97.707], [-78.125, -55.804], [24.724, -60.986]], "v": [[-109.515, 6.278], [-140.207, 41.155], [-99.749, 103.935], [-198.801, 101.144], [-204.381, -84.403], [-272.741, -201.591]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.229475781322, 0.871841967106, 0.928370118141, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"t": 89, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [74]}, {"t": 112, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "line2-shadow", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 263, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-18.298, -34.765], [6.145, -49.158], [23.717, 54.409], [82.997, 59.283], [-20.926, 51.619]], "o": [[0, 0], [13.951, 26.507], [-6.975, 55.804], [-42.59, -97.707], [-78.125, -55.804], [24.724, -60.986]], "v": [[-109.515, 6.278], [-140.207, 41.155], [-99.749, 103.935], [-198.801, 101.144], [-204.381, -84.403], [-272.741, -201.591]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.136043414474, 0.760898292065, 0.815885424614, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"t": 89, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [74]}, {"t": 112, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "line1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [118.524, 118.524, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-68.993, 30.184], [-30.269, -67.288], [65.772, 23.71], [-18.177, 16.41], [-1.369, 0]], "o": [[0, 0], [21.904, -9.583], [11.224, 24.951], [-112.563, -40.577], [43.088, -38.898], [1.369, 0]], "v": [[-28.749, 238.203], [41.081, 79.166], [149.909, 122.231], [64.432, 190.55], [92.934, 25.645], [225.935, -28.096]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.928370098039, 0.229475776822, 0.589907537722, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"t": 90, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [85]}, {"t": 110, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": -11, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "line1-shadow", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 263, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [118.524, 118.524, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-68.993, 30.184], [-30.269, -67.288], [65.772, 23.71], [-18.177, 16.41], [-1.369, 0]], "o": [[0, 0], [21.904, -9.583], [11.224, 24.951], [-112.563, -40.577], [43.088, -38.898], [1.369, 0]], "v": [[-28.749, 238.203], [41.081, 79.166], [149.909, 122.231], [64.432, 190.55], [92.934, 25.645], [225.935, -28.096]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.833011627197, 0.120498292148, 0.487953603268, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"t": 90, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [85]}, {"t": 110, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": -11, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "eyeR", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [-5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [5]}, {"t": 175, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [50.626, -27.841, 0], "to": [-0.667, -4.833, 0], "ti": [0.667, 6.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [46.626, -56.841, 0], "to": [-0.667, -6.5, 0], "ti": [0, -1.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [46.626, -66.841, 0], "to": [0, 1.333, 0], "ti": [4.167, -2.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [46.626, -48.841, 0], "to": [-4.167, 2.833, 0], "ti": [3.104, -2.973, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [21.626, -49.841, 0], "to": [-3.104, 2.973, 0], "ti": [-2.833, 2.527, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [28, -31, 0], "to": [2.833, -2.527, 0], "ti": [-3.438, 2.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [38.626, -65, 0], "to": [3.438, -2.5, 0], "ti": [0.333, -3.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [48.626, -46, 0], "to": [-0.333, 3.167, 0], "ti": [3.838, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [36.626, -46, 0], "to": [-3.838, 0, 0], "ti": [-0.562, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [25.6, -46, 0], "to": [0.562, 0, 0], "ti": [-2.4, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [40, -46, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [40, -46, 0], "to": [1.767, 3.033, 0], "ti": [-1.767, -3.033, 0]}, {"t": 175, "s": [50.6, -27.8, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [160, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 56, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 74, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [125, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [125, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [125, 100, 100]}, {"t": 175, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[4.714, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.529, -1.982], [-1.481, -3.383], [0, -4.204], [3.089, -4.132]], "o": [[-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -3.153], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [3.143, 0], [2.529, 1.982], [1.481, 3.383], [0, 6.306], [-3.089, 4.132]], "v": [[0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-15.729, -8.888], [-12.071, -16.146], [-6.645, -21.04], [0, -22.834], [8.616, -19.717], [14.74, -11.525], [17.07, 0], [12.071, 16.146]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [{"i": [[2.13, 0], [1.358, 1.103], [0.744, -0.403], [2.342, 2.181], [-1.383, 2.887], [-20.374, -0.953], [-0.504, -0.225], [1.649, -3.692], [3.687, 1.637], [0, 0], [15.777, -6.287], [-11.306, -9.182], [2.549, -3.139]], "o": [[-1.622, 0], [-21.161, -17.185], [-2.781, 1.582], [-2.342, -2.182], [8.166, -17.049], [14.373, 0.672], [3.692, 1.65], [-1.648, 3.687], [0, 0], [-1.13, -0.5], [9.044, 0.796], [3.139, 2.549], [-1.447, 1.782]], "v": [[288.522, 299.3], [283.911, 297.661], [244.311, 290.177], [235.728, 289.154], [234.146, 280.616], [277.79, 256.012], [304.671, 262.208], [308.368, 271.88], [298.708, 275.583], [298.708, 275.583], [262.001, 272.825], [293.143, 286.294], [294.211, 296.593]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [{"i": [[2.13, 0], [1.358, 1.103], [0.744, -0.403], [2.342, 2.181], [-1.383, 2.887], [-20.374, -0.953], [-0.504, -0.225], [1.649, -3.692], [3.687, 1.637], [0, 0], [15.777, -6.287], [-11.306, -9.182], [2.549, -3.139]], "o": [[-1.622, 0], [-21.161, -17.185], [-2.781, 1.582], [-2.342, -2.182], [8.166, -17.049], [14.373, 0.672], [3.692, 1.65], [-1.648, 3.687], [0, 0], [-1.13, -0.5], [9.044, 0.796], [3.139, 2.549], [-1.447, 1.782]], "v": [[288.522, 299.3], [283.911, 297.661], [244.311, 290.177], [235.728, 289.154], [234.146, 280.616], [277.79, 256.012], [304.671, 262.208], [308.368, 271.88], [298.708, 275.583], [298.708, 275.583], [262.001, 272.825], [293.143, 286.294], [294.211, 296.593]], "c": true}]}, {"t": 175, "s": [{"i": [[4.714, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.529, -1.982], [-1.481, -3.383], [0, -4.204], [3.089, -4.132]], "o": [[-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -3.153], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [3.143, 0], [2.529, 1.982], [1.481, 3.383], [0, 6.306], [-3.089, 4.132]], "v": [[0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-15.729, -8.888], [-12.071, -16.146], [-6.645, -21.04], [0, -22.834], [8.616, -19.717], [14.74, -11.525], [17.07, 0], [12.071, 16.146]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.001, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-23.209, 8.149], "ix": 5}, "e": {"a": 0, "k": [11.197, 7.697], "ix": 6}, "t": 1, "nm": "Gradient Fill 777", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, 0], "to": [44.333, 42.833], "ti": [-44.333, -42.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [266, 257], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [266, 257], "to": [-44.333, -42.833], "ti": [44.333, 42.833]}, {"t": 175, "s": [0, 0]}], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "eyeR-shadow", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [-5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [5]}, {"t": 175, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [50.626, -24.851, 0], "to": [-0.671, -4.825, 0], "ti": [0.671, 6.492, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [46.6, -53.8, 0], "to": [-0.671, -6.492, 0], "ti": [0, -1.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [46.6, -63.8, 0], "to": [0, 1.5, 0], "ti": [4.167, -3.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [46.6, -44.8, 0], "to": [-4.167, 3.167, 0], "ti": [3.1, -3.3, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [21.6, -44.8, 0], "to": [-3.1, 3.3, 0], "ti": [-2.833, 2.56, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [28, -25, 0], "to": [2.833, -2.56, 0], "ti": [-3.433, 2.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [38.6, -60.158, 0], "to": [3.433, -2.667, 0], "ti": [0.333, -3.193, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [48.6, -41, 0], "to": [-0.333, 3.193, 0], "ti": [3.833, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [36.6, -41, 0], "to": [-3.833, 0, 0], "ti": [-0.567, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [25.6, -41, 0], "to": [0.567, 0, 0], "ti": [-2.4, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [40, -41, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [40, -41, 0], "to": [1.767, 2.683, 0], "ti": [-1.767, -2.683, 0]}, {"t": 175, "s": [50.6, -24.9, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [100, 99.676, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [160, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 56, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 74, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [125, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [125, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [125, 100, 100]}, {"t": 175, "s": [100, 99.7, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, -6.306], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [2.357, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-1.481, 3.383], [-2.529, 1.982], [-3.143, 0], [-3.089, -4.132]], "o": [[0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -4.204], [1.481, -3.383], [2.529, -1.982], [4.714, 0], [3.089, 4.132]], "v": [[17.07, 0], [15.729, 8.888], [12.071, 16.146], [6.645, 21.04], [0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-14.74, -11.525], [-8.616, -19.717], [0, -22.834], [12.071, -16.146]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[2.13, 0], [1.358, 1.103], [0.744, -0.403], [2.342, 2.181], [-1.383, 2.887], [-20.374, -0.953], [-0.504, -0.225], [1.649, -3.692], [3.687, 1.637], [0, 0], [15.777, -6.287], [-11.306, -9.182], [2.549, -3.139]], "o": [[-1.622, 0], [-21.161, -17.185], [-2.781, 1.582], [-2.342, -2.182], [8.166, -17.049], [14.373, 0.672], [3.692, 1.65], [-1.648, 3.687], [0, 0], [-1.13, -0.5], [9.044, 0.796], [3.139, 2.549], [-1.447, 1.782]], "v": [[288.548, 297.259], [283.937, 295.62], [244.337, 288.136], [235.754, 287.113], [234.172, 278.576], [277.816, 253.971], [304.697, 260.167], [308.394, 269.839], [298.735, 273.543], [298.735, 273.543], [262.027, 270.784], [293.17, 284.253], [294.237, 294.552]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [{"i": [[2.13, 0], [1.358, 1.103], [0.744, -0.403], [2.342, 2.181], [-1.383, 2.887], [-20.374, -0.953], [-0.504, -0.225], [1.649, -3.692], [3.687, 1.637], [0, 0], [15.777, -6.287], [-11.306, -9.182], [2.549, -3.139]], "o": [[-1.622, 0], [-21.161, -17.185], [-2.781, 1.582], [-2.342, -2.182], [8.166, -17.049], [14.373, 0.672], [3.692, 1.65], [-1.648, 3.687], [0, 0], [-1.13, -0.5], [9.044, 0.796], [3.139, 2.549], [-1.447, 1.782]], "v": [[288.548, 297.259], [283.937, 295.62], [244.337, 288.136], [235.754, 287.113], [234.172, 278.576], [277.816, 253.971], [304.697, 260.167], [308.394, 269.839], [298.735, 273.543], [298.735, 273.543], [262.027, 270.784], [293.17, 284.253], [294.237, 294.552]], "c": true}]}, {"t": 175, "s": [{"i": [[0, -6.306], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [2.357, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-1.481, 3.383], [-2.529, 1.982], [-3.143, 0], [-3.089, -4.132]], "o": [[0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -4.204], [1.481, -3.383], [2.529, -1.982], [4.714, 0], [3.089, 4.132]], "v": [[17.07, 0], [15.729, 8.888], [12.071, 16.146], [6.645, 21.04], [0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-14.74, -11.525], [-8.616, -19.717], [0, -22.834], [12.071, -16.146]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.92549020052, 0.505882382393, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, 0], "to": [44.333, 42.833], "ti": [-44.333, -42.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [266, 257], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [266, 257], "to": [-44.333, -42.833], "ti": [44.333, 42.833]}, {"t": 175, "s": [0, 0]}], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "eyeL", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [-29]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [-11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [11]}, {"t": 175, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [-76.156, -27.841, 0], "to": [2, -10.167, 0], "ti": [-2, 11.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [-64.156, -88.841, 0], "to": [2, -11.833, 0], "ti": [0, -1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [-64.156, -98.841, 0], "to": [0, 1, 0], "ti": [6.5, -9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [-64.156, -82.841, 0], "to": [-6.5, 9, 0], "ti": [7.333, -12.14, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [-103.156, -44.841, 0], "to": [-7.333, 12.14, 0], "ti": [0.333, 1.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-108.156, -10, 0], "to": [-0.333, -1.667, 0], "ti": [-2.333, 7.39, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-105.156, -54.841, 0], "to": [2.333, -7.39, 0], "ti": [-5.5, 6, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [-94.156, -54.341, 0], "to": [5.5, -6, 0], "ti": [-2, 4.14, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [-72.156, -90.841, 0], "to": [2, -4.14, 0], "ti": [0.507, -1.91, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [-82.156, -79.181, 0], "to": [-0.507, 1.91, 0], "ti": [-1.159, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [-75.2, -79.383, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [-75.2, -79.383, 0], "to": [-0.167, 8.597, 0], "ti": [0.167, -8.597, 0]}, {"t": 175, "s": [-76.2, -27.8, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [100, 99.676, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [160, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 56, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 74, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [95, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [100, 100, 100]}, {"t": 175, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[4.714, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.529, -1.982], [-1.481, -3.383], [0, -4.204], [3.089, -4.132]], "o": [[-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -3.153], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [3.143, 0], [2.529, 1.982], [1.481, 3.383], [0, 6.306], [-3.089, 4.132]], "v": [[0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-15.729, -8.888], [-12.071, -16.146], [-6.645, -21.04], [0, -22.834], [8.616, -19.717], [14.74, -11.525], [17.07, 0], [12.071, 16.146]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [{"i": [[1.949, 0], [1.243, 1.136], [-2.336, 3.228], [-11.458, 0.438], [2.3, 0.663], [0.178, -0.125], [1.961, 3.53], [-3.141, 2.204], [-21.328, -6.147], [-6.117, -12.272], [1.648, -2.391], [2.602, 0.771], [13.78, -19.039]], "o": [[-1.487, 0], [-2.873, -2.626], [10.335, -14.28], [-1.984, -1.321], [-15.574, -4.487], [-3.142, 2.203], [-1.961, -3.53], [0.925, -0.649], [10.759, 3.101], [1.315, 2.638], [-1.648, 2.391], [-0.25, -0.072], [-1.325, 1.831]], "v": [[232.7, 326.654], [228.474, 324.966], [227.502, 314.366], [263.807, 295.795], [257.386, 292.824], [223.598, 300.684], [214.36, 298.284], [216.496, 287.902], [260.718, 278.228], [286.151, 301.395], [285.603, 309.69], [278.593, 312.363], [237.907, 323.874]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [{"i": [[1.949, 0], [1.243, 1.136], [-2.336, 3.228], [-11.458, 0.438], [2.3, 0.663], [0.178, -0.125], [1.961, 3.53], [-3.141, 2.204], [-21.328, -6.147], [-6.117, -12.272], [1.648, -2.391], [2.602, 0.771], [13.78, -19.039]], "o": [[-1.487, 0], [-2.873, -2.626], [10.335, -14.28], [-1.984, -1.321], [-15.574, -4.487], [-3.142, 2.203], [-1.961, -3.53], [0.925, -0.649], [10.759, 3.101], [1.315, 2.638], [-1.648, 2.391], [-0.25, -0.072], [-1.325, 1.831]], "v": [[232.7, 326.654], [228.474, 324.966], [227.502, 314.366], [263.807, 295.795], [257.386, 292.824], [223.598, 300.684], [214.36, 298.284], [216.496, 287.902], [260.718, 278.228], [286.151, 301.395], [285.603, 309.69], [278.593, 312.363], [237.907, 323.874]], "c": true}]}, {"t": 175, "s": [{"i": [[4.714, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.529, -1.982], [-1.481, -3.383], [0, -4.204], [3.089, -4.132]], "o": [[-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -3.153], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [3.143, 0], [2.529, 1.982], [1.481, 3.383], [0, 6.306], [-3.089, 4.132]], "v": [[0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-15.729, -8.888], [-12.071, -16.146], [-6.645, -21.04], [0, -22.834], [8.616, -19.717], [14.74, -11.525], [17.07, 0], [12.071, 16.146]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [27.434, 4.94], "ix": 5}, "e": {"a": 0, "k": [-13.047, 2.957], "ix": 6}, "t": 1, "nm": "Gradient Fill 7777", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, 0], "to": [44.167, 42.667], "ti": [-44.167, -42.667]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [265, 256], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [265, 256], "to": [-44.167, -42.667], "ti": [44.167, 42.667]}, {"t": 175, "s": [0, 0]}], "ix": 1}, "s": {"a": 0, "k": [100, 95], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "eyeL-shadow", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [-29]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [-11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [11]}, {"t": 175, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [-76.156, -24.851, 0], "to": [1.993, -9.992, 0], "ti": [-1.993, 11.658, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [-64.2, -84.8, 0], "to": [1.993, -11.658, 0], "ti": [0, -1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [-64.2, -94.8, 0], "to": [0, 1, 0], "ti": [6.5, -9.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [-64.2, -78.8, 0], "to": [-6.5, 9.5, 0], "ti": [7.333, -12.967, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [-103.2, -37.8, 0], "to": [-7.333, 12.967, 0], "ti": [0.333, 1.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-108.2, -1, 0], "to": [-0.333, -1.833, 0], "ti": [-2.333, 7.967, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-105.2, -48.8, 0], "to": [2.333, -7.967, 0], "ti": [-5.5, 6.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [-94.2, -48.8, 0], "to": [5.5, -6.167, 0], "ti": [-2, 4.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [-72.2, -85.8, 0], "to": [2, -4.167, 0], "ti": [0.5, -2, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [-82.2, -73.8, 0], "to": [-0.5, 2, 0], "ti": [-1.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [-75.2, -73.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [-75.2, -73.8, 0], "to": [-0.167, 8.15, 0], "ti": [0.167, -8.15, 0]}, {"t": 175, "s": [-76.2, -24.9, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [100, 99.676, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [160, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 56, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 74, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [110, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [95, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [100, 100, 100]}, {"t": 175, "s": [100, 99.7, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, -6.306], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [2.357, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-1.481, 3.383], [-2.529, 1.982], [-3.143, 0], [-3.089, -4.132]], "o": [[0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -4.204], [1.481, -3.383], [2.529, -1.982], [4.714, 0], [3.089, 4.132]], "v": [[17.07, 0], [15.729, 8.888], [12.071, 16.146], [6.645, 21.04], [0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-14.74, -11.525], [-8.616, -19.717], [0, -22.834], [12.071, -16.146]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [{"i": [[1.949, 0], [1.243, 1.136], [-2.336, 3.228], [-11.458, 0.438], [2.3, 0.663], [0.178, -0.125], [1.961, 3.53], [-3.141, 2.204], [-21.328, -6.147], [-6.117, -12.272], [1.648, -2.391], [2.602, 0.771], [13.78, -19.039]], "o": [[-1.487, 0], [-2.873, -2.626], [10.335, -14.28], [-1.984, -1.321], [-15.574, -4.487], [-3.142, 2.203], [-1.961, -3.53], [0.925, -0.649], [10.759, 3.101], [1.315, 2.638], [-1.648, 2.391], [-0.25, -0.072], [-1.325, 1.831]], "v": [[232.785, 323.514], [228.558, 321.826], [227.587, 311.226], [263.892, 292.655], [257.471, 289.684], [223.683, 297.544], [214.444, 295.144], [216.581, 284.762], [260.802, 275.088], [286.236, 298.255], [285.687, 306.55], [278.677, 309.223], [237.991, 320.734]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [{"i": [[1.949, 0], [1.243, 1.136], [-2.336, 3.228], [-11.458, 0.438], [2.3, 0.663], [0.178, -0.125], [1.961, 3.53], [-3.141, 2.204], [-21.328, -6.147], [-6.117, -12.272], [1.648, -2.391], [2.602, 0.771], [13.78, -19.039]], "o": [[-1.487, 0], [-2.873, -2.626], [10.335, -14.28], [-1.984, -1.321], [-15.574, -4.487], [-3.142, 2.203], [-1.961, -3.53], [0.925, -0.649], [10.759, 3.101], [1.315, 2.638], [-1.648, 2.391], [-0.25, -0.072], [-1.325, 1.831]], "v": [[232.785, 323.514], [228.558, 321.826], [227.587, 311.226], [263.892, 292.655], [257.471, 289.684], [223.683, 297.544], [214.444, 295.144], [216.581, 284.762], [260.802, 275.088], [286.236, 298.255], [285.687, 306.55], [278.677, 309.223], [237.991, 320.734]], "c": true}]}, {"t": 175, "s": [{"i": [[0, -6.306], [0.864, -2.732], [1.545, -2.066], [2.042, -1.155], [2.357, 0], [2.042, 1.155], [1.544, 2.066], [0.864, 2.732], [0, 3.153], [-1.481, 3.383], [-2.529, 1.982], [-3.143, 0], [-3.089, -4.132]], "o": [[0, 3.153], [-0.864, 2.732], [-1.545, 2.066], [-2.042, 1.155], [-2.357, 0], [-2.042, -1.155], [-1.544, -2.066], [-0.864, -2.732], [0, -4.204], [1.481, -3.383], [2.529, -1.982], [4.714, 0], [3.089, 4.132]], "v": [[17.07, 0], [15.729, 8.888], [12.071, 16.146], [6.645, 21.04], [0, 22.834], [-6.645, 21.04], [-12.07, 16.146], [-15.729, 8.888], [-17.07, 0], [-14.74, -11.525], [-8.616, -19.717], [0, -22.834], [12.071, -16.146]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.925476074219, 0.505889892578, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, 0], "to": [44.167, 42.667], "ti": [-44.167, -42.667]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [265, 256], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [265, 256], "to": [-44.167, -42.667], "ti": [44.167, 42.667]}, {"t": 175, "s": [0, 0]}], "ix": 1}, "s": {"a": 0, "k": [100, 95], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "mouth", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 48, "s": [-5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [-20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [-18]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [-13]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [0]}, {"t": 175, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [-15.597, 50.077, 0], "to": [-4.833, -6.667, 0], "ti": [4.833, 6.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [-44.597, 10.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [-44.597, 10.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [-44.597, 36.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [-44.597, 68.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [-44.597, 57.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-27.597, 75.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-27.597, 43.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [-27.597, 43.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [-61.597, 37.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [-61.6, 47.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [-45.6, 47.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [-45.6, 47.077, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 175, "s": [-15.6, 50.1, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [100, 100.018, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [91, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [95, 110, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 48, "s": [95, 110, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 56, "s": [100, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 74, "s": [100, 133, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 85, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [100, 118, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [100, 100, 100]}, {"t": 175, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [9.405, -29.357], [-15.646, 1.191], [-15.488, -17.925], [0, 0]], "o": [[0, 0], [-5.223, 16.304], [0, 0], [16.036, 18.559], [0, 0]], "v": [[16.316, -31.907], [-24.678, -23.605], [3.795, 4.427], [-11.115, 32.672], [25.598, 24.425]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.882352948189, 0.105882354081, 0.105882354081, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "bo rder 22", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.906, 0.596, 0.5, 1, 0.873, 0.408, 1, 1, 0.839, 0.22], "ix": 9}}, "s": {"a": 0, "k": [-21.482, 13.575], "ix": 5}, "e": {"a": 0, "k": [21.8, 6.444], "ix": 6}, "t": 1, "nm": "jian bian3123", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -4.167], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [0, -25], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 4.167], "ti": [0, -4.167]}, {"t": 50, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 50, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 50, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "blow-3", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-116.877, 64.111, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [123.525, 370.498, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100.541, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[14.233, 8.002], [-2.356, 16.157], [-14.233, -8.002], [2.356, -16.157]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.347351074219, 0.686248779297, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0.347, 0.686, 0.812, 0.09, 0.27, 0.629, 0.994, 0.18, 0.192, 0.573], "ix": 9}}, "s": {"a": 0, "k": [-14.793, -0.763], "ix": 5}, "e": {"a": 0, "k": [13.672, -0.763], "ix": 6}, "t": 1, "nm": "Gradient Fill 3-1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [216.793, 314.763], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.919, 0.002], [-0.714, 7.344], [-5.826, 3.609], [0, 0], [-1.633, -4.925], [3.862, -2.199], [0, 0]], "o": [[-6.522, 0], [0.689, -7.091], [0, 0], [3.862, -2.199], [1.634, 4.923], [0, 0], [-1.089, 0.62]], "v": [[-92.775, 67.158], [-105.819, 55.158], [-94.08, 39.766], [95.314, -66.307], [105.266, -61.372], [101.23, -48.477], [-74.775, 59.332]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.347351074219, 0.686248779297, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [122.056, 372.604], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.349, 13.89], [4.328, -3.353], [7.354, -5.105], [-4.99, -4.266], [-4.321, 0.17], [0, 0], [0, 0]], "o": [[-0.422, 2.875], [-4.493, -0.811], [-13.38, 9.287], [3.484, 2.978], [-0.311, 4.211], [0, 0], [0.737, -5.774]], "v": [[18.352, -23.179], [9.732, -13.221], [-8.339, -7.42], [-15.612, 12.412], [-3.48, 16.546], [-4.114, 23.179], [18.114, 3.613]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-19.289, -0.21], "ix": 5}, "e": {"a": 0, "k": [18.839, -0.21], "ix": 6}, "t": 1, "nm": "Gradient Fill 3-3", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.289, 419.211], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.398, 16.478], [-1, 6.81], [0.737, -5.774]], "o": [[0, 0], [0, 0], [1.349, 13.89], [0, 0]], "v": [[-11.589, 23.179], [-10.455, -4.75], [10.877, -23.179], [10.639, 3.613]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [38.936, -15.242], "ix": 5}, "e": {"a": 0, "k": [77.388, -32.3], "ix": 6}, "t": 1, "nm": "Gradient Fill 3-4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [80.7, 397.72], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.882, 15.63], [-1.46, 6.548], [1.133, -5.544]], "o": [[0, 0], [0, 0], [0.211, 13.098], [0, 0]], "v": [[-12.344, 22.884], [-9.149, -3.745], [12.321, -22.884], [10.057, 2.596]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [51.54, 5.687], "ix": 5}, "e": {"a": 0, "k": [89.762, -11.882], "ix": 6}, "t": 1, "nm": "Gradient Fill 3-5", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [117.018, 376.167], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.882, 15.63], [-1.46, 6.548], [1.133, -5.544]], "o": [[0, 0], [0, 0], [0.211, 13.098], [0, 0]], "v": [[-12.344, 22.884], [-9.149, -3.745], [12.321, -22.884], [10.057, 2.596]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [68.708, 28.357], "ix": 5}, "e": {"a": 0, "k": [106.93, 10.788], "ix": 6}, "t": 1, "nm": "Gradient Fill 3-6", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [151.214, 355.911], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.648, -10.918], [0, 0], [4.446, 11.09]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.273, -6.526], [-10.792, 19.052], [10.792, 1.739], [4.494, -19.052]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-1.9, 11.665], "ix": 5}, "e": {"a": 0, "k": [69.632, -83.155], "ix": 6}, "t": 1, "nm": "Gradient Fill 3-7", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [186.368, 339.155], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.919, 0.002], [-0.714, 7.344], [-5.826, 3.609], [0, 0], [-1.633, -4.925], [3.862, -2.199], [0, 0]], "o": [[-6.522, 0], [0.689, -7.091], [0, 0], [3.862, -2.199], [1.634, 4.923], [0, 0], [-1.089, 0.62]], "v": [[-92.775, 67.158], [-105.819, 55.158], [-94.08, 39.766], [95.314, -66.307], [105.266, -61.372], [101.23, -48.477], [-74.775, 59.332]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 0.722, 0.953, 0.696, 0.137, 0.524, 0.81, 0.994, 0, 0.325, 0.667], "ix": 9}}, "s": {"a": 0, "k": [-105.893, -0.066], "ix": 5}, "e": {"a": 0, "k": [105.842, -0.066], "ix": 6}, "t": 1, "nm": "Gradient Fill 3-9", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [121.893, 372.066], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0]], "o": [[0, 0]], "v": [[0, 0]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 10, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0.347, 0.686, 0.812, 0.09, 0.27, 0.629, 0.994, 0.18, 0.192, 0.573], "ix": 8}}, "s": {"a": 0, "k": [-0.995, -0.428], "ix": 4}, "e": {"a": 0, "k": [-0.995, -0.428], "ix": 5}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "Gradient Stroke 3-9", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [72.995, 343.428], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [123.525, 370.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [131.525, 371.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [131.525, 358.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [142.525, 361.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [131.786, 332.628], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [158.525, 369.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [158.525, 369.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [150, 304.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [150, 279.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [140, 337.498], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [140, 308.498], "to": [0, 0], "ti": [0, 0]}, {"t": 146, "s": [143, 337.498]}], "ix": 2}, "a": {"a": 0, "k": [123.525, 370.498], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 50, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 61, "s": [100, 82]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 77, "s": [83, 84]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 100, "s": [68, 98]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 104, "s": [68, 98]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 110, "s": [68, 88]}, {"t": 115, "s": [68, 88]}], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [25]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [46]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [62]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [21]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [44]}, {"t": 146, "s": [22]}], "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [100]}, {"t": 171, "s": [0]}], "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"t": 61, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"t": 61, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "blow-2", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-88.13, 23.887, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [152.272, 329.968, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 99.982, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.585, 1.19], [0, 0], [2.48, -1.219], [0, 0], [0.428, -1.255], [-0.585, -1.19], [0, 0], [-1.846, 0], [-0.709, 0.349], [0, 0], [-0.428, 1.255]], "o": [[0, 0], [-1.218, -2.478], [0, 0], [-1.19, 0.585], [-0.428, 1.256], [0, 0], [0.869, 1.769], [0.74, 0], [0, 0], [1.19, -0.585], [0.428, -1.256]], "v": [[18.719, 5.796], [6.843, -18.363], [0.15, -20.644], [-16.439, -12.489], [-18.966, -9.615], [-18.72, -5.795], [-6.843, 18.363], [-2.352, 21.158], [-0.15, 20.644], [16.439, 12.489], [18.966, 9.615]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0.347, 0.686, 0.812, 0.09, 0.27, 0.629, 0.994, 0.18, 0.192, 0.573], "ix": 9}}, "s": {"a": 0, "k": [-19.835, -0.269], "ix": 5}, "e": {"a": 0, "k": [18.63, -0.269], "ix": 6}, "t": 1, "nm": "Gradient Fill 2-1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [215.835, 314.269], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.014, 0.007], [0.014, -0.007]], "o": [[-0.014, 0.006], [0.014, -0.007]], "v": [[-31.413, 24.189], [-31.454, 24.208]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[14.087, 0.661], [0, 0], [2.273, 14.389], [-1.451, 1.677], [-5.931, 0.312], [-10.342, -14.259], [2.183, -9.312], [11.475, 0], [-3.057, 11.011], [0.418, 6.962], [6.018, 0.687], [0.007, -0.001], [6.067, -7.012], [-0.86, -9.359], [-4.993, -3.502], [-0.537, 0.067], [-4.103, 1.226], [-0.004, 0.002], [0, 0], [-1.466, -4.339], [3.481, -1.651], [0, 0], [0, 0], [6.012, 0]], "o": [[-14.087, -0.661], [-2.676, -2.326], [-2.932, -18.561], [9.331, -10.784], [4.422, -0.255], [5.412, 7.461], [-0.945, 4.033], [-4.142, 0], [1.196, -4.309], [-0.282, -4.702], [-0.007, -0.001], [-2.753, 0.264], [-4.717, 5.452], [0.806, 8.775], [2.198, 1.542], [5.881, -0.731], [0.004, -0.001], [0, 0], [4.013, -1.9], [1.233, 3.65], [0, 0], [0, 0], [-5.904, 2.78], [0, 0]], "v": [[-42.626, 40.481], [-65.321, 32.924], [-79.43, 7.678], [-70.023, -25.832], [-41.078, -40.368], [-9.594, -27.318], [-2.321, 0], [-19.938, 12.518], [-27.756, 0], [-26.191, -11.293], [-36.831, -22.641], [-40.226, -23.424], [-55.961, -15.598], [-60.357, 4.75], [-48.541, 23.752], [-40.91, 25.913], [-30.926, 23.956], [-30.915, 23.952], [69.225, -23.556], [79.556, -19.151], [75.368, -9.868], [-24.907, 37.704], [-24.909, 37.7], [-41.724, 40.481]], "c": false}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 10, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0.347, 0.686, 0.812, 0.09, 0.27, 0.629, 0.994, 0.18, 0.192, 0.573], "ix": 8}}, "s": {"a": 0, "k": [-85.627, -0.017], "ix": 4}, "e": {"a": 0, "k": [84.23, -0.017], "ix": 5}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "Gradient Fill 2-2", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [149.627, 330.017], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.652, -8.254], [0, 0], [3.857, 9.011]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.55, -5.662], [-12.255, 13.915], [12.255, 2.77], [7.204, -13.915]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-12.575, -0.381], "ix": 5}, "e": {"a": 0, "k": [11.934, -0.381], "ix": 6}, "t": 1, "nm": "Gradient Fill 2-3", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [184.575, 331.381], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.343, 12.276], [-4.596, 5.861], [6.627, -14.561]], "o": [[0, 0], [0, 0], [-0.638, 11.018], [0, 0]], "v": [[-20.036, 20.23], [-8.861, -3.639], [20.036, -20.23], [14.572, 3.425]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [57.594, 22.315], "ix": 5}, "e": {"a": 0, "k": [104.263, 16.441], "ix": 6}, "t": 1, "nm": "Gradient Fill 2-4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [138.128, 351.611], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [11.11, 18.417], [-10.488, -2.491], [0, 0]], "o": [[0, 0], [0, 0], [18.28, 4.341], [0, 0]], "v": [[0.26, -6.013], [-20.486, -13.619], [1.103, 13.002], [20.486, -2.627]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [127.375, -79.385], "ix": 5}, "e": {"a": 0, "k": [131.085, -101.9], "ix": 6}, "t": 1, "nm": "Gradient Fill 2-5", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [89.963, 354.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-20.548, 1.876], [4.865, -13.725], [0, 0]], "o": [[0, 0], [0, 0], [-5.449, 15.372], [0, 0]], "v": [[7.222, -0.067], [19.78, -19.523], [-18.722, -1.083], [-6.795, 19.523]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [160.409, -68.4], "ix": 5}, "e": {"a": 0, "k": [164.078, -100.489], "ix": 6}, "t": 1, "nm": "Gradient Fill 2-6", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [94.854, 307.618], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.626, 22.844], [-8.475, -1.096], [1.924, -14.811]], "o": [[0.234, -8.537], [6.131, 0.793], [-3.339, 25.705]], "v": [[-12.224, -4.263], [2.519, -17.674], [11.825, 2.457]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-168.7, -73.811], "ix": 5}, "e": {"a": 0, "k": [-149.829, -62.99], "ix": 6}, "t": 1, "nm": "Gradient Fill 2-7", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [133.253, 323.993], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-20.162, -3.353], [6.816, -14.942], [2.088, 8.953], [12.726, 0.522], [-0.165, -13.255], [-24.164, 3.917], [0, 0], [-9.71, -2.983], [30.783, -15.055], [0, 0], [0.631, 39.319]], "o": [[0, 0], [20.162, 3.353], [-6.816, 14.942], [-2.088, -8.953], [-12.726, -0.522], [0.165, 13.255], [24.164, -3.917], [0, 0], [9.71, 2.983], [-30.783, 15.055], [0, 0], [-0.631, -39.319]], "v": [[-73.296, -23.003], [-38.735, -39.374], [-1.904, -1.325], [-29.268, 10.497], [-32.717, -20.369], [-62.261, -6.077], [-36.998, 25.287], [41.667, -8.629], [71.63, -23.091], [60.731, 1.099], [-26.813, 38.799], [-81.334, 8.777]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 0.722, 0.953, 0.696, 0.137, 0.524, 0.81, 0.994, 0, 0.325, 0.667], "ix": 9}}, "s": {"a": 0, "k": [-81.531, -0.919], "ix": 5}, "e": {"a": 0, "k": [81.202, -0.919], "ix": 6}, "t": 1, "nm": "Gradient Fill 2-8", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [151.531, 328.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [152.272, 307.968], "to": [0.455, 1.672], "ti": [-0.455, -2.505]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [155, 318], "to": [0.455, 2.505], "ti": [0.455, -1.495]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [155, 323], "to": [-0.455, 1.495], "ti": [0.455, -0.661]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [152.272, 326.968], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [152.272, 326.968], "to": [1, 0.333], "ti": [-1, -0.333]}, {"t": 80, "s": [158.272, 328.968]}], "ix": 2}, "a": {"a": 0, "k": [152.272, 329.968], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 74, "s": [100, 100]}, {"t": 80, "s": [100, 87]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [100]}, {"t": 175, "s": [0]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "blow-1", "parent": 13, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [0]}, {"t": 175, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-73.396, 15.459, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [167.007, 321.539, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 99.982, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.585, 1.19], [0, 0], [2.48, -1.219], [0, 0], [0.428, -1.255], [-0.585, -1.19], [0, 0], [-1.846, 0], [-0.709, 0.349], [0, 0], [-0.428, 1.255]], "o": [[0, 0], [-1.218, -2.478], [0, 0], [-1.19, 0.585], [-0.428, 1.256], [0, 0], [0.869, 1.769], [0.74, 0], [0, 0], [1.19, -0.585], [0.428, -1.256]], "v": [[18.719, 5.796], [6.843, -18.363], [0.15, -20.644], [-16.439, -12.489], [-18.966, -9.615], [-18.72, -5.795], [-6.843, 18.363], [-2.352, 21.158], [-0.15, 20.644], [16.439, 12.489], [18.966, 9.615]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0.347, 0.686, 0.812, 0.09, 0.27, 0.629, 0.994, 0.18, 0.192, 0.573], "ix": 9}}, "s": {"a": 0, "k": [-19.835, -0.202], "ix": 5}, "e": {"a": 0, "k": [18.63, -0.202], "ix": 6}, "t": 1, "nm": "colr 2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [215.835, 314.202], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.8, -0.883], [5.918, -3.127], [6.077, -2.027], [4.535, -0.747], [2.944, 0.222], [6.335, 11.453], [-4.648, 9.797], [-14.552, 2.64], [-1.6, 0], [-2.596, -7.524], [8.499, -4.479], [4.893, 2.44], [0.042, 6.148], [-2.753, 0.453], [-0.396, -2.402], [0.056, -0.432], [-0.969, -1.082], [-1.445, 0.069], [-0.623, 0.452], [-0.546, 1.773], [0.673, 1.266], [8.063, -0.604], [0.353, -0.064], [2.736, -5.767], [-5.854, -10.583], [-12.558, 4.189], [-12.726, 6.72], [-12.021, 5.897], [-0.919, -0.314], [-0.428, -0.872]], "o": [[-1.964, 0.964], [-14.51, 7.668], [-2.337, 0.778], [-3.347, 0.552], [-6.751, -0.513], [-5.806, -10.497], [2.53, -5.331], [1.743, -0.318], [11.085, 0], [3.203, 9.283], [-5.902, 3.112], [-4.194, -2.094], [0.031, -2.442], [2.403, -0.397], [0.069, 0.421], [-0.186, 1.441], [0.97, 1.082], [1.701, -0.03], [3.27, -1.848], [1.148, -3.731], [-2.492, -4.687], [-0.345, -0.008], [-7.742, 1.406], [-1.833, 3.862], [8.514, 15.391], [3.327, -1.109], [9.646, -5.094], [0.872, -0.427], [0.919, 0.314], [0.883, 1.8]], "v": [[52.921, -2.22], [40.363, 4.341], [-4.508, 26.871], [-15.292, 29.701], [-24.773, 30.196], [-50.131, 15.66], [-51.876, -14.924], [-28.725, -29.804], [-23.71, -30.272], [-2.455, -15.598], [-11.45, 7.776], [-27.058, 8.744], [-33.185, -3.216], [-28.048, -8.559], [-22.972, -4.921], [-22.951, -3.636], [-21.717, 0.338], [-17.902, 2.001], [-14.326, 0.744], [-9.172, -5.344], [-10.139, -13.689], [-26.383, -22.727], [-27.434, -22.648], [-43.683, -11.526], [-41.861, 10.978], [-6.805, 19.969], [16.045, 8.581], [49.718, -8.75], [52.495, -8.925], [54.584, -7.086]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.604, 3.271], [3.447, 1.179], [3.269, -1.604], [9.705, -5.125], [1.575, -0.829], [2.519, 7.301], [19.61, -3.579], [3.534, -7.446], [-7.35, -13.286], [-15.127, -1.148], [-1.136, 0], [-2.923, 0.481], [-2.625, 0.875], [-20.827, 11.006], [-1.829, 0.896], [-1.178, 3.446]], "o": [[-1.604, -3.271], [-3.448, -1.179], [-12.154, 5.963], [-1.672, 0.883], [2.705, -6.428], [-4.32, -12.521], [-18.719, 3.396], [-6.022, 12.692], [6.38, 11.534], [1.096, 0.084], [2.742, 0], [5.318, -0.877], [6.329, -2.111], [5.844, -3.089], [3.27, -1.604], [1.178, -3.446]], "v": [[63.562, -11.49], [55.729, -18.388], [45.313, -17.727], [11.375, -0.261], [6.494, 2.314], [6.998, -18.86], [-30.516, -39.643], [-60.91, -19.212], [-58.882, 20.499], [-25.531, 40.166], [-22.182, 40.291], [-13.665, 39.568], [-1.34, 36.355], [45.036, 13.183], [57.325, 6.758], [64.223, -1.074]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0.347, 0.686, 0.812, 0.09, 0.27, 0.629, 0.994, 0.18, 0.192, 0.573], "ix": 9}}, "s": {"a": 0, "k": [-65.909, -0.539], "ix": 5}, "e": {"a": 0, "k": [64.017, -0.539], "ix": 6}, "t": 1, "nm": "Gradient Fill 2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [163.909, 321.539], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.652, -8.254], [0, 0], [3.857, 9.011]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.55, -5.662], [-12.255, 13.915], [12.255, 2.77], [7.204, -13.915]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-12.85, -0.939], "ix": 5}, "e": {"a": 0, "k": [11.659, -0.939], "ix": 6}, "t": 1, "nm": "Gradient Fill 3222", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [178.85, 334.939], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.84, 10.165], [-5.944, 2.15], [17.306, 1.672]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-20.131, -6.419], [-1.561, -10.775], [20.131, -6.623], [0.768, 10.671]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-21.008, -0.306], "ix": 5}, "e": {"a": 0, "k": [19.254, -0.306], "ix": 6}, "t": 1, "nm": "Gradient Fill 4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [131.008, 344.306], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-13.302, 12.266], [0.492, -9.592], [0, 0]], "o": [[0, 0], [0, 0], [-0.858, 16.717], [0, 0]], "v": [[6.084, 0.487], [9.228, -18.843], [-9.195, 3.769], [6.284, 18.795]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-9.273, -0.33], "ix": 5}, "e": {"a": 0, "k": [9.182, -0.33], "ix": 6}, "t": 1, "nm": "Gradient Fill 5", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [115.273, 309.33], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.407, -14.606], [11.155, 2.592], [0, 0]], "o": [[0, 0], [0, 0], [-12.493, -2.903], [0, 0]], "v": [[0.085, 5.679], [16.149, 13.412], [-0.256, -12.983], [-16.149, -3.09]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-16.552, -0.507], "ix": 5}, "e": {"a": 0, "k": [15.746, -0.507], "ix": 6}, "t": 1, "nm": "Gradient Fill 6", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [150.552, 302.507], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[12.655, 10.909], [-3.183, 5.371], [-7.745, -8.125]], "o": [[-4.729, -4.077], [2.303, -3.886], [13.441, 14.101]], "v": [[-6.125, 7.475], [-9.2, -8.892], [4.937, -6.112]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-11.422, -0.539], "ix": 5}, "e": {"a": 0, "k": [10.331, -0.539], "ix": 6}, "t": 1, "nm": "col2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [140.422, 321.539], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.109, 4.299], [4.298, -2.11], [5.929, -1.978], [6.372, 11.518], [-2.026, 4.27], [-5.222, 0.948], [-0.098, 0.016], [-1.51, -2.84], [4.771, -2.515], [0.435, -0.008], [0.154, 0.932], [5.152, -0.849], [0.064, -5.049], [-7.767, -3.875], [-8.028, 4.23], [4.042, 11.716], [16.798, -3.069], [3.044, -6.415], [-8.896, -16.084], [-11.893, -0.903], [-3.295, 0.543], [-2.512, 0.838], [-6.852, 3.362]], "o": [[-2.109, -4.299], [-25.478, 12.499], [-12.927, 4.312], [-4.163, -7.527], [2.361, -4.976], [0.099, -0.018], [7.036, -1.159], [0.68, 1.28], [-0.094, 0.05], [0.114, -0.891], [-0.849, -5.152], [-5.015, 0.826], [0.035, 5.221], [5.792, 2.89], [10.775, -5.68], [-3.655, -10.593], [-16.698, 3.029], [-3.174, 6.688], [6.135, 11.092], [3.768, 0.286], [4.949, -0.815], [8.985, -2.997], [4.299, -2.109]], "v": [[59.32, -9.33], [47.717, -13.295], [-8.405, 15.281], [-37.62, 8.586], [-39.306, -9.424], [-26.631, -17.803], [-27.337, -17.665], [-14.597, -11.392], [-17.08, -3.497], [-18.048, -3.015], [-18.095, -5.76], [-28.961, -13.551], [-38.32, -3.298], [-29.392, 13.264], [-9.14, 12.243], [2.295, -17.303], [-29.726, -34.865], [-56.601, -17.14], [-54.706, 18.146], [-25.236, 35.314], [-14.521, 34.765], [-2.919, 31.73], [55.355, 2.273]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 0.722, 0.953, 0.696, 0.137, 0.524, 0.81, 0.994, 0, 0.325, 0.667], "ix": 9}}, "s": {"a": 0, "k": [-60.895, -0.694], "ix": 5}, "e": {"a": 0, "k": [59.521, -0.694], "ix": 6}, "t": 1, "nm": "Gradient Fill 8", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [163.895, 320.694], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [173.007, 317.539], "to": [-0.001, -4.5], "ti": [0.001, 4.5]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [173, 290.539], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [173, 290.539], "to": [-0.999, 5.16], "ti": [0.999, -5.16]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [167.007, 321.5], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [167.007, 321.5], "to": [0.999, -0.667], "ti": [-0.999, 0.667]}, {"t": 175, "s": [173, 317.5]}], "ix": 2}, "a": {"a": 0, "k": [167.007, 321.539], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 50, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 50, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "hat-line", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [118.394, -114.984, 0], "to": [0, 1.5, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [118.394, -105.984, 0], "to": [0, -0.003, 0], "ti": [0, 2.503, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [118.394, -115, 0], "to": [0, -2.503, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [118.394, -121, 0], "to": [0, 0, 0], "ti": [0, -1, 0]}, {"t": 84, "s": [118.394, -115, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 95, 100]}, {"t": 66, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-90.691, -36.012], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[52.539, 81.461], [81.82, -81.461], [-81.82, -56.935]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.347351402044, 0.686274528503, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 0], "ti": [0, 0]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "blue-line-1", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [107.446, -104.391, 0], "to": [0, 1.167, 0], "ti": [0, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [107.446, -97.391, 0], "to": [0, -0.001, 0], "ti": [0, 2.668, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [107.446, -104.4, 0], "to": [0, -2.668, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [107.446, -113.4, 0], "to": [0, 0, 0], "ti": [0, -1.5, 0]}, {"t": 84, "s": [107.446, -104.4, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 95, 100]}, {"t": 66, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-55.42, -6.485], [0, 0], [51.269, 110.82]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-36.403, -72.478], [70.872, 27.645], [62.472, 72.478], [-70.872, -67.528]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 0.722, 0.953, 0.696, 0.137, 0.524, 0.81, 0.994, 0, 0.325, 0.667], "ix": 9}}, "s": {"a": 0, "k": [-71.446, -0.609], "ix": 5}, "e": {"a": 0, "k": [70.299, -0.609], "ix": 6}, "t": 1, "nm": "Gradient Fill 18", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "blue-line-2", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [136.508, -137.591, 0], "to": [0, 2, 0], "ti": [0, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [136.508, -125.591, 0], "to": [0, -0.002, 0], "ti": [0, 3.502, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [136.508, -137.6, 0], "to": [0, -3.502, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [136.508, -146.6, 0], "to": [0, 0, 0], "ti": [0, -1.5, 0]}, {"t": 84, "s": [136.508, -137.6, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 95, 100]}, {"t": 66, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-34.084, -4.171], [0, 0], [47.418, 80.687]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-20.521, -46.231], [51.06, 12.507], [45.1, 46.231], [-51.06, -41.654]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 0.722, 0.953, 0.696, 0.137, 0.524, 0.81, 0.994, 0, 0.325, 0.667], "ix": 9}}, "s": {"a": 0, "k": [-51.508, -0.409], "ix": 5}, "e": {"a": 0, "k": [50.612, -0.409], "ix": 6}, "t": 1, "nm": "Gradient Fill 19", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "blue-line-3", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -23, "s": [161.004, -162.118, 0], "to": [0, 2, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -14, "s": [161.004, -150.118, 0], "to": [0, 0.003, 0], "ti": [0, 1.997, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -6, "s": [161.004, -162.1, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [161.004, -162.1, 0], "to": [0, 2, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [161.004, -150.1, 0], "to": [0, 0, 0], "ti": [0, 3.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [161.004, -162.1, 0], "to": [0, -3.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [161.004, -169.1, 0], "to": [0, 0, 0], "ti": [0, -1.167, 0]}, {"t": 84, "s": [161.004, -162.1, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": -23, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": -14, "s": [100, 95, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": -6, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 95, 100]}, {"t": 66, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-22.296, -2.276], [0, 0], [25.825, 41.865]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.861, -27.422], [32.483, 3.103], [28.112, 27.422], [-32.483, -23.582]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.275, 0.722, 0.953, 0.696, 0.137, 0.524, 0.81, 0.994, 0, 0.326, 0.667], "ix": 9}}, "s": {"a": 0, "k": [-33.004, -0.882], "ix": 5}, "e": {"a": 0, "k": [31.962, -0.882], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -52, "s": [0, 0], "to": [0, -4.167], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -42, "s": [0, -25], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -32, "s": [0, -25], "to": [0, 4.167], "ti": [0, -4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -23, "s": [0, 0], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -52, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -42, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -32, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -52, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -42, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -32, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": -72, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "blue-line-4", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [181.608, -181.815, 0], "to": [0, 2.167, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [181.608, -168.815, 0], "to": [0, 0.003, 0], "ti": [0, 2.997, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [181.608, -181.8, 0], "to": [0, -2.997, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [181.608, -186.8, 0], "to": [0, 0, 0], "ti": [0, -0.833, 0]}, {"t": 84, "s": [181.608, -181.8, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 95, 100]}, {"t": 66, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.307, 20.262], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-18.606, -9.053], [18.606, -14.631], [13.627, 14.631]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.273, 0.721, 0.953, 0.7, 0.136, 0.523, 0.81, 1, 0, 0.325, 0.667], "ix": 9}}, "s": {"a": 0, "k": [-18.608, -0.185], "ix": 5}, "e": {"a": 0, "k": [18.603, -0.185], "ix": 6}, "t": 1, "nm": "Gradient Fill 21", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "hat-white", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [118.674, -114.984, 0], "to": [0, 2.333, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [118.674, -100.984, 0], "to": [0, -0.003, 0], "ti": [0, 2.336, 0]}, {"t": 66, "s": [118.674, -115, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 95, 100]}, {"t": 66, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-90.691, -36.012], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[52.539, 81.461], [81.82, -81.461], [-81.82, -56.935]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.812, 0.843, 0.892, 0.955, 0.994, 0.686, 0.784, 0.91], "ix": 9}}, "s": {"a": 0, "k": [-82.674, -0.016], "ix": 5}, "e": {"a": 0, "k": [80.966, -0.016], "ix": 6}, "t": 1, "nm": "Gradient Fill 22", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "face-line", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 424, 0], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 287, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 288, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 289, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 290, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 291, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 292, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 293, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 294, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 295, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 296, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 297, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 298, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 299, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 300, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 301, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 302, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 303, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 304, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 305, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 306, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 307, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 308, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 309, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 310, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 311, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 312, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 313, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 314, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 315, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 316, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 317, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 318, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 319, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 320, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 321, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 322, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 323, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 324, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 325, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 326, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 327, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 328, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 329, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 331, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 332, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 333, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 334, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 335, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 336, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 337, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 338, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 339, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 340, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 341, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 342, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 343, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 344, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 345, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 346, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 347, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 348, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 349, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 350, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 351, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 352, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 353, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 354, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 355, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 356, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 357, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 358, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 359, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 360, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 361, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 362, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 363, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 364, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 365, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 366, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 367, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 368, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 369, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 370, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 371, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 372, "s": [0, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 373, "s": [0, 168, 0]}], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 1, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 2, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 3, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 4, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 6, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 7, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 8, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 9, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 10, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 11, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 12, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 14, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 15, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 16, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 17, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 19, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 20, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 21, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 22, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 25, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 28, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 29, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 30, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 31, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 32, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 33, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 34, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 35, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 36, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 37, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 38, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 39, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 40, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 41, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 42, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 43, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 44, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 45, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 46, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 47, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1.114, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 48, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.083, 0]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 50, "s": [100, 98.636, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 51, "s": [100, 97.273, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 52, "s": [100, 95.909, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 53, "s": [100, 94.545, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 54, "s": [100, 93.182, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 55, "s": [100, 91.818, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 56, "s": [100, 90.455, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 57, "s": [100, 89.091, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 58, "s": [100, 87.727, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.933, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 59, "s": [100, 86.364, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.737, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.344, 0]}, "t": 60, "s": [100, 85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.782, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.122, 0]}, "t": 61, "s": [100, 85.266, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.135, 0]}, "t": 62, "s": [100, 85.84, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.144, 0]}, "t": 63, "s": [100, 86.765, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.15, 0]}, "t": 64, "s": [100, 88.042, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.155, 0]}, "t": 65, "s": [100, 89.639, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.159, 0]}, "t": 66, "s": [100, 91.496, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 67, "s": [100, 93.54, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.166, 0]}, "t": 68, "s": [100, 95.69, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.864, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.168, 0]}, "t": 69, "s": [100, 97.866, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.214, 0]}, "t": 70, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 71, "s": [100, 101.357, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 72, "s": [100, 102.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 73, "s": [100, 103.008, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 74, "s": [100, 103.254, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 75, "s": [100, 103.148, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 76, "s": [100, 102.754, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 77, "s": [100, 102.156, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 78, "s": [100, 101.441, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 79, "s": [100, 100.697, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 80, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 81, "s": [100, 99.41, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 82, "s": [100, 98.968, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 83, "s": [100, 98.693, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 84, "s": [100, 98.586, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 85, "s": [100, 98.632, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 86, "s": [100, 98.803, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 87, "s": [100, 99.063, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 88, "s": [100, 99.374, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 89, "s": [100, 99.697, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 90, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 91, "s": [100, 100.256, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 92, "s": [100, 100.449, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 93, "s": [100, 100.568, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 94, "s": [100, 100.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 95, "s": [100, 100.595, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 96, "s": [100, 100.52, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 97, "s": [100, 100.407, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 98, "s": [100, 100.272, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.537, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 99, "s": [100, 100.132, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.102, 0]}, "t": 100, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 101, "s": [100, 99.4, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 102, "s": [100, 98.8, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 103, "s": [100, 98.2, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 104, "s": [100, 97.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 105, "s": [100, 97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 106, "s": [100, 96.4, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 107, "s": [100, 95.8, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 108, "s": [100, 95.2, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.96, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 109, "s": [100, 94.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.797, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.076, 0]}, "t": 110, "s": [100, 94, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.805, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.142, 0]}, "t": 111, "s": [100, 94.314, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.145, 0]}, "t": 112, "s": [100, 94.764, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.818, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.15, 0]}, "t": 113, "s": [100, 95.368, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.823, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.154, 0]}, "t": 114, "s": [100, 96.127, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.756, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.157, 0]}, "t": 115, "s": [100, 97.027, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.126, 0]}, "t": 116, "s": [100, 98.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.169, 0]}, "t": 117, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.176, 0]}, "t": 118, "s": [100, 101.901, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.179, 0]}, "t": 119, "s": [100, 103.607, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.181, 0]}, "t": 120, "s": [100, 105.094, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 121, "s": [100, 106.359, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 122, "s": [100, 107.424, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.928, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.179, 0]}, "t": 123, "s": [100, 108.324, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1.126, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.528, 0]}, "t": 124, "s": [100, 109.107, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.912, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.05, 0]}, "t": 125, "s": [100, 109, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 2.603, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 1.725, 0]}, "t": 126, "s": [100, 109.268, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.745, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.079, 0]}, "t": 127, "s": [100, 109.282, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.795, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.124, 0]}, "t": 128, "s": [100, 109.006, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.14, 0]}, "t": 129, "s": [100, 108.44, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.149, 0]}, "t": 130, "s": [100, 107.611, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.156, 0]}, "t": 131, "s": [100, 106.566, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.161, 0]}, "t": 132, "s": [100, 105.367, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.165, 0]}, "t": 133, "s": [100, 104.081, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.169, 0]}, "t": 134, "s": [100, 102.773, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.895, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 135, "s": [100, 101.5, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.733, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.406, 0]}, "t": 136, "s": [100, 100.308, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.121, 0]}, "t": 137, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 138, "s": [100, 99.321, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 139, "s": [100, 98.812, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 140, "s": [100, 98.496, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 141, "s": [100, 98.373, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 142, "s": [100, 98.426, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 143, "s": [100, 98.623, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 144, "s": [100, 98.922, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 145, "s": [100, 99.28, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 146, "s": [100, 99.652, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 147, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 148, "s": [100, 100.295, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 149, "s": [100, 100.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 150, "s": [100, 100.654, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 151, "s": [100, 100.707, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 152, "s": [100, 100.684, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 153, "s": [100, 100.598, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 154, "s": [100, 100.468, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 155, "s": [100, 100.313, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 156, "s": [100, 100.151, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 157, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 158, "s": [100, 99.872, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 159, "s": [100, 99.776, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 160, "s": [100, 99.716, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 161, "s": [100, 99.693, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 162, "s": [100, 99.703, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 163, "s": [100, 99.74, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 164, "s": [100, 99.796, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 165, "s": [100, 99.864, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 166, "s": [100, 99.934, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 167, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 168, "s": [100, 100.056, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 169, "s": [100, 100.098, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 170, "s": [100, 100.123, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 171, "s": [100, 100.134, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 172, "s": [100, 100.129, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 173, "s": [100, 100.113, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 174, "s": [100, 100.088, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 175, "s": [100, 100.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 176, "s": [100, 100.029, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 177, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 178, "s": [100, 99.976, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 179, "s": [100, 99.958, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 180, "s": [100, 99.946, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 181, "s": [100, 99.942, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 182, "s": [100, 99.944, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 183, "s": [100, 99.951, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 184, "s": [100, 99.962, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 185, "s": [100, 99.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 186, "s": [100, 99.988, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 187, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 188, "s": [100, 100.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 189, "s": [100, 100.018, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 190, "s": [100, 100.023, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 191, "s": [100, 100.025, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 192, "s": [100, 100.024, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 193, "s": [100, 100.021, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 194, "s": [100, 100.017, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 195, "s": [100, 100.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 196, "s": [100, 100.005, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 197, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 198, "s": [100, 99.995, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 199, "s": [100, 99.992, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 200, "s": [100, 99.99, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 201, "s": [100, 99.989, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 202, "s": [100, 99.989, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 203, "s": [100, 99.991, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 204, "s": [100, 99.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 205, "s": [100, 99.995, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 206, "s": [100, 99.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 207, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 208, "s": [100, 100.002, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 209, "s": [100, 100.003, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 210, "s": [100, 100.004, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 211, "s": [100, 100.005, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 212, "s": [100, 100.005, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 213, "s": [100, 100.004, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 214, "s": [100, 100.003, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 215, "s": [100, 100.002, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 216, "s": [100, 100.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 217, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 218, "s": [100, 99.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 219, "s": [100, 99.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 220, "s": [100, 99.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 221, "s": [100, 99.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 222, "s": [100, 99.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 223, "s": [100, 99.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 224, "s": [100, 99.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 225, "s": [100, 99.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 226, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 227, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 228, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 229, "s": [100, 100.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 230, "s": [100, 100.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 231, "s": [100, 100.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 232, "s": [100, 100.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 233, "s": [100, 100.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 234, "s": [100, 100.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 235, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 236, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 237, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 238, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 239, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 240, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 241, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 242, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 243, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 244, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 245, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 246, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 247, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 248, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 249, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 250, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 251, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 252, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 253, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 254, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 255, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 256, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 257, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 258, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 259, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 260, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 261, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 262, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 263, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 264, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 265, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 266, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 267, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 268, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 269, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 270, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 271, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 272, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 273, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 274, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 275, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 276, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 277, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 278, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 279, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 280, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 281, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 282, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 283, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 284, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 285, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 286, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 287, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 288, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 289, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 290, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 291, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 292, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 293, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 294, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 295, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 296, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 297, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 298, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 299, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 300, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 301, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 302, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 303, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 304, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 305, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 306, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 307, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 308, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 309, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 310, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 311, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 312, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 313, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 314, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 315, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 316, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 317, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 318, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 319, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 320, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 321, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 322, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 323, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 324, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 325, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 326, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 327, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 328, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 329, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 330, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 331, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 332, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 333, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 334, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 335, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 336, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 337, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 338, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 339, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 340, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 341, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 342, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 343, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 344, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 345, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 346, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 347, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 348, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 349, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 350, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 351, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 352, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 353, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 354, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 355, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 356, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 357, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 358, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 359, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 360, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 361, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 362, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 363, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 364, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 365, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 366, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 367, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 368, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 369, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 370, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 371, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 372, "s": [100, 100, 100]}, {"t": 373, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-101.5, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [104.5, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -182]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"t": 83, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.913725495338, 0.482352942228, 0.0941176489, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, 0], "ti": [0, -6.529]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 5.042], "ti": [0, -1.816]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [30]}, {"t": 49, "s": [0]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "face-color-2", "parent": 23, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.719, -67.328, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-2.215, -59.393, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 100.262, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 95.249, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 66, "s": [100, 100.262, 100]}, {"t": 80, "s": [100, 100.262, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.215, -155.803]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-92.07, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [88.93, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.43, -164.803]], "c": true}]}, {"t": 82, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.215, -155.803]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.342, 1, 0.926, 0.57, 0.999, 1, 0.852, 0.14, 0, 1, 0.342, 0.5, 0.999, 0], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [-3, -156], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [-86, -133], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-3, -156], "to": [0, -1], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [-3, -162], "to": [0, 0], "ti": [0, -1]}, {"t": 165, "s": [-3, -156]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [-3, 36.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [-34, -61.18], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-3, 36.82], "to": [8.667, 2.333], "ti": [0, 0.003]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [49, 50.82], "to": [0, -0.003], "ti": [8.667, 2.337]}, {"t": 165, "s": [-3, 36.8]}], "ix": 6}, "t": 1, "nm": "<PERSON>radient Fill 444", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [0, 0], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [30]}, {"t": 49, "s": [0]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "face-color-1", "parent": 23, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [-1.215, -22.578, 0], "to": [2.667, -1.671, 0], "ti": [-2.667, 1.671, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [14.785, -32.604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [14.785, -32.604, 0], "to": [-2.664, 1.667, 0], "ti": [2.664, -1.667, 0]}, {"t": 175, "s": [-1.2, -22.6, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-2.215, -14.081, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 82, "s": [100, 100.262, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [105.984, 106.262, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 160, "s": [105.984, 106.262, 100]}, {"t": 175, "s": [100, 100.262, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.215, -154.514]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[-3.844, -71.384], [83.615, 0], [-1.993, 83.591], [-83.615, 0]], "o": [[4.496, 83.494], [-83.615, 0], [1.452, -60.884], [83.615, 0]], "v": [[148.688, -24.616], [-2.43, 103.353], [-154.107, -24.616], [-2.36, -165.014]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-71.07, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [74.86, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.36, -139.264]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-71.07, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [74.86, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.36, -139.264]], "c": true}]}, {"t": 175, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.215, -154.514]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 0.64, 0.342, 1, 0.926, 0.371, 0.999, 1, 0.852, 0.102, 0, 1, 0.342, 0.5, 0.999, 0], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [-3, -155], "to": [-47.833, 34.667], "ti": [47.833, -34.667]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [-290, 53], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [-290, 53], "to": [47.833, -34.667], "ti": [-47.833, 34.667]}, {"t": 175, "s": [-3, -155]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [-3, 125.867], "to": [-48.5, -4.333], "ti": [48.5, 4.333]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [-294, 99.867], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [-294, 99.867], "to": [48.5, 4.339], "ti": [-48.5, -4.339]}, {"t": 175, "s": [-3, 125.9]}], "ix": 6}, "t": 1, "nm": "Gradient Fill 50", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [0, 0], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [30]}, {"t": 49, "s": [0]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "face-color", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 424, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 1, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 2, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 3, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 4, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 6, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 7, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 8, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 9, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 10, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 11, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 12, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 14, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 15, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 16, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 17, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 19, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 20, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 21, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 22, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 25, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 28, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 29, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 30, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 31, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 32, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 33, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 34, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 35, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 36, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 37, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 38, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 39, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 40, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 41, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 42, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 43, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 44, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 45, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 46, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 47, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1.114, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 48, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.083, 0]}, "t": 49, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 50, "s": [100, 98.636, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 51, "s": [100, 97.273, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 52, "s": [100, 95.909, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 53, "s": [100, 94.545, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 54, "s": [100, 93.182, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 55, "s": [100, 91.818, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 56, "s": [100, 90.455, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 57, "s": [100, 89.091, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 58, "s": [100, 87.727, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.933, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 59, "s": [100, 86.364, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.737, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.344, 0]}, "t": 60, "s": [100, 85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.782, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.122, 0]}, "t": 61, "s": [100, 85.266, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.135, 0]}, "t": 62, "s": [100, 85.84, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.144, 0]}, "t": 63, "s": [100, 86.765, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.15, 0]}, "t": 64, "s": [100, 88.042, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.155, 0]}, "t": 65, "s": [100, 89.639, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.159, 0]}, "t": 66, "s": [100, 91.496, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 67, "s": [100, 93.54, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.166, 0]}, "t": 68, "s": [100, 95.69, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.864, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.168, 0]}, "t": 69, "s": [100, 97.866, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.214, 0]}, "t": 70, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 71, "s": [100, 101.357, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 72, "s": [100, 102.376, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 73, "s": [100, 103.008, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 74, "s": [100, 103.254, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 75, "s": [100, 103.148, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 76, "s": [100, 102.754, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 77, "s": [100, 102.156, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 78, "s": [100, 101.441, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 79, "s": [100, 100.697, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 80, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 81, "s": [100, 99.41, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 82, "s": [100, 98.968, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 83, "s": [100, 98.693, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 84, "s": [100, 98.586, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 85, "s": [100, 98.632, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 86, "s": [100, 98.803, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 87, "s": [100, 99.063, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 88, "s": [100, 99.374, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 89, "s": [100, 99.697, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 90, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 91, "s": [100, 100.256, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 92, "s": [100, 100.449, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 93, "s": [100, 100.568, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 94, "s": [100, 100.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 95, "s": [100, 100.595, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 96, "s": [100, 100.52, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 97, "s": [100, 100.407, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 98, "s": [100, 100.272, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.537, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 99, "s": [100, 100.132, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.102, 0]}, "t": 100, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 101, "s": [100, 99.4, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 102, "s": [100, 98.8, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 103, "s": [100, 98.2, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 104, "s": [100, 97.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 105, "s": [100, 97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 106, "s": [100, 96.4, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 107, "s": [100, 95.8, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 108, "s": [100, 95.2, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.96, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 109, "s": [100, 94.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.797, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.076, 0]}, "t": 110, "s": [100, 94, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.805, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.142, 0]}, "t": 111, "s": [100, 94.314, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.145, 0]}, "t": 112, "s": [100, 94.764, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.818, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.15, 0]}, "t": 113, "s": [100, 95.368, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.823, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.154, 0]}, "t": 114, "s": [100, 96.127, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.756, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.157, 0]}, "t": 115, "s": [100, 97.027, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.126, 0]}, "t": 116, "s": [100, 98.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.169, 0]}, "t": 117, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.176, 0]}, "t": 118, "s": [100, 101.901, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.179, 0]}, "t": 119, "s": [100, 103.607, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.181, 0]}, "t": 120, "s": [100, 105.094, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 121, "s": [100, 106.359, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 122, "s": [100, 107.424, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.928, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.179, 0]}, "t": 123, "s": [100, 108.324, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1.126, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.528, 0]}, "t": 124, "s": [100, 109.107, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.912, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.05, 0]}, "t": 125, "s": [100, 109, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 2.603, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 1.725, 0]}, "t": 126, "s": [100, 109.268, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.745, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.079, 0]}, "t": 127, "s": [100, 109.282, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.795, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.124, 0]}, "t": 128, "s": [100, 109.006, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.14, 0]}, "t": 129, "s": [100, 108.44, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.149, 0]}, "t": 130, "s": [100, 107.611, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.156, 0]}, "t": 131, "s": [100, 106.566, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.161, 0]}, "t": 132, "s": [100, 105.367, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.165, 0]}, "t": 133, "s": [100, 104.081, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.169, 0]}, "t": 134, "s": [100, 102.773, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.895, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 135, "s": [100, 101.5, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.733, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.406, 0]}, "t": 136, "s": [100, 100.308, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.121, 0]}, "t": 137, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 138, "s": [100, 99.321, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 139, "s": [100, 98.812, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 140, "s": [100, 98.496, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 141, "s": [100, 98.373, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 142, "s": [100, 98.426, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 143, "s": [100, 98.623, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 144, "s": [100, 98.922, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 145, "s": [100, 99.28, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 146, "s": [100, 99.652, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 147, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 148, "s": [100, 100.295, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 149, "s": [100, 100.516, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 150, "s": [100, 100.654, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 151, "s": [100, 100.707, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 152, "s": [100, 100.684, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 153, "s": [100, 100.598, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 154, "s": [100, 100.468, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 155, "s": [100, 100.313, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 156, "s": [100, 100.151, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 157, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 158, "s": [100, 99.872, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 159, "s": [100, 99.776, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 160, "s": [100, 99.716, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 161, "s": [100, 99.693, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 162, "s": [100, 99.703, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 163, "s": [100, 99.74, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 164, "s": [100, 99.796, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 165, "s": [100, 99.864, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 166, "s": [100, 99.934, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 167, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.865, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 168, "s": [100, 100.056, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.884, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.217, 0]}, "t": 169, "s": [100, 100.098, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.953, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.298, 0]}, "t": 170, "s": [100, 100.123, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.608, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.109, 0]}, "t": 171, "s": [100, 100.134, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.79, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.106, 0]}, "t": 172, "s": [100, 100.129, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.138, 0]}, "t": 173, "s": [100, 100.113, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.153, 0]}, "t": 174, "s": [100, 100.088, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.163, 0]}, "t": 175, "s": [100, 100.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.846, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.172, 0]}, "t": 176, "s": [100, 100.029, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.182, 0]}, "t": 177, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.194, 0]}, "t": 178, "s": [100, 99.976, 100]}, {"t": 179, "s": [100, 99.958, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -180.5]], "c": true}]}, {"t": 83, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-1, -4], "to": [-2, 3], "ti": [9.167, 7.333]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [-13, 14], "to": [-9.167, -7.333], "ti": [2.5, 1.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [-56, -48], "to": [-2.5, -1.833], "ti": [-4.667, -8.5]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [-28, 3], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [-28, 3], "to": [4.5, -1.167], "ti": [-4.5, 1.167]}, {"t": 175, "s": [-1, -4]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-0.381, 160], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [-0.381, 160], "to": [-13.167, -2.333], "ti": [18.667, 3.5]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [-79.381, 146], "to": [-18.667, -3.5], "ti": [5.5, 1.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [-112.381, 139], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [-112.381, 139], "to": [18.663, 3.5], "ti": [-18.663, -3.5]}, {"t": 175, "s": [-0.4, 160]}], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 26", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [0, 0], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [0, -6.833], "ti": [0, 4.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [0, -41], "to": [0, -4.167], "ti": [0, -6.833]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0, -25], "to": [0, 6.833], "ti": [0, -4.167]}, {"t": 49, "s": [0, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [30]}, {"t": 49, "s": [0]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [5]}, {"t": 49, "s": [0]}], "ix": 4}, "sa": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [55]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [55]}, {"t": 49, "s": [0]}], "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "line5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 276, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.671, 58.226], [-24.07, -17.992], [-40.458, 54.409], [0, 0]], "o": [[0, 0], [1.986, -24.757], [104.627, 78.207], [40.458, -54.409], [0, 0]], "v": [[-152.763, 27.204], [-191.34, -102.172], [-134.627, -140.207], [-52.131, -195.777], [80, -268]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.928370098039, 0.229475776822, 0.589907537722, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"t": 100, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [75]}, {"t": 114, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "line5-shadow", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 283, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.671, 58.226], [-24.07, -17.992], [-40.458, 54.409], [0, 0]], "o": [[0, 0], [1.986, -24.757], [104.627, 78.207], [40.458, -54.409], [0, 0]], "v": [[-152.763, 27.204], [-191.34, -102.172], [-134.627, -140.207], [-52.131, -195.777], [80, -268]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.832977294922, 0.120483398438, 0.487945556641, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"t": 100, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [75]}, {"t": 114, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}], "markers": []}