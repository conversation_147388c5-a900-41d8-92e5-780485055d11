{"v": "5.7.8", "fr": 60, "ip": 0, "op": 270, "w": 512, "h": 512, "nm": "ked<PERSON><PERSON>", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "love7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 158.678, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159.734, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207.412, "s": [100]}, {"t": 208.470703125, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [17.28, 421.301, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [102.03, 21.301, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159.734, "s": [{"i": [[11.393, 1.749], [1.667, -1.54], [0.277, 0.256], [11.789, -1.7], [-12.955, -10.942], [-7.823, 0], [0, 0]], "o": [[-10.791, -1.656], [-0.277, 0.256], [-1.667, -1.54], [-12.954, 1.868], [0, 0], [7.143, 0], [16.515, -14.428]], "v": [[118.532, 5.723], [103.034, 12.928], [102.049, 12.928], [86.551, 5.723], [82.286, 38.627], [102.439, 49.782], [123.545, 37.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168.211, "s": [{"i": [[6.762, 5.438], [1.827, -1.688], [0.304, 0.281], [7.466, -3.809], [-8.395, -13.868], [-8.573, 0], [0, 0]], "o": [[-9.322, -7.497], [-0.304, 0.281], [-1.827, -1.688], [-9.068, 4.626], [0, 0], [7.827, 0], [9.803, -15.595]], "v": [[119.488, -15.688], [103.005, -14.043], [101.926, -14.043], [88.193, -17.563], [87.77, 14.868], [102.354, 26.343], [115.822, 16.595]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.689, "s": [{"i": [[8.604, 5.144], [3.274, 0.013], [1.26, -0.018], [3.404, -3.764], [-6.207, -16.137], [-9.323, 0], [0, 0]], "o": [[-3.448, -2.581], [-0.45, -0.002], [-3.928, 0.263], [-6.283, 7.298], [0, 0], [8.511, 0], [8.118, -16.274]], "v": [[114.573, -43.731], [104.07, -47.17], [101.303, -47.076], [88.658, -42.236], [88.082, -10.551], [102.268, 2.903], [116.632, -8.976]], "c": true}]}, {"t": 206.3515625, "s": [{"i": [[20.808, 3.194], [3.044, -2.813], [0.507, 0.468], [21.532, -3.105], [-23.661, -19.984], [-14.289, 0], [0, 0]], "o": [[-19.708, -3.025], [-0.507, 0.468], [-3.044, -2.813], [-23.661, 3.412], [0, 0], [13.046, 0], [30.163, -26.352]], "v": [[131.094, -232.855], [102.787, -219.695], [100.988, -219.695], [72.681, -232.855], [64.893, -172.758], [101.701, -152.383], [137.25, -171.711]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.109803922474, 0.1254902035, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.404, 0.435, 0.5, 1, 0.3, 0.331, 0.999, 1, 0.196, 0.227], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159.734, "s": [102, -4], "to": [0, -36.5], "ti": [0, 36.5]}, {"t": 207.412109375, "s": [102, -223]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159.734, "s": [102, 45.787], "to": [0, -34], "ti": [0, 34]}, {"t": 207.412109375, "s": [102, -158.213]}], "ix": 6}, "t": 1, "nm": "Gradient Fill 7", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [76.637, -108.871], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -38, "op": 535, "st": -38, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "love6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115.244, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116.303, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163.973, "s": [100]}, {"t": 165.03515625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [17.28, 421.301, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [102.03, 21.301, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116.303, "s": [{"i": [[11.393, 1.749], [1.667, -1.54], [0.277, 0.256], [11.789, -1.7], [-12.955, -10.942], [-7.823, 0], [0, 0]], "o": [[-10.791, -1.656], [-0.277, 0.256], [-1.667, -1.54], [-12.954, 1.868], [0, 0], [7.143, 0], [16.515, -14.428]], "v": [[118.532, 5.723], [103.034, 12.928], [102.049, 12.928], [86.551, 5.723], [82.286, 38.627], [102.439, 49.782], [123.545, 37.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124.775, "s": [{"i": [[6.762, 5.438], [1.827, -1.688], [0.304, 0.281], [7.466, -3.809], [-8.395, -13.868], [-8.573, 0], [0, 0]], "o": [[-9.322, -7.497], [-0.304, 0.281], [-1.827, -1.688], [-9.068, 4.626], [0, 0], [7.827, 0], [9.803, -15.595]], "v": [[119.488, -15.688], [103.005, -14.043], [101.926, -14.043], [88.193, -17.563], [87.77, 14.868], [102.354, 26.343], [115.822, 16.595]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133.25, "s": [{"i": [[8.604, 5.144], [3.274, 0.013], [1.26, -0.018], [3.404, -3.764], [-6.207, -16.137], [-9.323, 0], [0, 0]], "o": [[-3.448, -2.581], [-0.45, -0.002], [-3.928, 0.263], [-6.283, 7.298], [0, 0], [8.511, 0], [8.118, -16.274]], "v": [[114.573, -43.731], [104.07, -47.17], [101.303, -47.076], [88.658, -42.236], [88.082, -10.551], [102.268, 2.903], [116.632, -8.976]], "c": true}]}, {"t": 162.9140625, "s": [{"i": [[20.808, 3.194], [3.044, -2.813], [0.507, 0.468], [21.532, -3.105], [-23.661, -19.984], [-14.289, 0], [0, 0]], "o": [[-19.708, -3.025], [-0.507, 0.468], [-3.044, -2.813], [-23.661, 3.412], [0, 0], [13.046, 0], [30.163, -26.352]], "v": [[131.094, -232.855], [102.787, -219.695], [100.988, -219.695], [72.681, -232.855], [64.893, -172.758], [101.701, -152.383], [137.25, -171.711]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.109803922474, 0.1254902035, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.404, 0.435, 0.5, 1, 0.3, 0.331, 0.999, 1, 0.196, 0.227], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116.303, "s": [102, -4], "to": [0, -36.5], "ti": [0, 36.5]}, {"t": 163.97265625, "s": [102, -223]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116.303, "s": [102, 45.787], "to": [0, -34], "ti": [0, 34]}, {"t": 163.97265625, "s": [102, -158.213]}], "ix": 6}, "t": 1, "nm": "Gradient Fill 6", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [76.637, -108.871], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -38, "op": 535, "st": -38, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "love5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88.754, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89.814, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137.488, "s": [100]}, {"t": 138.544921875, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [17.28, 421.301, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [102.03, 21.301, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.814, "s": [{"i": [[11.393, 1.749], [1.667, -1.54], [0.277, 0.256], [11.789, -1.7], [-12.955, -10.942], [-7.823, 0], [0, 0]], "o": [[-10.791, -1.656], [-0.277, 0.256], [-1.667, -1.54], [-12.954, 1.868], [0, 0], [7.143, 0], [16.515, -14.428]], "v": [[118.532, 5.723], [103.034, 12.928], [102.049, 12.928], [86.551, 5.723], [82.286, 38.627], [102.439, 49.782], [123.545, 37.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98.289, "s": [{"i": [[6.762, 5.438], [1.827, -1.688], [0.304, 0.281], [7.466, -3.809], [-8.395, -13.868], [-8.573, 0], [0, 0]], "o": [[-9.322, -7.497], [-0.304, 0.281], [-1.827, -1.688], [-9.068, 4.626], [0, 0], [7.827, 0], [9.803, -15.595]], "v": [[119.488, -15.688], [103.005, -14.043], [101.926, -14.043], [88.193, -17.563], [87.77, 14.868], [102.354, 26.343], [115.822, 16.595]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106.764, "s": [{"i": [[8.604, 5.144], [3.274, 0.013], [1.26, -0.018], [3.404, -3.764], [-6.207, -16.137], [-9.323, 0], [0, 0]], "o": [[-3.448, -2.581], [-0.45, -0.002], [-3.928, 0.263], [-6.283, 7.298], [0, 0], [8.511, 0], [8.118, -16.274]], "v": [[114.573, -43.731], [104.07, -47.17], [101.303, -47.076], [88.658, -42.236], [88.082, -10.551], [102.268, 2.903], [116.632, -8.976]], "c": true}]}, {"t": 136.42578125, "s": [{"i": [[20.808, 3.194], [3.044, -2.813], [0.507, 0.468], [21.532, -3.105], [-23.661, -19.984], [-14.289, 0], [0, 0]], "o": [[-19.708, -3.025], [-0.507, 0.468], [-3.044, -2.813], [-23.661, 3.412], [0, 0], [13.046, 0], [30.163, -26.352]], "v": [[131.094, -232.855], [102.787, -219.695], [100.988, -219.695], [72.681, -232.855], [64.893, -172.758], [101.701, -152.383], [137.25, -171.711]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.109803922474, 0.1254902035, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.404, 0.435, 0.5, 1, 0.3, 0.331, 0.999, 1, 0.196, 0.227], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.814, "s": [102, -4], "to": [0, -36.5], "ti": [0, 36.5]}, {"t": 137.48828125, "s": [102, -223]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.814, "s": [102, 45.787], "to": [0, -34], "ti": [0, 34]}, {"t": 137.48828125, "s": [102, -158.213]}], "ix": 6}, "t": 1, "nm": "Gradient Fill 555", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [76.637, -108.871], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -38, "op": 535, "st": -38, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "love4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88.754, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89.814, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137.488, "s": [100]}, {"t": 138.544921875, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [17.28, 421.301, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [102.03, 21.301, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.814, "s": [{"i": [[11.393, 1.749], [1.667, -1.54], [0.277, 0.256], [11.789, -1.7], [-12.955, -10.942], [-7.823, 0], [0, 0]], "o": [[-10.791, -1.656], [-0.277, 0.256], [-1.667, -1.54], [-12.954, 1.868], [0, 0], [7.143, 0], [16.515, -14.428]], "v": [[118.532, 5.723], [103.034, 12.928], [102.049, 12.928], [86.551, 5.723], [82.286, 38.627], [102.439, 49.782], [123.545, 37.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98.289, "s": [{"i": [[6.762, 5.438], [1.827, -1.688], [0.304, 0.281], [7.466, -3.809], [-8.395, -13.868], [-8.573, 0], [0, 0]], "o": [[-9.322, -7.497], [-0.304, 0.281], [-1.827, -1.688], [-9.068, 4.626], [0, 0], [7.827, 0], [9.803, -15.595]], "v": [[119.488, -15.688], [103.005, -14.043], [101.926, -14.043], [88.193, -17.563], [87.77, 14.868], [102.354, 26.343], [115.822, 16.595]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106.764, "s": [{"i": [[8.604, 5.144], [3.274, 0.013], [1.26, -0.018], [3.404, -3.764], [-6.207, -16.137], [-9.323, 0], [0, 0]], "o": [[-3.448, -2.581], [-0.45, -0.002], [-3.928, 0.263], [-6.283, 7.298], [0, 0], [8.511, 0], [8.118, -16.274]], "v": [[114.573, -43.731], [104.07, -47.17], [101.303, -47.076], [88.658, -42.236], [88.082, -10.551], [102.268, 2.903], [116.632, -8.976]], "c": true}]}, {"t": 136.42578125, "s": [{"i": [[20.808, 3.194], [3.044, -2.813], [0.507, 0.468], [21.532, -3.105], [-23.661, -19.984], [-14.289, 0], [0, 0]], "o": [[-19.708, -3.025], [-0.507, 0.468], [-3.044, -2.813], [-23.661, 3.412], [0, 0], [13.046, 0], [30.163, -26.352]], "v": [[131.094, -232.855], [102.787, -219.695], [100.988, -219.695], [72.681, -232.855], [64.893, -172.758], [101.701, -152.383], [137.25, -171.711]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.109803922474, 0.1254902035, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.342, 1, 0.926, 0.57, 0.999, 1, 0.852, 0.14, 0, 1, 0.342, 0.5, 0.999, 0], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.814, "s": [102, -4], "to": [0, -36.5], "ti": [0, 36.5]}, {"t": 137.48828125, "s": [102, -223]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.814, "s": [102, 45.787], "to": [0, -34], "ti": [0, 34]}, {"t": 137.48828125, "s": [102, -158.213]}], "ix": 6}, "t": 1, "nm": "Gradient Fill 4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [76.637, -108.871], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -38, "op": 535, "st": -38, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "love3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171.391, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172.447, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220.123, "s": [100]}, {"t": 221.181640625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [358.28, 300.301, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [102.03, 21.301, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172.447, "s": [{"i": [[11.393, 1.749], [1.667, -1.54], [0.277, 0.256], [11.789, -1.7], [-12.955, -10.942], [-7.823, 0], [0, 0]], "o": [[-10.791, -1.656], [-0.277, 0.256], [-1.667, -1.54], [-12.954, 1.868], [0, 0], [7.143, 0], [16.515, -14.428]], "v": [[118.532, 5.723], [103.034, 12.928], [102.049, 12.928], [86.551, 5.723], [82.286, 38.627], [102.439, 49.782], [123.545, 37.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181.984, "s": [{"i": [[6.762, 5.438], [1.827, -1.688], [0.304, 0.281], [7.466, -3.809], [-8.395, -13.868], [-8.573, 0], [0, 0]], "o": [[-9.322, -7.497], [-0.304, 0.281], [-1.827, -1.688], [-9.068, 4.626], [0, 0], [7.827, 0], [9.803, -15.595]], "v": [[119.488, -15.688], [103.005, -14.043], [101.926, -14.043], [88.193, -17.563], [87.77, 14.868], [102.354, 26.343], [115.822, 16.595]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191.518, "s": [{"i": [[8.604, 5.144], [3.274, 0.013], [1.26, -0.018], [3.404, -3.764], [-6.207, -16.137], [-9.323, 0], [0, 0]], "o": [[-3.448, -2.581], [-0.45, -0.002], [-3.928, 0.263], [-6.283, 7.298], [0, 0], [8.511, 0], [8.118, -16.274]], "v": [[114.573, -43.731], [104.07, -47.17], [101.303, -47.076], [88.658, -42.236], [88.082, -10.551], [102.268, 2.903], [116.632, -8.976]], "c": true}]}, {"t": 219.064453125, "s": [{"i": [[20.808, 3.194], [3.044, -2.813], [0.507, 0.468], [21.532, -3.105], [-23.661, -19.984], [-14.289, 0], [0, 0]], "o": [[-19.708, -3.025], [-0.507, 0.468], [-3.044, -2.813], [-23.661, 3.412], [0, 0], [13.046, 0], [30.163, -26.352]], "v": [[131.094, -232.855], [102.787, -219.695], [100.988, -219.695], [72.681, -232.855], [64.893, -172.758], [101.701, -152.383], [137.25, -171.711]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.109803922474, 0.1254902035, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.404, 0.435, 0.5, 1, 0.3, 0.331, 0.999, 1, 0.196, 0.227], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172.447, "s": [102, -4], "to": [0, -36.5], "ti": [0, 36.5]}, {"t": 220.123046875, "s": [102, -223]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172.447, "s": [102, 45.787], "to": [0, -34], "ti": [0, 34]}, {"t": 220.123046875, "s": [102, -158.213]}], "ix": 6}, "t": 1, "nm": "Gradient Fill 333", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.047, 28.566], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -38, "op": 535, "st": -38, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "love2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125.834, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126.893, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 174.568, "s": [100]}, {"t": 175.625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [358.28, 300.301, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [102.03, 21.301, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126.893, "s": [{"i": [[11.393, 1.749], [1.667, -1.54], [0.277, 0.256], [11.789, -1.7], [-12.955, -10.942], [-7.823, 0], [0, 0]], "o": [[-10.791, -1.656], [-0.277, 0.256], [-1.667, -1.54], [-12.954, 1.868], [0, 0], [7.143, 0], [16.515, -14.428]], "v": [[118.532, 5.723], [103.034, 12.928], [102.049, 12.928], [86.551, 5.723], [82.286, 38.627], [102.439, 49.782], [123.545, 37.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135.369, "s": [{"i": [[6.762, 5.438], [1.827, -1.688], [0.304, 0.281], [7.466, -3.809], [-8.395, -13.868], [-8.573, 0], [0, 0]], "o": [[-9.322, -7.497], [-0.304, 0.281], [-1.827, -1.688], [-9.068, 4.626], [0, 0], [7.827, 0], [9.803, -15.595]], "v": [[119.488, -15.688], [103.005, -14.043], [101.926, -14.043], [88.193, -17.563], [87.77, 14.868], [102.354, 26.343], [115.822, 16.595]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.844, "s": [{"i": [[8.604, 5.144], [3.274, 0.013], [1.26, -0.018], [3.404, -3.764], [-6.207, -16.137], [-9.323, 0], [0, 0]], "o": [[-3.448, -2.581], [-0.45, -0.002], [-3.928, 0.263], [-6.283, 7.298], [0, 0], [8.511, 0], [8.118, -16.274]], "v": [[114.573, -43.731], [104.07, -47.17], [101.303, -47.076], [88.658, -42.236], [88.082, -10.551], [102.268, 2.903], [116.632, -8.976]], "c": true}]}, {"t": 173.51171875, "s": [{"i": [[20.808, 3.194], [3.044, -2.813], [0.507, 0.468], [21.532, -3.105], [-23.661, -19.984], [-14.289, 0], [0, 0]], "o": [[-19.708, -3.025], [-0.507, 0.468], [-3.044, -2.813], [-23.661, 3.412], [0, 0], [13.046, 0], [30.163, -26.352]], "v": [[131.094, -232.855], [102.787, -219.695], [100.988, -219.695], [72.681, -232.855], [64.893, -172.758], [101.701, -152.383], [137.25, -171.711]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.109803922474, 0.1254902035, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.404, 0.435, 0.5, 1, 0.3, 0.331, 0.999, 1, 0.196, 0.227], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126.893, "s": [102, -4], "to": [0, -36.5], "ti": [0, 36.5]}, {"t": 174.568359375, "s": [102, -223]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126.893, "s": [102, 45.787], "to": [0, -34], "ti": [0, 34]}, {"t": 174.568359375, "s": [102, -158.213]}], "ix": 6}, "t": 1, "nm": "Gradient Fill 2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.047, 28.566], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -38, "op": 535, "st": -38, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "love1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99.346, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100.41, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148.082, "s": [100]}, {"t": 149.142578125, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [358.28, 300.301, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [102.03, 21.301, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100.41, "s": [{"i": [[11.393, 1.749], [1.667, -1.54], [0.277, 0.256], [11.789, -1.7], [-12.955, -10.942], [-7.823, 0], [0, 0]], "o": [[-10.791, -1.656], [-0.277, 0.256], [-1.667, -1.54], [-12.954, 1.868], [0, 0], [7.143, 0], [16.515, -14.428]], "v": [[118.532, 5.723], [103.034, 12.928], [102.049, 12.928], [86.551, 5.723], [82.286, 38.627], [102.439, 49.782], [123.545, 37.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108.883, "s": [{"i": [[6.762, 5.438], [1.827, -1.688], [0.304, 0.281], [7.466, -3.809], [-8.395, -13.868], [-8.573, 0], [0, 0]], "o": [[-9.322, -7.497], [-0.304, 0.281], [-1.827, -1.688], [-9.068, 4.626], [0, 0], [7.827, 0], [9.803, -15.595]], "v": [[119.488, -15.688], [103.005, -14.043], [101.926, -14.043], [88.193, -17.563], [87.77, 14.868], [102.354, 26.343], [115.822, 16.595]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117.359, "s": [{"i": [[8.604, 5.144], [3.274, 0.013], [1.26, -0.018], [3.404, -3.764], [-6.207, -16.137], [-9.323, 0], [0, 0]], "o": [[-3.448, -2.581], [-0.45, -0.002], [-3.928, 0.263], [-6.283, 7.298], [0, 0], [8.511, 0], [8.118, -16.274]], "v": [[114.573, -43.731], [104.07, -47.17], [101.303, -47.076], [88.658, -42.236], [88.082, -10.551], [102.268, 2.903], [116.632, -8.976]], "c": true}]}, {"t": 147.0234375, "s": [{"i": [[20.808, 3.194], [3.044, -2.813], [0.507, 0.468], [21.532, -3.105], [-23.661, -19.984], [-14.289, 0], [0, 0]], "o": [[-19.708, -3.025], [-0.507, 0.468], [-3.044, -2.813], [-23.661, 3.412], [0, 0], [13.046, 0], [30.163, -26.352]], "v": [[131.094, -232.855], [102.787, -219.695], [100.988, -219.695], [72.681, -232.855], [64.893, -172.758], [101.701, -152.383], [137.25, -171.711]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.109803922474, 0.1254902035, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.404, 0.435, 0.5, 1, 0.3, 0.331, 0.999, 1, 0.196, 0.227], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100.41, "s": [102, -4], "to": [0, -36.5], "ti": [0, 36.5]}, {"t": 148.08203125, "s": [102, -223]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100.41, "s": [102, 45.787], "to": [0, -34], "ti": [0, 34]}, {"t": 148.08203125, "s": [102, -158.213]}], "ix": 6}, "t": 1, "nm": "<PERSON><PERSON><PERSON> 111", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.047, 28.566], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -38, "op": 535, "st": -38, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Left hand Stroke", "parent": 37, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102.855, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257.143, "s": [100]}, {"t": 267.4296875, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96.43, "s": [-40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 176.145, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 207.002, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 221.143, "s": [17]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 231.43, "s": [22]}, {"t": 267.4296875, "s": [-40]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.809, "y": 1}, "o": {"x": 1, "y": 0}, "t": 79.715, "s": [-670.732, 2127.652, 0], "to": [0, -226.496, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.561, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 118.285, "s": [-670.732, 768.678, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.809, "y": 1}, "o": {"x": 0.335, "y": 0}, "t": 135, "s": [-670.732, 948.165, 0], "to": [0, 0, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 154.287, "s": [-670.732, 866.114, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 243, "s": [-670.732, 768.678, 0], "to": [0, 226.496, 0], "ti": [0, -226.496, 0]}, {"t": 267.4296875, "s": [-670.732, 2127.652, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67.752, 206.504, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-461.538, 461.538, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.287, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.145, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.857, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.002, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.143, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"t": 231.4296875, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886260986328, 0.541168212891, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.276, 121.885], "ix": 2}, "a": {"a": 0, "k": [22.758, 133.191], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 372, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Left hand shadow", "parent": 37, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102.855, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257.143, "s": [100]}, {"t": 267.4296875, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96.43, "s": [-40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 176.145, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 207.002, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 221.143, "s": [17]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 231.43, "s": [22]}, {"t": 267.4296875, "s": [-40]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.809, "y": 1}, "o": {"x": 1, "y": 0}, "t": 79.715, "s": [-670.732, 2127.652, 0], "to": [0, -226.496, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.561, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 118.285, "s": [-670.732, 768.678, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.809, "y": 1}, "o": {"x": 0.335, "y": 0}, "t": 135, "s": [-670.732, 948.165, 0], "to": [0, 0, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 154.287, "s": [-670.732, 866.114, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 243, "s": [-670.732, 768.678, 0], "to": [0, 226.496, 0], "ti": [0, -226.496, 0]}, {"t": 267.4296875, "s": [-670.732, 2127.652, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67.752, 206.504, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-461.538, 461.538, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.287, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.145, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.857, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.002, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.143, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"t": 231.4296875, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.64, 1, 0.722, 0.224, 0.82, 1, 0.722, 0.224, 1, 1, 0.722, 0.224, 0.64, 0, 0.82, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [15.989, 123.344], "ix": 5}, "e": {"a": 0, "k": [125.051, 147.705], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 890", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.276, 121.885], "ix": 2}, "a": {"a": 0, "k": [22.758, 133.191], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 372, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Left hand", "parent": 37, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102.855, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257.143, "s": [100]}, {"t": 267.4296875, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96.43, "s": [-40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 176.145, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 207.002, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 221.143, "s": [17]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 231.43, "s": [22]}, {"t": 267.4296875, "s": [-40]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.809, "y": 1}, "o": {"x": 1, "y": 0}, "t": 79.715, "s": [-670.732, 2127.652, 0], "to": [0, -226.496, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.561, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 118.285, "s": [-670.732, 768.678, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.809, "y": 1}, "o": {"x": 0.335, "y": 0}, "t": 135, "s": [-670.732, 948.165, 0], "to": [0, 0, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 154.287, "s": [-670.732, 866.114, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 243, "s": [-670.732, 768.678, 0], "to": [0, 226.496, 0], "ti": [0, -226.496, 0]}, {"t": 267.4296875, "s": [-670.732, 2127.652, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67.752, 206.504, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-461.538, 461.538, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.287, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.145, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.857, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.002, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.143, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"t": 231.4296875, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.906, 0.596, 0.5, 1, 0.873, 0.408, 1, 1, 0.839, 0.22], "ix": 9}}, "s": {"a": 0, "k": [14.472, 90.286], "ix": 5}, "e": {"a": 0, "k": [80.889, 217.547], "ix": 6}, "t": 1, "nm": "Gradient Fill 9", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.276, 121.885], "ix": 2}, "a": {"a": 0, "k": [22.758, 133.191], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 372, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Right hand Stroke", "parent": 37, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102.855, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257.143, "s": [100]}, {"t": 267.4296875, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96.43, "s": [40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 176.145, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [-17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 207.002, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 221.143, "s": [-17]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 231.43, "s": [-22]}, {"t": 267.4296875, "s": [40]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.809, "y": 1}, "o": {"x": 1, "y": 0}, "t": 79.715, "s": [170.294, 2128.109, 0], "to": [0, -226.496, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.561, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 118.285, "s": [170.294, 769.135, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.809, "y": 1}, "o": {"x": 0.335, "y": 0}, "t": 135, "s": [170.294, 948.622, 0], "to": [0, 0, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.577, "y": 0.883}, "o": {"x": 0.214, "y": 0}, "t": 154.287, "s": [170.294, 866.57, 0], "to": [0, 0, 0], "ti": [0, 9.215, 0]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.412, "y": 0.065}, "t": 176.145, "s": [170.294, 851.201, 0], "to": [0, -27.452, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 243, "s": [170.294, 769.135, 0], "to": [0, 226.496, 0], "ti": [0, -226.496, 0]}, {"t": 267.4296875, "s": [170.294, 2128.109, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67.752, 206.504, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [461.538, 461.538, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.287, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.145, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.857, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.002, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.143, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"t": 231.4296875, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886260986328, 0.541168212891, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.276, 121.885], "ix": 2}, "a": {"a": 0, "k": [22.758, 133.191], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Right hand shadow", "parent": 37, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102.855, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257.143, "s": [100]}, {"t": 267.4296875, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96.43, "s": [40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 176.145, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [-17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 207.002, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 221.143, "s": [-17]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 231.43, "s": [-22]}, {"t": 267.4296875, "s": [40]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.809, "y": 1}, "o": {"x": 1, "y": 0}, "t": 79.715, "s": [170.294, 2128.109, 0], "to": [0, -226.496, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.561, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 118.285, "s": [170.294, 769.135, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.809, "y": 1}, "o": {"x": 0.335, "y": 0}, "t": 135, "s": [170.294, 948.622, 0], "to": [0, 0, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.577, "y": 0.883}, "o": {"x": 0.214, "y": 0}, "t": 154.287, "s": [170.294, 866.57, 0], "to": [0, 0, 0], "ti": [0, 9.215, 0]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.412, "y": 0.065}, "t": 176.145, "s": [170.294, 851.201, 0], "to": [0, -27.452, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 243, "s": [170.294, 769.135, 0], "to": [0, 226.496, 0], "ti": [0, -226.496, 0]}, {"t": 267.4296875, "s": [170.294, 2128.109, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67.752, 206.504, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [461.538, 461.538, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.287, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.145, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.857, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.002, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.143, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"t": 231.4296875, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.64, 1, 0.722, 0.224, 0.82, 1, 0.722, 0.224, 1, 1, 0.722, 0.224, 0.64, 0, 0.82, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [3.997, 123.976], "ix": 5}, "e": {"a": 0, "k": [110.82, 182.951], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1024", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.276, 121.885], "ix": 2}, "a": {"a": 0, "k": [22.758, 133.191], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Right hand", "parent": 37, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102.855, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257.143, "s": [100]}, {"t": 267.4296875, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96.43, "s": [40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 176.145, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [-17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 207.002, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 221.143, "s": [-17]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 231.43, "s": [-22]}, {"t": 267.4296875, "s": [40]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.809, "y": 1}, "o": {"x": 1, "y": 0}, "t": 79.715, "s": [170.294, 2128.109, 0], "to": [0, -226.496, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.561, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 118.285, "s": [170.294, 769.135, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.809, "y": 1}, "o": {"x": 0.335, "y": 0}, "t": 135, "s": [170.294, 948.622, 0], "to": [0, 0, 0], "ti": [0, 226.496, 0]}, {"i": {"x": 0.577, "y": 0.883}, "o": {"x": 0.214, "y": 0}, "t": 154.287, "s": [170.294, 866.57, 0], "to": [0, 0, 0], "ti": [0, 9.215, 0]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.412, "y": 0.065}, "t": 176.145, "s": [170.294, 851.201, 0], "to": [0, -27.452, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 243, "s": [170.294, 769.135, 0], "to": [0, 226.496, 0], "ti": [0, -226.496, 0]}, {"t": 267.4296875, "s": [170.294, 2128.109, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67.752, 206.504, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [461.538, 461.538, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.287, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.145, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.857, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.002, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.143, "s": [{"i": [[0, 0], [0.202, 9.596], [11.609, 24.442], [5.888, 5.189], [4.22, -5.28], [-5.558, -7.113], [15.955, -43.156], [-11.48, -5.304], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-5.353, -11.27], [-9.963, -8.781], [-10.943, 13.691], [-30, -31.596], [-14.928, 40.38], [10.683, 4.935], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [102.5, 88.111], [80.797, 57.529], [54.055, 59.5], [63.278, 98.278], [-51.722, 102.278], [-14.556, 174.111], [40.344, 187.498], [45.618, 218.088]], "c": false}]}, {"t": 231.4296875, "s": [{"i": [[0, 0], [0.202, 9.596], [15.428, 19.716], [10.131, 3.012], [-5.371, -2.439], [-5.558, -7.113], [9.288, -38.331], [-12.311, -2.895], [-19.622, -2.433], [0.204, -8.409]], "o": [[0, 0], [-0.575, -27.278], [-7.689, -9.826], [-19.177, -5.702], [14.157, 6.429], [-40.809, -22.749], [-8.575, 35.389], [13.801, 3.245], [8.006, 0.992], [-0.607, 24.993]], "v": [[115.618, 216.088], [116.177, 189.045], [86.884, 100.185], [50.835, 73.704], [28.819, 87.314], [56.552, 102.712], [-44.729, 123.848], [-13.431, 176.651], [40.594, 186.416], [45.618, 218.088]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.906, 0.596, 0.5, 1, 0.873, 0.408, 1, 1, 0.839, 0.22], "ix": 9}}, "s": {"a": 0, "k": [37.656, 102.805], "ix": 5}, "e": {"a": 0, "k": [68.763, 220.49], "ix": 6}, "t": 1, "nm": "Gradient Fill 1995", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.276, 121.885], "ix": 2}, "a": {"a": 0, "k": [22.758, 133.191], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Right tear", "parent": 25, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232.715, "s": [100]}, {"t": 243, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -3.872, "ix": 10}, "p": {"a": 0, "k": [-52.077, 9.167, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-75.792, 26.344, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-61.984, 61.984, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.723, -7.231], [0, 0], [-3.512, -0.566]], "o": [[0, 0], [3.062, 2.5], [0, 0], [2.346, 0.378]], "v": [[-104.451, 30.188], [-103.076, 40.12], [-88.388, 41.933], [-86.262, 37.061]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.158762658811, 0.793010457357, 0.945450367647, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [99.848, 100.528], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [15.078, 15.078], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.158762658811, 0.793010457357, 0.945450367647, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-95.661, 42.477], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-22.52, 0], [0, -8.121], [22.52, 0], [0, 8.121]], "o": [[22.52, 0], [0, 8.121], [-22.52, 0], [0, -8.121]], "v": [[-0.625, -2.079], [37.679, -6], [-0.625, 12.704], [-36.406, -6]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.158762658811, 0.793010457357, 0.945450367647, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-75.416, 25.172], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Left eyebrow", "parent": 37, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [27]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [43]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [20]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 59, "s": [28]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 68.045, "s": [38]}, {"t": 79.12109375, "s": [22]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [209.275, -530.23, 0], "to": [-6.91, -33.12, 0], "ti": [-14.949, -23.918, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [167.814, -728.948, 0], "to": [19.231, 30.769, 0], "ti": [-9.067, -114.105, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [214.48, -360.999, 0], "to": [9.067, 114.105, 0], "ti": [0, 28.205, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [222.219, -44.321, 0], "to": [0, -26.334, 0], "ti": [-0.31, 7.612, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [209.275, -532.794, 0], "to": [4.68, -114.746, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [158.48, -739.205, 0], "to": [0, 0, 0], "ti": [-8.466, -34.402, 0]}, {"t": 79.12109375, "s": [209.275, -532.794, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\ne = 0.7;\ng = 5000;\nnMax = 9;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n    vl = length(v);\n    if ($bm_isInstanceOfArray(value)) {\n        vu = vl > 0 ? normalize(v) : [\n            0,\n            0,\n            0\n        ];\n    } else {\n        vu = v < 0 ? -1 : 1;\n    }\n    tCur = 0;\n    segDur = $bm_div($bm_mul(2, vl), g);\n    tNext = segDur;\n    nb = 1;\n    while (tNext < t && nb <= nMax) {\n        vl *= e;\n        segDur *= e;\n        tCur = tNext;\n        tNext = $bm_sum(tNext, segDur);\n        nb++;\n    }\n    if (nb <= nMax) {\n        delta = $bm_sub(t, tCur);\n        $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n    } else {\n        $bm_rt = value;\n    }\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [-63.934, -96.181, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [523.077, 523.077, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [435.897, 435.897, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 59, "s": [523.077, 523.077, 100]}, {"t": 79.12109375, "s": [523.077, 523.077, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-21.584, 3.546], [0, 0]], "o": [[0, 0], [24.814, -4.077], [0, 0]], "v": [[-108.25, -87.75], [-71.759, -98.851], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [-21.584, 3.546], [0, 0]], "o": [[0, 0], [24.814, -4.077], [0, 0]], "v": [[-108.25, -87.75], [-75.745, -89.837], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [{"i": [[0, 0], [-21.584, 3.546], [0, 0]], "o": [[0, 0], [24.814, -4.077], [0, 0]], "v": [[-108.25, -87.75], [-75.745, -89.837], [-39.469, -101.555]], "c": false}]}, {"t": 59, "s": [{"i": [[0, 0], [-21.584, 3.546], [0, 0]], "o": [[0, 0], [24.814, -4.077], [0, 0]], "v": [[-108.25, -87.75], [-71.759, -98.851], [-39.469, -101.555]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.373870849609, 0.175048828125, 0.024780273438, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [86.562, 102.528], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "abcd 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [68, 68, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [{"i": [[-144.853, -0.618], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[141.384, 0.603], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[0, -256], [256, 0], [0, 256], [-256, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [{"i": [[-144.854, 0], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[146.75, 0], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[1.412, -260.471], [256, 0], [0, 256], [-256, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [{"i": [[-144.854, 0], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[146.75, 0], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[1.412, -260.471], [256, 0], [0, 256], [-256, 0]], "c": true}]}, {"t": 109, "s": [{"i": [[-144.854, 0], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[146.75, 0], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[1.412, -258.632], [256, 0], [0, 256], [-256, 0]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Left eyebrow shadow", "parent": 15, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-62.965, -93.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-63.465, -96.7, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-73.362, -98.691], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-77.376, -89.001], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-77.376, -89.001], [-39.469, -101.555]], "c": false}]}, {"t": 59, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-73.362, -98.691], [-39.469, -101.555]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [86.562, 102.528], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Left tears", "parent": 30, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43.715, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232.715, "s": [100]}, {"t": 243, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-56.655, 9.783, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-75.792, 26.344, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [58.824, 58.824, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.723, -7.231], [0, 0], [-3.512, -0.566]], "o": [[0, 0], [3.062, 2.5], [0, 0], [2.346, 0.378]], "v": [[-104.451, 30.188], [-103.076, 40.12], [-88.388, 41.933], [-86.262, 37.061]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223541259766, 0.729339599609, 0.850921630859, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [99.848, 100.528], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [15.078, 15.078], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223541259766, 0.729339599609, 0.850921630859, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-95.661, 42.477], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-22.52, 0], [0, -8.121], [22.52, 0], [0, 8.121]], "o": [[22.52, 0], [0, 8.121], [-22.52, 0], [0, -8.121]], "v": [[-0.625, -2.079], [37.679, -6], [-0.625, 12.704], [-36.406, -6]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223541259766, 0.729339599609, 0.850921630859, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-75.416, 25.172], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Left eyebrow", "parent": 37, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [-19]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [20]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 59, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 68.045, "s": [-14]}, {"t": 79.12109375, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-724.058, -530.23, 0], "to": [6.838, -33.333, 0], "ti": [0, -28.205, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [-683.033, -730.23, 0], "to": [0, 28.205, 0], "ti": [4.68, -114.746, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [-724.058, -360.999, 0], "to": [-4.68, 114.746, 0], "ti": [0, 28.205, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [-711.115, -41.757, 0], "to": [0, -28.205, 0], "ti": [-4.68, 114.746, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [-724.058, -530.23, 0], "to": [4.68, -114.746, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68.045, "s": [-683.033, -730.23, 0], "to": [0, 0, 0], "ti": [6.838, -33.333, 0]}, {"t": 79.12109375, "s": [-724.058, -530.23, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\ne = 0.7;\ng = 5000;\nnMax = 9;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n    vl = length(v);\n    if ($bm_isInstanceOfArray(value)) {\n        vu = vl > 0 ? normalize(v) : [\n            0,\n            0,\n            0\n        ];\n    } else {\n        vu = v < 0 ? -1 : 1;\n    }\n    tCur = 0;\n    segDur = $bm_div($bm_mul(2, vl), g);\n    tNext = segDur;\n    nb = 1;\n    while (tNext < t && nb <= nMax) {\n        vl *= e;\n        segDur *= e;\n        tCur = tNext;\n        tNext = $bm_sum(tNext, segDur);\n        nb++;\n    }\n    if (nb <= nMax) {\n        delta = $bm_sub(t, tCur);\n        $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n    } else {\n        $bm_rt = value;\n    }\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [-63.934, -96.181, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [523.077, 523.077, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [435.897, 435.897, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 59, "s": [523.077, 523.077, 100]}, {"t": 79.12109375, "s": [523.077, 523.077, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-75.554, -99.198], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-72.977, -89.349], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-72.977, -89.349], [-39.469, -101.555]], "c": false}]}, {"t": 59, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-75.554, -99.198], [-39.469, -101.555]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.373870849609, 0.175048828125, 0.024780273438, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [86.562, 102.528], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "abcd", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [68, 68, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [{"i": [[-144.853, -0.618], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[141.384, 0.603], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[0, -256], [256, 0], [0, 256], [-256, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[-145.588, -1.353], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[141.379, 1.314], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[0, -257.471], [256, 0], [0, 256], [-256, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[-151.471, 0.118], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[141.385, -0.11], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[0, -257.471], [256, 0], [0, 256], [-256, 0]], "c": true}]}, {"t": 94, "s": [{"i": [[-151.471, 0.118], [0, -141.385], [141.385, 0], [0, 141.385]], "o": [[141.385, -0.11], [0, 141.385], [-141.385, 0], [0, -141.385]], "v": [[0, -258.941], [256, 0], [0, 256], [-256, 0]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Left eyebrow shadow", "parent": 19, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-62.965, -93.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-63.465, -96.7, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-75.73, -98.743], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-71.353, -89.636], [-39.469, -101.555]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-71.353, -89.636], [-39.469, -101.555]], "c": false}]}, {"t": 59, "s": [{"i": [[0, 0], [-22.133, 3.508], [0, 0]], "o": [[0, 0], [24.837, -3.936], [0, 0]], "v": [[-108.25, -87.75], [-75.73, -98.743], [-39.469, -101.555]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [86.562, 102.528], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "Right highlight8", "parent": 24, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [0]}, {"t": 46, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": -3.872, "ix": 10}, "p": {"a": 0, "k": [-70.617, -4.256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-84.798, -43.919, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-32.555, 32.555, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-17.479, 0.886], [-3.818, -30.471], [36.12, -3.842], [0.374, 11.888]], "o": [[15.877, -0.804], [1.479, 11.802], [-54.836, 5.833], [-0.815, -25.899]], "v": [[18.315, -76.702], [74.552, -33.075], [25.03, -1.144], [-44.145, -23.972]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[-17.479, 0.886], [-3.818, -30.471], [36.12, -3.842], [0.374, 11.888]], "o": [[15.877, -0.804], [1.479, 11.802], [-54.836, 5.833], [-0.815, -25.899]], "v": [[18.315, -76.702], [74.552, -33.075], [25.03, -1.144], [-44.145, -23.972]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[-11.894, 0], [-2.168, 0.537], [11.894, 0], [0.192, 0.054]], "o": [[11.894, 0], [-0.237, -0.428], [-11.894, 0], [0.353, -0.106]], "v": [[-2.414, 5.981], [22.019, 0], [-2.092, 6.069], [-22.984, 2.092]], "c": true}]}, {"t": 59, "s": [{"i": [[-17.479, 0.886], [-3.818, -30.471], [36.12, -3.842], [0.374, 11.888]], "o": [[15.877, -0.804], [1.479, 11.802], [-54.836, 5.833], [-0.815, -25.899]], "v": [[18.315, -76.702], [74.552, -33.075], [25.03, -1.144], [-44.145, -23.972]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-84.798, -43.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [115.595, 115.595], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Right highlight7", "parent": 24, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [0]}, {"t": 46, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-45.108, -25.081, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-84.798, -43.919, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [47.78, 47.78, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-18.098, -0.207], [-3.062, -11.493], [25.785, 2.77], [-0.174, 16.541]], "o": [[14.769, 0.169], [5.513, 20.695], [-11.826, -1.271], [0.125, -11.893]], "v": [[-14.266, -76.254], [17.021, -44.966], [-8.126, -10.608], [-48.805, -33.95]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[-18.098, -0.207], [-3.062, -11.493], [25.785, 2.77], [-0.174, 16.541]], "o": [[14.769, 0.169], [5.513, 20.695], [-11.826, -1.271], [0.125, -11.893]], "v": [[-14.266, -76.254], [17.021, -44.966], [-8.126, -10.608], [-48.805, -33.95]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[-10.168, 2.37], [-2.836, 0.679], [9.529, -1.897], [-1.562, 0.497]], "o": [[11.584, -2.7], [-0.204, -0.308], [-8.201, 1.633], [3.036, -0.965]], "v": [[-3.728, 32.079], [15.474, 26.862], [-3.283, 32.049], [-21.098, 37.169]], "c": true}]}, {"t": 59, "s": [{"i": [[-18.098, -0.207], [-3.062, -11.493], [25.785, 2.77], [-0.174, 16.541]], "o": [[14.769, 0.169], [5.513, 20.695], [-11.826, -1.271], [0.125, -11.893]], "v": [[-14.266, -76.254], [17.021, -44.966], [-8.126, -10.608], [-48.805, -33.95]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-84.798, -43.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [115.595, 115.595], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "Right highlight6", "parent": 25, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [0]}, {"t": 46, "s": [100]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6.43, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12.857, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 19.287, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25.715, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.145, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38.57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51.428, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 57.857, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70.715, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 77.143, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 83.572, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 96.43, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102.855, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 109.285, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 115.713, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 122.143, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 128.57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 135, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 141.43, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 147.857, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 160.715, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 167.145, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 173.57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 180, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 186.428, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 199.285, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 205.715, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 212.143, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 218.572, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 225, "s": [-11]}, {"t": 231.4296875, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [-55.725, -21.9, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-73.468, -36.705, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [73.721, 73.721, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-8.912, 1.473], [-0.249, -10.572], [10.243, -3.289], [0.199, 9.359]], "o": [[4.706, -0.778], [0.082, 3.491], [-7.22, 2.318], [-0.253, -11.891]], "v": [[-8.111, -19.897], [5.746, -6.299], [-7.248, 5.66], [-24.47, 1.035]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[-8.912, 1.473], [-0.249, -10.572], [10.243, -3.289], [0.199, 9.359]], "o": [[4.706, -0.778], [0.082, 3.491], [-7.22, 2.318], [-0.253, -11.891]], "v": [[-8.111, -19.897], [5.746, -6.299], [-7.248, 5.66], [-24.47, 1.035]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[-11.84, -1.138], [-0.193, -0.352], [9.293, 1.184], [0.146, 0.12]], "o": [[11.657, 1.12], [-0.036, 0.067], [-11.799, -1.503], [0.303, -0.038]], "v": [[-0.201, 34.413], [20.869, 34.785], [-0.352, 34.507], [-21.641, 26.927]], "c": true}]}, {"t": 59, "s": [{"i": [[-8.912, 1.473], [-0.249, -10.572], [10.243, -3.289], [0.199, 9.359]], "o": [[4.706, -0.778], [0.082, 3.491], [-7.22, 2.318], [-0.253, -11.891]], "v": [[-8.111, -19.897], [5.746, -6.299], [-7.248, 5.66], [-24.47, 1.035]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-84.798, -43.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [115.595, 115.595], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Left eye 3", "parent": 37, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [107.524, -140.767, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.734, "s": [107.524, -304.869, 0], "to": [0, 22.222, 0], "ti": [0, -27.35, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35.605, "s": [107.524, -7.433, 0], "to": [0, 27.35, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59.34, "s": [107.524, -140.767, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68.045, "s": [107.524, -279.228, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 79.12109375, "s": [107.524, -140.767, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-55.589, -22.081, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23.734, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35.605, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44.309, "s": [717.949, 717.949, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 59.34, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 68.045, "s": [779.487, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 79.121, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 95.143, "s": [717.949, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 105.43, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 114.43, "s": [717.949, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 124.713, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 135, "s": [769.231, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 145.287, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 154.287, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 164.57, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 176.145, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 185.145, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 192.857, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 200.572, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 207.002, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 214.715, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 221.143, "s": [769.231, 666.667, 100]}, {"t": 231.4296875, "s": [871.795, 871.795, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.734, "s": [{"i": [[-9.287, 0], [0, -9.571], [9.571, 0], [0, 9.571], [-0.013, 0.28]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -0.284], [0.441, -9.178]], "v": [[0.143, -17.401], [17.472, -0.072], [0.143, 17.258], [-17.186, -0.072], [-17.166, -0.917]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35.605, "s": [{"i": [[-9.287, 0], [-0.064, -8.687], [9.571, 0], [0.097, 8.912], [-0.011, 0.257]], "o": [[9.571, 0], [0.065, 8.769], [-9.571, 0], [-0.003, -0.262], [0.358, -8.422]], "v": [[0.132, -15.254], [17.472, -0.072], [0.132, 15.111], [-17.186, -0.072], [-17.174, -0.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.309, "s": [{"i": [[-9.106, 0], [-1.5, -1.873], [9.571, 0], [-0.762, 3.618], [-0.122, 0.114]], "o": [[9.571, 0], [2.577, 3.218], [-9.571, 0], [0.044, -0.211], [1.863, -1.742]], "v": [[0.127, 10.715], [17.235, 6.296], [0.132, 15.111], [-17.185, 6.009], [-17.011, 5.469]], "c": true}]}, {"t": 59.33984375, "s": [{"i": [[-9.287, 0], [0, -9.571], [9.571, 0], [0, 9.571], [-0.013, 0.28]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -0.284], [0.441, -9.178]], "v": [[0.143, -17.401], [17.472, -0.072], [0.143, 17.258], [-17.186, -0.072], [-17.166, -0.917]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-0.293, -13.562], "ix": 5}, "e": {"a": 0, "k": [-0.077, 15.843], "ix": 6}, "t": 1, "nm": "Gradient Fill 123", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-55.589, -22.636], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [205.558, 205.558], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Left eye shadow 3", "parent": 25, "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-55.589, -18.947, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-55.589, -22.081, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-9.571, 0], [0, -9.571], [9.571, 0], [0, 9.571]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -9.571]], "v": [[0, -17.329], [17.329, 0], [0, 17.329], [-17.329, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.734, "s": [{"i": [[-9.571, 0], [0, -9.571], [9.571, 0], [0, 9.571]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -9.571]], "v": [[0, -17.329], [17.329, 0], [0, 17.329], [-17.329, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35.605, "s": [{"i": [[-9.571, 0], [-0.22, -9.568], [9.571, 0], [0.169, 8.461]], "o": [[9.571, 0], [0.208, 9.033], [-9.571, 0], [-0.191, -9.569]], "v": [[-0.009, -16.542], [17.329, 0], [-0.009, 14.754], [-17.329, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.309, "s": [{"i": [[-9.571, 0], [-3.205, -1.626], [9.571, 0], [0.095, 4.526]], "o": [[9.571, 0], [0.229, 5.599], [-9.571, 0], [4.673, -1.197]], "v": [[-0.224, 10.715], [17.308, 5.223], [-0.009, 14.754], [-17.327, 5.079]], "c": true}]}, {"t": 59.33984375, "s": [{"i": [[-9.571, 0], [0, -9.571], [9.571, 0], [0, 9.571]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -9.571]], "v": [[0, -17.329], [17.329, 0], [0, 17.329], [-17.329, 0]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-55.589, -22.636], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [205.558, 205.558], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Left highlight5", "parent": 29, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [0]}, {"t": 46, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": -3.872, "ix": 10}, "p": {"a": 0, "k": [-70.617, -4.256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-84.798, -43.919, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-32.555, 32.555, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-17.479, 0.886], [-3.818, -30.471], [36.12, -3.842], [0.374, 11.888]], "o": [[15.877, -0.804], [1.479, 11.802], [-54.836, 5.833], [-0.815, -25.899]], "v": [[18.315, -76.702], [74.552, -33.075], [25.03, -1.144], [-44.145, -23.972]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[-17.479, 0.886], [-3.818, -30.471], [36.12, -3.842], [0.374, 11.888]], "o": [[15.877, -0.804], [1.479, 11.802], [-54.836, 5.833], [-0.815, -25.899]], "v": [[18.315, -76.702], [74.552, -33.075], [25.03, -1.144], [-44.145, -23.972]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[-11.894, 0], [-2.168, 0.537], [11.894, 0], [0.192, 0.054]], "o": [[11.894, 0], [-0.237, -0.428], [-11.894, 0], [0.353, -0.106]], "v": [[-2.414, 5.981], [22.019, 0], [-2.092, 6.069], [-22.984, 2.092]], "c": true}]}, {"t": 59, "s": [{"i": [[-17.479, 0.886], [-3.818, -30.471], [36.12, -3.842], [0.374, 11.888]], "o": [[15.877, -0.804], [1.479, 11.802], [-54.836, 5.833], [-0.815, -25.899]], "v": [[18.315, -76.702], [74.552, -33.075], [25.03, -1.144], [-44.145, -23.972]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-84.798, -43.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [115.595, 115.595], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Left highlight2", "parent": 29, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [0]}, {"t": 46, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-45.108, -25.081, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-84.798, -43.919, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [47.78, 47.78, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-18.098, -0.207], [-3.062, -11.493], [25.785, 2.77], [-0.174, 16.541]], "o": [[14.769, 0.169], [5.513, 20.695], [-11.826, -1.271], [0.125, -11.893]], "v": [[-14.266, -76.254], [17.021, -44.966], [-8.126, -10.608], [-48.805, -33.95]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[-18.098, -0.207], [-3.062, -11.493], [25.785, 2.77], [-0.174, 16.541]], "o": [[14.769, 0.169], [5.513, 20.695], [-11.826, -1.271], [0.125, -11.893]], "v": [[-14.266, -76.254], [17.021, -44.966], [-8.126, -10.608], [-48.805, -33.95]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[-10.168, 2.37], [-2.836, 0.679], [9.529, -1.897], [-1.562, 0.497]], "o": [[11.584, -2.7], [-0.204, -0.308], [-8.201, 1.633], [3.036, -0.965]], "v": [[-3.728, 32.079], [15.474, 26.862], [-3.283, 32.049], [-21.098, 37.169]], "c": true}]}, {"t": 59, "s": [{"i": [[-18.098, -0.207], [-3.062, -11.493], [25.785, 2.77], [-0.174, 16.541]], "o": [[14.769, 0.169], [5.513, 20.695], [-11.826, -1.271], [0.125, -11.893]], "v": [[-14.266, -76.254], [17.021, -44.966], [-8.126, -10.608], [-48.805, -33.95]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-84.798, -43.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [115.595, 115.595], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "Left highlight1", "parent": 30, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [0]}, {"t": 46, "s": [100]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6.43, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12.857, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 19.287, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25.715, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.145, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38.57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51.428, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 57.857, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64.285, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70.715, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 77.143, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 83.572, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 96.43, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102.855, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 109.285, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 115.713, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 122.143, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 128.57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 135, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 141.43, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 147.857, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154.287, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 160.715, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 167.145, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 173.57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 180, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 186.428, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 192.857, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 199.285, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 205.715, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 212.143, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 218.572, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 225, "s": [-11]}, {"t": 231.4296875, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [-55.725, -21.9, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-73.468, -36.705, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [73.721, 73.721, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-8.912, 1.473], [-0.249, -10.572], [10.243, -3.289], [0.199, 9.359]], "o": [[4.706, -0.778], [0.082, 3.491], [-7.22, 2.318], [-0.253, -11.891]], "v": [[-8.111, -19.897], [5.746, -6.299], [-7.248, 5.66], [-24.47, 1.035]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[-8.912, 1.473], [-0.249, -10.572], [10.243, -3.289], [0.199, 9.359]], "o": [[4.706, -0.778], [0.082, 3.491], [-7.22, 2.318], [-0.253, -11.891]], "v": [[-8.111, -19.897], [5.746, -6.299], [-7.248, 5.66], [-24.47, 1.035]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[-11.84, -1.138], [-0.193, -0.352], [9.293, 1.184], [0.146, 0.12]], "o": [[11.657, 1.12], [-0.036, 0.067], [-11.799, -1.503], [0.303, -0.038]], "v": [[-0.201, 34.413], [20.869, 34.785], [-0.352, 34.507], [-21.641, 26.927]], "c": true}]}, {"t": 59, "s": [{"i": [[-8.912, 1.473], [-0.249, -10.572], [10.243, -3.289], [0.199, 9.359]], "o": [[4.706, -0.778], [0.082, 3.491], [-7.22, 2.318], [-0.253, -11.891]], "v": [[-8.111, -19.897], [5.746, -6.299], [-7.248, 5.66], [-24.47, 1.035]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-84.798, -43.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [115.595, 115.595], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "Left eye", "parent": 37, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-623.246, -140.767, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.734, "s": [-623.246, -304.869, 0], "to": [0, 22.222, 0], "ti": [0, -27.35, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35.605, "s": [-623.246, -7.433, 0], "to": [0, 27.35, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59.34, "s": [-623.246, -140.767, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68.045, "s": [-623.246, -279.228, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 79.12109375, "s": [-623.246, -140.767, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-55.589, -22.081, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23.734, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35.605, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44.309, "s": [717.949, 717.949, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 59.34, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 68.045, "s": [779.487, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 79.121, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 95.143, "s": [717.949, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 105.43, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 114.43, "s": [717.949, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 124.713, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 135, "s": [769.231, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 145.287, "s": [871.795, 871.795, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 154.287, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 164.57, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 176.145, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 185.145, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 192.857, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 200.572, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 207.002, "s": [769.231, 666.667, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 214.715, "s": [871.795, 769.231, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 221.143, "s": [769.231, 666.667, 100]}, {"t": 231.4296875, "s": [871.795, 871.795, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.734, "s": [{"i": [[-9.287, 0], [0, -9.571], [9.571, 0], [0, 9.571], [-0.013, 0.28]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -0.284], [0.441, -9.178]], "v": [[0.143, -17.401], [17.472, -0.072], [0.143, 17.258], [-17.186, -0.072], [-17.166, -0.917]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35.605, "s": [{"i": [[-9.287, 0], [-0.064, -8.687], [9.571, 0], [0.097, 8.912], [-0.011, 0.257]], "o": [[9.571, 0], [0.065, 8.769], [-9.571, 0], [-0.003, -0.262], [0.358, -8.422]], "v": [[0.132, -15.254], [17.472, -0.072], [0.132, 15.111], [-17.186, -0.072], [-17.174, -0.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.309, "s": [{"i": [[-9.106, 0], [-1.5, -1.873], [9.571, 0], [-0.762, 3.618], [-0.122, 0.114]], "o": [[9.571, 0], [2.577, 3.218], [-9.571, 0], [0.044, -0.211], [1.863, -1.742]], "v": [[0.127, 10.715], [17.235, 6.296], [0.132, 15.111], [-17.185, 6.009], [-17.011, 5.469]], "c": true}]}, {"t": 59.33984375, "s": [{"i": [[-9.287, 0], [0, -9.571], [9.571, 0], [0, 9.571], [-0.013, 0.28]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -0.284], [0.441, -9.178]], "v": [[0.143, -17.401], [17.472, -0.072], [0.143, 17.258], [-17.186, -0.072], [-17.166, -0.917]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-0.293, -13.562], "ix": 5}, "e": {"a": 0, "k": [-0.077, 15.843], "ix": 6}, "t": 1, "nm": "Gradient Fill 123", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-55.589, -22.636], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [205.558, 205.558], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "Left eye shadow", "parent": 30, "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-55.589, -18.947, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-55.589, -22.081, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-9.571, 0], [0, -9.571], [9.571, 0], [0, 9.571]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -9.571]], "v": [[0, -17.329], [17.329, 0], [0, 17.329], [-17.329, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.734, "s": [{"i": [[-9.571, 0], [0, -9.571], [9.571, 0], [0, 9.571]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -9.571]], "v": [[0, -17.329], [17.329, 0], [0, 17.329], [-17.329, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35.605, "s": [{"i": [[-9.571, 0], [-0.22, -9.568], [9.571, 0], [0.169, 8.461]], "o": [[9.571, 0], [0.208, 9.033], [-9.571, 0], [-0.191, -9.569]], "v": [[-0.009, -16.542], [17.329, 0], [-0.009, 14.754], [-17.329, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.309, "s": [{"i": [[-9.571, 0], [-3.205, -1.626], [9.571, 0], [0.095, 4.526]], "o": [[9.571, 0], [0.229, 5.599], [-9.571, 0], [4.673, -1.197]], "v": [[-0.224, 10.715], [17.308, 5.223], [-0.009, 14.754], [-17.327, 5.079]], "c": true}]}, {"t": 59.33984375, "s": [{"i": [[-9.571, 0], [0, -9.571], [9.571, 0], [0, 9.571]], "o": [[9.571, 0], [0, 9.571], [-9.571, 0], [0, -9.571]], "v": [[0, -17.329], [17.329, 0], [0, 17.329], [-17.329, 0]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-55.589, -22.636], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [205.558, 205.558], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "mouth 2", "parent": 37, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.99}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-257.853, 323.993, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.976}, "o": {"x": 0.333, "y": 0.017}, "t": 2.57, "s": [-257.853, 284.947, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.982}, "o": {"x": 0.333, "y": 0.012}, "t": 5.143, "s": [-257.853, 308.29, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.974}, "o": {"x": 0.333, "y": 0.026}, "t": 7.713, "s": [-257.853, 261.253, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.979}, "o": {"x": 0.333, "y": 0.01}, "t": 10.287, "s": [-257.853, 294.16, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.02, "y": 1}, "t": 14.143, "s": [-257.853, 170.51, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.143, "s": [-257.853, 170.147, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.802}, "o": {"x": 0.333, "y": 0}, "t": 30.857, "s": [-257.853, 323.993, 0], "to": [0, -22.067, 0], "ti": [0, 50.361, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.034}, "t": 55.285, "s": [-257.853, 267.749, 0], "to": [0, -23.76, 0], "ti": [0, 0.548, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 65.572, "s": [-257.853, 129.121, 0], "to": [0, -1.709, 0], "ti": [0, -48.718, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 86.143, "s": [-253.601, 313.736, 0], "to": [0, 48.718, 0], "ti": [0.709, -17.949, 0]}, {"t": 104.142578125, "s": [-257.853, 421.429, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\ne = 0.7;\ng = 5000;\nnMax = 9;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n    vl = length(v);\n    if ($bm_isInstanceOfArray(value)) {\n        vu = vl > 0 ? normalize(v) : [\n            0,\n            0,\n            0\n        ];\n    } else {\n        vu = v < 0 ? -1 : 1;\n    }\n    tCur = 0;\n    segDur = $bm_div($bm_mul(2, vl), g);\n    tNext = segDur;\n    nb = 1;\n    while (tNext < t && nb <= nMax) {\n        vl *= e;\n        segDur *= e;\n        tCur = tNext;\n        tNext = $bm_sum(tNext, segDur);\n        nb++;\n    }\n    if (nb <= nMax) {\n        delta = $bm_sub(t, tCur);\n        $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n    } else {\n        $bm_rt = value;\n    }\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23.143, "s": [358.974, 358.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 65.572, "s": [358.974, 358.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 86.143, "s": [615.385, 615.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 104.143, "s": [615.385, 615.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255.857, "s": [615.385, 615.385, 100]}, {"t": 270, "s": [358.974, 358.974, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.143, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[12.2, 0.063], [11.417, 16.398], [-3.216, 0.801], [-2.236, -2.445], [-8.796, 0.071], [-7.558, 6.23], [-3.217, -0.779], [1.672, -2.86]], "o": [[-11.375, -0.058], [-1.893, -2.719], [3.218, -0.799], [6.38, 6.977], [9.429, -0.076], [2.557, -2.108], [3.221, 0.782], [-9.399, 16.079]], "v": [[-0.558, 44.416], [-42.547, 28.621], [-38.175, 21.349], [-30.902, 25.721], [-0.38, 31.345], [28.036, 24.86], [35.282, 20.444], [39.698, 27.69]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55.285, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.143, "s": [{"i": [[10.553, 0], [2.209, 11.369], [0, 1.878], [-0.278, 1.595], [-10.875, 0], [-1.819, -11.93], [0, -1.48], [0.407, -1.911]], "o": [[-10.707, 0], [-0.342, -1.762], [0, -1.688], [2.027, -11.638], [11.059, 0], [0.215, 1.411], [0, 2.052], [-2.368, 11.124]], "v": [[-0.25, 37.876], [-22.119, 18], [-22.641, 12.529], [-22.218, 7.597], [-0.25, -12.818], [21.813, 8.187], [22.141, 12.529], [21.518, 18.488]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255.857, "s": [{"i": [[10.553, 0], [2.209, 11.369], [0, 1.878], [-0.278, 1.595], [-10.875, 0], [-1.819, -11.93], [0, -1.48], [0.407, -1.911]], "o": [[-10.707, 0], [-0.342, -1.762], [0, -1.688], [2.027, -11.638], [11.059, 0], [0.215, 1.411], [0, 2.052], [-2.368, 11.124]], "v": [[-0.25, 37.876], [-22.119, 18], [-22.641, 12.529], [-22.218, 7.597], [-0.25, -12.818], [21.813, 8.187], [22.141, 12.529], [21.518, 18.488]], "c": true}]}, {"t": 270, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.281, 0.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 536, "st": 0, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "tongue", "parent": 34, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73.285, "s": [0.722, -30.812, 0], "to": [0, 4.306, 0], "ti": [0, -4.306, 0]}, {"t": 86.142578125, "s": [0.722, -4.978, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2.174, 96.361, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [48.332, 36.223], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270568847656, 0.270568847656, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.668, 96.977], "ix": 2}, "a": {"a": 0, "k": [0.494, 0.615], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "mouth567", "parent": 37, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.99}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-257.853, 323.993, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.976}, "o": {"x": 0.333, "y": 0.017}, "t": 2.57, "s": [-257.853, 284.947, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.982}, "o": {"x": 0.333, "y": 0.012}, "t": 5.143, "s": [-257.853, 308.29, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.974}, "o": {"x": 0.333, "y": 0.026}, "t": 7.713, "s": [-257.853, 261.253, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.979}, "o": {"x": 0.333, "y": 0.01}, "t": 10.287, "s": [-257.853, 294.16, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.02, "y": 1}, "t": 14.143, "s": [-257.853, 170.51, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.143, "s": [-257.853, 170.147, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.802}, "o": {"x": 0.333, "y": 0}, "t": 30.857, "s": [-257.853, 323.993, 0], "to": [0, -22.067, 0], "ti": [0, 50.361, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.034}, "t": 55.285, "s": [-257.853, 267.749, 0], "to": [0, -23.76, 0], "ti": [0, 0.548, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 65.572, "s": [-257.853, 129.121, 0], "to": [0, -1.709, 0], "ti": [0, -48.718, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 86.143, "s": [-253.601, 313.736, 0], "to": [0, 48.718, 0], "ti": [0.709, -17.949, 0]}, {"t": 104.142578125, "s": [-257.853, 421.429, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\ne = 0.7;\ng = 5000;\nnMax = 9;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n    vl = length(v);\n    if ($bm_isInstanceOfArray(value)) {\n        vu = vl > 0 ? normalize(v) : [\n            0,\n            0,\n            0\n        ];\n    } else {\n        vu = v < 0 ? -1 : 1;\n    }\n    tCur = 0;\n    segDur = $bm_div($bm_mul(2, vl), g);\n    tNext = segDur;\n    nb = 1;\n    while (tNext < t && nb <= nMax) {\n        vl *= e;\n        segDur *= e;\n        tCur = tNext;\n        tNext = $bm_sum(tNext, segDur);\n        nb++;\n    }\n    if (nb <= nMax) {\n        delta = $bm_sub(t, tCur);\n        $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n    } else {\n        $bm_rt = value;\n    }\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23.143, "s": [358.974, 358.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 65.572, "s": [358.974, 358.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 86.143, "s": [615.385, 615.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 104.143, "s": [615.385, 615.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255.857, "s": [615.385, 615.385, 100]}, {"t": 270, "s": [358.974, 358.974, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.143, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[12.2, 0.063], [11.417, 16.398], [-3.216, 0.801], [-2.236, -2.445], [-8.796, 0.071], [-7.558, 6.23], [-3.217, -0.779], [1.672, -2.86]], "o": [[-11.375, -0.058], [-1.893, -2.719], [3.218, -0.799], [6.38, 6.977], [9.429, -0.076], [2.557, -2.108], [3.221, 0.782], [-9.399, 16.079]], "v": [[-0.558, 44.416], [-42.547, 28.621], [-38.175, 21.349], [-30.902, 25.721], [-0.38, 31.345], [28.036, 24.86], [35.282, 20.444], [39.698, 27.69]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55.285, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.143, "s": [{"i": [[10.553, 0], [2.209, 11.369], [0, 1.878], [-0.278, 1.595], [-10.875, 0], [-1.819, -11.93], [0, -1.48], [0.407, -1.911]], "o": [[-10.707, 0], [-0.342, -1.762], [0, -1.688], [2.027, -11.638], [11.059, 0], [0.215, 1.411], [0, 2.052], [-2.368, 11.124]], "v": [[-0.25, 37.876], [-22.119, 18], [-22.641, 12.529], [-22.218, 7.597], [-0.25, -12.818], [21.813, 8.187], [22.141, 12.529], [21.518, 18.488]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255.857, "s": [{"i": [[10.553, 0], [2.209, 11.369], [0, 1.878], [-0.278, 1.595], [-10.875, 0], [-1.819, -11.93], [0, -1.48], [0.407, -1.911]], "o": [[-10.707, 0], [-0.342, -1.762], [0, -1.688], [2.027, -11.638], [11.059, 0], [0.215, 1.411], [0, 2.052], [-2.368, 11.124]], "v": [[-0.25, 37.876], [-22.119, 18], [-22.641, 12.529], [-22.218, 7.597], [-0.25, -12.818], [21.813, 8.187], [22.141, 12.529], [21.518, 18.488]], "c": true}]}, {"t": 270, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 0.765, 0.188, 0.004, 0.501, 0.765, 0.188, 0.004, 1, 0.765, 0.188, 0.004], "ix": 9}}, "s": {"a": 0, "k": [-1, 49], "ix": 5}, "e": {"a": 0, "k": [-1, 8.072], "ix": 6}, "t": 1, "nm": "Gradient Fill 1289320", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.281, 0.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.702941176471, 0.238235294118, 0.110784313725, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 536, "st": 0, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "Mouth shadow", "parent": 37, "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.99}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-257.853, 341.941, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.976}, "o": {"x": 0.333, "y": 0.017}, "t": 2.57, "s": [-257.853, 302.895, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.982}, "o": {"x": 0.333, "y": 0.012}, "t": 5.143, "s": [-257.853, 326.239, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.974}, "o": {"x": 0.333, "y": 0.026}, "t": 7.713, "s": [-257.853, 279.202, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.979}, "o": {"x": 0.333, "y": 0.01}, "t": 10.287, "s": [-257.853, 312.109, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.02, "y": 1}, "t": 14.143, "s": [-257.853, 188.459, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23.143, "s": [-257.853, 188.095, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.802}, "o": {"x": 0.333, "y": 0}, "t": 30.857, "s": [-257.853, 341.941, 0], "to": [0, -22.067, 0], "ti": [0, 50.361, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.034}, "t": 55.285, "s": [-257.853, 285.698, 0], "to": [0, -23.76, 0], "ti": [0, 0.548, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 65.572, "s": [-257.853, 147.07, 0], "to": [0, -1.709, 0], "ti": [0, -48.718, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 86.143, "s": [-257.853, 331.685, 0], "to": [0, 48.718, 0], "ti": [0, -17.949, 0]}, {"t": 104.142578125, "s": [-257.853, 439.377, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23.143, "s": [358.974, 358.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 65.572, "s": [358.974, 358.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 86.143, "s": [615.385, 615.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 104.143, "s": [615.385, 615.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255.857, "s": [615.385, 615.385, 100]}, {"t": 270, "s": [358.974, 358.974, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.143, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[12.2, 0.063], [11.417, 16.398], [-3.216, 0.801], [-2.236, -2.445], [-8.796, 0.071], [-7.558, 6.23], [-3.217, -0.779], [1.672, -2.86]], "o": [[-11.375, -0.058], [-1.893, -2.719], [3.218, -0.799], [6.38, 6.977], [9.429, -0.076], [2.557, -2.108], [3.221, 0.782], [-9.399, 16.079]], "v": [[-0.558, 44.416], [-42.547, 28.621], [-38.175, 21.349], [-30.902, 25.721], [-0.38, 31.345], [28.036, 24.86], [35.282, 20.444], [39.698, 27.69]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55.285, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.143, "s": [{"i": [[10.553, 0], [2.209, 11.369], [0, 1.878], [-0.278, 1.595], [-10.875, 0], [-1.819, -11.93], [0, -1.48], [0.407, -1.911]], "o": [[-10.707, 0], [-0.342, -1.762], [0, -1.688], [2.027, -11.638], [11.059, 0], [0.215, 1.411], [0, 2.052], [-2.368, 11.124]], "v": [[-0.25, 37.876], [-22.119, 18], [-22.641, 12.529], [-22.218, 7.597], [-0.25, -12.818], [21.813, 8.187], [22.141, 12.529], [21.518, 18.488]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255.857, "s": [{"i": [[10.553, 0], [2.209, 11.369], [0, 1.878], [-0.278, 1.595], [-10.875, 0], [-1.819, -11.93], [0, -1.48], [0.407, -1.911]], "o": [[-10.707, 0], [-0.342, -1.762], [0, -1.688], [2.027, -11.638], [11.059, 0], [0.215, 1.411], [0, 2.052], [-2.368, 11.124]], "v": [[-0.25, 37.876], [-22.119, 18], [-22.641, 12.529], [-22.218, 7.597], [-0.25, -12.818], [21.813, 8.187], [22.141, 12.529], [21.518, 18.488]], "c": true}]}, {"t": 270, "s": [{"i": [[23.062, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.104], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-0.022, 49.773], [-47.904, 16.479], [-43.532, 9.206], [-36.259, 13.578], [-0.022, 37.773], [36.25, 13.432], [43.496, 9.016], [47.912, 16.262]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.281, 0.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 536, "st": 0, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 3, "nm": "Five senses up and down controller", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123.43, "s": [256, 256, 0], "to": [0, 3, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [256, 274, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145.287, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155.57, "s": [256, 274, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165.857, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.145, "s": [256, 274, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185.145, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.857, "s": [256, 274, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200.572, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.002, "s": [256, 274, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214.715, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.143, "s": [256, 274, 0], "to": [0, 0, 0], "ti": [0, 3, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231.43, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 243, "s": [256, 256, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\ne = 0.7;\ng = 5000;\nnMax = 9;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n    vl = length(v);\n    if ($bm_isInstanceOfArray(value)) {\n        vu = vl > 0 ? normalize(v) : [\n            0,\n            0,\n            0\n        ];\n    } else {\n        vu = v < 0 ? -1 : 1;\n    }\n    tCur = 0;\n    segDur = $bm_div($bm_mul(2, vl), g);\n    tNext = segDur;\n    nb = 1;\n    while (tNext < t && nb <= nMax) {\n        vl *= e;\n        segDur *= e;\n        tCur = tNext;\n        tNext = $bm_sum(tNext, segDur);\n        nb++;\n    }\n    if (nb <= nMax) {\n        delta = $bm_sub(t, tCur);\n        $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n    } else {\n        $bm_rt = value;\n    }\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [19.5, 19.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 3, "nm": "Five senses graphics controller", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.309, "s": [0, 184.615, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 59.34, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83.078, "s": [0, 0, 0], "to": [-17.094, -36.752, 0], "ti": [-26.496, 32.479, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 95.143, "s": [-102.564, -220.513, 0], "to": [26.496, -32.479, 0], "ti": [5.983, -36.752, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 114.43, "s": [158.974, -194.872, 0], "to": [-5.983, 36.752, 0], "ti": [6.838, -32.479, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [-138.462, 0, 0], "to": [-6.838, 32.479, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 155.57, "s": [117.949, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 176.145, "s": [-138.462, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 192.857, "s": [117.949, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 207.002, "s": [-138.462, 0, 0], "to": [0, 0, 0], "ti": [-23.077, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 221.143, "s": [117.949, 0, 0], "to": [23.077, 0, 0], "ti": [19.658, -28.205, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 231.43, "s": [0, 0, 0], "to": [-19.658, 28.205, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 243, "s": [0, 169.231, 0], "to": [0, 0, 0], "ti": [0, 28.205, 0]}, {"t": 268.712890625, "s": [0, 0, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-256.41, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar e, g, nMax, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, nb, delta;\ne = 0.7;\ng = 5000;\nnMax = 9;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), e);\n    vl = length(v);\n    if ($bm_isInstanceOfArray(value)) {\n        vu = vl > 0 ? normalize(v) : [\n            0,\n            0,\n            0\n        ];\n    } else {\n        vu = v < 0 ? -1 : 1;\n    }\n    tCur = 0;\n    segDur = $bm_div($bm_mul(2, vl), g);\n    tNext = segDur;\n    nb = 1;\n    while (tNext < t && nb <= nMax) {\n        vl *= e;\n        segDur *= e;\n        tCur = tNext;\n        tNext = $bm_sum(tNext, segDur);\n        nb++;\n    }\n    if (nb <= nMax) {\n        delta = $bm_sub(t, tCur);\n        $bm_rt = $bm_sum(value, $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(g, delta), 2))));\n    } else {\n        $bm_rt = value;\n    }\n} else\n    $bm_rt = value;"}}, "ao": 0, "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "Left red", "parent": 39, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-93.933, -2.748, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-93.933, -2.748, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, 1.039], [0, 0], [1.039, -1.039], [0, 0], [-1.039, -1.039], [0, 0], [-1.039, 1.039], [0, 0]], "o": [[0, 0], [-1.039, -1.039], [0, 0], [-1.039, 1.039], [0, 0], [1.039, 1.039], [0, 0], [1.039, -1.039]], "v": [[-71.807, -12.847], [-71.807, -12.847], [-75.569, -12.847], [-92.006, 3.589], [-92.006, 7.351], [-92.006, 7.351], [-88.243, 7.351], [-71.807, -9.085]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, -1.039], [0, 0], [-1.039, -1.039], [-1.039, 1.039], [0, 0], [1.039, 1.039]], "o": [[0, 0], [-1.039, 1.039], [1.039, 1.039], [0, 0], [1.039, -1.039], [-1.039, -1.039]], "v": [[-99.623, -12.847], [-116.059, 3.589], [-116.059, 7.351], [-112.297, 7.351], [-95.861, -9.085], [-95.861, -12.847]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 374, "st": 0, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 4, "nm": "Right red", "parent": 37, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [245.812, 296.164, 0], "to": [0, -27.35, 0], "ti": [0, -11.966, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.734, "s": [245.812, 132.061, 0], "to": [0, 11.966, 0], "ti": [0, -27.35, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.605, "s": [245.812, 367.959, 0], "to": [0, 13.327, 0], "ti": [0, 19.108, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44.309, "s": [245.812, 276.517, 0], "to": [0, -20.105, 0], "ti": [0, 6.135, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59.34, "s": [245.812, 296.164, 0], "to": [0, -11.966, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68.045, "s": [245.812, 152.574, 0], "to": [0, 0, 0], "ti": [0, 6.135, 0]}, {"t": 83.078125, "s": [245.812, 296.164, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [101.933, -2.748, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [512.821, 512.821, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, 1.039], [1.039, -1.039], [0, 0], [-1.039, -1.039], [-1.039, 1.039], [0, 0]], "o": [[-1.039, -1.039], [0, 0], [-1.039, 1.039], [1.039, 1.039], [0, 0], [1.039, -1.039]], "v": [[124.06, -12.847], [120.297, -12.847], [103.861, 3.589], [103.861, 7.351], [107.623, 7.351], [124.06, -9.085]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, -1.039], [0, 0], [-1.039, -1.039], [-1.039, 1.039], [0, 0], [1.039, 1.039]], "o": [[0, 0], [-1.039, 1.039], [1.039, 1.039], [0, 0], [1.039, -1.039], [-1.039, -1.039]], "v": [[96.243, -12.847], [79.807, 3.589], [79.807, 7.351], [83.57, 7.351], [100.006, -9.085], [100.006, -12.847]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 536, "st": 0, "bm": 0}, {"ddd": 0, "ind": 40, "ty": 4, "nm": "Real blush", "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [255, 258, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-196.048, -51.393, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [288, 84, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -36.364], [36.364, 0], [0, 36.364], [-36.364, 0]], "o": [[0, 36.364], [-36.364, 0], [0, -36.364], [36.364, 0]], "v": [[-130.205, -51.393], [-196.048, 14.45], [-261.892, -51.393], [-196.048, -117.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.314, 0.447, 0.494, 1, 0.292, 0.359, 0.988, 1, 0.271, 0.271, 0, 0.5, 0.494, 0.25, 0.988, 0], "ix": 9}}, "s": {"a": 0, "k": [-195, -50.998], "ix": 5}, "e": {"a": 0, "k": [-135.986, -53.998], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 30", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 41, "ty": 4, "nm": "Face zoom controller", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 429.92, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 256, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 123.43, "s": [19.5, 19.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 135, "s": [19.5, 18.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 145.287, "s": [19.5, 19.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 155.57, "s": [19.5, 18.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 165.857, "s": [19.5, 19.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 176.145, "s": [19.5, 18.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 185.145, "s": [19.5, 19.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 192.857, "s": [19.5, 18.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 200.572, "s": [19.5, 19.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 207.002, "s": [19.5, 18.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 214.715, "s": [19.5, 19.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 221.143, "s": [19.5, 18.5, 100]}, {"t": 231.4296875, "s": [19.5, 19.5, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\n$bm_rt = transform.scale;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [512, 512], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 4, "nm": "\nFace", "parent": 41, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-6.231, -749.131, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-2.215, -14.081, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [512.821, 512.821, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0.793, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.215, -154.514]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.734, "s": [{"i": [[-3.844, -71.384], [83.615, 0], [-1.993, 83.591], [-83.615, 0]], "o": [[4.496, 83.494], [-83.615, 0], [1.452, -60.884], [83.615, 0]], "v": [[148.688, -24.616], [-2.43, 103.353], [-154.107, -24.616], [-2.36, -165.014]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.605, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-71.07, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [74.86, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.36, -139.264]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44.309, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[150.183, 33.884], [-1.43, 152.853], [-152.613, 33.884], [-1.215, -117.514]], "c": true}]}, {"t": 59.33984375, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.215, -154.514]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 0.64, 0.342, 1, 0.926, 0.371, 0.999, 1, 0.852, 0.102, 0, 1, 0.342, 0.5, 0.999, 0], "ix": 9}}, "s": {"a": 0, "k": [-3, -155], "ix": 5}, "e": {"a": 0, "k": [-3, 125.867], "ix": 6}, "t": 1, "nm": "Gradient Fill 50", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 43, "ty": 4, "nm": "Face highlight", "parent": 41, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.605, "s": [-1.103, -978.939, 0], "to": [0, 35.043, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44.309, "s": [-1.103, -768.683, 0], "to": [0, 0, 0], "ti": [0, 40.171, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59.34, "s": [-1.103, -978.939, 0], "to": [0, -40.171, 0], "ti": [0, 11.966, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62.504, "s": [-1.103, -1009.708, 0], "to": [0, -11.966, 0], "ti": [0, -5.128, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71.209, "s": [-1.103, -1050.734, 0], "to": [0, 5.128, 0], "ti": [0, -11.966, 0]}, {"t": 83.078125, "s": [-1.103, -978.939, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-2.215, -59.393, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [512.821, 512.821, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1.287, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.192, -151.053]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.734, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-92.07, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [88.93, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.43, -164.803]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[0, -73.595], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -70.095], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.471, -135.053]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43.715, "s": [{"i": [[0, -82.932], [83.615, 0], [0, 83.615], [-98.685, 1.127]], "o": [[0, 83.615], [-83.615, 0], [0, -82.694], [97.687, -1.115]], "v": [[158.742, 5.183], [-6.143, 1.701], [-160.827, 5.183], [-2.45, -150.407]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52.715, "s": [{"i": [[0, -83.325], [83.615, 0], [0, 83.615], [-90.005, 0.478]], "o": [[0, 83.615], [-83.615, 0], [0, -83.224], [89.582, -0.473]], "v": [[153.237, -0.339], [-7.355, 6.695], [-156.672, -0.339], [-2.38, -148.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59.34, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.215, -155.803]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70.715, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.034, -156.667]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91.285, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.36, -149.803]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102.855, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.221, -153.303]], "c": true}]}, {"t": 114.4296875, "s": [{"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.192, -151.053]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.342, 1, 0.926, 0.57, 0.999, 1, 0.852, 0.14, 0, 1, 0.342, 0.5, 0.999, 0], "ix": 9}}, "s": {"a": 0, "k": [-3, -156], "ix": 5}, "e": {"a": 0, "k": [-3, 36.82], "ix": 6}, "t": 1, "nm": "Gradient Fill 13", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}, {"ddd": 0, "ind": 44, "ty": 4, "nm": "Face stroke", "parent": 41, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 225.641, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23.734, "s": [512.821, 512.821, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 35.605, "s": [512.821, 487.179, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 44.309, "s": [512.821, 451.282, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 59.34, "s": [512.821, 512.821, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 71.209, "s": [512.821, 538.462, 100]}, {"t": 83.078125, "s": [512.821, 512.821, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.734, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-101.5, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [104.5, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -182]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.605, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"t": 59.33984375, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274516582, 0.541176497936, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 536, "st": 0, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "Yellow bottom", "parent": 41, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 225.641, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23.734, "s": [512.821, 512.821, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 35.605, "s": [512.821, 487.179, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 44.309, "s": [512.821, 512.821, 100]}, {"t": 59.33984375, "s": [512.821, 512.821, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0]], "o": [[0, 0]], "v": [[426, -96.428]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 2, "k": {"a": 0, "k": [0, 1, 1, 1, 1, 0, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "渐变填充 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.734, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -180.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.605, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44.309, "s": [{"i": [[0, -62], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -58], [93.888, 0]], "v": [[170, 7.5], [0, 168], [-170, 6.5], [1, -129.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51.428, "s": [{"i": [[0, -77.105], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -75], [93.888, 0]], "v": [[170, 3], [0, 168], [-170, 2.474], [0.553, -140.762]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, -86.804], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -85.915], [93.888, 0]], "v": [[170, 0.111], [0, 168], [-170, -0.112], [0.467, -165.838]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59.34, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65.572, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -186.687]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71.209, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -189.5]], "c": true}]}, {"t": 83.078125, "s": [{"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-1, -4], "to": [0, -7], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.734, "s": [-1, -46], "to": [0, 0], "ti": [0, -7]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.605, "s": [-1, -4], "to": [0, 0], "ti": [0, 0]}, {"t": 59.33984375, "s": [-1, -4]}], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-0.381, 160], "to": [0, -7.167], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.734, "s": [-0.381, 117], "to": [0, 0], "ti": [0, -7.167]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.605, "s": [-0.381, 160], "to": [0, 0], "ti": [0, 0]}, {"t": 59.33984375, "s": [-0.381, 160]}], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 14", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 522, "st": 0, "bm": 0}], "markers": []}