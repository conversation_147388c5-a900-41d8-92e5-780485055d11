{"v": "5.7.13", "fr": 60, "ip": 0, "op": 270, "w": 512, "h": 512, "nm": "我想开了 内", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "光效3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 101, "s": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 181, "s": [90]}, {"t": 193, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [252, 292, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [113.608, 81.965, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [658.156, 647.633], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 1, 0.994, 0, 0.24, 1, 0.996, 0.079, 0.481, 1, 0.998, 0.157, 0.74, 1, 0.932, 0.254, 1, 1, 0.867, 0.352, 0, 1, 0.24, 0.83, 0.48, 0.66, 0.6, 0.33, 0.72, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [341.844, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian9", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-22.703, -50.488], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [35.26, 42.51], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 270, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "形状图层 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [], "ip": 0, "op": 277, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "花瓣高光1", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -2.334, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [3.434, 15.277], [-2.811, 2.319], [-8.469, 10.983], [0, 0], [-5.859, -4.921], [3.67, -21.558], [19.425, -2.162]], "o": [[-18.948, -2.312], [-4.298, -19.119], [5.903, -4.869], [0, 0], [8.469, 10.983], [5.847, 4.911], [-2.665, 15.657], [0, 0]], "v": [[-0.816, 49.097], [-36.26, 23.216], [-21.588, -11.027], [-0.128, -29.071], [-0.128, -29.071], [21.43, -11.027], [36.26, 23.216], [0, 49.193]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [{"i": [[0, 0], [4.681, 14.942], [-8.806, 7.361], [-8.469, 10.983], [0, 0], [-5.87, -4.908], [7.122, -22.734], [19.425, -2.162]], "o": [[-18.948, -2.312], [-7.122, -22.734], [5.87, -4.908], [0, 0], [8.469, 10.983], [8.806, 7.361], [-4.748, 15.156], [0, 0]], "v": [[-0.816, 49.097], [-36.26, 23.216], [-21.509, -25.356], [0, -49.193], [0, -49.193], [21.509, -25.356], [36.26, 23.216], [0, 49.193]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [4.681, 14.942], [-8.806, 7.361], [-8.469, 10.983], [0, 0], [-5.87, -4.908], [7.122, -22.734], [19.425, -2.162]], "o": [[-18.948, -2.312], [-7.122, -22.734], [5.87, -4.908], [0, 0], [8.469, 10.983], [8.806, 7.361], [-4.748, 15.156], [0, 0]], "v": [[-0.816, 49.097], [-36.26, 23.216], [-21.509, -25.356], [0, -49.193], [0, -49.193], [21.509, -25.356], [36.26, 23.216], [0, 49.193]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [3.434, 15.277], [-2.811, 2.319], [-8.469, 10.983], [0, 0], [-5.859, -4.921], [3.67, -21.558], [19.425, -2.162]], "o": [[-18.948, -2.312], [-4.298, -19.119], [5.903, -4.869], [0, 0], [8.469, 10.983], [5.847, 4.911], [-2.665, 15.657], [0, 0]], "v": [[-0.816, 49.097], [-36.26, 23.216], [-21.588, -11.027], [-0.128, -29.071], [-0.128, -29.071], [21.43, -11.027], [36.26, 23.216], [0, 49.193]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-0.428, -44.8], "ix": 5}, "e": {"a": 0, "k": [-0.428, 46.548], "ix": 6}, "t": 1, "nm": "jianbian20hdasjksa", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状结合", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "花瓣1", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 74, "s": [0]}, {"t": 212, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [-26.242, 139.655, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-26.242, 139.655, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [-26.242, 139.655, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 212, "s": [-26.242, 139.655, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 62.318, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 49, "s": [82, 82, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"t": 212, "s": [82, 82, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [5.866, 19.208], [-12.249, 9.189], [-12.143, 13.914], [0, 0], [-8.491, -6.116], [8.672, -25.513], [27.852, -2.739]], "o": [[-27.167, -2.929], [-6.861, -22.464], [8.37, -6.279], [0, 0], [12.143, 13.914], [10.217, 7.36], [-6.555, 19.287], [0, 0]], "v": [[-1.171, 62.197], [-51.99, 29.41], [-30.748, -18.402], [-0.24, -42.348], [-0.24, -42.348], [30.932, -18.402], [51.99, 29.41], [0, 62.318]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [{"i": [[0, 0], [6.712, 18.929], [-12.626, 9.325], [-12.143, 13.914], [0, 0], [-8.417, -6.217], [10.212, -28.799], [27.852, -2.739]], "o": [[-27.167, -2.929], [-10.212, -28.799], [8.417, -6.217], [0, 0], [12.143, 13.914], [12.626, 9.325], [-6.808, 19.199], [0, 0]], "v": [[-1.171, 62.197], [-51.99, 29.41], [-30.84, -32.121], [0, -62.318], [0, -62.318], [30.84, -32.121], [51.99, 29.41], [0, 62.318]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [6.712, 18.929], [-12.626, 9.325], [-12.143, 13.914], [0, 0], [-8.417, -6.217], [10.212, -28.799], [27.852, -2.739]], "o": [[-27.167, -2.929], [-10.212, -28.799], [8.417, -6.217], [0, 0], [12.143, 13.914], [12.626, 9.325], [-6.808, 19.199], [0, 0]], "v": [[-1.171, 62.197], [-51.99, 29.41], [-30.84, -32.121], [0, -62.318], [0, -62.318], [30.84, -32.121], [51.99, 29.41], [0, 62.318]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [5.866, 19.208], [-12.249, 9.189], [-12.143, 13.914], [0, 0], [-8.491, -6.116], [8.672, -25.513], [27.852, -2.739]], "o": [[-27.167, -2.929], [-6.861, -22.464], [8.37, -6.279], [0, 0], [12.143, 13.914], [10.217, 7.36], [-6.555, 19.287], [0, 0]], "v": [[-1.171, 62.197], [-51.99, 29.41], [-30.748, -18.402], [-0.24, -42.348], [-0.24, -42.348], [30.932, -18.402], [51.99, 29.41], [0, 62.318]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.275, 0.275, 0.5, 1, 0.575, 0.586, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [1, 70.5], "ix": 5}, "e": {"a": 0, "k": [1.742, -58.337], "ix": 6}, "t": 1, "nm": "jianbian802", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状结合", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "花瓣高光2", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.057, 19.146, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [43.061, 24.17, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [7.959, 5.333], [1.831, 2.176], [2.292, -6.595], [-5.595, -3.259], [-7.17, -0.253], [-3.432, 1.122]], "o": [[-6.681, -8.338], [-6.235, -4.178], [-4.35, 4.081], [-4.858, 13.978], [9.855, 5.74], [11.568, 0.408], [0, 0]], "v": [[16.787, -9.468], [-5.113, -26.352], [-17.221, -34.844], [-31.372, -16.462], [-17.405, 14.987], [9.617, 21.478], [36.687, 19.006]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [{"i": [[0, 0], [9.277, 2.394], [1.689, 0.751], [2.859, -6.369], [-16.434, -15.413], [-10.962, -0.079], [-11.351, 7.421]], "o": [[-6.681, -8.338], [-9.277, -2.394], [-1.863, 5.757], [-3.55, 7.91], [7.689, 7.211], [14.544, 0.105], [0, 0]], "v": [[7.528, -14.329], [-16.408, -30.428], [-32.857, -35.146], [-39.939, -16.957], [-30.937, 24.075], [4.219, 35.143], [43.061, 24.17]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [9.277, 2.394], [1.689, 0.751], [2.859, -6.369], [-16.434, -15.413], [-10.962, -0.079], [-11.351, 7.421]], "o": [[-6.681, -8.338], [-9.277, -2.394], [-1.863, 5.757], [-3.55, 7.91], [7.689, 7.211], [14.544, 0.105], [0, 0]], "v": [[7.528, -14.329], [-16.408, -30.428], [-32.857, -35.146], [-39.939, -16.957], [-30.937, 24.075], [4.219, 35.143], [43.061, 24.17]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [7.959, 5.333], [1.831, 2.176], [2.292, -6.595], [-5.595, -3.259], [-7.17, -0.253], [-3.432, 1.122]], "o": [[-6.681, -8.338], [-6.235, -4.178], [-4.35, 4.081], [-4.858, 13.978], [9.855, 5.74], [11.568, 0.408], [0, 0]], "v": [[16.787, -9.468], [-5.113, -26.352], [-17.221, -34.844], [-31.372, -16.462], [-17.405, 14.987], [9.617, 21.478], [36.687, 19.006]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-80.428, -75.3], "ix": 5}, "e": {"a": 0, "k": [45.572, 31.048], "ix": 6}, "t": 1, "nm": "gaoguanghdsajdhasj", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "花瓣2", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [-0.523]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [-0.999]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [-1.42]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [-1.778]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [-2.069]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [-2.289]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [-2.438]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [-2.517]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [-2.529]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [-2.476]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [-2.366]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [-2.203]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [-1.996]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [-1.753]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [-1.482]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [-1.192]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [-0.89]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [-0.586]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [-0.287]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0.268]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0.513]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [0.729]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0.913]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [1.062]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [1.175]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [1.252]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [1.293]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [1.298]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [1.271]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [1.215]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 106, "s": [1.131]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [1.025]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0.9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [0.761]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0.612]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [0.457]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [0.301]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [0.147]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [-0.138]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [-0.263]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [-0.374]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [-0.545]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [-0.603]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [-0.643]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122, "s": [-0.664]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [-0.667]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [-0.653]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [-0.624]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126, "s": [-0.581]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127, "s": [-0.526]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [-0.462]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [-0.391]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [-0.314]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [-0.235]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [-0.154]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [-0.076]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [0.071]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [0.135]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137, "s": [0.192]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [0.241]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 139, "s": [0.28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [0.31]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [0.33]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 142, "s": [0.341]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143, "s": [0.342]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 144, "s": [0.335]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 145, "s": [0.32]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 146, "s": [0.298]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 147, "s": [0.27]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148, "s": [0.237]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [0.201]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [0.161]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 151, "s": [0.12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 152, "s": [0.079]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 153, "s": [0.039]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 154, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 155, "s": [-0.036]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 156, "s": [-0.069]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 157, "s": [-0.099]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 158, "s": [-0.124]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159, "s": [-0.144]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 160, "s": [-0.159]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 161, "s": [-0.169]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 162, "s": [-0.175]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163, "s": [-0.176]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [-0.172]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [-0.164]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 166, "s": [-0.153]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [-0.139]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [-0.122]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 169, "s": [-0.103]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [-0.083]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171, "s": [-0.062]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [-0.041]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 173, "s": [-0.02]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 174, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 175, "s": [0.019]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 176, "s": [0.036]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 177, "s": [0.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [0.063]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 179, "s": [0.074]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0.082]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 181, "s": [0.087]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 182, "s": [0.09]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 183, "s": [0.09]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 184, "s": [0.088]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [0.084]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 186, "s": [0.079]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 188, "s": [0.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 189, "s": [0.164]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 190, "s": [0.358]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 191, "s": [0.617]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 192, "s": [0.936]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 193, "s": [1.306]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 194, "s": [1.722]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 195, "s": [2.175]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 196, "s": [2.659]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 197, "s": [3.168]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 198, "s": [3.694]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 199, "s": [4.23]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 200, "s": [4.77]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 201, "s": [5.306]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 202, "s": [5.832]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 203, "s": [6.341]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 204, "s": [6.825]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 205, "s": [7.278]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 206, "s": [7.694]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [8.064]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 208, "s": [8.383]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 209, "s": [8.642]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 210, "s": [8.836]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 211, "s": [8.958]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 213, "s": [9.012]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 214, "s": [9.024]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 215, "s": [9.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 216, "s": [9.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 217, "s": [9.049]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 218, "s": [9.054]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 219, "s": [9.057]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220, "s": [9.059]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 221, "s": [9.06]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 222, "s": [9.058]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 223, "s": [9.056]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [9.052]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 225, "s": [9.047]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [9.041]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 227, "s": [9.035]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 228, "s": [9.028]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [9.021]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 230, "s": [9.014]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 231, "s": [9.007]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232, "s": [9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [8.994]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [8.988]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 235, "s": [8.983]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 236, "s": [8.978]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [8.975]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [8.972]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 239, "s": [8.97]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 240, "s": [8.97]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [8.969]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 242, "s": [8.97]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 243, "s": [8.971]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 244, "s": [8.973]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [8.976]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 246, "s": [8.979]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [8.982]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 248, "s": [8.986]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [8.989]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 250, "s": [8.993]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 251, "s": [8.997]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [9.003]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 254, "s": [9.006]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [9.009]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 256, "s": [9.011]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257, "s": [9.013]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 258, "s": [9.014]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 259, "s": [9.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 260, "s": [9.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 261, "s": [9.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [9.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 263, "s": [9.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 264, "s": [9.014]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 265, "s": [9.012]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [9.011]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 267, "s": [9.009]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [9.007]}, {"t": 269, "s": [9.006]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [-3.146, 130.96, 0], "to": [-4.417, -2.417, 0], "ti": [4.417, 2.417, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-29.646, 116.46, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-29.646, 116.46, 0], "to": [4.417, 2.417, 0], "ti": [-4.417, -2.417, 0]}, {"t": 212, "s": [-3.146, 130.96, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [61.107, 34.098, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 49, "s": [82, 82, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"t": 212, "s": [82, 82, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [9.855, 5.023], [2.088, 2.965], [6.859, -9.11], [-22.601, -21.304], [-15.774, -0.352], [-16.846, 9.829]], "o": [[-9.046, -11.476], [-12.404, -6.322], [-5.832, 5.58], [-7.261, 9.644], [10.575, 9.968], [20.927, 0.467], [0, 0]], "v": [[32.509, -2.394], [-5.968, -35.715], [-24.871, -47.517], [-47.009, -26.208], [-43.18, 30.737], [4.449, 48.141], [61.107, 34.098]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [{"i": [[0, 0], [13.189, 3.459], [2.38, 1.058], [4.55, -8.589], [-22.601, -21.304], [-15.774, -0.352], [-16.846, 9.829]], "o": [[-9.046, -11.476], [-13.189, -3.459], [-3.075, 7.779], [-5.651, 10.667], [10.575, 9.968], [20.927, 0.467], [0, 0]], "v": [[12.594, -18.995], [-20.759, -41.398], [-44.113, -48.173], [-55.551, -23.621], [-45.398, 32.323], [4.449, 48.141], [61.107, 34.098]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [13.189, 3.459], [2.38, 1.058], [4.55, -8.589], [-22.601, -21.304], [-15.774, -0.352], [-16.846, 9.829]], "o": [[-9.046, -11.476], [-13.189, -3.459], [-3.075, 7.779], [-5.651, 10.667], [10.575, 9.968], [20.927, 0.467], [0, 0]], "v": [[12.594, -18.995], [-20.759, -41.398], [-44.113, -48.173], [-55.551, -23.621], [-45.398, 32.323], [4.449, 48.141], [61.107, 34.098]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [9.855, 5.023], [2.088, 2.965], [6.859, -9.11], [-22.601, -21.304], [-15.774, -0.352], [-16.846, 9.829]], "o": [[-9.046, -11.476], [-12.404, -6.322], [-5.832, 5.58], [-7.261, 9.644], [10.575, 9.968], [20.927, 0.467], [0, 0]], "v": [[32.509, -2.394], [-5.968, -35.715], [-24.871, -47.517], [-47.009, -26.208], [-43.18, 30.737], [4.449, 48.141], [61.107, 34.098]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.275, 0.275, 0.5, 1, 0.575, 0.586, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [15.5, 65], "ix": 5}, "e": {"a": 0, "k": [-45.758, -42.837], "ix": 6}, "t": 1, "nm": "jianbian8", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-10备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "花瓣阴影2", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 39, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.046, -20.287, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [-7.418, -9.122], [0, 0]], "o": [[0, 0], [5.549, 6.825], [0, 0]], "v": [[-15.783, -14.383], [-3.077, -19.749], [4.869, 7.244]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [{"i": [[0, 0], [-13.451, -15.084], [0, 0]], "o": [[0, 0], [16.559, 18.569], [0, 0]], "v": [[-21.485, -18.564], [13.158, -10.491], [15.918, 26.33]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [-13.451, -15.084], [0, 0]], "o": [[0, 0], [16.559, 18.569], [0, 0]], "v": [[-21.485, -18.564], [13.158, -10.491], [15.918, 26.33]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [-7.418, -9.122], [0, 0]], "o": [[0, 0], [5.549, 6.825], [0, 0]], "v": [[-15.783, -14.383], [-3.077, -19.749], [4.869, 7.244]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "花瓣高光3", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-34.054, 13.509, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-44.36, 17.561, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [-8.638, 4.143], [-0.418, 1.891], [-1.131, -7.785], [14.973, -9.003], [9.162, 0.276], [8.271, 3.966]], "o": [[17.549, -20.461], [6.563, -3.148], [3.427, 2.334], [1.145, 7.884], [-5.378, 3.234], [-14.538, -0.438], [0, 0]], "v": [[-27.498, -2.21], [10.921, -29.428], [20.093, -38.253], [29.709, -19.236], [10.16, 15.329], [-18.336, 23.752], [-44.36, 17.561]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [{"i": [[0, 0], [-9.484, 1.359], [-1.762, 0.56], [2.809, -15.513], [17.27, -3.306], [10.905, 1.127], [10.83, -0.01]], "o": [[10.777, -9.153], [9.484, -1.359], [1.218, 5.926], [-2.817, 15.559], [-9.993, 1.913], [-14.467, -1.495], [0, 0]], "v": [[-7.057, -19.546], [20.505, -31.414], [37.623, -33.043], [40.911, 0.814], [8.95, 32.607], [-29.711, 32.99], [-51.86, 31.311]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [-9.484, 1.359], [-1.762, 0.56], [2.809, -15.513], [17.27, -3.306], [10.905, 1.127], [10.83, -0.01]], "o": [[10.777, -9.153], [9.484, -1.359], [1.218, 5.926], [-2.817, 15.559], [-9.993, 1.913], [-14.467, -1.495], [0, 0]], "v": [[-7.057, -19.546], [20.505, -31.414], [37.623, -33.043], [40.911, 0.814], [8.95, 32.607], [-29.711, 32.99], [-51.86, 31.311]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [-8.638, 4.143], [-0.418, 1.891], [-1.131, -7.785], [14.973, -9.003], [9.162, 0.276], [8.271, 3.966]], "o": [[17.549, -20.461], [6.563, -3.148], [3.427, 2.334], [1.145, 7.884], [-5.378, 3.234], [-14.538, -0.438], [0, 0]], "v": [[-27.498, -2.21], [10.921, -29.428], [20.093, -38.253], [29.709, -19.236], [10.16, 15.329], [-18.336, 23.752], [-44.36, 17.561]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [73.072, -56.3], "ix": 5}, "e": {"a": 0, "k": [-25.928, 24.048], "ix": 6}, "t": 1, "nm": "gaoguangdhsajdhsa", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "花瓣3", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [-16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0.93]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [1.776]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [2.524]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [3.161]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [3.678]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [4.07]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [4.335]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [4.476]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [4.496]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [4.402]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [4.206]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [3.917]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [3.549]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [3.117]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [2.635]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [2.119]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [1.583]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [1.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [0.51]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [-0.477]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [-0.912]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [-1.296]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [-1.623]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [-1.888]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [-2.089]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [-2.226]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [-2.298]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [-2.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [-2.26]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [-2.159]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 106, "s": [-2.011]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [-1.822]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [-1.6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [-1.353]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [-1.088]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [-0.813]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [-0.535]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [-0.262]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [0.245]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [0.468]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [0.665]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0.833]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [0.969]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [1.073]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [1.143]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122, "s": [1.18]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [1.185]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [1.16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [1.109]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126, "s": [1.032]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127, "s": [0.936]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0.822]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [0.695]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [0.558]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [0.417]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [0.275]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [0.134]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [-0.126]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [-0.24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137, "s": [-0.342]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [-0.428]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 139, "s": [-0.498]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [-0.551]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [-0.587]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 142, "s": [-0.606]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143, "s": [-0.608]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 144, "s": [-0.596]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 145, "s": [-0.569]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 146, "s": [-0.53]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 147, "s": [-0.48]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148, "s": [-0.422]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [-0.357]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [-0.287]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 151, "s": [-0.214]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 152, "s": [-0.141]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 153, "s": [-0.069]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 154, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 155, "s": [0.065]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 156, "s": [0.123]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 157, "s": [0.175]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 158, "s": [0.22]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159, "s": [0.256]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 160, "s": [0.283]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 161, "s": [0.301]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 162, "s": [0.311]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163, "s": [0.312]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [0.306]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [0.292]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 166, "s": [0.272]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [0.247]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [0.217]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 169, "s": [0.183]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [0.147]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171, "s": [0.11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [0.072]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 173, "s": [0.035]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 174, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 175, "s": [-0.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 176, "s": [-0.063]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 177, "s": [-0.09]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [-0.113]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 179, "s": [-0.131]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [-0.145]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 181, "s": [-0.155]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 182, "s": [-0.16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 183, "s": [-0.16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 184, "s": [-0.157]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [-0.15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 186, "s": [-0.14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 188, "s": [-0.075]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 189, "s": [-0.291]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 190, "s": [-0.636]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 191, "s": [-1.098]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 192, "s": [-1.664]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 193, "s": [-2.322]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 194, "s": [-3.061]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 195, "s": [-3.867]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 196, "s": [-4.728]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 197, "s": [-5.632]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 198, "s": [-6.567]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 199, "s": [-7.52]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 200, "s": [-8.48]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 201, "s": [-9.433]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 202, "s": [-10.368]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 203, "s": [-11.272]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 204, "s": [-12.133]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 205, "s": [-12.939]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 206, "s": [-13.678]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [-14.336]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 208, "s": [-14.902]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 209, "s": [-15.364]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 210, "s": [-15.709]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 211, "s": [-15.925]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [-16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 213, "s": [-16.022]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 214, "s": [-16.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 215, "s": [-16.06]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 216, "s": [-16.075]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 217, "s": [-16.087]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 218, "s": [-16.096]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 219, "s": [-16.102]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220, "s": [-16.106]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 221, "s": [-16.106]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 222, "s": [-16.104]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 223, "s": [-16.099]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [-16.092]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 225, "s": [-16.084]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [-16.073]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 227, "s": [-16.062]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 228, "s": [-16.05]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [-16.037]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 230, "s": [-16.025]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 231, "s": [-16.012]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232, "s": [-16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [-15.989]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [-15.978]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 235, "s": [-15.969]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 236, "s": [-15.962]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [-15.955]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [-15.951]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 239, "s": [-15.948]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 240, "s": [-15.946]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [-15.946]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 242, "s": [-15.947]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 243, "s": [-15.949]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 244, "s": [-15.953]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [-15.957]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 246, "s": [-15.962]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [-15.968]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 248, "s": [-15.974]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [-15.981]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 250, "s": [-15.987]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 251, "s": [-15.994]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [-16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [-16.006]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 254, "s": [-16.011]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [-16.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 256, "s": [-16.02]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257, "s": [-16.023]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 258, "s": [-16.025]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 259, "s": [-16.027]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 260, "s": [-16.028]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 261, "s": [-16.028]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-16.027]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 263, "s": [-16.026]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 264, "s": [-16.024]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 265, "s": [-16.022]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [-16.019]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 267, "s": [-16.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [-16.013]}, {"t": 269, "s": [-16.01]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [-49.767, 130.692, 0], "to": [3.083, -4, 0], "ti": [-3.083, 4, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [-31.267, 106.692, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-31.267, 106.692, 0], "to": [-3.083, 4, 0], "ti": [3.083, -4, 0]}, {"t": 212, "s": [-49.767, 130.692, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-63.241, 21.942, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 49, "s": [82, 82, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"t": 212, "s": [82, 82, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [-11.728, 6.954], [-3.32, 3.029], [-3.889, -8.908], [25.399, -17.876], [15.664, 1.894], [15.278, 12.123]], "o": [[17.559, -11.609], [6.888, -4.084], [11.831, 12.131], [5.396, 12.362], [-11.884, 8.364], [-20.781, -2.512], [0, 0]], "v": [[-34.45, -7.414], [14.575, -37.02], [32.159, -49.947], [57.928, -18.785], [42.435, 35.322], [-9.154, 43.895], [-63.241, 21.942]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [{"i": [[0, 0], [-13.547, 1.549], [-2.507, 0.709], [-3.283, -9.149], [25.399, -17.876], [15.664, 1.894], [15.278, 12.123]], "o": [[10.585, -10.074], [13.547, -1.549], [1.938, 8.137], [4.078, 11.362], [-11.884, 8.364], [-20.781, -2.512], [0, 0]], "v": [[-7.674, -23.718], [28.524, -41.153], [52.604, -44.54], [60.436, -18.611], [42.435, 35.322], [-9.154, 43.895], [-63.241, 21.942]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [-13.547, 1.549], [-2.507, 0.709], [-3.283, -9.149], [25.399, -17.876], [15.664, 1.894], [15.278, 12.123]], "o": [[10.585, -10.074], [13.547, -1.549], [1.938, 8.137], [4.078, 11.362], [-11.884, 8.364], [-20.781, -2.512], [0, 0]], "v": [[-7.674, -23.718], [28.524, -41.153], [52.604, -44.54], [60.436, -18.611], [42.435, 35.322], [-9.154, 43.895], [-63.241, 21.942]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [-11.728, 6.954], [-3.32, 3.029], [-3.889, -8.908], [25.399, -17.876], [15.664, 1.894], [15.278, 12.123]], "o": [[17.559, -11.609], [6.888, -4.084], [11.831, 12.131], [5.396, 12.362], [-11.884, 8.364], [-20.781, -2.512], [0, 0]], "v": [[-34.45, -7.414], [14.575, -37.02], [32.159, -49.947], [57.928, -18.785], [42.435, 35.322], [-9.154, 43.895], [-63.241, 21.942]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.275, 0.275, 0.5, 1, 0.575, 0.586, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [-42.5, 15], "ix": 5}, "e": {"a": 0, "k": [50.742, -59.337], "ix": 6}, "t": 1, "nm": "jianbian703", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.734, 0.855], "ix": 2}, "a": {"a": 0, "k": [0.734, 0.855], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-10", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "花瓣阴影3", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 39, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-1.327, -22.409, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [15.746, -13.216], [0, 0]], "o": [[0, 0], [-19.057, 15.995], [0, 0]], "v": [[19.603, -15.936], [-19.144, -10.125], [-19.337, 24.348]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 276, "st": 23, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "花瓣4", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [-0.232]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [-0.444]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [-0.631]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [-0.79]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [-0.919]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [-1.017]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [-1.084]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [-1.119]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [-1.124]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [-1.101]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [-1.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [-0.979]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [-0.887]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [-0.779]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [-0.659]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [-0.53]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [-0.396]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [-0.26]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [-0.128]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0.119]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [0.228]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [0.324]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0.406]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0.472]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 106, "s": [0.522]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [0.556]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0.574]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [0.577]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0.565]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [0.54]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [0.503]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [0.456]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [0.4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [0.338]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [0.272]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [0.203]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0.134]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [0.065]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [-0.061]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122, "s": [-0.117]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [-0.166]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [-0.208]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [-0.242]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126, "s": [-0.268]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127, "s": [-0.286]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [-0.295]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [-0.296]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [-0.29]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [-0.277]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [-0.258]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [-0.234]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 134, "s": [-0.205]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [-0.174]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [-0.14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137, "s": [-0.104]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [-0.069]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 139, "s": [-0.034]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [0.031]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 142, "s": [0.06]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143, "s": [0.085]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 144, "s": [0.107]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 145, "s": [0.124]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 146, "s": [0.138]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 147, "s": [0.147]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148, "s": [0.151]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [0.152]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [0.149]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 151, "s": [0.142]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 152, "s": [0.133]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 153, "s": [0.12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 154, "s": [0.105]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 155, "s": [0.089]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 156, "s": [0.072]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 157, "s": [0.054]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 158, "s": [0.035]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159, "s": [0.017]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 160, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 161, "s": [-0.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 162, "s": [-0.031]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163, "s": [-0.044]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [-0.055]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [-0.064]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 166, "s": [-0.071]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [-0.075]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [-0.078]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 169, "s": [-0.078]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [-0.076]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171, "s": [-0.073]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [-0.068]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 173, "s": [-0.062]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 174, "s": [-0.054]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 175, "s": [-0.046]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 176, "s": [-0.037]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 177, "s": [-0.027]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [-0.018]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 179, "s": [-0.009]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 181, "s": [0.008]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 182, "s": [0.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 183, "s": [0.023]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 184, "s": [0.028]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [0.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 186, "s": [0.036]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 213, "s": [4.232]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 214, "s": [4.444]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 215, "s": [4.631]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 216, "s": [4.79]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 217, "s": [4.919]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 218, "s": [5.017]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 219, "s": [5.084]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220, "s": [5.119]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 221, "s": [5.124]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 222, "s": [5.101]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 223, "s": [5.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [4.979]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 225, "s": [4.887]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [4.779]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 227, "s": [4.659]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 228, "s": [4.53]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [4.396]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 230, "s": [4.26]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 231, "s": [4.128]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [3.881]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [3.772]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 235, "s": [3.676]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 236, "s": [3.594]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [3.528]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [3.478]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 239, "s": [3.444]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 240, "s": [3.426]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [3.423]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 242, "s": [3.435]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 243, "s": [3.46]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 244, "s": [3.497]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [3.544]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 246, "s": [3.6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [3.662]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 248, "s": [3.728]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [3.797]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 250, "s": [3.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 251, "s": [3.935]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [4.061]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 254, "s": [4.117]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [4.166]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 256, "s": [4.208]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257, "s": [4.242]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 258, "s": [4.268]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 259, "s": [4.286]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 260, "s": [4.295]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 261, "s": [4.296]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [4.29]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 263, "s": [4.277]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 264, "s": [4.258]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 265, "s": [4.234]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [4.205]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 267, "s": [4.174]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [4.14]}, {"t": 269, "s": [4.104]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [-37.135, 116.337, 0], "to": [-3.417, -3.583, 0], "ti": [3.417, 3.583, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [-57.635, 94.837, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-57.635, 94.837, 0], "to": [3.417, 3.583, 0], "ti": [-3.417, -3.583, 0]}, {"t": 212, "s": [-37.135, 116.337, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [21.17, 49.775, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 55, "s": [82, 82, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"t": 212, "s": [82, 82, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [6.389, 4.927], [1.612, 1.63], [10.25, -11.309], [-10.736, -6.92], [-33.193, -9.398]], "o": [[-3.399, -11.228], [-10.058, -7.757], [-5.941, 3.44], [-14.993, 16.542], [7.216, 4.651], [0, 0]], "v": [[32.35, -4.784], [18.332, -24.371], [4.616, -37.342], [-19.793, -17.739], [-21.039, 18.737], [30.335, 36.604]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [{"i": [[0, 0], [10.559, 6.602], [1.612, 1.63], [10.35, -10.696], [-4.586, -11.922], [-35.116, -13.469]], "o": [[-3.399, -11.228], [-10.559, -6.602], [-3.654, 5.678], [-15.525, 16.044], [3.057, 7.948], [0, 0]], "v": [[38.05, -10.683], [17.113, -37.427], [-1.143, -49.775], [-22.15, -25.215], [-36.09, 17.65], [21.17, 49.775]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [10.559, 6.602], [1.612, 1.63], [10.35, -10.696], [-4.586, -11.922], [-35.116, -13.469]], "o": [[-3.399, -11.228], [-10.559, -6.602], [-3.654, 5.678], [-15.525, 16.044], [3.057, 7.948], [0, 0]], "v": [[38.05, -10.683], [17.113, -37.427], [-1.143, -49.775], [-22.15, -25.215], [-36.09, 17.65], [21.17, 49.775]], "c": true}]}, {"t": 218, "s": [{"i": [[0, 0], [6.389, 4.927], [1.612, 1.63], [10.25, -11.309], [-10.736, -6.92], [-33.193, -9.398]], "o": [[-3.399, -11.228], [-10.058, -7.757], [-5.941, 3.44], [-14.993, 16.542], [7.216, 4.651], [0, 0]], "v": [[32.35, -4.784], [18.332, -24.371], [4.616, -37.342], [-19.793, -17.739], [-21.039, 18.737], [30.335, 36.604]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.275, 0.275, 0.5, 1, 0.575, 0.586, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [-2, 27], "ix": 5}, "e": {"a": 0, "k": [-1.758, -49.837], "ix": 6}, "t": 1, "nm": "jianbian823", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-12备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -13, "op": 281, "st": 29, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "花瓣阴影4", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 39, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [31.036, -27.539, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[0, 0], [-12.754, -5.145], [0, 0], [0, 0]], "o": [[0, 0], [0.825, 0.333], [0, 0], [0, 0]], "v": [[-25.014, -7.311], [3.553, -4.127], [-4.198, 7.535], [-11.367, 0.971]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [{"i": [[0, 0], [-12.133, -10.228], [0, 0], [0, 0]], "o": [[0, 0], [0.68, 0.573], [0, 0], [0, 0]], "v": [[-4.923, -6.016], [19.652, 2.955], [8.143, 9.766], [4.258, 4.378]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [-12.133, -10.228], [0, 0], [0, 0]], "o": [[0, 0], [0.68, 0.573], [0, 0], [0, 0]], "v": [[-4.923, -6.016], [19.652, 2.955], [8.143, 9.766], [4.258, 4.378]], "c": true}]}, {"t": 211, "s": [{"i": [[0, 0], [-12.754, -5.145], [0, 0], [0, 0]], "o": [[0, 0], [0.825, 0.333], [0, 0], [0, 0]], "v": [[-25.014, -7.311], [3.553, -4.127], [-4.198, 7.535], [-11.367, 0.971]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "花瓣5", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [-5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0.291]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [0.555]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [0.789]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [0.988]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [1.149]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [1.272]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [1.355]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [1.399]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [1.405]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [1.376]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [1.314]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [1.224]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [1.109]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0.974]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0.823]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0.662]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [0.495]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0.326]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [0.159]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [-0.149]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [-0.285]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [-0.405]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [-0.507]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [-0.59]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 106, "s": [-0.653]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [-0.696]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [-0.718]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [-0.721]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [-0.706]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [-0.675]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [-0.569]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [-0.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [-0.423]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [-0.34]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [-0.254]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [-0.167]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [-0.082]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [0.077]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122, "s": [0.146]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [0.208]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [0.26]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0.303]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126, "s": [0.335]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127, "s": [0.357]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0.369]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [0.37]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [0.363]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [0.346]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [0.323]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [0.292]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 134, "s": [0.257]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [0.217]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [0.175]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137, "s": [0.13]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [0.086]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 139, "s": [0.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [-0.039]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 142, "s": [-0.075]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143, "s": [-0.107]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 144, "s": [-0.134]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 145, "s": [-0.156]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 146, "s": [-0.172]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 147, "s": [-0.183]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148, "s": [-0.189]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [-0.19]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [-0.186]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 151, "s": [-0.178]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 152, "s": [-0.166]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 153, "s": [-0.15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 154, "s": [-0.132]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 155, "s": [-0.111]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 156, "s": [-0.09]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 157, "s": [-0.067]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 158, "s": [-0.044]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159, "s": [-0.022]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 160, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 161, "s": [0.02]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 162, "s": [0.039]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163, "s": [0.055]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [0.069]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [0.08]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 166, "s": [0.088]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [0.094]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [0.097]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 169, "s": [0.098]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [0.096]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171, "s": [0.091]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [0.085]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 173, "s": [0.077]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 174, "s": [0.068]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 175, "s": [0.057]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 176, "s": [0.046]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 177, "s": [0.034]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [0.023]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 179, "s": [0.011]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 181, "s": [-0.01]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 182, "s": [-0.02]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 183, "s": [-0.028]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 184, "s": [-0.035]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [-0.041]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 186, "s": [-0.045]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [-5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 213, "s": [-5.291]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 214, "s": [-5.555]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 215, "s": [-5.789]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 216, "s": [-5.988]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 217, "s": [-6.149]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 218, "s": [-6.272]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 219, "s": [-6.355]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220, "s": [-6.399]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 221, "s": [-6.405]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 222, "s": [-6.376]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 223, "s": [-6.314]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [-6.224]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 225, "s": [-6.109]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [-5.974]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 227, "s": [-5.823]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 228, "s": [-5.662]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [-5.495]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 230, "s": [-5.326]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 231, "s": [-5.159]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232, "s": [-5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [-4.851]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [-4.715]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 235, "s": [-4.595]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 236, "s": [-4.493]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [-4.41]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [-4.347]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 239, "s": [-4.304]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 240, "s": [-4.282]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [-4.279]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 242, "s": [-4.294]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 243, "s": [-4.325]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 244, "s": [-4.372]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [-4.431]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 246, "s": [-4.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [-4.577]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 248, "s": [-4.66]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [-4.746]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 250, "s": [-4.833]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 251, "s": [-4.918]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [-5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [-5.077]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 254, "s": [-5.146]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [-5.208]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 256, "s": [-5.26]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257, "s": [-5.303]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 258, "s": [-5.335]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 259, "s": [-5.357]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 260, "s": [-5.369]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 261, "s": [-5.37]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-5.363]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 263, "s": [-5.346]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 264, "s": [-5.323]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 265, "s": [-5.292]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [-5.257]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 267, "s": [-5.217]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [-5.175]}, {"t": 269, "s": [-5.13]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [-16.864, 115.45, 0], "to": [3.583, -3.417, 0], "ti": [-3.583, 3.417, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [4.636, 94.95, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [4.636, 94.95, 0], "to": [-3.583, 3.417, 0], "ti": [3.583, -3.417, 0]}, {"t": 212, "s": [-16.864, 115.45, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-20.882, 49.837, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 55, "s": [82, 82, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"t": 212, "s": [82, 82, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[0, 0], [-10.736, 6.309], [-4.434, 3.394], [-5.437, -7.744], [4.44, -9.648], [34.893, -14.283]], "o": [[0.98, -6.232], [5.947, -3.494], [6.193, 4.235], [5.859, 8.344], [-3.958, 8.6], [0, 0]], "v": [[-38.232, -9.008], [-20.376, -25.111], [-2.093, -36.938], [16.89, -19.258], [20.82, 16.506], [-20.882, 49.837]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [{"i": [[0, 0], [-10.522, 6.66], [-1.603, 1.639], [-10.41, -10.638], [4.519, -11.947], [35.041, -13.665]], "o": [[3.336, -11.247], [10.522, -6.66], [3.686, 5.657], [15.614, 15.957], [-3.013, 7.965], [0, 0]], "v": [[-38.099, -10.527], [-17.312, -37.387], [0.875, -49.837], [22.018, -25.394], [36.198, 17.393], [-20.882, 49.837]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 188, "s": [{"i": [[0, 0], [-10.522, 6.66], [-1.603, 1.639], [-10.41, -10.638], [4.519, -11.947], [35.041, -13.665]], "o": [[3.336, -11.247], [10.522, -6.66], [3.686, 5.657], [15.614, 15.957], [-3.013, 7.965], [0, 0]], "v": [[-38.099, -10.527], [-17.312, -37.387], [0.875, -49.837], [22.018, -25.394], [36.198, 17.393], [-20.882, 49.837]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [-10.736, 6.309], [-4.434, 3.394], [-5.437, -7.744], [4.44, -9.648], [34.893, -14.283]], "o": [[0.98, -6.232], [5.947, -3.494], [6.193, 4.235], [5.859, 8.344], [-3.958, 8.6], [0, 0]], "v": [[-38.232, -9.008], [-20.376, -25.111], [-2.093, -36.938], [16.89, -19.258], [20.82, 16.506], [-20.882, 49.837]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.275, 0.275, 0.5, 1, 0.575, 0.586, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [-2, 27], "ix": 5}, "e": {"a": 0, "k": [-1.758, -49.837], "ix": 6}, "t": 1, "nm": "jianbian821", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-12", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -13, "op": 281, "st": 29, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "花瓣阴影5", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 39, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-26.673, -32.394, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [{"i": [[0, 0], [19.551, -11.398], [0, 0], [0, 0]], "o": [[0, 0], [-0.768, 0.448], [0, 0], [0, 0]], "v": [[21.093, -0.906], [-7.224, 4.489], [3.504, 12.643], [8.371, 8.368]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [{"i": [[0, 0], [13.204, -11.534], [0, 0], [0, 0]], "o": [[0, 0], [-0.67, 0.585], [0, 0], [0, 0]], "v": [[4.752, -3.594], [-22.402, 5.705], [-9.329, 18.285], [-4.751, 7.892]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [13.204, -11.534], [0, 0], [0, 0]], "o": [[0, 0], [-0.67, 0.585], [0, 0], [0, 0]], "v": [[4.752, -3.594], [-22.402, 5.705], [-9.329, 18.285], [-4.751, 7.892]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [19.551, -11.398], [0, 0], [0, 0]], "o": [[0, 0], [-0.768, 0.448], [0, 0], [0, 0]], "v": [[21.093, -0.906], [-7.224, 4.489], [3.504, 12.643], [8.371, 8.368]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "花瓣6", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [0]}, {"t": 212, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [-26.242, 98.837, 0], "to": [0, -3.583, 0], "ti": [0, 3.583, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [-26.242, 77.337, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-26.242, 77.337, 0], "to": [0, 3.583, 0], "ti": [0, -3.583, 0]}, {"t": 212, "s": [-26.242, 98.837, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.024, 46.474, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [80, 80, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 82, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"t": 212, "s": [80, 80, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [{"i": [[0, 0], [6.223, 14.403], [-11.637, 7.141], [-12.912, 7.165], [0, 0], [-7.781, -4.723], [9.373, -21.988], [25.704, -2.152]], "o": [[-25.025, -2.186], [-9.48, -21.942], [7.758, -4.761], [0, 0], [13.026, 8.571], [11.672, 7.084], [-6.249, 14.659], [0, 0]], "v": [[-1.088, 47.432], [-47.96, 22.549], [-28.572, -15.992], [-0.872, -30.655], [-0.794, -30.733], [28.363, -16.131], [48.022, 22.314], [0.092, 47.53]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [{"i": [[0, 0], [6.223, 14.403], [-11.637, 7.141], [-11.183, 10.64], [0, 0], [-7.781, -4.723], [9.373, -21.988], [25.704, -2.152]], "o": [[-25.025, -2.186], [-9.48, -21.942], [7.758, -4.761], [0, 0], [11.235, 10.585], [11.672, 7.084], [-6.249, 14.659], [0, 0]], "v": [[-1.088, 47.432], [-47.96, 22.549], [-28.551, -24.43], [-0.14, -47.53], [-0.14, -47.53], [28.384, -24.569], [48.022, 22.314], [0.092, 47.53]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [6.223, 14.403], [-11.637, 7.141], [-11.183, 10.64], [0, 0], [-7.781, -4.723], [9.373, -21.988], [25.704, -2.152]], "o": [[-25.025, -2.186], [-9.48, -21.942], [7.758, -4.761], [0, 0], [11.235, 10.585], [11.672, 7.084], [-6.249, 14.659], [0, 0]], "v": [[-1.088, 47.432], [-47.96, 22.549], [-28.551, -24.43], [-0.14, -47.53], [-0.14, -47.53], [28.384, -24.569], [48.022, 22.314], [0.092, 47.53]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [6.223, 14.403], [-11.637, 7.141], [-12.912, 7.165], [0, 0], [-7.781, -4.723], [9.373, -21.988], [25.704, -2.152]], "o": [[-25.025, -2.186], [-9.48, -21.942], [7.758, -4.761], [0, 0], [13.026, 8.571], [11.672, 7.084], [-6.249, 14.659], [0, 0]], "v": [[-1.088, 47.432], [-47.96, 22.549], [-28.572, -15.992], [-0.872, -30.655], [-0.794, -30.733], [28.363, -16.131], [48.022, 22.314], [0.092, 47.53]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.835294127464, 0.247058823705, 0.247058823705, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.275, 0.275, 0.5, 1, 0.575, 0.586, 1, 1, 0.875, 0.898], "ix": 9}}, "s": {"a": 0, "k": [-1, 26.5], "ix": 5}, "e": {"a": 0, "k": [-1.758, -49.837], "ix": 6}, "t": 1, "nm": "gaoguangdhsajdhs2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状结合备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -16, "op": 283, "st": 31, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "荧光 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 73.867, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 86.41, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 91.426, "s": [86]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 95.605, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 105.639, "s": [100]}, {"t": 120, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.519}, "o": {"x": 0.333, "y": 0}, "t": 63, "s": [221.09, 270.952, 0], "to": [5.083, -19.417, 0], "ti": [12.765, 85.579, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.517}, "t": 104.666, "s": [251.59, 154.452, 0], "to": [-8.271, -55.453, 0], "ti": [6.458, 21.924, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 140.773, "s": [223.84, 57.702, 0], "to": [-10.078, -34.215, 0], "ti": [-0.167, 0.292, 0]}, {"t": 152.53125, "s": [232.34, -16.298, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 76.564, "s": [89.296, 89.296, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99.174, "s": [51.296, 51.296, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 129.018, "s": [89.296, 89.296, 100]}, {"t": 143.486328125, "s": [38.296, 38.296, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.976, 1, 0.438, 0.675, 0.988, 0.999, 0.499, 0.782, 1, 0.998, 0.561, 0.891, 1, 0.999, 0.78, 1, 1, 1, 1, 0, 1, 0.39, 1, 0.781, 1, 0.89, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "quan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 2, "k": {"a": 0, "k": [0, 1, 1, 1, 1, 0, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "渐变填充 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [32.099, 32.099], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [32.099, 32.099], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 281, "st": 23, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "荧光 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 73.852, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 84.648, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 97.104, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102.086, "s": [86]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 106.238, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 116.205, "s": [100]}, {"t": 128, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.557}, "o": {"x": 0.333, "y": 0}, "t": 73.852, "s": [221.09, 270.952, 0], "to": [-6.833, -36.583, 0], "ti": [-14.235, 36.579, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.547}, "t": 120.721, "s": [180.09, 51.452, 0], "to": [20.333, -52.25, 0], "ti": [17.667, 14.5, 0]}, {"t": 151.626953125, "s": [158.09, -59.548, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 87.418, "s": [89.296, 89.296, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 110.025, "s": [51.296, 51.296, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 139.869, "s": [89.296, 89.296, 100]}, {"t": 154.33984375, "s": [38.296, 38.296, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.976, 1, 0.438, 0.675, 0.988, 0.999, 0.499, 0.782, 1, 0.998, 0.561, 0.891, 1, 0.999, 0.78, 1, 1, 1, 1, 0, 1, 0.39, 1, 0.781, 1, 0.89, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "dhaskdghsa", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 2, "k": {"a": 0, "k": [0, 1, 1, 1, 1, 0, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "渐变填充 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [32.099, 32.099], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [32.099, 32.099], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 281, "st": 23, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "荧光", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 86.514, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 97.137, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 109.396, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 114.301, "s": [86]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.387, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 128.193, "s": [100]}, {"t": 142, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.443}, "o": {"x": 0.333, "y": 0}, "t": 91.035, "s": [295.09, 271.952, 0], "to": [9.333, -15.667, 0], "ti": [5.5, 37.75, 0]}, {"i": {"x": 0.667, "y": 0.698}, "o": {"x": 0.333, "y": 0.322}, "t": 118.207, "s": [351.09, 177.952, 0], "to": [-5.5, -37.75, 0], "ti": [9.667, 35.417, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.575}, "t": 140.652, "s": [262.09, 45.452, 0], "to": [-9.667, -35.417, 0], "ti": [-5.167, 13.333, 0]}, {"t": 164.287109375, "s": [293.09, -34.548, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100.078, "s": [89.296, 89.296, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 122.688, "s": [51.296, 51.296, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 152.531, "s": [89.296, 89.296, 100]}, {"t": 167, "s": [38.296, 38.296, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.976, 1, 0.438, 0.675, 0.988, 0.999, 0.499, 0.782, 1, 0.998, 0.561, 0.891, 1, 0.999, 0.78, 1, 1, 1, 1, 0, 1, 0.39, 1, 0.781, 1, 0.89, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "quanquandshadjah", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 2, "k": {"a": 0, "k": [0, 1, 1, 1, 1, 0, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "渐变填充 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [32.099, 32.099], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [32.099, 32.099], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 281, "st": 23, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "光效", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.5, 20.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [239.936, 239.936, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.504, "y": 0.469}, "t": 60, "s": [{"i": [[11.5, -0.75], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-16.246, 1.06], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10, -34.5], [-21.5, -27.25], [-15.25, 7], [-7.75, 7.75], [0.5, -28.5]], "c": true}]}, {"i": {"x": 0.265, "y": 1}, "o": {"x": 0.167, "y": 0.201}, "t": 68, "s": [{"i": [[11.5, -0.75], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-16.246, 1.06], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10, -85.25], [-21.5, -78], [-15.25, 7], [-7.75, 7.75], [0.5, -79.25]], "c": true}]}, {"i": {"x": 0.265, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 120, "s": [{"i": [[79.725, -10.641], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-70.439, 9.401], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-14.083, -119.082], [-95.252, -50.914], [-30.75, 29], [9.75, 28.5], [90.338, -58.163]], "c": true}]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.735, "y": 0}, "t": 187, "s": [{"i": [[79.725, -10.641], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-70.439, 9.401], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-14.083, -119.082], [-95.252, -50.914], [-30.75, 29], [9.75, 28.5], [90.338, -58.163]], "c": true}]}, {"i": {"x": 0.496, "y": 0.531}, "o": {"x": 0.167, "y": 0.201}, "t": 199, "s": [{"i": [[11.5, -0.75], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-16.246, 1.06], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10, -85.25], [-21.5, -78], [-15.25, 7], [-7.75, 7.75], [0.5, -79.25]], "c": true}]}, {"t": 207, "s": [{"i": [[11.5, -0.75], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-16.246, 1.06], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10, -34.5], [-21.5, -27.25], [-15.25, 7], [-7.75, 7.75], [0.5, -28.5]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 69, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 187, "s": [100]}, {"t": 206, "s": [0]}], "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 1, 0.998, 0, 0.178, 1, 0.998, 0, 0.357, 1, 0.998, 0, 0.678, 1, 0.932, 0.176, 1, 1, 0.867, 0.352, 0, 1, 0.178, 0.83, 0.356, 0.66, 0.538, 0.33, 0.72, 0], "ix": 9}}, "s": {"a": 0, "k": [-8.5, -4], "ix": 5}, "e": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [-8, -20], "to": [0, -8.417], "ti": [0, 8.417]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [-8, -70.5], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-8, -70.5], "to": [0, 8.417], "ti": [0, -8.417]}, {"t": 201, "s": [-8, -20]}], "ix": 6}, "t": 1, "nm": "jianbian40", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -2, "op": 284, "st": 32, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "树叶阴影1", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 54, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [12.761, 0.113, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [{"i": [[0, 0], [-13.955, 9.118], [0, 0]], "o": [[0, 0], [20.577, -13.445], [0, 0]], "v": [[-17.508, 3.399], [15.878, 2.297], [8.359, -6.231]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[0, 0], [-22.682, 9.471], [0, 0]], "o": [[0, 0], [22.682, -9.471], [0, 0]], "v": [[-24.508, 8.399], [15.878, 2.297], [9.359, -8.981]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [-22.682, 9.471], [0, 0]], "o": [[0, 0], [22.682, -9.471], [0, 0]], "v": [[-24.508, 8.399], [15.878, 2.297], [9.359, -8.981]], "c": true}]}, {"t": 203, "s": [{"i": [[0, 0], [-13.955, 9.118], [0, 0]], "o": [[0, 0], [20.577, -13.445], [0, 0]], "v": [[-17.508, 3.399], [15.878, 2.297], [8.359, -6.231]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.317647069693, 0.058823529631, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "树叶1", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-78.184, 131.722, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[6.943, 5.269], [21.736, -18.469], [9.426, -7.631], [-24.395, 5.168]], "o": [[-9.295, -7.053], [-7.861, 6.68], [8.676, 7.619], [39.274, -8.32]], "v": [[42.491, -16.24], [-11.955, -13.526], [-31.992, 6.535], [20.829, 21.61]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [{"i": [[8.801, 7.661], [24.354, -14.846], [13.676, -4.256], [-25.895, 6.668]], "o": [[-8.801, -7.661], [-16.236, 9.898], [16.676, 16.244], [39.928, -10.281]], "v": [[41.991, -16.99], [-13.455, -13.401], [-49.492, 11.535], [19.579, 27.61]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[8.801, 7.661], [24.354, -14.846], [13.676, -4.256], [-25.895, 6.668]], "o": [[-8.801, -7.661], [-16.236, 9.898], [16.676, 16.244], [39.928, -10.281]], "v": [[41.991, -16.99], [-13.455, -13.401], [-49.492, 11.535], [19.579, 27.61]], "c": true}]}, {"t": 203, "s": [{"i": [[6.943, 5.269], [21.736, -18.469], [9.426, -7.631], [-24.395, 5.168]], "o": [[-9.295, -7.053], [-7.861, 6.68], [8.676, 7.619], [39.274, -8.32]], "v": [[42.491, -16.24], [-11.955, -13.526], [-31.992, 6.535], [20.829, 21.61]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.509803950787, 0.117647059262, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.658823549747, 0.368627458811, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-8", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "树叶阴影2", "parent": 25, "sr": 1, "ks": {"o": {"a": 0, "k": 54, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-5.187, 0.932, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[0, 0], [22.391, 10.142], [0, 0], [0, 0]], "o": [[0, 0], [-22.391, -10.142], [0, 0], [0, 0]], "v": [[21.063, 6.757], [-11.623, 5.706], [-9.272, -4.623], [2.504, 3.62]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [{"i": [[0, 0], [22.391, 10.142], [0, 0], [0, 0]], "o": [[0, 0], [-22.391, -10.142], [0, 0], [0, 0]], "v": [[24.313, 9.007], [-15.873, 1.706], [-9.022, -9.373], [16.177, 4.521]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [22.391, 10.142], [0, 0], [0, 0]], "o": [[0, 0], [-22.391, -10.142], [0, 0], [0, 0]], "v": [[24.313, 9.007], [-15.873, 1.706], [-9.022, -9.373], [16.177, 4.521]], "c": true}]}, {"t": 203, "s": [{"i": [[0, 0], [22.391, 10.142], [0, 0], [0, 0]], "o": [[0, 0], [-22.391, -10.142], [0, 0], [0, 0]], "v": [[21.063, 6.757], [-11.623, 5.706], [-9.272, -4.623], [2.504, 3.62]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.317647069693, 0.058823529631, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "树叶2", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [16.647, 128.035, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[-7.244, 7.318], [-21.217, -23.129], [-7.578, -4.472], [27.271, 10.671]], "o": [[7.222, -7.296], [9.425, 10.274], [-14.328, 16.028], [-32.743, -12.813]], "v": [[-37.581, -23.937], [12.609, -19.104], [40.469, 5.239], [-27.13, 21.346]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [{"i": [[-8.822, 7.499], [-21.897, -18.502], [-13.884, -7.493], [27.09, 8.949]], "o": [[9.417, -8.004], [14.336, 12.049], [-20.134, 20.507], [-38.825, -12.826]], "v": [[-42.776, -18.978], [16.345, -18.326], [51.275, 10.76], [-24.949, 28.318]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[-8.822, 7.499], [-21.897, -18.502], [-13.884, -7.493], [27.09, 8.949]], "o": [[9.417, -8.004], [14.336, 12.049], [-20.134, 20.507], [-38.825, -12.826]], "v": [[-42.776, -18.978], [16.345, -18.326], [51.275, 10.76], [-24.949, 28.318]], "c": true}]}, {"t": 204, "s": [{"i": [[-7.244, 7.318], [-21.217, -23.129], [-7.578, -4.472], [27.271, 10.671]], "o": [[7.222, -7.296], [9.425, 10.274], [-14.328, 16.028], [-32.743, -12.813]], "v": [[-37.581, -23.937], [12.609, -19.104], [40.469, 5.239], [-27.13, 21.346]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.509803950787, 0.117647059262, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.658823549747, 0.368627458811, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.212, 0.197], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-8备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "形状图层 6", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-5.566, -116.441, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [-45.914, 6.175]], "o": [[0, 0], [0, 0], [26.25, -3.531]], "v": [[55.25, 122.25], [-23.5, 121.75], [30.25, 148.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96, "s": [{"i": [[0, 0], [0, 0], [-45.626, 8.035]], "o": [[0, 0], [0, 0], [39.75, -7]], "v": [[67.5, 121], [-31.75, 130.5], [31, 152.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [0, 0], [-45.626, 8.035]], "o": [[0, 0], [0, 0], [39.75, -7]], "v": [[67.5, 121], [-31.75, 130.5], [31, 152.5]], "c": true}]}, {"t": 258, "s": [{"i": [[0, 0], [0, 0], [-45.914, 6.175]], "o": [[0, 0], [0, 0], [26.25, -3.531]], "v": [[55.25, 122.25], [-23.5, 121.75], [30.25, 148.5]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.6, 0.366371693331, 0, 1], "ix": 4}, "o": {"a": 0, "k": 37, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 277, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "形状图层 5", "parent": 25, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-31.772, -114.005, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [-34.625, 1.469]], "o": [[0, 0], [0, 0], [40.325, -1.711]], "v": [[73.25, 121], [-14.25, 126.25], [28, 149.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96, "s": [{"i": [[0, 0], [0, 0], [-45.626, 8.035]], "o": [[0, 0], [0, 0], [39.75, -7]], "v": [[67.5, 121], [-31.75, 130.5], [31, 152.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [0, 0], [-45.626, 8.035]], "o": [[0, 0], [0, 0], [39.75, -7]], "v": [[67.5, 121], [-31.75, 130.5], [31, 152.5]], "c": true}]}, {"t": 258, "s": [{"i": [[0, 0], [0, 0], [-34.625, 1.469]], "o": [[0, 0], [0, 0], [40.325, -1.711]], "v": [[73.25, 121], [-14.25, 126.25], [28, 149.25]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.6, 0.366371693331, 0, 1], "ix": 4}, "o": {"a": 0, "k": 37, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 277, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "形状图层 8", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-113.506, -48.057, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [60.25, -66.154, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-145, 145.009, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.603, -12.034]], "o": [[0, 0], [-21.246, 33.628]], "v": [[62.664, -73.388], [40.5, -62.879]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274569642, 0.541176470588, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [8]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 177, "s": [8]}, {"t": 187, "s": [0]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 270, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "形状图层 7", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [68.369, -47.38, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [60.25, -66.154, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [145, 142.744, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.603, -12.034]], "o": [[0, 0], [-21.246, 33.628]], "v": [[62.664, -73.388], [40.5, -62.879]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274569642, 0.541176470588, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [8]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 177, "s": [8]}, {"t": 187, "s": [0]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 270, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "右眼", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [44.813, -49.681, 0], "to": [-1.417, -5, 0], "ti": [1.417, 5, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 42, "s": [36.313, -79.681, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [36.313, -79.681, 0], "to": [1.417, 5, 0], "ti": [-1.417, -5, 0]}, {"t": 203, "s": [44.813, -49.681, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [100, 86, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 31, "s": [100, 17.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 36, "s": [100, 85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [100, 35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 191, "s": [100, 35, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 195, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 234, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 240, "s": [100, 85, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 245, "s": [100, 17.5, 100]}, {"t": 250, "s": [100, 86, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-14.002, 0], [0, -16.502], [14.002, 0], [0, 16.502]], "o": [[14.002, 0], [0, 16.502], [-14.002, 0], [0, -16.502]], "v": [[0, -29.88], [25.353, 0], [0, 29.88], [-25.353, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.322, 0.137, 0.008, 0.5, 0.415, 0.195, 0.022, 1, 0.508, 0.253, 0.037], "ix": 9}}, "s": {"a": 0, "k": [-24.305, -79.992], "ix": 5}, "e": {"a": 0, "k": [-4.035, 28.07], "ix": 6}, "t": 1, "nm": "jianbian23", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份-2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "右眼高光", "parent": 30, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.011, 2.922, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.167, -16.979], [14.135, 0.139], [-0.167, 16.978], [-14.135, -0.139]], "o": [[14.135, 0.139], [-0.167, 16.978], [-14.135, -0.139], [0.167, -16.979], [0, 0]], "v": [[0.303, -30.743], [25.594, 0.253], [-0.303, 30.743], [-25.594, -0.252], [0.303, -30.743]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.13, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.13, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-25.59, -1.925], "ix": 5}, "e": {"a": 0, "k": [25.602, -1.925], "ix": 6}, "t": 1, "nm": "jianbian666", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "右眼阴影", "parent": 30, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.779, -2.377, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.175, -17.808], [14.135, 0.139], [-0.175, 17.808], [-14.135, -0.139]], "o": [[14.135, 0.139], [-0.175, 17.808], [-14.135, -0.139], [0.175, -17.808], [0, 0]], "v": [[0.318, -32.245], [25.594, 0.253], [-0.318, 32.245], [-25.594, -0.252], [0.318, -32.245]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.772549033165, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "左眼", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-75.708, -49.681, 0], "to": [-1.417, -5, 0], "ti": [1.417, 5, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 42, "s": [-84.208, -79.681, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-84.208, -79.681, 0], "to": [1.417, 5, 0], "ti": [-1.417, -5, 0]}, {"t": 203, "s": [-75.708, -49.681, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 26, "s": [100, 86, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 31, "s": [100, 17.5, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 36, "s": [100, 85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [100, 35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 191, "s": [100, 35, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 195, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 234, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 240, "s": [100, 85, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 245, "s": [100, 17.5, 100]}, {"t": 250, "s": [100, 86, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-14.002, 0], [0, -16.502], [14.002, 0], [0, 16.502]], "o": [[14.002, 0], [0, 16.502], [-14.002, 0], [0, -16.502]], "v": [[0, -29.88], [25.353, 0], [0, 29.88], [-25.353, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.322, 0.137, 0.008, 0.5, 0.415, 0.195, 0.022, 1, 0.508, 0.253, 0.037], "ix": 9}}, "s": {"a": 0, "k": [-22.906, -47.804], "ix": 5}, "e": {"a": 0, "k": [11.732, 32.217], "ix": 6}, "t": 1, "nm": "jianbian24dsa88", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "左眼高光", "parent": 33, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.378, 2.867, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.167, -16.979], [14.135, 0.139], [-0.167, 16.978], [-14.135, -0.139]], "o": [[14.135, 0.139], [-0.167, 16.978], [-14.135, -0.139], [0.167, -16.979], [0, 0]], "v": [[0.303, -30.743], [25.594, 0.253], [-0.303, 30.743], [-25.594, -0.252], [0.303, -30.743]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.13, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.13, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [-26.459, -1.872], "ix": 5}, "e": {"a": 0, "k": [24.733, -1.872], "ix": 6}, "t": 1, "nm": "jianbian4sdas", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 273, "st": 23, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "左眼阴影", "parent": 33, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.389, -2.377, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.175, -17.808], [14.135, 0.139], [-0.175, 17.808], [-14.135, -0.139]], "o": [[14.135, 0.139], [-0.175, 17.808], [-14.135, -0.139], [0.175, -17.808], [0, 0]], "v": [[0.318, -32.245], [25.594, 0.253], [-0.318, 32.245], [-25.594, -0.252], [0.318, -32.245]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.952941179276, 0.772549033165, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 273, "st": 23, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "嘴巴", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.798}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-18.163, 8.269, 0], "to": [-1.146, -5.104, 0], "ti": [1.458, 6.708, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.249, "y": 1}, "t": 41, "s": [-25.038, -22.356, 0], "to": [-1.458, -6.708, 0], "ti": [0.312, 1.604, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 72, "s": [-26.913, -31.981, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-26.913, -31.981, 0], "to": [1.458, 6.708, 0], "ti": [-1.458, -6.708, 0]}, {"t": 202, "s": [-18.163, 8.269, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[0, 0], [-2.499, -0.32], [-13.412, 1.904], [-3.127, 0.704]], "o": [[2.125, 0.481], [11.621, 1.488], [3.437, -0.488], [0, 0]], "v": [[-26.996, -2.555], [-20.035, -1.354], [20.107, -2.017], [29.996, -3.805]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 72, "s": [{"i": [[0, 0], [-3.745, -0.567], [-20.041, 2.427], [-5.691, 0.955]], "o": [[3.885, 0.801], [17.42, 2.636], [5.136, -0.622], [0, 0]], "v": [[-40.746, -2.055], [-29.323, -0.004], [24.545, 0.31], [40.746, -2.055]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 187, "s": [{"i": [[0, 0], [-3.745, -0.567], [-20.041, 2.427], [-5.691, 0.955]], "o": [[3.885, 0.801], [17.42, 2.636], [5.136, -0.622], [0, 0]], "v": [[-40.746, -2.055], [-29.323, -0.004], [24.545, 0.31], [40.746, -2.055]], "c": false}]}, {"t": 218, "s": [{"i": [[0, 0], [-2.499, -0.32], [-13.412, 1.904], [-3.127, 0.704]], "o": [[2.125, 0.481], [11.621, 1.488], [3.437, -0.488], [0, 0]], "v": [[-26.996, -2.555], [-20.035, -1.354], [20.107, -2.017], [29.996, -3.805]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.764705896378, 0.192156866193, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 14.487, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-7", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "形状图层 3", "parent": 43, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 231.179, "ix": 10}, "p": {"a": 0, "k": [-4.875, -16.572, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67, 128.875, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.702, -23.309]], "o": [[0, 0], [0.256, 8.511]], "v": [[62.395, 115.855], [80.75, 145.5]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274569642, 0.541176470588, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 273, "st": 0, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "形状图层 2", "parent": 39, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 8, "ix": 10}, "p": {"a": 0, "k": [1.242, -34.463, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [67, 128.875, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.125, -22.75]], "o": [[0, 0], [2.214, 8.222]], "v": [[53.25, 112.25], [74.125, 139.375]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274569642, 0.541176470588, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 273, "st": 0, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 4, "nm": "右手", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [-8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [0.429]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0.812]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [1.135]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 48, "s": [1.387]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [1.564]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [1.661]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 51, "s": [1.68]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [1.625]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [1.503]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 54, "s": [1.324]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [1.099]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [0.841]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57, "s": [0.563]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [0.278]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 59, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [-0.26]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [-0.492]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [-0.688]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 63, "s": [-0.842]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [-0.949]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [-1.008]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [-1.019]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [-0.986]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [-0.912]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 69, "s": [-0.803]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [-0.666]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [-0.51]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [-0.341]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [-0.169]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0.158]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [0.417]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0.51]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0.575]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0.611]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0.618]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [0.598]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [0.553]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [0.487]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0.404]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [0.309]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [0.207]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0.102]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [-0.096]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [-0.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [-0.253]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [-0.31]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [-0.349]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [-0.371]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [-0.375]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [-0.363]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [-0.335]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [-0.295]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [-0.245]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [-0.188]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [-0.126]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [-0.062]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0.058]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 106, "s": [0.11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [0.154]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0.188]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [0.212]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0.225]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [0.227]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [0.22]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [0.203]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [0.179]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [0.149]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [0.114]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [0.076]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0.038]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [-0.035]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [-0.067]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122, "s": [-0.093]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [-0.114]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [-0.128]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [-0.136]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126, "s": [-0.138]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127, "s": [-0.133]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [-0.123]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [-0.109]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [-0.09]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [-0.069]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [-0.046]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [-0.023]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [0.021]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [0.04]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137, "s": [0.056]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [0.069]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 139, "s": [0.078]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [0.083]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [0.084]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 142, "s": [0.081]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143, "s": [0.075]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 144, "s": [0.066]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 145, "s": [0.055]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 146, "s": [0.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 147, "s": [0.028]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148, "s": [0.014]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [-0.013]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 151, "s": [-0.025]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 152, "s": [-0.034]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 153, "s": [-0.042]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 154, "s": [-0.047]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 155, "s": [-0.05]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 156, "s": [-0.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 157, "s": [-0.049]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 158, "s": [-0.045]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159, "s": [-0.04]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 160, "s": [-0.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 161, "s": [-0.025]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 162, "s": [-0.017]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163, "s": [-0.008]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [0.008]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 166, "s": [0.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [0.021]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [0.025]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 169, "s": [0.029]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [0.03]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171, "s": [0.031]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [0.03]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 173, "s": [0.028]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 174, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 175, "s": [0.02]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 176, "s": [0.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 177, "s": [0.01]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [0.005]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [-0.005]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 181, "s": [-0.009]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 182, "s": [-0.013]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 183, "s": [-0.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 184, "s": [-0.017]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [-0.018]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 186, "s": [-0.019]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 205, "s": [-8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 206, "s": [-8.429]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [-8.812]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 208, "s": [-9.135]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 209, "s": [-9.387]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 210, "s": [-9.564]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 211, "s": [-9.661]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [-9.68]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 213, "s": [-9.625]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 214, "s": [-9.503]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 215, "s": [-9.324]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 216, "s": [-9.099]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 217, "s": [-8.841]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 218, "s": [-8.563]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 219, "s": [-8.278]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220, "s": [-8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 221, "s": [-7.74]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 222, "s": [-7.508]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 223, "s": [-7.312]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [-7.158]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 225, "s": [-7.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [-6.992]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 227, "s": [-6.981]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 228, "s": [-7.014]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [-7.088]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 230, "s": [-7.197]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 231, "s": [-7.334]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232, "s": [-7.49]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [-7.659]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [-7.831]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 235, "s": [-8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 236, "s": [-8.158]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [-8.299]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [-8.417]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 239, "s": [-8.51]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 240, "s": [-8.575]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [-8.611]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 242, "s": [-8.618]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 243, "s": [-8.598]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 244, "s": [-8.553]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [-8.487]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 246, "s": [-8.404]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [-8.309]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 248, "s": [-8.207]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [-8.102]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 250, "s": [-8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 251, "s": [-7.904]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [-7.819]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [-7.747]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 254, "s": [-7.69]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [-7.651]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 256, "s": [-7.629]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257, "s": [-7.625]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 258, "s": [-7.637]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 259, "s": [-7.665]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 260, "s": [-7.705]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 261, "s": [-7.755]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [-7.812]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 263, "s": [-7.874]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 264, "s": [-7.938]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 265, "s": [-8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [-8.058]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 267, "s": [-8.11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [-8.154]}, {"t": 269, "s": [-8.188]}], "ix": 10}, "p": {"a": 0, "k": [422.759, 358.566, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [103.692, -46.632, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [22.456, -3.384], [7.199, -6.727], [2.707, -10.522], [-3.292, -10.654], [-8.772, -7.598], [-33.547, 5.708], [-22.762, 27.726], [0.842, 9.026], [25.387, 3.028], [11.451, 6.347], [0, 0], [0, 0], [0.391, 0.218], [0, 0], [1.596, 0.832]], "o": [[-12.067, -6.138], [-10.829, 1.632], [-7.89, 7.372], [-2.689, 10.452], [3.015, 9.758], [20.689, 17.921], [24.04, -4.091], [22.378, -27.258], [-15.964, 12.451], [-17.108, -2.04], [0, 0], [0, 0], [-0.391, -0.218], [0, 0], [-1.566, -0.867], [0, 0]], "v": [[-12.031, -54.082], [-60.194, -61.358], [-87.421, -48.244], [-103.517, -20.642], [-102.852, 11.736], [-85.326, 38.316], [4.572, 55.147], [81.41, 12.428], [102.064, -47.727], [40.154, -33.327], [-0.427, -47.749], [-2.099, -48.68], [-3.749, -49.602], [-4.922, -50.256], [-6.095, -50.908], [-10.829, -53.463]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274516582, 0.541176497936, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 7, "k": {"a": 0, "k": [0, 1, 0.851, 0.008, 0.394, 1, 0.851, 0.008, 0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173, 0.999, 1, 0.702, 0.173, 1, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [33, -119], "ix": 5}, "e": {"a": 0, "k": [-84.879, 34.394], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian507", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-6备份", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 40, "ty": 4, "nm": "左腮红", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-120.029, -9.542, 0], "to": [-1.583, -2.333, 0], "ti": [1.583, 2.333, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 42, "s": [-129.529, -23.542, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-129.529, -23.542, 0], "to": [1.583, 2.333, 0], "ti": [-1.583, -2.333, 0]}, {"t": 203, "s": [-120.029, -9.542, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [156.713, 156.713, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0, 0, 0.862, 1, 0.426, 0.113, 1, 1, 0.851, 0.226, 0, 0.75, 0.5, 0.375, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian604", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 41, "ty": 4, "nm": "右腮红", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [91.471, -9.542, 0], "to": [-1.583, -2.333, 0], "ti": [1.583, 2.333, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 42, "s": [81.971, -23.542, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [81.971, -23.542, 0], "to": [1.583, 2.333, 0], "ti": [-1.583, -2.333, 0]}, {"t": 203, "s": [91.471, -9.542, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [156.713, 156.713, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-18.503, 0], [0, -18.503], [18.503, 0], [0, 18.503]], "o": [[18.503, 0], [0, 18.503], [-18.503, 0], [0, -18.503]], "v": [[0, -33.502], [33.502, 0], [0, 33.502], [-33.502, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0, 0, 0.862, 1, 0.426, 0.113, 1, 1, 0.851, 0.226, 0, 0.75, 0.5, 0.375, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [29.25, 0.25], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian603", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形备份", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 3, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [261, 264, 0], "to": [0, -0.149, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [261, 263.107, 0], "to": [0, -0.298, 0], "ti": [0, 0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [261, 262.211, 0], "to": [0, -0.299, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [261, 261.315, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [261, 260.421, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [261, 259.526, 0], "to": [0, -0.298, 0], "ti": [0, 0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [261, 258.63, 0], "to": [0, -0.299, 0], "ti": [0, 0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [261, 257.733, 0], "to": [0, -0.299, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [261, 256.839, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [261, 255.945, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [261, 255.052, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [261, 254.159, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [261, 253.265, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [261, 252.369, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [261, 251.474, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [261, 250.58, 0], "to": [0, -0.298, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [261, 249.685, 0], "to": [0, -0.298, 0], "ti": [0, 0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [261, 248.789, 0], "to": [0, -0.299, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [261, 247.893, 0], "to": [0, -0.298, 0], "ti": [0, 0.291, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [261, 247, 0], "to": [0, -0.291, 0], "ti": [0, 0.268, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [261, 246.149, 0], "to": [0, -0.268, 0], "ti": [0, 0.233, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [261, 245.39, 0], "to": [0, -0.233, 0], "ti": [0, 0.19, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [261, 244.749, 0], "to": [0, -0.19, 0], "ti": [0, 0.142, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [261, 244.248, 0], "to": [0, -0.142, 0], "ti": [0, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [261, 243.898, 0], "to": [0, -0.09, 0], "ti": [0, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [261, 243.705, 0], "to": [0, -0.038, 0], "ti": [0, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [261, 243.667, 0], "to": [0, 0.012, 0], "ti": [0, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [261, 243.777, 0], "to": [0, 0.059, 0], "ti": [0, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [261, 244.019, 0], "to": [0, 0.1, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [261, 244.374, 0], "to": [0, 0.134, 0], "ti": [0, -0.16, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [261, 244.821, 0], "to": [0, 0.16, 0], "ti": [0, -0.177, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [261, 245.333, 0], "to": [0, 0.177, 0], "ti": [0, -0.186, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [261, 245.884, 0], "to": [0, 0.186, 0], "ti": [0, -0.186, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [261, 246.448, 0], "to": [0, 0.186, 0], "ti": [0, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [261, 247, 0], "to": [0, 0.178, 0], "ti": [0, -0.163, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [261, 247.516, 0], "to": [0, 0.163, 0], "ti": [0, -0.141, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [261, 247.977, 0], "to": [0, 0.141, 0], "ti": [0, -0.115, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [261, 248.365, 0], "to": [0, 0.115, 0], "ti": [0, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [261, 248.669, 0], "to": [0, 0.086, 0], "ti": [0, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [261, 248.882, 0], "to": [0, 0.055, 0], "ti": [0, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [261, 248.999, 0], "to": [0, 0.023, 0], "ti": [0, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [261, 249.021, 0], "to": [0, -0.007, 0], "ti": [0, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [261, 248.955, 0], "to": [0, -0.035, 0], "ti": [0, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [261, 248.808, 0], "to": [0, -0.06, 0], "ti": [0, 0.081, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [261, 248.593, 0], "to": [0, -0.081, 0], "ti": [0, 0.097, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [261, 248.322, 0], "to": [0, -0.097, 0], "ti": [0, 0.108, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [261, 248.011, 0], "to": [0, -0.108, 0], "ti": [0, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [261, 247.677, 0], "to": [0, -0.113, 0], "ti": [0, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [261, 247.335, 0], "to": [0, -0.113, 0], "ti": [0, 0.108, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [261, 247, 0], "to": [0, -0.108, 0], "ti": [0, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [261, 246.687, 0], "to": [0, -0.099, 0], "ti": [0, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [261, 246.408, 0], "to": [0, -0.086, 0], "ti": [0, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [261, 246.172, 0], "to": [0, -0.07, 0], "ti": [0, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [261, 245.988, 0], "to": [0, -0.052, 0], "ti": [0, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [261, 245.859, 0], "to": [0, -0.033, 0], "ti": [0, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [261, 245.788, 0], "to": [0, -0.014, 0], "ti": [0, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [261, 245.774, 0], "to": [0, 0.004, 0], "ti": [0, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [261, 245.814, 0], "to": [0, 0.022, 0], "ti": [0, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [261, 245.903, 0], "to": [0, 0.037, 0], "ti": [0, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [261, 246.034, 0], "to": [0, 0.049, 0], "ti": [0, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [261, 246.198, 0], "to": [0, 0.059, 0], "ti": [0, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [261, 246.387, 0], "to": [0, 0.065, 0], "ti": [0, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [261, 246.589, 0], "to": [0, 0.068, 0], "ti": [0, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [261, 246.797, 0], "to": [0, 0.068, 0], "ti": [0, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [261, 247, 0], "to": [0, 0.065, 0], "ti": [0, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [261, 247.19, 0], "to": [0, 0.06, 0], "ti": [0, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [261, 247.359, 0], "to": [0, 0.052, 0], "ti": [0, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [261, 247.502, 0], "to": [0, 0.042, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [261, 247.614, 0], "to": [0, 0.032, 0], "ti": [0, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [261, 247.692, 0], "to": [0, 0.02, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [261, 247.735, 0], "to": [0, 0.009, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [261, 247.744, 0], "to": [0, -0.003, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [261, 247.719, 0], "to": [0, -0.013, 0], "ti": [0, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [261, 247.665, 0], "to": [0, -0.022, 0], "ti": [0, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [261, 247.586, 0], "to": [0, -0.03, 0], "ti": [0, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [261, 247.486, 0], "to": [0, -0.036, 0], "ti": [0, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [261, 247.372, 0], "to": [0, -0.04, 0], "ti": [0, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [261, 247.249, 0], "to": [0, -0.041, 0], "ti": [0, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [261, 247.123, 0], "to": [0, -0.041, 0], "ti": [0, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [261, 247, 0], "to": [0, -0.04, 0], "ti": [0, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [261, 246.885, 0], "to": [0, -0.036, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [261, 246.782, 0], "to": [0, -0.032, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [261, 246.695, 0], "to": [0, -0.026, 0], "ti": [0, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [261, 246.628, 0], "to": [0, -0.019, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [261, 246.58, 0], "to": [0, -0.012, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [261, 246.554, 0], "to": [0, -0.005, 0], "ti": [0, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [261, 246.549, 0], "to": [0, 0.002, 0], "ti": [0, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [261, 246.564, 0], "to": [0, 0.008, 0], "ti": [0, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [261, 246.597, 0], "to": [0, 0.013, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [261, 246.645, 0], "to": [0, 0.018, 0], "ti": [0, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [261, 246.705, 0], "to": [0, 0.022, 0], "ti": [0, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [261, 246.774, 0], "to": [0, 0.024, 0], "ti": [0, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [261, 246.849, 0], "to": [0, 0.025, 0], "ti": [0, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [261, 246.925, 0], "to": [0, 0.025, 0], "ti": [0, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [261, 247, 0], "to": [0, 0.024, 0], "ti": [0, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [261, 247.07, 0], "to": [0, 0.022, 0], "ti": [0, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [261, 247.132, 0], "to": [0, 0.019, 0], "ti": [0, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [261, 247.185, 0], "to": [0, 0.016, 0], "ti": [0, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [261, 247.226, 0], "to": [0, 0.012, 0], "ti": [0, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [261, 247.255, 0], "to": [0, 0.007, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [261, 247.27, 0], "to": [0, 0.003, 0], "ti": [0, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [261, 247.274, 0], "to": [0, -0.001, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [261, 247.265, 0], "to": [0, -0.005, 0], "ti": [0, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [261, 247.245, 0], "to": [0, -0.008, 0], "ti": [0, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [261, 247.216, 0], "to": [0, -0.011, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [261, 247.179, 0], "to": [0, -0.013, 0], "ti": [0, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [261, 247.137, 0], "to": [0, -0.015, 0], "ti": [0, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [261, 247.092, 0], "to": [0, -0.015, 0], "ti": [0, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [261, 247.045, 0], "to": [0, -0.015, 0], "ti": [0, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [261, 247, 0], "to": [0, -0.015, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [261, 246.958, 0], "to": [0, -0.013, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [261, 246.92, 0], "to": [0, -0.012, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [261, 246.888, 0], "to": [0, -0.009, 0], "ti": [0, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [261, 246.863, 0], "to": [0, -0.007, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [261, 246.846, 0], "to": [0, -0.005, 0], "ti": [0, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [261, 246.836, 0], "to": [0, -0.002, 0], "ti": [0, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [261, 246.834, 0], "to": [0, 0.001, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [261, 246.84, 0], "to": [0, 0.003, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [261, 246.852, 0], "to": [0, 0.005, 0], "ti": [0, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [261, 246.869, 0], "to": [0, 0.007, 0], "ti": [0, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [261, 246.891, 0], "to": [0, 0.008, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [261, 246.917, 0], "to": [0, 0.009, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [261, 246.944, 0], "to": [0, 0.009, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [261, 246.973, 0], "to": [0, 0.009, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [261, 247, 0], "to": [0, 0.009, 0], "ti": [0, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [261, 247.026, 0], "to": [0, 0.008, 0], "ti": [0, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [261, 247.049, 0], "to": [0, 0.007, 0], "ti": [0, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [261, 247.068, 0], "to": [0, 0.006, 0], "ti": [0, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [261, 247.083, 0], "to": [0, 0.004, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [261, 247.094, 0], "to": [0, 0.003, 0], "ti": [0, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [261, 247.1, 0], "to": [0, 0.001, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [261, 247.101, 0], "to": [0, 0, 0], "ti": [0, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [261, 247.097, 0], "to": [0, -0.002, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [261, 247.09, 0], "to": [0, -0.003, 0], "ti": [0, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [261, 247.079, 0], "to": [0, -0.004, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [261, 247.066, 0], "to": [0, -0.005, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [261, 247.05, 0], "to": [0, -0.005, 0], "ti": [0, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [261, 247.034, 0], "to": [0, -0.006, 0], "ti": [0, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [261, 247.017, 0], "to": [0, -0.006, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [261, 247, 0], "to": [0, -0.005, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [261, 246.984, 0], "to": [0, -0.005, 0], "ti": [0, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [261, 246.971, 0], "to": [0, -0.004, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [261, 246.959, 0], "to": [0, -0.003, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [261, 246.95, 0], "to": [0, -0.003, 0], "ti": [0, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [261, 246.943, 0], "to": [0, -0.002, 0], "ti": [0, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [261, 246.94, 0], "to": [0, -0.001, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [261, 246.939, 0], "to": [0, 0, 0], "ti": [0, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [261, 246.941, 0], "to": [0, 0.001, 0], "ti": [0, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [261, 246.945, 0], "to": [0, 0.002, 0], "ti": [0, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [261, 246.952, 0], "to": [0, 0.002, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [261, 246.96, 0], "to": [0, 0.003, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [261, 246.969, 0], "to": [0, 0.003, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [261, 246.98, 0], "to": [0, 0.003, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [261, 246.99, 0], "to": [0, 0.003, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [261, 247, 0], "to": [0, 0.003, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [261, 247.009, 0], "to": [0, 0.003, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [261, 247.018, 0], "to": [0, 0.003, 0], "ti": [0, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [261, 247.025, 0], "to": [0, 0.002, 0], "ti": [0, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [261, 247.031, 0], "to": [0, 0.002, 0], "ti": [0, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [261, 247.034, 0], "to": [0, 0.001, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [261, 247.037, 0], "to": [0, 0, 0], "ti": [0, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [261, 247.037, 0], "to": [0, -0.006, 0], "ti": [0, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [261, 247, 0], "to": [0, 0.143, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [261, 247.893, 0], "to": [0, 0.298, 0], "ti": [0, -0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [261, 248.789, 0], "to": [0, 0.299, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [261, 249.685, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [261, 250.58, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [261, 251.474, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [261, 252.369, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [261, 253.265, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [261, 254.159, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [261, 255.052, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [261, 255.945, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [261, 256.839, 0], "to": [0, 0.298, 0], "ti": [0, -0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [261, 257.733, 0], "to": [0, 0.299, 0], "ti": [0, -0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [261, 258.63, 0], "to": [0, 0.299, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [261, 259.526, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [261, 260.421, 0], "to": [0, 0.298, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [261, 261.315, 0], "to": [0, 0.298, 0], "ti": [0, -0.299, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [261, 262.211, 0], "to": [0, 0.299, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [261, 263.107, 0], "to": [0, 0.298, 0], "ti": [0, -0.291, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [261, 264, 0], "to": [0, 0.291, 0], "ti": [0, -0.268, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [261, 264.851, 0], "to": [0, 0.268, 0], "ti": [0, -0.233, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [261, 265.61, 0], "to": [0, 0.233, 0], "ti": [0, -0.19, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [261, 266.251, 0], "to": [0, 0.19, 0], "ti": [0, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [261, 266.752, 0], "to": [0, 0.142, 0], "ti": [0, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [261, 267.102, 0], "to": [0, 0.09, 0], "ti": [0, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [261, 267.295, 0], "to": [0, 0.038, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [261, 267.333, 0], "to": [0, -0.012, 0], "ti": [0, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [261, 267.223, 0], "to": [0, -0.059, 0], "ti": [0, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [261, 266.981, 0], "to": [0, -0.1, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [261, 266.626, 0], "to": [0, -0.134, 0], "ti": [0, 0.16, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [261, 266.179, 0], "to": [0, -0.16, 0], "ti": [0, 0.177, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [261, 265.667, 0], "to": [0, -0.177, 0], "ti": [0, 0.186, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [261, 265.116, 0], "to": [0, -0.186, 0], "ti": [0, 0.186, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [261, 264.552, 0], "to": [0, -0.186, 0], "ti": [0, 0.178, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [261, 264, 0], "to": [0, -0.178, 0], "ti": [0, 0.163, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [261, 263.484, 0], "to": [0, -0.163, 0], "ti": [0, 0.141, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [261, 263.023, 0], "to": [0, -0.141, 0], "ti": [0, 0.115, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [261, 262.635, 0], "to": [0, -0.115, 0], "ti": [0, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [261, 262.331, 0], "to": [0, -0.086, 0], "ti": [0, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [261, 262.119, 0], "to": [0, -0.055, 0], "ti": [0, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [261, 262.002, 0], "to": [0, -0.023, 0], "ti": [0, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [261, 261.979, 0], "to": [0, 0.007, 0], "ti": [0, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [261, 262.045, 0], "to": [0, 0.036, 0], "ti": [0, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [261, 262.192, 0], "to": [0, 0.06, 0], "ti": [0, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [261, 262.407, 0], "to": [0, 0.081, 0], "ti": [0, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [261, 262.678, 0], "to": [0, 0.097, 0], "ti": [0, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [261, 262.989, 0], "to": [0, 0.108, 0], "ti": [0, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [261, 263.323, 0], "to": [0, 0.113, 0], "ti": [0, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [261, 263.665, 0], "to": [0, 0.113, 0], "ti": [0, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [261, 264, 0], "to": [0, 0.108, 0], "ti": [0, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [261, 264.313, 0], "to": [0, 0.099, 0], "ti": [0, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [261, 264.592, 0], "to": [0, 0.086, 0], "ti": [0, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [261, 264.828, 0], "to": [0, 0.07, 0], "ti": [0, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [261, 265.012, 0], "to": [0, 0.052, 0], "ti": [0, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241, "s": [261, 265.141, 0], "to": [0, 0.033, 0], "ti": [0, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242, "s": [261, 265.212, 0], "to": [0, 0.014, 0], "ti": [0, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243, "s": [261, 265.226, 0], "to": [0, -0.004, 0], "ti": [0, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244, "s": [261, 265.186, 0], "to": [0, -0.022, 0], "ti": [0, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [261, 265.097, 0], "to": [0, -0.037, 0], "ti": [0, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246, "s": [261, 264.966, 0], "to": [0, -0.049, 0], "ti": [0, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247, "s": [261, 264.802, 0], "to": [0, -0.059, 0], "ti": [0, 0.065, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [261, 264.613, 0], "to": [0, -0.065, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [261, 264.411, 0], "to": [0, -0.068, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [261, 264.203, 0], "to": [0, -0.068, 0], "ti": [0, 0.065, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251, "s": [261, 264, 0], "to": [0, -0.065, 0], "ti": [0, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252, "s": [261, 263.81, 0], "to": [0, -0.06, 0], "ti": [0, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253, "s": [261, 263.641, 0], "to": [0, -0.052, 0], "ti": [0, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [261, 263.498, 0], "to": [0, -0.042, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [261, 263.386, 0], "to": [0, -0.032, 0], "ti": [0, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256, "s": [261, 263.308, 0], "to": [0, -0.02, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257, "s": [261, 263.265, 0], "to": [0, -0.009, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258, "s": [261, 263.256, 0], "to": [0, 0.003, 0], "ti": [0, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259, "s": [261, 263.281, 0], "to": [0, 0.013, 0], "ti": [0, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [261, 263.335, 0], "to": [0, 0.022, 0], "ti": [0, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261, "s": [261, 263.414, 0], "to": [0, 0.03, 0], "ti": [0, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262, "s": [261, 263.514, 0], "to": [0, 0.036, 0], "ti": [0, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263, "s": [261, 263.628, 0], "to": [0, 0.04, 0], "ti": [0, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264, "s": [261, 263.751, 0], "to": [0, 0.041, 0], "ti": [0, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [261, 263.877, 0], "to": [0, 0.041, 0], "ti": [0, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266, "s": [261, 264, 0], "to": [0, 0.04, 0], "ti": [0, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267, "s": [261, 264.115, 0], "to": [0, 0.036, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268, "s": [261, 264.218, 0], "to": [0, 0.032, 0], "ti": [0, -0.014, 0]}, {"t": 269, "s": [261, 264.305, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [110, 110, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 43, "ty": 4, "nm": "左手", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [-0.536]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-1.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [-1.418]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 48, "s": [-1.734]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [-1.955]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [-2.076]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 51, "s": [-2.1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [-2.031]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [-1.879]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 54, "s": [-1.655]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [-1.373]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [-1.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57, "s": [-0.703]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [-0.348]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 59, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0.325]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [0.615]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [0.86]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 63, "s": [1.052]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [1.186]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [1.259]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [1.274]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [1.232]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [1.14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 69, "s": [1.004]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [0.833]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [0.637]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [0.427]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [0.211]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [-0.197]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [-0.373]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [-0.522]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [-0.638]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [-0.719]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [-0.764]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [-0.773]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [-0.747]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [-0.691]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [-0.609]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [-0.505]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [-0.387]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 87, "s": [-0.259]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [-0.128]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [0.12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [0.226]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [0.316]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [0.387]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0.436]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0.463]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0.469]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [0.453]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0.419]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [0.369]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [0.306]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0.234]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [0.157]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [0.078]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [-0.073]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 106, "s": [-0.137]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [-0.192]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [-0.235]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [-0.265]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [-0.281]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [-0.284]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [-0.275]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [-0.254]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [-0.224]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [-0.186]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [-0.142]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [-0.095]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [-0.047]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0.044]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [0.083]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122, "s": [0.116]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [0.142]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [0.16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0.17]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126, "s": [0.172]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127, "s": [0.167]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0.154]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [0.136]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [0.113]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [0.086]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [0.058]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [0.029]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 134, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [-0.027]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [-0.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137, "s": [-0.071]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [-0.086]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 139, "s": [-0.097]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [-0.103]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [-0.105]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 142, "s": [-0.101]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143, "s": [-0.094]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 144, "s": [-0.082]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 145, "s": [-0.068]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 146, "s": [-0.052]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 147, "s": [-0.035]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148, "s": [-0.017]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [0.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 151, "s": [0.031]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 152, "s": [0.043]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 153, "s": [0.052]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 154, "s": [0.059]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 155, "s": [0.063]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 156, "s": [0.063]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 157, "s": [0.061]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 158, "s": [0.057]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159, "s": [0.05]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 160, "s": [0.041]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 161, "s": [0.032]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 162, "s": [0.021]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163, "s": [0.01]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [-0.01]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 166, "s": [-0.019]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [-0.026]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168, "s": [-0.032]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 169, "s": [-0.036]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [-0.038]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 171, "s": [-0.038]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [-0.037]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 173, "s": [-0.034]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 174, "s": [-0.03]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 175, "s": [-0.025]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 176, "s": [-0.019]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 177, "s": [-0.013]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [-0.006]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0.006]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 181, "s": [0.011]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 182, "s": [0.016]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 183, "s": [0.019]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 184, "s": [0.022]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 185, "s": [0.023]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 186, "s": [0.023]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 187, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 205, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 206, "s": [10.536]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [11.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 208, "s": [11.418]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 209, "s": [11.734]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 210, "s": [11.955]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 211, "s": [12.076]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [12.1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 213, "s": [12.031]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 214, "s": [11.879]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 215, "s": [11.655]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 216, "s": [11.373]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 217, "s": [11.051]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 218, "s": [10.703]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 219, "s": [10.348]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 220, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 221, "s": [9.675]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 222, "s": [9.385]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 223, "s": [9.14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 224, "s": [8.948]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 225, "s": [8.814]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 226, "s": [8.741]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 227, "s": [8.726]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 228, "s": [8.768]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [8.86]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 230, "s": [8.996]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 231, "s": [9.167]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 232, "s": [9.363]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [9.573]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 234, "s": [9.789]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 235, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 236, "s": [10.197]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 237, "s": [10.373]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [10.522]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 239, "s": [10.638]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 240, "s": [10.719]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [10.764]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 242, "s": [10.773]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 243, "s": [10.747]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 244, "s": [10.691]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 245, "s": [10.609]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 246, "s": [10.505]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [10.387]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 248, "s": [10.259]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 249, "s": [10.128]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 250, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 251, "s": [9.88]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [9.774]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [9.684]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 254, "s": [9.613]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 255, "s": [9.564]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 256, "s": [9.537]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 257, "s": [9.531]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 258, "s": [9.547]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 259, "s": [9.581]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 260, "s": [9.631]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 261, "s": [9.694]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 262, "s": [9.766]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 263, "s": [9.843]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 264, "s": [9.922]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 265, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 266, "s": [10.073]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 267, "s": [10.137]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 268, "s": [10.192]}, {"t": 269, "s": [10.235]}], "ix": 10}, "p": {"a": 0, "k": [89.692, 360.581, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-76, -43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "旋转 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-19.797, -3.342], [-5.342, -6.081], [-2.121, -10.437], [13.914, -17.461], [30.089, 0.084], [18.895, 19.351], [2.396, 13.625], [-4.895, 10.889], [-5.686, -3.288], [-10.796, 1.722], [-9.759, 8.562], [-0.743, 0.651], [0, 0], [-0.456, 0.391]], "o": [[9.985, -8.426], [8.315, 1.403], [6.341, 7.218], [4.273, 21.028], [-10.861, 13.629], [-24.915, -0.07], [-11.547, -11.825], [-1.796, -10.213], [5.355, 5.996], [9.807, 5.671], [11.812, -1.884], [0.732, -0.642], [0, 0], [0.449, -0.391], [0, 0]], "v": [[7.063, -47.875], [48.674, -60.653], [69.195, -48.841], [84.393, -22.086], [72.544, 34.727], [14.14, 58.722], [-56.709, 29.913], [-79.409, -12.952], [-76.706, -45.04], [-60.117, -31.145], [-29.205, -25.264], [1.683, -43.198], [3.894, -45.14], [4.789, -45.923], [6.146, -47.096]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274516582, 0.541176497936, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 7, "k": {"a": 0, "k": [0, 1, 0.851, 0.008, 0.394, 1, 0.851, 0.008, 0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173, 0.999, 1, 0.702, 0.173, 1, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [-201, -47], "ix": 5}, "e": {"a": 0, "k": [37.121, -11.606], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian508", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-6", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 44, "ty": 4, "nm": "左眉毛", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-80.347, -104.96, 0], "to": [-1.417, -5, 0], "ti": [1.417, 5, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 42, "s": [-88.847, -134.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-88.847, -134.96, 0], "to": [1.417, 5, 0], "ti": [-1.417, -5, 0]}, {"t": 203, "s": [-80.347, -104.96, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [17.506, -6.409]], "o": [[-17.506, -4.645], [0, 0]], "v": [[26.258, 0.141], [-26.258, 2.787]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.537254929543, 0.258823543787, 0.047058828175, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 14, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-4备份-2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "右眉毛", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [52.514, -104.96, 0], "to": [-1.417, -5, 0], "ti": [1.417, 5, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 42, "s": [44.014, -134.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [44.014, -134.96, 0], "to": [1.417, 5, 0], "ti": [-1.417, -5, 0]}, {"t": 203, "s": [52.514, -104.96, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-20.685, -6.409]], "o": [[20.685, -4.645], [0, 0]], "v": [[-31.028, 0.141], [31.028, 2.787]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径-4", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.537254929543, 0.258823543787, 0.04705882445, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 14, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}, {"ddd": 0, "ind": 46, "ty": 4, "nm": "形状图层 10", "parent": 47, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [10, -26.054, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [118.221, 120.287, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [311.555, 311.555], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 1, 1, 1, 0.24, 1, 1, 1, 0.481, 1, 1, 1, 0.74, 1, 0.933, 0.676, 1, 1, 0.867, 0.352, 0, 0.78, 0.24, 0.415, 0.48, 0.05, 0.739, 0.025, 0.997, 0], "ix": 9}}, "s": {"a": 0, "k": [53.273, -142.016], "ix": 5}, "e": {"a": 0, "k": [-26.352, 127.297], "ix": 6}, "t": 1, "nm": "jianbian213213123", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-8.492, 12.621], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 420, "st": 0, "bm": 0}, {"ddd": 0, "ind": 47, "ty": 3, "nm": "头高光", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.619, 9.006, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": -1, "op": 273, "st": 23, "bm": 0}, {"ddd": 0, "ind": 49, "ty": 4, "nm": "头", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256.619, 447.101, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 200.107, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 31, "s": [100, 99.962, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 32, "s": [100, 99.854, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33, "s": [100, 99.688, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 34, "s": [100, 99.474, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 35, "s": [100, 99.222, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 36, "s": [100, 98.944, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 37, "s": [100, 98.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 38, "s": [100, 98.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 39, "s": [100, 98.056, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [100, 97.778, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 41, "s": [100, 97.526, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 42, "s": [100, 97.312, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 43, "s": [100, 97.146, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 44, "s": [100, 97.038, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 45, "s": [100, 97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 46, "s": [100, 97.027, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 47, "s": [100, 97.111, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 48, "s": [100, 97.24, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 49, "s": [100, 97.405, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [100, 97.6, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 51, "s": [100, 97.819, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 52, "s": [100, 98.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 53, "s": [100, 98.317, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 54, "s": [100, 98.588, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 55, "s": [100, 98.871, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 56, "s": [100, 99.161, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 57, "s": [100, 99.454, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [100, 99.739, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 59, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [100, 100.217, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 61, "s": [100, 100.411, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 62, "s": [100, 100.575, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 63, "s": [100, 100.703, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 64, "s": [100, 100.792, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 65, "s": [100, 100.842, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 66, "s": [100, 100.851, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 67, "s": [100, 100.823, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 68, "s": [100, 100.762, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 69, "s": [100, 100.671, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [100, 100.557, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 71, "s": [100, 100.426, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 72, "s": [100, 100.285, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 73, "s": [100, 100.141, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 74, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 75, "s": [100, 99.868, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 76, "s": [100, 99.751, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 77, "s": [100, 99.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [100, 99.574, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 79, "s": [100, 99.519, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 80, "s": [100, 99.489, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 81, "s": [100, 99.484, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 82, "s": [100, 99.501, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 83, "s": [100, 99.538, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 84, "s": [100, 99.593, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 85, "s": [100, 99.662, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 86, "s": [100, 99.742, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 87, "s": [100, 99.827, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 88, "s": [100, 99.915, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 89, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [100, 100.08, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 91, "s": [100, 100.151, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 92, "s": [100, 100.212, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 93, "s": [100, 100.259, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 94, "s": [100, 100.292, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 95, "s": [100, 100.31, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 96, "s": [100, 100.313, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 97, "s": [100, 100.303, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 98, "s": [100, 100.28, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 99, "s": [100, 100.247, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [100, 100.205, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 101, "s": [100, 100.157, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 102, "s": [100, 100.105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 103, "s": [100, 100.052, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 104, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 105, "s": [100, 99.951, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 106, "s": [100, 99.908, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 107, "s": [100, 99.872, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 108, "s": [100, 99.843, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 109, "s": [100, 99.823, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [100, 99.812, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 111, "s": [100, 99.81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 112, "s": [100, 99.816, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 113, "s": [100, 99.83, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 114, "s": [100, 99.85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [100, 99.876, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 116, "s": [100, 99.905, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 117, "s": [100, 99.936, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 118, "s": [100, 99.969, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 119, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 120, "s": [100, 100.029, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 121, "s": [100, 100.056, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 122, "s": [100, 100.078, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 123, "s": [100, 100.095, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 124, "s": [100, 100.107, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 125, "s": [100, 100.114, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 126, "s": [100, 100.115, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 127, "s": [100, 100.111, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 128, "s": [100, 100.103, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 129, "s": [100, 100.091, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 130, "s": [100, 100.075, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 131, "s": [100, 100.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 132, "s": [100, 100.039, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 133, "s": [100, 100.019, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 134, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 135, "s": [100, 99.982, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 136, "s": [100, 99.966, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 137, "s": [100, 99.953, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 138, "s": [100, 99.942, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 139, "s": [100, 99.935, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 140, "s": [100, 99.931, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 141, "s": [100, 99.93, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 142, "s": [100, 99.932, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 143, "s": [100, 99.937, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 144, "s": [100, 99.945, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 145, "s": [100, 99.954, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 146, "s": [100, 99.965, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 147, "s": [100, 99.977, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 148, "s": [100, 99.988, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 149, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 150, "s": [100, 100.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 151, "s": [100, 100.02, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 152, "s": [100, 100.029, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 153, "s": [100, 100.035, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 154, "s": [100, 100.039, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 155, "s": [100, 100.042, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 156, "s": [100, 100.042, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 157, "s": [100, 100.041, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 158, "s": [100, 100.038, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 159, "s": [100, 100.033, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 160, "s": [100, 100.028, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 161, "s": [100, 100.021, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 162, "s": [100, 100.014, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 163, "s": [100, 100.007, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 164, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 165, "s": [100, 99.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 166, "s": [100, 99.988, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 167, "s": [100, 99.983, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 168, "s": [100, 99.979, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 169, "s": [100, 99.976, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 170, "s": [100, 99.975, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 171, "s": [100, 99.974, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 172, "s": [100, 99.975, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 173, "s": [100, 99.977, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 174, "s": [100, 99.98, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 175, "s": [100, 99.983, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 176, "s": [100, 99.987, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 177, "s": [100, 99.991, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 178, "s": [100, 99.996, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 179, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 180, "s": [100, 100.004, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 181, "s": [100, 100.008, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 182, "s": [100, 100.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 183, "s": [100, 100.013, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 184, "s": [100, 100.015, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 185, "s": [100, 100.015, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 186, "s": [100, 100.016, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 187, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 188, "s": [100, 99.749, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 189, "s": [100, 99.469, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 190, "s": [100, 99.181, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 191, "s": [100, 98.895, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 192, "s": [100, 98.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 193, "s": [100, 98.345, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 194, "s": [100, 98.089, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 195, "s": [100, 97.848, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 196, "s": [100, 97.627, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 197, "s": [100, 97.43, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 198, "s": [100, 97.26, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 199, "s": [100, 97.126, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [100, 97.034, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 201, "s": [100, 97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 202, "s": [100, 97.031, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 203, "s": [100, 97.132, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 204, "s": [100, 97.293, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 205, "s": [100, 97.503, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 206, "s": [100, 97.752, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [100, 98.028, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 208, "s": [100, 98.322, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 209, "s": [100, 98.623, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 210, "s": [100, 98.919, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 211, "s": [100, 99.2, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 212, "s": [100, 99.455, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 213, "s": [100, 99.674, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 214, "s": [100, 99.845, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [100, 99.957, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 216, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 217, "s": [100, 100.008, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 218, "s": [100, 100.014, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 219, "s": [100, 100.02, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 220, "s": [100, 100.024, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 221, "s": [100, 100.028, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 222, "s": [100, 100.029, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 223, "s": [100, 100.03, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 224, "s": [100, 100.029, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 225, "s": [100, 100.027, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 226, "s": [100, 100.023, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 227, "s": [100, 100.019, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 228, "s": [100, 100.015, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 229, "s": [100, 100.01, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 230, "s": [100, 100.005, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 231, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 232, "s": [100, 99.995, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 233, "s": [100, 99.991, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 234, "s": [100, 99.988, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 235, "s": [100, 99.985, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 236, "s": [100, 99.983, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 237, "s": [100, 99.982, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 238, "s": [100, 99.982, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 239, "s": [100, 99.983, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 240, "s": [100, 99.984, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 241, "s": [100, 99.986, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 242, "s": [100, 99.988, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 243, "s": [100, 99.991, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 244, "s": [100, 99.994, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 245, "s": [100, 99.997, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 246, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 247, "s": [100, 100.003, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 248, "s": [100, 100.005, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 249, "s": [100, 100.007, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 250, "s": [100, 100.009, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 251, "s": [100, 100.01, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 252, "s": [100, 100.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 253, "s": [100, 100.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 254, "s": [100, 100.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 255, "s": [100, 100.01, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 256, "s": [100, 100.009, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 257, "s": [100, 100.007, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 258, "s": [100, 100.005, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 259, "s": [100, 100.004, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 260, "s": [100, 100.002, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 261, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 262, "s": [100, 99.998, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 263, "s": [100, 99.997, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 264, "s": [100, 99.996, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 265, "s": [100, 99.995, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 266, "s": [100, 99.994, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 267, "s": [100, 99.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 268, "s": [100, 99.993, 100]}, {"t": 269, "s": [100, 99.994, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "缩放 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}, {"ty": 5, "nm": "缩放 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-110.516, 0], [0, -110.516], [110.516, 0], [0, 110.516]], "o": [[110.516, 0], [0, 110.516], [-110.516, 0], [0, -110.516]], "v": [[0, -200.107], [200.107, 0], [0, 200.107], [-200.107, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274516582, 0.541176497936, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 7, "k": {"a": 0, "k": [0, 1, 0.851, 0.008, 0.394, 1, 0.851, 0.008, 0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173, 0.999, 1, 0.702, 0.173, 1, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [29, -159.5], "ix": 5}, "e": {"a": 0, "k": [-36.879, 140.394], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "jianbian401", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -1, "op": 275, "st": 23, "bm": 0}], "markers": [{"tm": 187, "cm": "1", "dr": 0}]}