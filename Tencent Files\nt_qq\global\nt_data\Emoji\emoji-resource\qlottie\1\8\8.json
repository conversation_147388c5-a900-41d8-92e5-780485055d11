{"v": "5.7.8", "fr": 60, "ip": 0, "op": 181, "w": 512, "h": 512, "nm": "羡慕", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 4, "ty": 1, "nm": "白色 纯色 1", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 262, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [110.938, 75.781, 100], "ix": 6, "l": 2}}, "ao": 0, "sw": 512, "sh": 512, "sc": "#ffffff", "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "右手", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [-54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [11]}, {"t": 77, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [350, 629, 0], "to": [3.333, -35, 0], "ti": [-3.333, 35, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 46, "s": [370, 419, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [370, 419, 0], "to": [-1.667, 2.167, 0], "ti": [4.5, -1.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [360, 432, 0], "to": [-4.5, 1.333, 0], "ti": [2.833, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 77, "s": [343, 427, 0], "to": [-2.833, 0.167, 0], "ti": [0, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 97, "s": [343, 433, 0], "to": [0, 0.5, 0], "ti": [0, 0.5, 0]}, {"t": 123, "s": [343, 430, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-94, 131, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[0, 0], [0.323, 31.06], [0.05, 1.956], [-6.833, -36.369], [0, 0]], "o": [[0, 0], [-0.061, -5.883], [-1.602, -62.272], [5.554, 29.561], [0, 0]], "v": [[-127, 125], [-124.678, 88.028], [-127.115, 43.673], [-55.167, 50.369], [-54, 135]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[0, 0], [0.323, 31.06], [-0.327, 1.93], [-4.833, -36.369], [0, 0]], "o": [[0, 0], [-0.061, -5.883], [10.115, -59.673], [3.962, 29.817], [0, 0]], "v": [[-127, 130], [-124.678, 93.028], [-123.115, 47.673], [-55.167, 55.369], [-54, 140]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0.323, 31.06], [0.93, 1.722], [-24.833, -55.368], [0, 0]], "o": [[0, 0], [-0.061, -5.883], [-20.885, -38.673], [12.899, 28.76], [0, 0]], "v": [[-127, 125], [-123.678, 96.028], [-136.115, 63.673], [-59.167, 72.368], [-54, 135]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 77, "s": [{"i": [[0, 0], [-2.327, 30.974], [1.01, 1.87], [-24.833, -55.368], [0, 0]], "o": [[0, 0], [0.678, -9.028], [-20.885, -38.673], [12.899, 28.76], [0, 0]], "v": [[-127, 125], [-122.678, 90.028], [-127.115, 55.673], [-59.167, 66.368], [-54, 135]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 97, "s": [{"i": [[0, 0], [-2.327, 30.974], [1.204, 1.752], [-31.833, -51.368], [0, 0]], "o": [[0, 0], [0.678, -9.028], [-25.885, -37.673], [16.604, 26.793], [0, 0]], "v": [[-127, 125], [-124.678, 97.028], [-136.115, 70.673], [-65.167, 63.368], [-54, 135]], "c": false}]}, {"t": 123, "s": [{"i": [[0, 0], [-2.096, 30.991], [0.914, 1.919], [-31.833, -51.368], [0, 0]], "o": [[0, 0], [0.678, -10.028], [-18.885, -39.673], [16.604, 26.793], [0, 0]], "v": [[-127, 125], [-124.678, 97.028], [-134.115, 64.673], [-63.167, 62.368], [-54, 135]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.913725495338, 0.482352942228, 0.0941176489, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.262, 1, 0.933, 0.671, 0.425, 0.998, 0.89, 0.455, 0.587, 0.996, 0.847, 0.239], "ix": 9}}, "s": {"a": 0, "k": [-29, -11], "ix": 5}, "e": {"a": 0, "k": [113, 58], "ix": 6}, "t": 2, "h": {"a": 0, "k": -94, "ix": 7}, "a": {"a": 0, "k": -50, "ix": 8}, "nm": "<PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 30, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 1, "nm": "白色 纯色 1", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 262, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [110.938, 75.781, 100], "ix": 6, "l": 2}}, "ao": 0, "sw": 512, "sh": 512, "sc": "#ffffff", "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "左手", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [-11]}, {"t": 77, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [162, 629, 0], "to": [0, -35, 0], "ti": [0, 35, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 46, "s": [162, 419, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [162, 419, 0], "to": [-1.667, 2.167, 0], "ti": [1.167, -1.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [152, 432, 0], "to": [-1.167, 1.333, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 77, "s": [155, 427, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 97, "s": [155, 433, 0], "to": [0, 0.5, 0], "ti": [0, 0.5, 0]}, {"t": 123, "s": [155, 430, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-94, 131, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 46, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 55, "s": [100, 100, 100]}, {"t": 60, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[0, 0], [0.323, 31.06], [0.05, 1.956], [-6.833, -36.369], [0, 0]], "o": [[0, 0], [-0.061, -5.883], [-1.602, -62.272], [5.554, 29.561], [0, 0]], "v": [[-127, 125], [-124.678, 88.028], [-127.115, 43.673], [-55.167, 50.369], [-54, 135]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[0, 0], [0.323, 31.06], [-0.327, 1.93], [-4.833, -36.369], [0, 0]], "o": [[0, 0], [-0.061, -5.883], [10.115, -59.673], [3.962, 29.817], [0, 0]], "v": [[-127, 130], [-124.678, 93.028], [-123.115, 47.673], [-55.167, 55.369], [-54, 140]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0.323, 31.06], [0.93, 1.722], [-24.833, -55.368], [0, 0]], "o": [[0, 0], [-0.061, -5.883], [-20.885, -38.673], [12.899, 28.76], [0, 0]], "v": [[-127, 125], [-123.678, 96.028], [-136.115, 63.673], [-59.167, 72.368], [-54, 135]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 77, "s": [{"i": [[0, 0], [-2.327, 30.974], [1.01, 1.87], [-24.833, -55.368], [0, 0]], "o": [[0, 0], [0.678, -9.028], [-20.885, -38.673], [12.899, 28.76], [0, 0]], "v": [[-127, 125], [-122.678, 90.028], [-127.115, 55.673], [-59.167, 66.368], [-54, 135]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 97, "s": [{"i": [[0, 0], [-2.327, 30.974], [1.204, 1.752], [-31.833, -51.368], [0, 0]], "o": [[0, 0], [0.678, -9.028], [-25.885, -37.673], [16.604, 26.793], [0, 0]], "v": [[-127, 125], [-124.678, 97.028], [-136.115, 70.673], [-65.167, 63.368], [-54, 135]], "c": false}]}, {"t": 123, "s": [{"i": [[0, 0], [-2.096, 30.991], [0.914, 1.919], [-31.833, -51.368], [0, 0]], "o": [[0, 0], [0.678, -10.028], [-18.885, -39.673], [16.604, 26.793], [0, 0]], "v": [[-127, 125], [-124.678, 97.028], [-134.115, 64.673], [-63.167, 62.368], [-54, 135]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.913725495338, 0.482352942228, 0.0941176489, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.262, 1, 0.933, 0.671, 0.425, 0.998, 0.89, 0.455, 0.587, 0.996, 0.847, 0.239], "ix": 9}}, "s": {"a": 0, "k": [-29, -11], "ix": 5}, "e": {"a": 0, "k": [113, 58], "ix": 6}, "t": 2, "h": {"a": 0, "k": -94, "ix": 7}, "a": {"a": 0, "k": -50, "ix": 8}, "nm": "This is wrong", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 30, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "嘴巴6", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -8.634, "ix": 10}, "p": {"a": 0, "k": [-25.388, 14.389, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.838, 92.237, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-24.14, -21.355], [-2.1, 6.562], [16.909, -3.874]], "o": [[3.012, 2.665], [10.303, -32.184], [-13.103, 3.002]], "v": [[14.702, -43.157], [41.663, -50.556], [27.359, -47.079]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52.801, "s": [{"i": [[-12.136, -7.45], [-6.735, 11.073], [30.264, -15.782]], "o": [[12.977, 7.291], [5.705, -16.285], [-25.581, 8.457]], "v": [[11.971, -32.234], [48.496, -40.591], [25.339, -49.12]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-9.613, -30.763], [-23.513, 35.456], [71.552, -0.723]], "o": [[12.96, 41.472], [18.676, -28.162], [-53.376, 0.539]], "v": [[-15.079, 0.269], [76.685, 12.168], [30.568, -27.534]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 67.199, "s": [{"i": [[1.105, -31.715], [-16.449, 34.115], [17.884, -0.234]], "o": [[-1.015, 29.133], [11.707, -24.281], [-13.327, 0.175]], "v": [[5.472, 80.497], [46.716, 102.005], [28.996, 32.794]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 74.4, "s": [{"i": [[1.901, -26.598], [-12.91, 39.244], [18.648, -0.378]], "o": [[-2.563, 30.03], [8.02, -24.379], [-13.325, 0.266]], "v": [[14.74, 77.094], [49.163, 87.447], [34.657, 28.184]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [{"i": [[3.782, -19.955], [-12.516, 38.077], [19.413, -0.521]], "o": [[-6.321, 33.352], [8.014, -24.38], [-13.323, 0.357]], "v": [[-5.071, 30.522], [46.411, 46.425], [21.285, 4.981]], "c": true}]}, {"t": 86, "s": [{"i": [[-6.64, -19.955], [-12.298, 16.691], [25.892, 4.335]], "o": [[8.979, 26.986], [14.314, -19.426], [-25.547, -4.277]], "v": [[-1.731, 30.579], [51.963, 41.827], [23.303, 47.898]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.882352948189, 0.105882354081, 0.105882354081, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882352948189, 0.105882354081, 0.105882354081, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 51, "op": 87, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "嘴巴9", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -8.634, "ix": 10}, "p": {"a": 0, "k": [-25.341, 17.967, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.838, 92.237, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-24.14, -21.355], [-2.1, 6.562], [16.909, -3.874]], "o": [[3.012, 2.665], [10.303, -32.184], [-13.103, 3.002]], "v": [[14.702, -43.157], [41.663, -50.556], [27.359, -47.079]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52.801, "s": [{"i": [[-12.136, -7.45], [-6.735, 11.073], [30.264, -15.782]], "o": [[12.977, 7.291], [5.705, -16.285], [-25.581, 8.457]], "v": [[11.971, -32.234], [48.496, -40.591], [25.339, -49.12]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-9.613, -30.763], [-23.513, 35.456], [71.552, -0.723]], "o": [[12.96, 41.472], [18.676, -28.162], [-53.376, 0.539]], "v": [[-15.079, 0.269], [76.685, 12.168], [30.568, -27.534]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 67.199, "s": [{"i": [[1.105, -31.715], [-16.449, 34.115], [17.884, -0.234]], "o": [[-1.015, 29.133], [11.707, -24.281], [-13.327, 0.175]], "v": [[5.472, 80.497], [46.716, 102.005], [28.996, 32.794]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 74.4, "s": [{"i": [[1.901, -26.598], [-12.91, 39.244], [18.648, -0.378]], "o": [[-2.563, 30.03], [8.02, -24.379], [-13.325, 0.266]], "v": [[14.74, 77.094], [49.163, 87.447], [34.657, 28.184]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [{"i": [[3.782, -19.955], [-12.516, 38.077], [19.413, -0.521]], "o": [[-6.321, 33.352], [8.014, -24.38], [-13.323, 0.357]], "v": [[-5.071, 30.522], [46.411, 46.425], [21.285, 4.981]], "c": true}]}, {"t": 86, "s": [{"i": [[-6.64, -19.955], [-12.298, 16.691], [25.892, 4.335]], "o": [[8.979, 26.986], [14.314, -19.426], [-25.547, -4.277]], "v": [[-1.731, 30.579], [51.963, 41.827], [23.303, 47.898]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.764705896378, 0.188235297799, 0.003921568859, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 51, "op": 87, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "嘴巴2", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -8.634, "ix": 10}, "p": {"a": 0, "k": [-24.821, 9.84, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.838, 92.237, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-24.14, -21.355], [-2.1, 6.562], [16.909, -3.874]], "o": [[3.012, 2.665], [10.303, -32.184], [-13.103, 3.002]], "v": [[14.702, -43.157], [41.663, -50.556], [27.359, -47.079]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52.801, "s": [{"i": [[-12.136, -7.45], [-6.735, 11.073], [30.264, -15.782]], "o": [[12.977, 7.291], [5.705, -16.285], [-25.581, 8.457]], "v": [[11.971, -32.234], [48.496, -40.591], [25.339, -49.12]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-9.613, -30.763], [-23.513, 35.456], [71.552, -0.723]], "o": [[12.96, 41.472], [18.676, -28.162], [-53.376, 0.539]], "v": [[-15.079, 0.269], [76.685, 12.168], [30.568, -27.534]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 67.199, "s": [{"i": [[1.105, -31.715], [-16.449, 34.115], [17.884, -0.234]], "o": [[-1.015, 29.133], [11.707, -24.281], [-13.327, 0.175]], "v": [[5.472, 80.497], [46.716, 102.005], [28.996, 32.794]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 74.4, "s": [{"i": [[1.901, -26.598], [-12.91, 39.244], [18.648, -0.378]], "o": [[-2.563, 30.03], [8.02, -24.379], [-13.325, 0.266]], "v": [[14.74, 77.094], [49.163, 87.447], [34.657, 28.184]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [{"i": [[3.782, -19.955], [-12.516, 38.077], [19.413, -0.521]], "o": [[-6.321, 33.352], [8.014, -24.38], [-13.323, 0.357]], "v": [[-5.071, 30.522], [46.411, 46.425], [21.285, 4.981]], "c": true}]}, {"t": 86, "s": [{"i": [[-6.64, -19.955], [-12.298, 16.691], [25.892, 4.335]], "o": [[8.979, 26.986], [14.314, -19.426], [-25.547, -4.277]], "v": [[-1.731, 30.579], [51.963, 41.827], [23.303, 47.898]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.992156922817, 0.796078503132, 0.180392161012, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.764705896378, 0.188235297799, 0.003921568859, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 51, "op": 87, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "右边嘴巴", "parent": 54, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76.801, "s": [100]}, {"t": 79.19921875, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -2.673, "ix": 10}, "p": {"a": 0, "k": [2.955, -7.042, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100.222, 109.899, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[0, 0], [-4.75, -1.75], [0, 0]], "o": [[0, 0], [4.229, 1.558], [0, 0]], "v": [[-57.579, -34.978], [-48.079, -32.978], [-41.829, -29.228]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57.6, "s": [{"i": [[0, 0], [-11.64, -4.873], [0, 0]], "o": [[0, 0], [15.999, 6.698], [0, 0]], "v": [[-87.704, -30.3], [-62.711, -28.952], [-42.154, -10.881]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 69.6, "s": [{"i": [[0, 0], [-31.096, -6.558], [0, 0]], "o": [[0, 0], [37.846, 7.982], [0, 0]], "v": [[-132.604, -6.164], [-79.787, -17.9], [-31.979, 21.323]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76.801, "s": [{"i": [[0, 0], [-7.732, -1.89], [0, 0]], "o": [[0, 0], [8.651, 2.114], [0, 0]], "v": [[-103.917, 3.662], [-87.448, 5.81], [-69.732, 13.266]], "c": false}]}, {"t": 79.19921875, "s": [{"i": [[0, 0], [-0.549, -0.219], [0, 0]], "o": [[0, 0], [0.627, 0.281], [0, 0]], "v": [[-90.357, 8.307], [-88.822, 8.889], [-87.578, 9.458]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.913725554943, 0.482352972031, 0.09411765635, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 56, "op": 80, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "左边嘴巴", "parent": 54, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76.801, "s": [100]}, {"t": 79.19921875, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -2.673, "ix": 10}, "p": {"a": 0, "k": [2.955, -7.042, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.222, 109.899, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 55.199, "s": [{"i": [[0, 0], [-4.75, -1.75], [0, 0]], "o": [[0, 0], [4.229, 1.558], [0, 0]], "v": [[-85.313, -37.203], [-75.813, -35.203], [-69.563, -31.453]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 57.6, "s": [{"i": [[0, 0], [-16.289, -9.179], [0, 0]], "o": [[0, 0], [12.582, 7.09], [0, 0]], "v": [[-95.576, -29.25], [-63.241, -24.37], [-44.13, -6.25]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [{"i": [[0, 0], [-29, -13], [0, 0]], "o": [[0, 0], [29, 13], [0, 0]], "v": [[-124, -26], [-65, -21], [-27, 21]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76.801, "s": [{"i": [[0, 0], [-4.154, -2.151], [0, 0]], "o": [[0, 0], [6.376, 1.821], [0, 0]], "v": [[-93.49, -7.888], [-79.493, -4.125], [-63.442, 2.747]], "c": false}]}, {"t": 79.19921875, "s": [{"i": [[0, 0], [-1.268, -0.514], [0, 0]], "o": [[0, 0], [0.627, 0.281], [0, 0]], "v": [[-86.786, -0.042], [-84.792, 0.374], [-83.153, 1.154]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.913725554943, 0.482352972031, 0.09411765635, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 56, "op": 80, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 1, "nm": "右边嘴巴1", "parent": 54, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55.602, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57.6, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [100]}, {"t": 79, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -5.195, "ix": 10}, "p": {"a": 0, "k": [-1.964, 5.685, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100.448, 104.161, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 57, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [35.312, 13.162]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-40.084, -14.94]], "v": [[122.623, 228.217], [121.547, 304.502], [198.6, 334.389], [218.389, 266.422], [186.596, 217.374]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [35.312, 13.162]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-40.084, -14.94]], "v": [[114.096, 241.953], [118.151, 320.081], [195.203, 349.968], [220.928, 267.547], [181.337, 221.157]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64.801, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [44.633, 13.103]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-45.736, -13.427]], "v": [[108.817, 252.17], [115.17, 323.857], [192.223, 353.744], [220.455, 274.05], [178.935, 227.618]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 69.6, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [36.897, 7.669]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-41.197, -8.562]], "v": [[105.489, 251.444], [125.97, 327.526], [191.966, 352.553], [205.94, 261.058], [173.085, 231.103]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 74.4, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [40.762, 8.147]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-42.173, -8.429]], "v": [[116.021, 266.758], [126.227, 328.717], [192.223, 353.744], [202.937, 277.936], [167.393, 240.068]], "c": true}]}, {"t": 77, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [36.531, 9.256]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-38.349, -9.716]], "v": [[128.746, 284.762], [126.227, 328.717], [192.223, 353.744], [192.65, 310.413], [162.157, 257.927]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "sy": [{"bm": {"a": 0, "k": 1, "ix": 1}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [100]}, {"t": 79, "s": [16]}], "ix": 2}, "gf": {"a": 0, "ix": 3}, "gs": {"a": 0, "k": 100, "ix": 4}, "a": {"a": 0, "k": 90, "ix": 5}, "gt": {"a": 0, "k": 2, "ix": 6}, "re": {"a": 0, "k": 0, "ix": 7}, "al": {"a": 0, "k": 1, "ix": 8}, "s": {"a": 0, "k": 100, "ix": 9}, "of": {"a": 0, "k": [-7, -35], "ix": 10}, "ty": 8, "nm": "渐变叠加"}], "sw": 512, "sh": 512, "sc": "#fed83d", "ip": 57, "op": 79, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 1, "nm": "左边嘴巴", "parent": 54, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 59, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [100]}, {"t": 79, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -5.195, "ix": 10}, "p": {"a": 0, "k": [-3.294, -4.561, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.448, 104.161, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 57, "s": [{"i": [[0, 0], [-17.348, -47.729], [11.321, 38.635], [0, 0], [34.263, 15.691]], "o": [[0, 0], [19.397, 53.364], [-9.745, -33.255], [0, 0], [-34.263, -15.691]], "v": [[133, 216], [112.603, 305.636], [219.961, 335.647], [219.01, 271.23], [195.263, 221.691]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64.801, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [34.263, 15.691]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-34.263, -15.691]], "v": [[133.116, 221.066], [112.72, 310.701], [220.077, 340.713], [232.116, 275.066], [195.38, 226.757]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 69.6, "s": [{"i": [[0, 0], [-17.348, -47.729], [-23.961, 32.353], [0, 0], [34.263, 15.691]], "o": [[0, 0], [19.397, 53.364], [20.328, -27.448], [0, 0], [-34.263, -15.691]], "v": [[131.459, 223.148], [111.062, 312.784], [218.42, 342.796], [230.459, 277.148], [196.245, 225.915]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74.4, "s": [{"i": [[0, 0], [-13.09, -35.269], [-26.094, 6.314], [0, 0], [27.212, 17.722]], "o": [[0, 0], [11.369, 30.633], [30.249, -7.32], [0, 0], [-24.341, -15.852]], "v": [[133.11, 234.009], [119.63, 300.511], [181.387, 328.227], [223.424, 278.887], [194.625, 238.191]], "c": true}]}, {"t": 79, "s": [{"i": [[0, 0], [-13.09, -35.269], [-19.512, 18.44], [0, 0], [23.356, 12.676]], "o": [[0, 0], [11.369, 30.633], [8.935, -8.444], [0, 0], [-23.518, -12.763]], "v": [[137.595, 252.238], [120.823, 292.989], [182.58, 320.705], [201.632, 286.742], [174.888, 254.197]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "sy": [{"bm": {"a": 0, "k": 1, "ix": 1}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [100]}, {"t": 79, "s": [16]}], "ix": 2}, "gf": {"a": 0, "ix": 3}, "gs": {"a": 0, "k": 100, "ix": 4}, "a": {"a": 0, "k": 90, "ix": 5}, "gt": {"a": 0, "k": 2, "ix": 6}, "re": {"a": 0, "k": 0, "ix": 7}, "al": {"a": 0, "k": 1, "ix": 8}, "s": {"a": 0, "k": 100, "ix": 9}, "of": {"a": 0, "k": [-7, -35], "ix": 10}, "ty": 8, "nm": "渐变叠加"}], "sw": 512, "sh": 512, "sc": "#fed83d", "ip": 57, "op": 79, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "嘴巴7", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.907, "ix": 10}, "p": {"a": 0, "k": [8.743, -2.125, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.092, 101.328, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-24.088, 0.978], [0, 0]], "o": [[0, 0], [24.045, -0.976], [0, 0]], "v": [[-37.933, 39.173], [-0.114, 56.479], [35.348, 35.255]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.882352948189, 0.105882354081, 0.105882354081, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 87, "op": 182.4, "st": 87, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "嘴巴8", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 1.907, "ix": 10}, "p": {"a": 0, "k": [8.848, 0.92, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.092, 101.328, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-24.088, 0.978], [0, 0]], "o": [[0, 0], [24.045, -0.976], [0, 0]], "v": [[-37.933, 39.173], [-0.114, 56.479], [35.348, 35.255]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 87, "op": 182.4, "st": 87, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "嘴巴3", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.907, "ix": 10}, "p": {"a": 0, "k": [8.661, -6.179, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.092, 101.328, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-24.088, 0.978], [0, 0]], "o": [[0, 0], [24.045, -0.976], [0, 0]], "v": [[-37.933, 39.173], [-0.114, 56.479], [35.348, 35.255]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.992156922817, 0.796078503132, 0.180392161012, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 87, "op": 182.4, "st": 87, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "嘴巴4", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.907, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [-6.311, -7.371, 0], "to": [-1.785, -1.173, 0], "ti": [1.785, 1.173, 0]}, {"t": 51, "s": [-17.019, -14.411, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.092, 101.328, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-4.913, -10.334], [17.064, -11.955], [29.923, -26.243]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-12.186, -17.065], [15.799, -17.293], [32.583, -32.619]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-12.186, -17.065], [15.799, -17.293], [32.583, -32.619]], "c": false}]}, {"t": 23, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-5.017, -13.293], [18.104, -11.988], [31.483, -26.293]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.882352948189, 0.105882354081, 0.105882354081, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 51, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "嘴巴5", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 5.907, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [-7.811, -11.371, 0], "to": [-1.503, -1.045, 0], "ti": [1.503, 1.045, 0]}, {"t": 51, "s": [-16.828, -17.639, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [89.092, 93.946, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-4.913, -10.334], [17.064, -11.955], [29.923, -26.243]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-12.186, -17.065], [15.799, -17.293], [32.583, -32.619]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-12.186, -17.065], [15.799, -17.293], [32.583, -32.619]], "c": false}]}, {"t": 23, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-5.017, -13.293], [18.104, -11.988], [31.483, -26.293]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.835294187069, 0.235294133425, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 51, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "嘴巴1", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.093, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [-3.811, -3.871, 0], "to": [-1.785, -1.173, 0], "ti": [1.785, 1.173, 0]}, {"t": 51, "s": [-14.519, -10.911, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96.092, 101.328, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-4.913, -10.334], [17.064, -11.955], [29.923, -26.243]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-12.186, -17.065], [15.799, -17.293], [32.583, -32.619]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-12.186, -17.065], [15.799, -17.293], [32.583, -32.619]], "c": false}]}, {"t": 23, "s": [{"i": [[0, 0], [-10.879, 4.304], [0, 0]], "o": [[0, 0], [10.879, -4.304], [0, 0]], "v": [[-5.017, -13.293], [18.104, -11.988], [31.483, -26.293]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.988235354424, 0.960784375668, 0.827451050282, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 51, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 0, "nm": "泪水合成2", "parent": 54, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [1]}, {"t": 74, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": -0.007, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [73.189, -59.484, 0], "to": [-0.009, 0.166, 0], "ti": [0.019, -0.375, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [68.925, -60.481, 0], "to": [-0.045, 0.865, 0], "ti": [0.02, -0.382, 0]}, {"t": 115, "s": [73.02, -56.199, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [330, 203, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 63, "s": [28.772, 28.895, 100]}, {"t": 74, "s": [90, 90.385, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 58, "op": 239, "st": 58, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 0, "nm": "泪水 合成 1", "parent": 54, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [1]}, {"t": 74, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0.94, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [-56.104, -61.721, 0], "to": [-0.009, 0.166, 0], "ti": [0.019, -0.375, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [-62.513, -64.626, 0], "to": [-0.045, 0.865, 0], "ti": [0.02, -0.382, 0]}, {"t": 115, "s": [-68.657, -58.346, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [330, 203, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 63, "s": [28.772, 28.895, 100]}, {"t": 74, "s": [90, 90.385, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 58, "op": 187.2, "st": 58, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "眼睛 2描邊", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 19, "s": [69.895, -0.686, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [69.895, -0.686, 0], "to": [-4.103, -0.137, 0], "ti": [-0.294, -0.527, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [45.276, -1.506, 0], "to": [0.294, 0.527, 0], "ti": [-4.397, -0.664, 0]}, {"t": 65, "s": [71.659, 2.476, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [3.576, -13.549], [-16.544, 5.578], [-2.467, 21.322]], "o": [[-14.244, 4.897], [-9.247, 35.035], [23.61, -7.961], [3.306, -28.581]], "v": [[-26.374, -127.991], [-59.316, -92.912], [-24.422, -48.53], [15.379, -94.743]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [{"i": [[18.36, -6.312], [3.576, -13.549], [-16.544, 5.578], [-2.467, 21.322]], "o": [[-14.244, 4.897], [-9.247, 35.035], [23.61, -7.961], [3.306, -28.581]], "v": [[-26.374, -127.991], [-59.316, -92.912], [-24.422, -48.53], [15.379, -94.743]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.241, -128.594], [-58.79, -95.964], [-25.525, -68.314], [7.828, -105.438]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[28.612, -1.423], [4.376, -38.467], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-18.34, 0.912], [-3.759, 33.045], [23.189, -2.799], [0.329, -20.779]], "v": [[0.269, -106.39], [-52.895, -62.405], [2.01, -12.402], [53.025, -62.204]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.311, -0.465], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [23.356, 0.308], [2.808, -27.397]], "v": [[-5.474, -120.882], [-63.984, -66.749], [-10.204, -4.747], [46.426, -57.227]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.28, -1.55], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [24.366, 1.07], [2.808, -27.397]], "v": [[6.759, -103.298], [-56.348, -52.291], [2.08, 8.745], [62.322, -44.203]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[32.185, 2.193], [4.955, -39.7], [-33.818, -2.183], [-4.65, 36.231]], "o": [[-28.321, -1.932], [-4.119, 33.002], [23.239, 1.5], [3.88, -30.231]], "v": [[2.065, -115.356], [-59.923, -57.868], [-5.154, 4.107], [53.116, -48.031]], "c": true}]}, {"t": 137, "s": [{"i": [[32.181, 2.25], [4.955, -39.7], [-32.817, -2.05], [-2.81, 34.602]], "o": [[-29.245, -2.045], [-4.119, 33.002], [24.342, 1.521], [2.368, -29.156]], "v": [[3.413, -111.793], [-58.662, -56.712], [-3.844, 3.305], [57.428, -49.134]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.603921592236, 0.305882364511, 0.105882354081, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.015], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "眼睛2遮罩", "parent": 54, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.609], "y": [0.421]}, "o": {"x": [0.247], "y": [0]}, "t": 0, "s": [-17.093]}, {"i": {"x": [0.675], "y": [-171.874]}, "o": {"x": [0.328], "y": [149.765]}, "t": 10, "s": [-12.318]}, {"i": {"x": [0.658], "y": [48.335]}, "o": {"x": [0.32], "y": [-47.493]}, "t": 19, "s": [-12.305]}, {"i": {"x": [0.701], "y": [1]}, "o": {"x": [0.348], "y": [0.606]}, "t": 30, "s": [-12.366]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [-3.093]}, {"t": 65, "s": [13.907]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.426}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [110.283, -2.667, 0], "to": [0.151, 0.452, 0], "ti": [1.937, -2.744, 0]}, {"i": {"x": 0.667, "y": 0.854}, "o": {"x": 0.333, "y": 0.348}, "t": 6, "s": [107.62, 2.869, 0], "to": [-1.479, 2.095, 0], "ti": [-0.287, -0.861, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.249, "y": 1}, "t": 10, "s": [103.72, 8.368, 0], "to": [0.584, 1.753, 0], "ti": [-0.234, -0.703, 0]}, {"i": {"x": 0.672, "y": 0}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [104.409, 9.871, 0], "to": [0.175, 0.526, 0], "ti": [7.796, 2.296, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.296}, "t": 30, "s": [103.966, 11.739, 0], "to": [-14.417, -4.247, 0], "ti": [0.534, -0.101, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [55.087, -4.265, 0], "to": [-0.823, 0.156, 0], "ti": [-0.859, 0, 0]}, {"t": 65, "s": [59.725, 0.841, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.577, 0.577, 0.577], "y": [0.077, 0.491, 1]}, "o": {"x": [0.228, 0.228, 0.228], "y": [0, 0, 0]}, "t": 0, "s": [119.509, 116.165, 100]}, {"i": {"x": [0.643, 0.643, 0.643], "y": [-1.281, 0.13, 1]}, "o": {"x": [0.304, 0.304, 0.304], "y": [1.629, -1.328, 0]}, "t": 10, "s": [116.749, 120.652, 100]}, {"i": {"x": [0.658, 0.658, 0.658], "y": [0.493, -0.588, 1]}, "o": {"x": [0.32, 0.32, 0.32], "y": [0.505, 0.595, 0]}, "t": 19, "s": [115.736, 119.537, 100]}, {"i": {"x": [0.701, 0.701, 0.701], "y": [1, 1, 1]}, "o": {"x": [0.348, 0.348, 0.348], "y": [0.337, 0.335, 0]}, "t": 30, "s": [110.73, 117.755, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.508, -118.146], [-59.056, -85.515], [-25.791, -57.866], [7.561, -94.99]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.895, -124.508], [-59.444, -91.878], [-26.179, -64.228], [7.174, -101.352]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-24.23, -124.818], [-57.779, -92.188], [-24.514, -64.538], [8.839, -101.663]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.241, -128.594], [-58.79, -95.964], [-25.525, -68.314], [7.828, -105.438]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[28.612, -1.423], [4.376, -38.467], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-18.34, 0.912], [-3.759, 33.045], [23.189, -2.799], [0.329, -20.779]], "v": [[0.269, -106.39], [-52.895, -62.405], [2.01, -12.402], [53.025, -62.204]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.311, -0.465], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [23.356, 0.308], [2.808, -27.397]], "v": [[-5.474, -120.882], [-63.984, -66.749], [-10.204, -4.747], [46.426, -57.227]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.28, -1.55], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [24.366, 1.07], [2.808, -27.397]], "v": [[6.759, -103.298], [-56.348, -52.291], [2.08, 8.745], [62.322, -44.203]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[32.185, 2.193], [4.955, -39.7], [-33.818, -2.183], [-4.65, 36.231]], "o": [[-28.321, -1.932], [-4.119, 33.002], [23.239, 1.5], [3.88, -30.231]], "v": [[2.065, -115.356], [-59.923, -57.868], [-5.154, 4.107], [53.116, -48.031]], "c": true}]}, {"t": 137, "s": [{"i": [[32.181, 2.25], [4.955, -39.7], [-32.817, -2.05], [-2.81, 34.602]], "o": [[-29.245, -2.045], [-4.119, 33.002], [24.342, 1.521], [2.368, -29.156]], "v": [[3.413, -111.793], [-58.662, -56.712], [-3.844, 3.305], [57.428, -49.134]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.015], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "眼珠 2", "parent": 54, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [39]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [53]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [0]}, {"t": 101, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [71.5, -104, 0], "to": [-3.333, 5, 0], "ti": [6.538, -0.827, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [51.5, -74, 0], "to": [-6.538, 0.827, 0], "ti": [-2.968, -1.563, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [32.272, -99.039, 0], "to": [2.968, 1.563, 0], "ti": [-7.339, -7.269, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62, "s": [69.306, -64.622, 0], "to": [7.339, 7.269, 0], "ti": [-1.167, -1.533, 0]}, {"t": 101, "s": [76.306, -55.427, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-136, -156, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [96, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 27, "s": [96, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [107, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [173, 128, 100]}, {"t": 101, "s": [173, 128, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [44, 60], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227450981736, 0.113725490868, 0.035294119269, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-136, -154], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "眼睛 2底色陰影", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 19, "s": [72.859, -1.248, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [72.859, -1.248, 0], "to": [-4.103, -0.137, 0], "ti": [-0.294, -0.527, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [48.24, -2.068, 0], "to": [0.294, 0.527, 0], "ti": [-4.397, -0.664, 0]}, {"t": 65, "s": [74.623, 1.914, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[19.091, 39.402], [6.042, -12.644], [-7.057, -7.001], [-10.09, 3.99]], "o": [[-10.493, -21.656], [-7.785, 16.292], [12.673, 12.573], [13.524, -5.348]], "v": [[-61.115, -64.695], [-51.046, -109.463], [-50.441, -63.011], [-8.804, -56.956]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [{"i": [[19.091, 39.402], [6.042, -12.644], [-7.057, -7.001], [-8.157, 7.138]], "o": [[-10.493, -21.656], [-7.785, 16.292], [12.673, 12.573], [10.945, -9.577]], "v": [[-59.772, -63.503], [-51.229, -107.236], [-50.12, -61.293], [-11.084, -55.156]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [{"i": [[16.315, 30.039], [6.042, -12.644], [-7.057, -7.001], [-8.267, 7.027]], "o": [[-11.485, -21.146], [-7.785, 16.292], [12.673, 12.573], [12.016, -10.213]], "v": [[-59.63, -74.102], [-51.34, -109.5], [-50.756, -77.143], [-13.858, -77.901]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[29.185, 17.797], [-0.908, -13.984], [-8.199, -5.62], [-8.267, 7.027]], "o": [[-21.008, -12.81], [1.137, 17.505], [14.661, 10.049], [12.016, -10.213]], "v": [[-42.734, -41.246], [-55.274, -80.456], [-38.039, -48.393], [5.272, -43.67]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [{"i": [[29.185, 17.797], [-0.908, -13.984], [-8.199, -5.62], [-8.267, 7.027]], "o": [[-21.008, -12.81], [1.137, 17.505], [14.661, 10.049], [12.016, -10.213]], "v": [[-40.176, -27.709], [-52.716, -66.919], [-35.481, -34.856], [7.829, -30.133]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[70.787, -1.028], [4.376, -38.467], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-71.464, 1.038], [-3.759, 33.045], [23.189, -2.799], [0.329, -20.779]], "v": [[-2.759, -26.13], [-52.895, -62.405], [2.01, -12.402], [49.881, -61.702]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[70.787, -1.028], [4.376, -38.467], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-71.464, 1.038], [-3.759, 33.045], [23.189, -2.799], [0.329, -20.779]], "v": [[-10.608, -18.736], [-66.634, -54.146], [-5.839, -5.008], [41.836, -56.077]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[70.787, -1.028], [2.836, -17.619], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-71.464, 1.038], [-3.888, 24.152], [23.189, -2.799], [0.329, -20.779]], "v": [[0.434, -4.314], [-58.824, -40.911], [3.059, 9.457], [59.433, -40.453]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 122, "s": [{"i": [[70.787, -1.028], [2.836, -17.619], [-30.089, 3.632], [-6.333, 38.731]], "o": [[-71.464, 1.038], [-3.888, 24.152], [23.189, -2.799], [3.353, -20.509]], "v": [[-7.304, -10.894], [-61.756, -48.093], [-4.678, 2.876], [49.072, -45.415]], "c": true}]}, {"t": 137, "s": [{"i": [[70.787, -1.028], [2.836, -17.619], [-30.089, 3.632], [-6.333, 38.731]], "o": [[-71.464, 1.038], [-3.888, 24.152], [23.189, -2.799], [3.353, -20.509]], "v": [[-4.336, -10.485], [-61.439, -46.592], [-1.711, 3.285], [53.662, -44.552]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.015], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.77254909277, 0.77254909277, 0.77254909277, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "眼睛2底色", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.609], "y": [0.421]}, "o": {"x": [0.247], "y": [0]}, "t": 0, "s": [-17.093]}, {"i": {"x": [0.675], "y": [-171.874]}, "o": {"x": [0.328], "y": [149.765]}, "t": 10, "s": [-12.318]}, {"i": {"x": [0.658], "y": [48.335]}, "o": {"x": [0.32], "y": [-47.493]}, "t": 19, "s": [-12.305]}, {"i": {"x": [0.701], "y": [1]}, "o": {"x": [0.348], "y": [0.606]}, "t": 30, "s": [-12.366]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [-3.093]}, {"t": 65, "s": [13.907]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.426}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [110.283, -2.667, 0], "to": [0.151, 0.452, 0], "ti": [1.937, -2.744, 0]}, {"i": {"x": 0.667, "y": 0.854}, "o": {"x": 0.333, "y": 0.348}, "t": 6, "s": [107.62, 2.869, 0], "to": [-1.479, 2.095, 0], "ti": [-0.287, -0.861, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.249, "y": 1}, "t": 10, "s": [103.72, 8.368, 0], "to": [0.584, 1.753, 0], "ti": [-0.234, -0.703, 0]}, {"i": {"x": 0.672, "y": 0}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [104.409, 9.871, 0], "to": [0.175, 0.526, 0], "ti": [7.796, 2.296, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.296}, "t": 30, "s": [103.966, 11.739, 0], "to": [-14.417, -4.247, 0], "ti": [0.534, -0.101, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [55.087, -4.265, 0], "to": [-0.823, 0.156, 0], "ti": [-0.859, 0, 0]}, {"t": 65, "s": [59.725, 0.841, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.577, 0.577, 0.577], "y": [0.077, 0.491, 1]}, "o": {"x": [0.228, 0.228, 0.228], "y": [0, 0, 0]}, "t": 0, "s": [119.509, 116.165, 100]}, {"i": {"x": [0.643, 0.643, 0.643], "y": [-1.281, 0.13, 1]}, "o": {"x": [0.304, 0.304, 0.304], "y": [1.629, -1.328, 0]}, "t": 10, "s": [116.749, 120.652, 100]}, {"i": {"x": [0.658, 0.658, 0.658], "y": [0.493, -0.588, 1]}, "o": {"x": [0.32, 0.32, 0.32], "y": [0.505, 0.595, 0]}, "t": 19, "s": [115.736, 119.537, 100]}, {"i": {"x": [0.701, 0.701, 0.701], "y": [1, 1, 1]}, "o": {"x": [0.348, 0.348, 0.348], "y": [0.337, 0.335, 0]}, "t": 30, "s": [110.73, 117.755, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.508, -118.146], [-59.056, -85.515], [-25.791, -57.866], [7.561, -94.99]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-26.661, -125.108], [-60.209, -92.477], [-26.945, -64.827], [6.408, -101.952]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.241, -128.594], [-58.79, -95.964], [-25.525, -68.314], [7.828, -105.438]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[28.612, -1.423], [4.376, -38.467], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-18.34, 0.912], [-3.759, 33.045], [23.189, -2.799], [0.329, -20.779]], "v": [[0.269, -106.39], [-52.895, -62.405], [2.01, -12.402], [53.025, -62.204]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.311, -0.465], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [23.356, 0.308], [2.808, -27.397]], "v": [[-5.474, -120.882], [-63.984, -66.749], [-10.204, -4.747], [46.426, -57.227]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.28, -1.55], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [24.366, 1.07], [2.808, -27.397]], "v": [[6.759, -103.298], [-56.348, -52.291], [2.08, 8.745], [62.322, -44.203]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[32.185, 2.193], [4.955, -39.7], [-33.818, -2.183], [-4.65, 36.231]], "o": [[-28.321, -1.932], [-4.119, 33.002], [23.239, 1.5], [3.88, -30.231]], "v": [[2.065, -115.356], [-59.923, -57.868], [-5.154, 4.107], [53.116, -48.031]], "c": true}]}, {"t": 137, "s": [{"i": [[32.181, 2.25], [4.955, -39.7], [-32.817, -2.05], [-2.81, 34.602]], "o": [[-29.245, -2.045], [-4.119, 33.002], [24.342, 1.521], [2.368, -29.156]], "v": [[3.413, -111.793], [-58.662, -56.712], [-3.844, 3.305], [57.428, -49.134]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.015], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "眼睛 2底色暗光", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [69.623, -7.159, 0], "to": [0, 0, 0], "ti": [6.09, 0.188, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.6, "s": [66.724, -7.988, 0], "to": [-6.722, -0.208, 0], "ti": [-0.076, -0.153, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [44.015, -7.792, 0], "to": [0.079, 0.159, 0], "ti": [-6.638, -0.555, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [58.508, -3.509, 0], "to": [1.45, 0.121, 0], "ti": [-1.528, -0.131, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [64.853, -5.818, 0], "to": [1.744, 0.15, 0], "ti": [-1.193, -0.128, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [67.41, -0.639, 0], "to": [1.778, 0.19, 0], "ti": [0, 0, 0]}, {"t": 65, "s": [73.99, -1.326, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 50, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [107.092, 106.6, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-28.368, -125.46], [-61.916, -92.83], [-28.651, -65.18], [4.701, -102.304]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-27.19, -125.338], [-60.738, -92.707], [-27.473, -65.058], [5.879, -102.182]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.241, -128.594], [-58.79, -95.964], [-25.525, -68.314], [7.828, -105.438]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[28.612, -1.423], [4.376, -38.467], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-18.34, 0.912], [-3.759, 33.045], [23.189, -2.799], [0.329, -20.779]], "v": [[0.269, -106.39], [-52.895, -62.405], [2.01, -12.402], [53.025, -62.204]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.311, -0.465], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [23.356, 0.308], [2.808, -27.397]], "v": [[-5.474, -120.882], [-63.984, -66.749], [-10.204, -4.747], [46.426, -57.227]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.28, -1.55], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [24.366, 1.07], [2.808, -27.397]], "v": [[6.759, -103.298], [-56.348, -52.291], [2.08, 8.745], [62.322, -44.203]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[32.185, 2.193], [4.955, -39.7], [-33.818, -2.183], [-4.65, 36.231]], "o": [[-28.321, -1.932], [-4.119, 33.002], [23.239, 1.5], [3.88, -30.231]], "v": [[2.065, -115.356], [-59.923, -57.868], [-5.154, 4.107], [53.116, -48.031]], "c": true}]}, {"t": 137, "s": [{"i": [[32.181, 2.25], [4.955, -39.7], [-32.817, -2.05], [-2.81, 34.602]], "o": [[-29.245, -2.045], [-4.119, 33.002], [24.342, 1.521], [2.368, -29.156]], "v": [[3.413, -111.793], [-58.662, -56.712], [-3.844, 3.305], [57.428, -49.134]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.015], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992156922817, 0.796078503132, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "眼睛 2底色高光", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [76.301, 6.917, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [75.33, 6.916, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [75.33, 6.901, 0], "to": [-4.103, -0.137, 0], "ti": [-0.127, -0.53, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [49.71, 6.098, 0], "to": [0.127, 0.53, 0], "ti": [-4.397, -0.664, 0]}, {"t": 65, "s": [76.093, 10.079, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [104.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.248, -110.749], [-58.796, -78.119], [-25.531, -50.469], [7.821, -87.594]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-25.248, -110.749], [-58.796, -78.119], [-25.531, -50.469], [7.821, -87.594]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-26.473, -129.239], [-60.021, -96.609], [-26.756, -68.959], [6.596, -106.083]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[28.612, -1.423], [4.376, -38.467], [-30.089, 3.632], [-0.591, 37.291]], "o": [[-18.34, 0.912], [-3.759, 33.045], [23.189, -2.799], [0.329, -20.779]], "v": [[0.269, -106.39], [-52.895, -62.405], [2.01, -12.402], [53.025, -62.204]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.311, -0.465], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [23.356, 0.308], [2.808, -27.397]], "v": [[-5.474, -120.882], [-63.984, -66.749], [-10.204, -4.747], [46.426, -57.227]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[32.19, 2.113], [4.955, -39.7], [-35.28, -1.55], [-3.978, 38.81]], "o": [[-27.014, -1.773], [-4.119, 33.002], [24.366, 1.07], [2.808, -27.397]], "v": [[6.759, -103.298], [-56.348, -52.291], [2.08, 8.745], [62.322, -44.203]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[32.185, 2.193], [4.955, -39.7], [-33.818, -2.183], [-4.65, 36.231]], "o": [[-28.321, -1.932], [-4.119, 33.002], [23.239, 1.5], [3.88, -30.231]], "v": [[2.065, -115.356], [-59.923, -57.868], [-5.154, 4.107], [53.116, -48.031]], "c": true}]}, {"t": 137, "s": [{"i": [[32.181, 2.25], [4.955, -39.7], [-32.817, -2.05], [-2.81, 34.602]], "o": [[-29.245, -2.045], [-4.119, 33.002], [24.342, 1.521], [2.368, -29.156]], "v": [[3.413, -111.793], [-58.662, -56.712], [-3.844, 3.305], [57.428, -49.134]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.015], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "眼睛描邊", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 51, "s": [-37.31, -115.87, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 65, "s": [-37.31, -115.87, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-36, -106, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[20.404, -5.253], [7.21, -21.349], [-21.342, 11.086], [-5.723, 19.509]], "o": [[-15.66, 4.032], [-9.898, 29.311], [15.482, -8.042], [7.862, -26.8]], "v": [[-32.068, -127.07], [-70.575, -87.371], [-27.586, -49.66], [2.629, -87.179]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [{"i": [[20.404, -5.253], [7.21, -21.349], [-21.342, 11.086], [-5.723, 19.509]], "o": [[-15.66, 4.032], [-9.898, 29.311], [15.482, -8.042], [7.862, -26.8]], "v": [[-32.068, -127.07], [-70.575, -87.371], [-27.586, -49.66], [2.629, -87.179]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-57.135, -110.478], [-90.683, -77.847], [-57.419, -50.198], [-24.066, -87.322]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[27.609, 3.296], [3.457, -25.416], [-37.7, -5.776], [-2.963, 18.418]], "o": [[-39.405, -4.704], [-2.94, 21.614], [38.205, 5.854], [4.473, -27.803]], "v": [[-40.687, -104.345], [-100.793, -62.596], [-49.584, -8.63], [8.925, -45.966]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[31.849, 1.529], [5.55, -31.209], [-37.861, -2.124], [-3.83, 24.161]], "o": [[-33.054, -1.587], [-4.27, 24.011], [40.484, 2.271], [5.259, -33.175]], "v": [[-57.702, -105.979], [-121.598, -54.134], [-74.769, 7.955], [-9.914, -40.56]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-54.984, -96.057], [-120.424, -44.23], [-68.427, 16.386], [-3.59, -30.594]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[33.591, 2.39], [4.832, -35.093], [-37.861, -2.124], [-4.88, 31.499]], "o": [[-33.009, -2.349], [-3.395, 24.654], [40.484, 2.271], [5.599, -36.144]], "v": [[-53.541, -107.369], [-117.952, -50.934], [-68.529, 12.243], [-4.207, -36.786]], "c": true}]}, {"t": 137, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-55.399, -103.026], [-120.84, -51.199], [-68.842, 9.418], [-4.005, -37.563]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.603921592236, 0.305882364511, 0.105882354081, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "形状图层 5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [0]}, {"t": 77, "s": [74]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 59, "s": [12.541]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [14.541]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [8.541]}, {"t": 111, "s": [6.5]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [-82.312, -3.268, 0], "to": [0.06, -1.047, 0], "ti": [1.259, -2.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [-81.952, -9.55, 0], "to": [-1.259, 2.667, 0], "ti": [1.296, -7.088, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [-89.865, 12.736, 0], "to": [-1.296, 7.088, 0], "ti": [-0.023, -3.373, 0]}, {"t": 111, "s": [-89.727, 32.975, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-96.508, 63.854, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 59, "s": [104.094, 96.354, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [120.094, 105.354, 100]}, {"t": 76, "s": [105.094, 83.354, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [69.016, 69.016], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.208, 0.278, 0.5, 1, 0.208, 0.278, 1, 1, 0.208, 0.278, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [1, 0], "ix": 5}, "e": {"a": 0, "k": [1, -34], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "45356", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [164.008, 351.008], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [173, 116], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 6}, "o": {"a": 0, "k": 70, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 181, "st": 0, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "形状图层 4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 77, "s": [74]}, {"t": 149, "s": [60]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 59, "s": [-14.541]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [-7.541]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [-14.541]}, {"t": 111, "s": [-6.541]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [104.251, 12.403, 0], "to": [-0.733, -4.65, 0], "ti": [2.963, -4.847, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [99.853, -15.498, 0], "to": [-2.963, 4.847, 0], "ti": [-1.913, -7.307, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 76, "s": [86.474, 41.484, 0], "to": [1.913, 7.307, 0], "ti": [-4.143, 2.19, 0]}, {"t": 111, "s": [111.33, 28.343, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [88.932, 52.754, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 59, "s": [-120.094, 105.354, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [-120.094, 105.354, 100]}, {"t": 76, "s": [-120.094, 105.354, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [61.084, 61.084], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.206, 0.277, 0.5, 1, 0.207, 0.278, 1, 1, 0.208, 0.278, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [28, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "23245", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-69.958, 341.792], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [184, 113], "ix": 3}, "r": {"a": 0, "k": -7, "ix": 6}, "o": {"a": 0, "k": 70, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 181, "st": 0, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 1, "nm": "阴影 2", "sr": 1, "ks": {"o": {"a": 0, "k": 71, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0.285]}, "t": 55.199, "s": [0.333]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 65, "s": [-18]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 79.199, "s": [-15]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98.4, "s": [-22]}, {"t": 120, "s": [-13]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.223}, "t": 55.199, "s": [162.083, 365.965, 0], "to": [-9.002, 10.133, 0], "ti": [0.359, -1.555, 0]}, {"i": {"x": 0.667, "y": 0.545}, "o": {"x": 0.333, "y": 0}, "t": 64.801, "s": [123, 410, 0], "to": [-0.272, 1.181, 0], "ti": [-8.634, 8.898, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.558}, "t": 72, "s": [138.749, 389.369, 0], "to": [7.212, -7.432, 0], "ti": [-2.655, 1.517, 0]}, {"i": {"x": 0.667, "y": 0.495}, "o": {"x": 0.333, "y": 0}, "t": 79.199, "s": [156, 377, 0], "to": [3.106, -1.775, 0], "ti": [1.625, -3.847, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.596}, "t": 88.801, "s": [155.524, 382.636, 0], "to": [-1.427, 3.378, 0], "ti": [0.156, -0.39, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 98.4, "s": [153, 389, 0], "to": [-0.333, 0.833, 0], "ti": [-0.333, 1.333, 0]}, {"t": 120, "s": [155, 381, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [152, 353, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.825, 0.825, 0.825], "y": [1, 1, 1]}, "o": {"x": [0.407, 0.407, 0.407], "y": [0.18, 0.369, -0.51]}, "t": 55.199, "s": [93.398, 102.398, 100]}, {"i": {"x": [0.582, 0.582, 0.582], "y": [0.696, 0.497, 0.563]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [111, 111, 100]}, {"i": {"x": [0.834, 0.834, 0.834], "y": [1, 1, 1]}, "o": {"x": [0.415, 0.415, 0.415], "y": [1.352, 0.497, 0.447]}, "t": 72, "s": [88.236, 105.593, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 79.199, "s": [83, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 98.4, "s": [84, 100, 100]}, {"t": 120, "s": [90, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [25.017, 28.223]], "o": [[0, 0], [0, 0], [-40.989, -46.243]], "v": [[126.924, 300.075], [197.341, 368.357], [184.37, 298.254]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "sw": 512, "sh": 512, "sc": "#fbc627", "ip": 55, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 40, "ty": 1, "nm": "阴影", "sr": 1, "ks": {"o": {"a": 0, "k": 71, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [-9.109]}, "t": 59, "s": [19.096]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 65, "s": [18]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [10]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [22]}, {"t": 119, "s": [21]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.379}, "t": 59, "s": [371.015, 390.432, 0], "to": [-0.786, 6.322, 0], "ti": [0.204, -0.886, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 65, "s": [356, 401, 0], "to": [-0.5, 2.167, 0], "ti": [1.5, 1.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 79.199, "s": [344, 380, 0], "to": [-1.5, -1.667, 0], "ti": [-0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 98, "s": [347, 391, 0], "to": [0.167, 0.167, 0], "ti": [0.333, 1.667, 0]}, {"t": 119, "s": [345, 381, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [152, 353, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [-93.398, 102.398, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [-93.398, 102.398, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 98, "s": [-88.398, 102.398, 100]}, {"t": 119, "s": [-93.398, 102.398, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [27, 31]], "o": [[0, 0], [0, 0], [-44.239, -50.793]], "v": [[118, 308], [194, 383], [180, 306]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "sw": 512, "sh": 512, "sc": "#f5b117", "ip": 59, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 41, "ty": 4, "nm": "眼珠", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [27]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [27]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [53]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [0]}, {"t": 101, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-18.5, -109, 0], "to": [-2.667, 5.333, 0], "ti": [5.147, -3.979, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [-34.5, -77, 0], "to": [-5.147, 3.979, 0], "ti": [2.564, -1.619, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [-49.38, -85.127, 0], "to": [-2.564, 1.619, 0], "ti": [2.584, -4.123, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62, "s": [-49.882, -67.286, 0], "to": [-2.584, 4.123, 0], "ti": [2.5, -1.149, 0]}, {"t": 101, "s": [-64.882, -60.39, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-136, -156, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [96, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [96, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [107, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [173, 128, 100]}, {"t": 101, "s": [173, 128, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-12.15, 0], [0, -16.569], [12.15, 0], [0, 16.569]], "o": [[12.15, 0], [0, 16.569], [-12.15, 0], [0, -16.569]], "v": [[0, -30], [20.465, -3.181], [-2.526, 27.856], [-22, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [{"i": [[-12.15, 0], [0, -16.569], [12.15, 0], [0, 16.569]], "o": [[12.15, 0], [0, 16.569], [-12.15, 0], [0, -16.569]], "v": [[0, -30], [13.658, -2.896], [-0.868, 29.263], [-22, 0]], "c": true}]}, {"t": 62, "s": [{"i": [[-12.15, 0], [0, -16.569], [12.15, 0], [0, 16.569]], "o": [[12.15, 0], [0, 16.569], [-12.15, 0], [0, -16.569]], "v": [[0, -30], [22, 0], [0, 30], [-22, 0]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227450981736, 0.113725490868, 0.035294119269, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-136, -154], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 4, "nm": "眼睛 2底色陰影 2", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 19, "s": [-14.891, -6.302, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [-14.891, -6.302, 0], "to": [-4.103, -0.137, 0], "ti": [-0.294, -0.527, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [-39.51, -7.122, 0], "to": [0.294, 0.527, 0], "ti": [-4.397, -0.664, 0]}, {"t": 65, "s": [-13.127, -3.14, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[19.091, 39.402], [6.042, -12.644], [-7.057, -7.001], [-10.09, 3.99]], "o": [[-10.493, -21.656], [-7.785, 16.292], [12.673, 12.573], [13.524, -5.348]], "v": [[-61.115, -64.695], [-51.046, -109.463], [-50.441, -63.011], [-8.804, -56.956]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [{"i": [[19.091, 39.402], [6.042, -12.644], [-7.057, -7.001], [-8.157, 7.138]], "o": [[-10.493, -21.656], [-7.785, 16.292], [12.673, 12.573], [10.945, -9.577]], "v": [[-59.772, -63.503], [-51.229, -107.236], [-50.12, -61.293], [-11.084, -55.156]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [{"i": [[17.881, 25.862], [6.042, -12.644], [-7.057, -7.001], [-8.267, 7.027]], "o": [[-13.685, -19.793], [-7.785, 16.292], [12.673, 12.573], [12.016, -10.213]], "v": [[-53.81, -58.639], [-45.396, -93.606], [-44.812, -61.249], [-7.913, -62.008]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [{"i": [[27.419, 19.057], [0.178, -13.774], [-8.021, -5.836], [-8.267, 7.027]], "o": [[-19.864, -13.901], [-0.257, 17.315], [14.351, 10.443], [12.016, -10.213]], "v": [[-60.605, -41.25], [-71.237, -78.345], [-54.532, -46.521], [-14.294, -42.37]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[29.185, 17.797], [-0.908, -13.984], [-8.199, -5.62], [-8.267, 7.027]], "o": [[-21.008, -12.81], [1.137, 17.505], [14.661, 10.049], [12.016, -10.213]], "v": [[-63.482, -36.308], [-76.022, -75.519], [-58.787, -43.456], [-15.476, -38.733]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [{"i": [[29.185, 17.797], [-0.908, -13.984], [-8.199, -5.62], [-10.601, 2.31]], "o": [[-21.008, -12.81], [1.137, 17.505], [14.661, 10.049], [12.158, -2.649]], "v": [[-72.278, -30.1], [-85.978, -72.678], [-67.524, -35.546], [-20.274, -22.437]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[70.787, -1.028], [1.913, -21.779], [-30.089, 3.632], [-0.688, 31.767]], "o": [[-71.464, 1.038], [-2.437, 27.752], [23.189, -2.799], [0.284, -13.115]], "v": [[-40.438, -23.647], [-92.156, -58.785], [-33.578, -9.861], [15.312, -56.768]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[70.787, -1.028], [1.913, -21.779], [-30.089, 3.632], [-0.688, 31.767]], "o": [[-71.464, 1.038], [-2.437, 27.752], [23.189, -2.799], [0.284, -13.115]], "v": [[-61.558, -9.009], [-113.276, -44.147], [-54.698, 4.777], [-5.808, -42.131]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[70.787, -1.028], [1.913, -21.779], [-30.089, 3.632], [-0.688, 31.767]], "o": [[-71.464, 1.038], [-2.437, 27.752], [23.189, -2.799], [0.284, -13.115]], "v": [[-58.426, -0.74], [-113.907, -36.308], [-51.566, 13.046], [1.633, -32.938]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 122, "s": [{"i": [[70.787, -1.028], [2.836, -17.619], [-30.089, 3.632], [-6.333, 38.731]], "o": [[-71.464, 1.038], [-3.888, 24.151], [23.189, -2.799], [3.353, -20.509]], "v": [[-55.33, -4.323], [-109.782, -41.521], [-52.705, 9.448], [1.045, -38.843]], "c": true}]}, {"t": 137, "s": [{"i": [[70.787, -1.028], [2.836, -17.619], [-30.089, 3.632], [-6.333, 38.731]], "o": [[-71.464, 1.038], [-3.888, 24.152], [23.189, -2.799], [3.353, -20.509]], "v": [[-55.793, -7.855], [-112.896, -43.961], [-53.168, 5.916], [2.205, -41.921]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.015], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.77254909277, 0.77254909277, 0.77254909277, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 43, "ty": 4, "nm": "眼睛底色", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.589], "y": [-1.469]}, "o": {"x": [0.165], "y": [0]}, "t": 0, "s": [-22.093]}, {"i": {"x": [0.692], "y": [1]}, "o": {"x": [0.348], "y": [0.123]}, "t": 27, "s": [-20.924]}, {"i": {"x": [0.802], "y": [1]}, "o": {"x": [0.438], "y": [0]}, "t": 39, "s": [-12.093]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [-22.093]}, {"t": 65, "s": [1.907]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-50.81, -116.37, 0], "to": [1.417, 1.75, 0], "ti": [-0.769, 0.629, 0]}, {"i": {"x": 0.659, "y": 0.568}, "o": {"x": 0.297, "y": 0}, "t": 27, "s": [-42.31, -105.87, 0], "to": [0.353, -0.289, 0], "ti": [1.171, 2.977, 0]}, {"i": {"x": 0.815, "y": 1}, "o": {"x": 0.425, "y": 0.412}, "t": 39, "s": [-39.737, -112.685, 0], "to": [-1.38, -3.507, 0], "ti": [-0.406, 0.901, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [-46.198, -120.144, 0], "to": [0.75, -1.667, 0], "ti": [-1.481, -0.712, 0]}, {"t": 65, "s": [-37.31, -115.87, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-36, -106, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0, "s": [118.525, 116.182, 100]}, {"i": {"x": [0.583, 0.583, 0.583], "y": [0.735, 0.559, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [119.597, 116.485, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.417, 0.417, 0.417], "y": [4.356, 0.578, 0]}, "t": 39, "s": [107.053, 100.198, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [106.289, 87.785, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-30.621, -115.513], [-64.17, -82.883], [-30.905, -55.233], [2.448, -92.357]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-33.083, -126.297], [-66.631, -93.667], [-33.366, -66.017], [-0.014, -103.142]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-57.135, -110.478], [-90.683, -77.847], [-57.419, -50.198], [-24.066, -87.322]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[27.609, 3.296], [3.457, -25.416], [-37.7, -5.776], [-2.963, 18.418]], "o": [[-39.405, -4.704], [-2.94, 21.614], [38.205, 5.854], [4.473, -27.803]], "v": [[-40.687, -104.345], [-100.793, -62.596], [-49.584, -8.63], [8.925, -45.966]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[31.849, 1.529], [5.55, -31.209], [-37.861, -2.124], [-3.83, 24.161]], "o": [[-33.054, -1.587], [-4.27, 24.011], [40.484, 2.271], [5.259, -33.175]], "v": [[-57.702, -105.979], [-121.598, -54.134], [-74.769, 7.955], [-9.914, -40.56]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-54.984, -96.057], [-120.424, -44.23], [-68.427, 16.386], [-3.59, -30.594]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[33.591, 2.39], [4.832, -35.093], [-37.861, -2.124], [-4.88, 31.499]], "o": [[-33.009, -2.349], [-3.395, 24.654], [40.484, 2.271], [5.599, -36.144]], "v": [[-53.541, -107.369], [-117.952, -50.934], [-68.529, 12.243], [-4.207, -36.786]], "c": true}]}, {"t": 137, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-55.399, -103.026], [-120.84, -51.199], [-68.842, 9.418], [-4.005, -37.563]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 44, "ty": 4, "nm": "眼睛底色暗光", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [-8.093]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-47.723, -115.702, 0], "to": [1.667, -1.167, 0], "ti": [-1.606, 1.66, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [-37.723, -122.702, 0], "to": [1.606, -1.66, 0], "ti": [0.061, 0.493, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 65, "s": [-38.09, -125.661, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 95, "s": [-36.087, -121.478, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 122, "s": [-38.09, -125.661, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-36, -106, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0, "s": [106.092, 111.873, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-30.968, -125.377], [-64.516, -92.746], [-31.251, -65.097], [2.101, -102.221]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-33.083, -126.297], [-66.631, -93.667], [-33.366, -66.017], [-0.014, -103.142]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-57.135, -110.478], [-90.683, -77.847], [-57.419, -50.198], [-24.066, -87.322]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[27.609, 3.296], [3.457, -25.416], [-37.7, -5.776], [-2.963, 18.418]], "o": [[-39.405, -4.704], [-2.94, 21.614], [38.205, 5.854], [4.473, -27.803]], "v": [[-40.687, -104.345], [-100.793, -62.596], [-49.584, -8.63], [8.925, -45.966]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[31.849, 1.529], [5.55, -31.209], [-37.861, -2.124], [-3.83, 24.161]], "o": [[-33.054, -1.587], [-4.27, 24.011], [40.484, 2.271], [5.259, -33.175]], "v": [[-57.702, -105.979], [-121.598, -54.134], [-74.769, 7.955], [-9.914, -40.56]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-54.984, -96.057], [-120.424, -44.23], [-68.427, 16.386], [-3.59, -30.594]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[33.591, 2.39], [4.832, -35.093], [-37.861, -2.124], [-4.88, 31.499]], "o": [[-33.009, -2.349], [-3.395, 24.654], [40.484, 2.271], [5.599, -36.144]], "v": [[-53.541, -107.369], [-117.952, -50.934], [-68.529, 12.243], [-4.207, -36.786]], "c": true}]}, {"t": 137, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-55.399, -103.026], [-120.84, -51.199], [-68.842, 9.418], [-4.005, -37.563]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078491211, 0.84705889225, 0.239215701818, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "眼睛边缘高光", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.569], "y": [-4.304]}, "o": {"x": [0.176], "y": [0]}, "t": 0, "s": [-4.093]}, {"i": {"x": [0.828], "y": [1]}, "o": {"x": [0.329], "y": [0.246]}, "t": 20, "s": [-3.866]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [1.907]}, {"t": 65, "s": [1.907]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.778, "y": 0}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-39.533, -93.828, 0], "to": [0.097, -0.436, 0], "ti": [-0.068, -0.024, 0]}, {"i": {"x": 0.667, "y": 0.493}, "o": {"x": 0.333, "y": 0.174}, "t": 20, "s": [-38.916, -93.473, 0], "to": [0.244, 0.085, 0], "ti": [-1.489, 1.579, 0]}, {"i": {"x": 0.667, "y": 0.751}, "o": {"x": 0.333, "y": 0.218}, "t": 30, "s": [-35.348, -93.722, 0], "to": [0.94, -0.996, 0], "ti": [-0.22, 1.671, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.333}, "t": 37, "s": [-33.405, -99.703, 0], "to": [0.396, -3.01, 0], "ti": [-0.043, 0.193, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [-36.453, -108.59, 0], "to": [0, 0, 0], "ti": [0.088, -0.084, 0]}, {"t": 65, "s": [-36.983, -108.087, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-36, -106, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.831, 0.831, 0.831], "y": [1, 1, 1]}, "o": {"x": [0.165, 0.165, 0.165], "y": [0, 0, 0]}, "t": 0, "s": [106.092, 101.328, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [96.092, 101.328, 100]}, {"t": 65, "s": [96.092, 101.328, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-30.968, -125.377], [-64.516, -92.746], [-31.251, -65.097], [2.101, -102.221]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-33.083, -126.297], [-66.631, -93.667], [-33.366, -66.017], [-0.014, -103.142]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [{"i": [[18.36, -6.312], [4.139, -13.388], [-17.059, 7.462], [-2.537, 15.371]], "o": [[-14.244, 4.897], [-7.592, 24.558], [19.643, -8.592], [3.791, -22.968]], "v": [[-57.135, -110.478], [-90.683, -77.847], [-57.419, -50.198], [-24.066, -87.322]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65, "s": [{"i": [[27.609, 3.296], [3.457, -25.416], [-37.7, -5.776], [-2.963, 18.418]], "o": [[-39.405, -4.704], [-2.94, 21.614], [38.205, 5.854], [4.473, -27.803]], "v": [[-40.687, -104.345], [-100.793, -62.596], [-49.584, -8.63], [8.925, -45.966]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84, "s": [{"i": [[31.849, 1.529], [5.55, -31.209], [-37.861, -2.124], [-3.83, 24.161]], "o": [[-33.054, -1.587], [-4.27, 24.011], [40.484, 2.271], [5.259, -33.175]], "v": [[-57.702, -105.979], [-121.598, -54.134], [-74.769, 7.955], [-9.914, -40.56]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 102, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-54.984, -96.057], [-120.424, -44.23], [-68.427, 16.386], [-3.59, -30.594]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[33.591, 2.39], [4.832, -35.093], [-37.861, -2.124], [-4.88, 31.499]], "o": [[-33.009, -2.349], [-3.395, 24.654], [40.484, 2.271], [5.599, -36.144]], "v": [[-53.541, -107.369], [-117.952, -50.934], [-68.529, 12.243], [-4.207, -36.786]], "c": true}]}, {"t": 137, "s": [{"i": [[33.591, 2.39], [7.16, -34.693], [-37.861, -2.124], [-4.492, 27.138]], "o": [[-33.009, -2.349], [-4.929, 23.884], [40.484, 2.271], [5.485, -33.139]], "v": [[-55.399, -103.026], [-120.84, -51.199], [-68.842, 9.418], [-4.005, -37.563]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 46, "ty": 4, "nm": "高光4", "parent": 48, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -37.002, "ix": 10}, "p": {"a": 0, "k": [168.127, -101.804, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [10.75, -144.375, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [119.626, 155.945, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6.5, 5.5], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.75, -144.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 81, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 47, "ty": 4, "nm": "高光3", "parent": 49, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -38.427, "ix": 10}, "p": {"a": 0, "k": [168.118, -102.348, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [10.75, -144.375, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [119.599, 155.98, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6.5, 5.5], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.75, -144.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 81, "st": 0, "bm": 0}, {"ddd": 0, "ind": 48, "ty": 4, "nm": "高光 2", "parent": 53, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [43.56]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [22.56]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 82, "s": [22.56]}, {"t": 92, "s": [17.56]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [90.662, -103.113, 0], "to": [-19.113, -22.919, 0], "ti": [14.393, -3.305, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [32.122, -139.441, 0], "to": [-9.718, 2.231, 0], "ti": [0, 0, 0]}, {"t": 82, "s": [32.122, -139.441, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [147.63, -105.016, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [98.301, 55.934, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [28, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [147, -105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 81, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 49, "ty": 4, "nm": "高光", "parent": 54, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [44.815]}, {"t": 62, "s": [23.815]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [111.042, -94.954, 0], "to": [-19.167, -23.18, 0], "ti": [14.917, -2.969, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [51.542, -132.31, 0], "to": [-10.072, 2.005, 0], "ti": [0, 0, 0]}, {"t": 82, "s": [51.542, -132.31, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [147.63, -105.016, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.738, 55.92, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [28, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [147, -105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 81, "st": 0, "bm": 0}, {"ddd": 0, "ind": 50, "ty": 0, "nm": "身体高光 2", "parent": 54, "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [10]}, {"t": 62, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [6, 22, 0], "to": [0.296, -0.373, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [7.777, 19.76, 0], "to": [0, 0, 0], "ti": [0.302, -0.376, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [6, 22, 0], "to": [-0.302, 0.376, 0], "ti": [-0.146, -1.551, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [5.965, 22.014, 0], "to": [0.146, 1.551, 0], "ti": [-0.303, -0.507, 0]}, {"i": {"x": 0.667, "y": 0.816}, "o": {"x": 0.333, "y": 0}, "t": 99, "s": [6.876, 31.305, 0], "to": [0.113, 0.19, 0], "ti": [-0.132, 1.006, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.738}, "t": 104, "s": [9.256, 26.78, 0], "to": [0.22, -1.679, 0], "ti": [-0.094, 0.537, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 114, "s": [7.781, 25.058, 0], "to": [0.15, -0.859, 0], "ti": [0, -0.182, 0]}, {"t": 136, "s": [7.779, 26.153, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262, 286, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0, 0]}, "t": 0, "s": [94.3, 96, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 51, "s": [96.3, 96, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [90.3, 96, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 62, "s": [91.3, 96, 100]}, {"i": {"x": [0.562, 0.562, 0.562], "y": [1, 0.883, 1]}, "o": {"x": [0.188, 0.188, 0.188], "y": [0, 0, 0]}, "t": 82, "s": [101.3, 96, 100]}, {"i": {"x": [0.842, 0.842, 0.842], "y": [1, 1, 1]}, "o": {"x": [0.368, 0.368, 0.368], "y": [0, -1.154, 0]}, "t": 87, "s": [101.3, 98.514, 100]}, {"t": 99, "s": [101.3, 98, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 187.2, "st": 0, "bm": 0}, {"ddd": 0, "ind": 51, "ty": 4, "nm": "身体 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [12]}, {"t": 62, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [257, 431, 0], "to": [0, 0.667, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [257, 435, 0], "to": [0, 0, 0], "ti": [0, 0.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [257, 431, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27, "s": [257, 431, 0], "to": [-1.333, -1.5, 0], "ti": [1.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [249, 422, 0], "to": [-1.5, 0.167, 0], "ti": [0.5, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62, "s": [248, 432, 0], "to": [-0.5, 0, 0], "ti": [0.333, 1.667, 0]}, {"t": 73, "s": [246, 422, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [1, 143, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [104, 97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [97, 111, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [96, 87, 100]}, {"t": 73, "s": [94, 96, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[61.122, -73.889], [-18.872, -50.319], [-19.033, -15.334], [-61.839, 52.938], [-9.123, 17.797], [60.741, 54.944]], "o": [[-30.827, 37.266], [8.068, 21.513], [59.29, 47.768], [13.246, -11.339], [26.835, -52.353], [-62.408, -56.451]], "v": [[-114.122, -97.111], [-135.66, 50.679], [-95.29, 107.232], [105.789, 109.695], [140.103, 65.209], [108.609, -115.622]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [{"i": [[61.122, -73.889], [-18.872, -50.319], [-19.033, -15.334], [-61.839, 52.938], [-9.123, 17.797], [60.741, 54.944]], "o": [[-30.827, 37.266], [8.068, 21.513], [59.29, 47.768], [13.246, -11.339], [26.835, -52.353], [-62.408, -56.451]], "v": [[-114.122, -97.111], [-135.66, 50.679], [-95.29, 107.232], [105.789, 109.695], [140.103, 65.209], [108.609, -115.622]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [{"i": [[69.286, -157.774], [-5.778, -26.113], [-21.743, -11.242], [-65.11, 35.874], [-13.455, 35.306], [20.939, 58.033]], "o": [[-16.322, 20.088], [8.351, 41.047], [49.133, 29.931], [14.876, -8.383], [11.669, -30.777], [-46.241, -121.248]], "v": [[-131.539, -45.808], [-146.407, 54.043], [-90.516, 120.4], [104.245, 122.783], [159.198, 62.465], [148.39, -58.833]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62, "s": [{"i": [[65.122, -188.625], [-7.383, -53.233], [-24.71, -6.761], [-81.79, 25.259], [-6.103, 41.01], [1.029, 5.62]], "o": [[-0.442, 1.279], [8.66, 62.436], [38.013, 10.4], [16.66, -5.145], [8.66, -58.189], [-28.609, -156.32]], "v": [[-140.122, -29.294], [-171.66, 50.679], [-85.29, 134.818], [112.79, 133.833], [180.103, 59.461], [146.61, -23.668]], "c": true}]}, {"t": 82, "s": [{"i": [[17.038, -219.209], [-16.278, -19.795], [-21.911, -6.208], [-38.677, 14.621], [-21.628, 34.044], [-2.033, 26.313]], "o": [[-2.852, 36.69], [23.333, 28.374], [35.098, 9.944], [16.31, -6.166], [10.815, -17.024], [15.704, -203.307]], "v": [[-148.304, -5.104], [-118.604, 89.434], [-39.606, 141.618], [63.443, 140.016], [137.844, 84.165], [164.061, 14.41]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078491211, 0.84705889225, 0.239215701818, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 82, "st": 0, "bm": 0}, {"ddd": 0, "ind": 52, "ty": 0, "nm": "身体阴影 合成 1", "parent": 54, "tt": 1, "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.028, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [7.325, 2.452, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [6.325, 2.452, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [7.951, 5.098, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.199, "s": [8.071, 7.271, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.4, "s": [8.501, 3.351, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100.801, "s": [8.442, 5.417, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 115.19921875, "s": [8.501, 3.351, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [95, 93.398, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [115.093, 95.398, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 67.199, "s": [118.093, 95.398, 100]}, {"t": 81.599609375, "s": [101.093, 99.122, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 187.2, "st": 0, "bm": 0}, {"ddd": 0, "ind": 53, "ty": 4, "nm": "身体", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 82, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 94, "s": [0]}, {"t": 106, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 2;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 84, "s": [249, 432, 0], "to": [0.667, 0.333, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 94, "s": [253, 434, 0], "to": [0.833, 0.5, 0], "ti": [-0.167, -0.167, 0]}, {"t": 106, "s": [254, 435, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 2;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-7, 145, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0.25, 0.25, 0]}, "t": 84, "s": [99.074, 101.556, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 94, "s": [100, 96, 100]}, {"t": 106, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [306, 306], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.913725495338, 0.482352942228, 0.0941176489, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-8, -7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078491211, 0.84705889225, 0.239215701818, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 82, "op": 182.4, "st": 0, "bm": 0}, {"ddd": 0, "ind": 54, "ty": 4, "nm": "身体", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [12]}, {"t": 62, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [257, 431, 0], "to": [0, 0.667, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [257, 435, 0], "to": [0, 0, 0], "ti": [0, 0.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [257, 431, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27, "s": [257, 431, 0], "to": [-1.333, -1.5, 0], "ti": [1.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51, "s": [249, 422, 0], "to": [-1.5, 0.167, 0], "ti": [0.5, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62, "s": [248, 432, 0], "to": [-0.5, 0.667, 0], "ti": [0.333, 1, 0]}, {"t": 73, "s": [246, 426, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [1, 143, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [104, 97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [99, 111, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [100, 87, 100]}, {"t": 73, "s": [97, 98, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.5;\nfreq = 2;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[61.122, -73.889], [-18.872, -50.319], [-19.033, -15.334], [-61.839, 52.938], [-9.123, 17.797], [60.741, 54.944]], "o": [[-30.827, 37.266], [8.068, 21.513], [59.29, 47.768], [13.246, -11.339], [26.835, -52.353], [-62.408, -56.451]], "v": [[-114.122, -97.111], [-135.66, 50.679], [-95.29, 107.232], [105.789, 109.695], [140.103, 65.209], [108.609, -115.622]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [{"i": [[61.122, -73.889], [-18.872, -50.319], [-19.033, -15.334], [-61.839, 52.938], [-9.123, 17.797], [60.741, 54.944]], "o": [[-30.827, 37.266], [8.068, 21.513], [59.29, 47.768], [13.246, -11.339], [26.835, -52.353], [-62.408, -56.451]], "v": [[-114.122, -97.111], [-135.66, 50.679], [-95.29, 107.232], [105.789, 109.695], [140.103, 65.209], [108.609, -115.622]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [{"i": [[69.286, -157.774], [-5.778, -26.113], [-21.743, -11.242], [-65.11, 35.874], [-13.455, 35.306], [20.939, 58.033]], "o": [[-16.322, 20.088], [8.351, 41.047], [49.133, 29.931], [14.876, -8.383], [11.669, -30.777], [-46.241, -121.248]], "v": [[-131.539, -45.808], [-146.407, 54.043], [-90.516, 120.4], [104.245, 122.783], [159.198, 62.465], [148.39, -58.833]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62, "s": [{"i": [[65.122, -188.625], [-7.383, -53.233], [-24.71, -6.761], [-81.79, 25.259], [-6.103, 41.01], [1.029, 5.62]], "o": [[-0.442, 1.279], [8.66, 62.436], [38.013, 10.4], [16.66, -5.145], [8.66, -58.189], [-28.609, -156.32]], "v": [[-140.122, -29.294], [-171.66, 50.679], [-85.29, 134.818], [112.79, 133.833], [180.103, 59.461], [146.61, -23.668]], "c": true}]}, {"t": 82, "s": [{"i": [[17.038, -219.209], [-16.278, -19.795], [-21.911, -6.208], [-38.677, 14.621], [-21.628, 34.044], [-2.033, 26.313]], "o": [[-2.852, 36.69], [23.333, 28.374], [35.098, 9.944], [16.31, -6.166], [10.815, -17.024], [15.704, -203.307]], "v": [[-148.304, -5.104], [-118.604, 89.434], [-39.606, 141.618], [63.443, 140.016], [137.844, 84.165], [164.061, 14.41]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.913725495338, 0.482352942228, 0.0941176489, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 3, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078491211, 0.84705889225, 0.239215701818, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 82, "st": 0, "bm": 0}, {"ddd": 0, "ind": 55, "ty": 0, "nm": "爱心", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [268, 288, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 72, "op": 187.2, "st": 72, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "泪水2", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0.207]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [28.207]}, {"t": 20, "s": [0.207]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [331.884, 201.801, 0], "to": [-1.742, 0.493, 0], "ti": [0.714, -1.136, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [330.409, 202.441, 0], "to": [-0.714, 1.136, 0], "ti": [-1.796, 0.965, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [331.493, 202.795, 0], "to": [1.796, -0.965, 0], "ti": [-0.617, 1.024, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [332.856, 202.466, 0], "to": [0.617, -1.024, 0], "ti": [-0.262, -0.939, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [331.551, 203.701, 0], "to": [0.262, 0.939, 0], "ti": [0.255, -1.875, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [331.789, 203.734, 0], "to": [-0.255, 1.875, 0], "ti": [0.036, 1.444, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [332.685, 201.973, 0], "to": [-0.036, -1.444, 0], "ti": [1.13, -0.023, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [332.17, 200.438, 0], "to": [-1.13, 0.023, 0], "ti": [1.566, 1.443, 0]}, {"t": 46, "s": [331.884, 201.801, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "a": {"a": 0, "k": [332.003, 199, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [103.021, 102.3, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10, "s": [111.021, 110.244, 100]}, {"t": 20, "s": [103.021, 102.3, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 181, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.463, "ix": 10}, "p": {"a": 0, "k": [270.248, 230.313, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.115, 112.575, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[12.306, -7.547], [-29.241, 6.147]], "o": [[-9.652, 5.919], [32.096, -6.747]], "v": [[57.944, -17.953], [71.085, 0.477]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[8.197, -23.749], [-24.241, -0.103]], "o": [[-3.694, 10.703], [29.506, 0.126]], "v": [[68.944, -42.453], [83.741, -22.897]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 491, "st": -61, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.463, "ix": 10}, "p": {"a": 0, "k": [270.248, 230.313, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.115, 112.575, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.197, -23.749], [-24.241, -0.103]], "o": [[-3.694, 10.703], [29.506, 0.126]], "v": [[68.944, -42.453], [83.741, -22.897]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[8.197, -23.749], [-24.241, -0.103]], "o": [[-3.694, 10.703], [29.506, 0.126]], "v": [[68.944, -42.453], [83.741, -22.897]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 491, "st": -61, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "形状图层 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.463, "ix": 10}, "p": {"a": 0, "k": [270.248, 230.313, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.115, 112.575, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.056, -23.797], [-21.844, 16.438]], "o": [[-2.645, 7.814], [13.759, -10.353]], "v": [[34.944, -32.203], [55.741, -24.897]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 491, "st": -61, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "泪水", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0.207]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [28.207]}, {"t": 20, "s": [0.207]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [331.884, 201.801, 0], "to": [-1.742, 0.493, 0.084], "ti": [0.714, -1.136, 0.801]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [330.409, 202.441, 0], "to": [-0.714, 1.136, -0.801], "ti": [-1.796, 0.965, 0.749]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [331.493, 202.795, 0], "to": [1.796, -0.965, -0.749], "ti": [-0.617, 1.024, -0.483]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [332.856, 202.466, 0], "to": [0.617, -1.024, 0.483], "ti": [-0.262, -0.939, -0.082]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [331.551, 203.701, 0], "to": [0.262, 0.939, 0.082], "ti": [0.255, -1.875, 0.506]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [331.789, 203.734, 0], "to": [-0.255, 1.875, -0.506], "ti": [0.036, 1.444, 0.255]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [332.685, 201.973, 0], "to": [-0.036, -1.444, -0.255], "ti": [1.13, -0.023, -1.334]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [332.17, 200.438, 0], "to": [-1.13, 0.023, 1.334], "ti": [1.566, 1.443, 0.75]}, {"t": 46, "s": [331.884, 201.801, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "a": {"a": 0, "k": [332.003, 199, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [103.021, 102.3, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10, "s": [111.021, 110.244, 100]}, {"t": 20, "s": [103.021, 102.3, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 482, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.463, "ix": 10}, "p": {"a": 0, "k": [270.248, 230.313, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.115, 112.575, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[12.306, -7.547], [-29.241, 6.147]], "o": [[-9.652, 5.919], [32.096, -6.747]], "v": [[57.944, -17.953], [71.085, 0.477]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[8.197, -23.749], [-24.241, -0.103]], "o": [[-3.694, 10.703], [29.506, 0.126]], "v": [[68.944, -42.453], [83.741, -22.897]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 491, "st": -61, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.463, "ix": 10}, "p": {"a": 0, "k": [270.248, 230.313, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.115, 112.575, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.197, -23.749], [-24.241, -0.103]], "o": [[-3.694, 10.703], [29.506, 0.126]], "v": [[68.944, -42.453], [83.741, -22.897]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[8.197, -23.749], [-24.241, -0.103]], "o": [[-3.694, 10.703], [29.506, 0.126]], "v": [[68.944, -42.453], [83.741, -22.897]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 491, "st": -61, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "形状图层 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -1.463, "ix": 10}, "p": {"a": 0, "k": [270.248, 230.313, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.115, 112.575, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.056, -23.797], [-21.844, 16.438]], "o": [[-2.645, 7.814], [13.759, -10.353]], "v": [[34.944, -32.203], [55.741, -24.897]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 491, "st": -61, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262, 254.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [308.512, 308.512], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.937, 0.69, 0.5, 1, 0.937, 0.69, 1, 1, 0.937, 0.69, 0, 1, 0.239, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [2.452, -155.906], "ix": 5}, "e": {"a": 0, "k": [2.799, 94.082], "ix": 6}, "t": 1, "nm": "yyanse23", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.861, -2.699], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 363, "st": 0, "bm": 0}]}, {"id": "comp_6", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [257, 254.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [308.512, 308.512], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.781, 1, 0.851, 0.008, 0.862, 1, 0.776, 0.09, 0.999, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [2.452, -20.906], "ix": 5}, "e": {"a": 0, "k": [1.799, 156.082], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "yyanse", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.861, -2.699], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 363, "st": 0, "bm": 0}]}, {"id": "comp_7", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "爱心 12", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 8.025, "ix": 10}, "p": {"a": 0, "k": [362, 166, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-66.005, 78.495, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 92, "op": 126, "st": 92, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "爱心 11", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 8.025, "ix": 10}, "p": {"a": 0, "k": [362, 166, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-72.93, 78.495, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 58, "op": 92, "st": 58, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "爱心 10", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 8.025, "ix": 10}, "p": {"a": 0, "k": [362, 166, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-67.696, 78.495, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 24, "op": 58, "st": 24, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "爱心 9", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [332, 188, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-61.606, 73.62, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 76, "op": 110, "st": 76, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "爱心 8", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [332, 188, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-61.606, 73.118, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 42, "op": 76, "st": 42, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "爱心 7", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [332, 188, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-91.963, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 8, "op": 42, "st": 8, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "爱心 5", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 8.025, "ix": 10}, "p": {"a": 0, "k": [142, 164, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [78.495, 78.495, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 84, "op": 118, "st": 84, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "爱心 4", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 8.025, "ix": 10}, "p": {"a": 0, "k": [142, 164, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [78.495, 78.495, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 50, "op": 84, "st": 50, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "爱心 2", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 8.025, "ix": 10}, "p": {"a": 0, "k": [142, 164, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [78.495, 78.495, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 16, "op": 50, "st": 16, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "爱心 6", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [112, 186, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 68, "op": 102, "st": 68, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "爱心 3", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [112, 186, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 34, "op": 68, "st": 34, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "爱心", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [112, 186, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [112, 186, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 34, "st": 0, "bm": 0}]}, {"id": "comp_8", "layers": [{"ddd": 0, "ind": 1, "ty": 1, "nm": "中间色红色 纯色 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[4.971, 0], [0, -4.971], [-4.971, 0], [0, 4.971]], "o": [[-4.971, 0], [0, 4.971], [4.971, 0], [0, -4.971]], "v": [[78, 85.5], [69, 94.5], [78, 103.5], [87, 94.5]], "c": true}]}, {"t": 33, "s": [{"i": [[4.971, 0], [0, -4.971], [-4.971, 0], [0, 4.971]], "o": [[-4.971, 0], [0, 4.971], [4.971, 0], [0, -4.971]], "v": [[86, 95.5], [77, 104.5], [86, 113.5], [95, 104.5]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [0]}, {"t": 33, "s": [-7]}], "ix": 4}, "nm": "蒙版 1"}, {"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[6.489, 0], [0, -6.489], [-6.489, 0], [0, 6.489]], "o": [[-6.489, 0], [0, 6.489], [6.489, 0], [0, -6.489]], "v": [[89.25, 56.5], [77.5, 68.25], [89.25, 80], [101, 68.25]], "c": true}]}, {"t": 33, "s": [{"i": [[6.489, 0], [0, -6.489], [-6.489, 0], [0, 6.489]], "o": [[-6.489, 0], [0, 6.489], [6.489, 0], [0, -6.489]], "v": [[104.25, 51.5], [92.5, 63.25], [104.25, 75], [116, 63.25]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [0]}, {"t": 33, "s": [-10]}], "ix": 4}, "nm": "蒙版 2"}, {"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[6.489, 0], [0, -6.489], [-6.489, 0], [0, 6.489]], "o": [[-6.489, 0], [0, 6.489], [6.489, 0], [0, -6.489]], "v": [[55.25, 65.5], [43.5, 77.25], [55.25, 89], [67, 77.25]], "c": true}]}, {"t": 33, "s": [{"i": [[6.489, 0], [0, -6.489], [-6.489, 0], [0, 6.489]], "o": [[-6.489, 0], [0, 6.489], [6.489, 0], [0, -6.489]], "v": [[41.5, 70.25], [29.75, 82], [41.5, 93.75], [53.25, 82]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [0]}, {"t": 33, "s": [-11]}], "ix": 4}, "nm": "蒙版 3"}, {"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[2.347, 0], [0, -2.347], [-2.347, 0], [0, 2.347]], "o": [[-2.347, 0], [0, 2.347], [2.347, 0], [0, -2.347]], "v": [[55.25, 52.5], [51, 56.75], [55.25, 61], [59.5, 56.75]], "c": true}]}, {"t": 33, "s": [{"i": [[2.347, 0], [0, -2.347], [-2.347, 0], [0, 2.347]], "o": [[-2.347, 0], [0, 2.347], [2.347, 0], [0, -2.347]], "v": [[49.25, 36.75], [45, 41], [49.25, 45.25], [53.5, 41]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [0]}, {"t": 33, "s": [-3]}], "ix": 4}, "nm": "蒙版 4"}], "sw": 512, "sh": 512, "sc": "#f9484f", "ip": 28, "op": 34, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "爱心", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-43]}, {"t": 28, "s": [4]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [142, 216, 0], "to": [-43.667, -30.5, 0], "ti": [-2.333, 39, 0]}, {"t": 28, "s": [71, 79.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [19, -172, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 2.837]}, "t": 0, "s": [39, 39, 100]}, {"t": 28, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.75, -9], [-19.499, -0.039], [12.324, 9.366], [0, 0]], "o": [[-13.681, 11.454], [13.431, 0.027], [-15.642, -11.888], [0, 0]], "v": [[-4.75, -195], [24.526, -152.397], [48, -195.5], [21, -188]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.729411780834, 0.160784319043, 0.180392161012, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976470649242, 0.282352954149, 0.309803932905, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": -72, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "预合成 3", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 222, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [112, 112, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 512, "h": 512, "ip": 0, "op": 181, "st": 0, "bm": 0}], "markers": []}