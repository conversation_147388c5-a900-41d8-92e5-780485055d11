{"v": "5.6.2", "fr": 60, "ip": 0, "op": 240, "w": 512, "h": 512, "nm": "LSP正式", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "喷血 2", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 11.787, "ix": 10}, "p": {"a": 0, "k": [-169.661, 340.428, 0], "ix": 2, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [124.168, 176.138, 0], "ix": 1}, "s": {"a": 0, "k": [-99.83, 84.815, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0.438, -0.381], [-0.156, 0.138], [-1.409, -2.386], [0.997, -2.048], [0.59, -0.717], [1.766, -0.856], [1.736, -0.499], [-3.478, 1.062], [-1.209, 0.646]], "o": [[0.156, -0.136], [3.174, -2.811], [1.357, 2.297], [-0.036, -0.028], [-0.964, 1.219], [-1.766, 0.856], [-4.274, 1.228], [1.286, -0.392], [1.209, -0.646]], "v": [[74.578, -43.048], [75.047, -43.46], [82.769, -47.107], [80.232, -40.552], [77.479, -38.137], [73.176, -35.429], [69.018, -33.66], [68.427, -39.409], [71.506, -41.151]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-1.126, 1.095], [-0.188, 0.167], [-1.124, -2.434], [1.541, -2.331], [1.151, -1.967], [-2.875, -2.224], [2.508, 0.147], [0.36, 2.788], [-1.863, 0.654]], "o": [[0.191, -0.171], [3.816, -3.385], [1.082, 2.373], [-0.036, -0.028], [-1.213, 2.073], [1.882, 1.456], [-4.35, 0.52], [-0.136, -3.242], [1.736, -0.634]], "v": [[74.033, -43.271], [74.601, -43.778], [82.746, -47.137], [76.981, -41.52], [72.109, -37.601], [76.156, -32.608], [73.776, -28.551], [66.573, -35.235], [69.04, -40.714]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0.438, -0.381], [-0.156, 0.138], [-1.409, -2.386], [0.997, -2.048], [0.59, -0.717], [1.766, -0.856], [1.736, -0.499], [-3.478, 1.062], [-1.209, 0.646]], "o": [[0.156, -0.136], [3.174, -2.811], [1.357, 2.297], [-0.036, -0.028], [-0.964, 1.219], [-1.766, 0.856], [-4.274, 1.228], [1.286, -0.392], [1.209, -0.646]], "v": [[74.578, -43.048], [75.047, -43.46], [82.769, -47.107], [80.232, -40.552], [77.479, -38.137], [73.176, -35.429], [69.018, -33.66], [68.427, -39.409], [71.506, -41.151]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [{"i": [[-1.126, 1.095], [-0.188, 0.167], [-1.124, -2.434], [1.541, -2.331], [1.151, -1.967], [-2.875, -2.224], [2.508, 0.147], [0.36, 2.788], [-1.863, 0.654]], "o": [[0.191, -0.171], [3.816, -3.385], [1.082, 2.373], [-0.036, -0.028], [-1.213, 2.073], [1.882, 1.456], [-4.35, 0.52], [-0.136, -3.242], [1.736, -0.634]], "v": [[74.033, -43.271], [74.601, -43.778], [82.746, -47.137], [76.981, -41.52], [72.109, -37.601], [76.156, -32.608], [73.776, -28.551], [66.573, -35.235], [69.04, -40.714]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [{"i": [[-7.241, 6.868], [-0.315, 0.298], [-0.008, -2.623], [3.671, -3.439], [0.699, -0.612], [8.398, -2.335], [5.527, 2.673], [-9.716, 0.381], [-4.418, 0.684]], "o": [[0.325, -0.309], [6.4, -6.054], [0.008, 2.668], [-0.036, -0.028], [-3.064, 2.684], [-8.163, 2.27], [-4.647, -2.247], [14.026, -0.55], [3.794, -0.587]], "v": [[71.045, -39.125], [72.006, -40.035], [82.658, -47.255], [75.696, -35.391], [70.379, -31.239], [57.825, -25.27], [36.123, -23.034], [40.462, -32.821], [57.722, -32.841]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [{"i": [[-7.455, 6.635], [-0.816, 0.697], [-0.008, -2.623], [10.867, -16.385], [0.699, -0.612], [10.389, -9.872], [6.25, -6.028], [-7.16, 6.578], [-3.351, 2.96]], "o": [[0.849, -0.755], [16.568, -14.157], [0.008, 2.668], [-0.036, -0.028], [-3.064, 2.684], [-6.142, 5.836], [-7.04, 6.791], [12.611, -11.586], [2.741, -2.422]], "v": [[54.284, -30.64], [56.781, -32.818], [83.131, -46.723], [73.468, -31.262], [72.34, -30.361], [49.955, -9.441], [30.912, 8.787], [21.985, -1.587], [41.444, -19.303]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [{"i": [[-7.455, 6.635], [-0.872, 0.79], [-0.008, -2.623], [10.867, -16.385], [2.608, -4.7], [27.699, -45.329], [7.02, -5.449], [4.362, -2.727], [-27.475, 27.186]], "o": [[0.849, -0.755], [17.71, -16.039], [0.008, 2.668], [-0.036, -0.028], [-11.439, 20.612], [-16.375, 26.798], [-7.727, 5.998], [-57.919, 36.208], [22.479, -22.243]], "v": [[34.497, -10.521], [37.08, -12.841], [76.731, -48.68], [43.871, 1.676], [39.744, 9.004], [-32.484, 134.877], [-70.369, 188.818], [-10.634, 56.168], [-26.645, 45.854]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [{"i": [[-20.964, 25.045], [-1.734, 2.049], [-0.008, -2.623], [-46.955, 44.237], [6.309, -11.344], [23.554, -38.735], [7.28, -5.651], [-185.57, 254.037], [-6.086, -19.487]], "o": [[1.673, -1.969], [35.203, -41.604], [0.008, 2.668], [-0.057, -0.045], [-13.541, 24.356], [-17.118, 28.167], [-7.727, 5.998], [42.124, -57.665], [5.173, 14.14]], "v": [[-17.445, 57.225], [-12.332, 51.193], [69.272, -44.098], [16.571, 64.086], [-2.722, 109.425], [-42.269, 138.752], [-149.187, 310.018], [-154.025, 192.678], [-29.26, 55.439]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [{"i": [[-27.719, 34.249], [-2.165, 2.679], [-0.008, -2.623], [-75.867, 74.548], [8.16, -14.666], [21.481, -35.437], [7.41, -5.752], [4.362, -2.727], [4.609, -42.824]], "o": [[2.085, -2.576], [43.949, -54.387], [0.008, 2.668], [-0.068, -0.053], [-14.592, 26.228], [-17.489, 28.851], [-7.727, 5.998], [-61.87, 38.678], [-3.48, 32.331]], "v": [[-43.416, 91.098], [-37.038, 83.211], [65.542, -41.806], [2.921, 95.291], [-23.954, 159.636], [-47.161, 140.689], [-160.796, 311.32], [-225.72, 260.934], [-30.567, 60.231]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[20.827, -27.383], [3.43, -4.728], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [-4.869, 19.841], [52.447, -34.486], [-22.239, 44.873], [4.609, -42.824]], "o": [[-4.664, 6.132], [-69.64, 95.985], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [8.041, -32.766], [-8.173, 5.374], [32.4, -65.377], [-3.48, 32.331]], "v": [[-19.15, 25.962], [-31.265, 42.224], [73.407, -41.16], [25.724, 58.861], [-23.954, 159.636], [-161.796, 294.692], [-133.609, 224.245], [-152.201, 218.323], [-84.621, 126.224]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [{"i": [[-14.21, 15.84], [-1.303, 1.42], [-0.008, -2.623], [-18.044, 13.926], [4.459, -8.022], [25.626, -42.032], [-4.972, 7.563], [16.212, -8.877], [-16.78, 3.85]], "o": [[1.261, -1.362], [26.456, -28.822], [0.008, 2.668], [-0.047, -0.037], [-12.49, 22.484], [-16.746, 27.482], [68.521, -104.229], [-61.274, 33.552], [13.826, -4.051]], "v": [[8.526, 23.352], [12.374, 19.176], [73.001, -46.389], [30.221, 32.881], [18.511, 59.214], [-37.376, 136.814], [-163.268, 300.082], [-166.201, 207.861], [-27.952, 50.647]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[-27.719, 34.249], [-2.165, 2.679], [-0.008, -2.623], [-75.867, 74.548], [8.16, -14.666], [21.481, -35.437], [7.41, -5.752], [4.362, -2.727], [4.609, -42.824]], "o": [[2.085, -2.576], [43.949, -54.387], [0.008, 2.668], [-0.068, -0.053], [-14.592, 26.228], [-17.489, 28.851], [-7.727, 5.998], [-61.87, 38.678], [-3.48, 32.331]], "v": [[-43.416, 91.098], [-37.038, 83.211], [65.542, -41.806], [2.921, 95.291], [-23.954, 159.636], [-47.161, 140.689], [-160.796, 311.32], [-225.72, 260.934], [-30.567, 60.231]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [{"i": [[8.691, -11.975], [2.031, -2.876], [-0.008, -2.623], [-74.41, 94.329], [7.702, -14.909], [10.219, 6.265], [-1.282, 15.789], [-15.589, 32.973], [4.609, -42.824]], "o": [[-2.977, 3.955], [-41.243, 58.392], [0.008, 2.668], [-0.068, -0.053], [-110.627, 218.971], [-38.66, -18.027], [-9.721, -5.446], [8.833, -39.363], [-3.48, 32.331]], "v": [[-25.216, 42.246], [-32.708, 52.47], [71.441, -41.321], [20.024, 67.969], [-23.954, 159.636], [-161.416, 330.11], [-200.402, 308.333], [-170.581, 228.976], [-71.108, 109.726]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [{"i": [[20.827, -27.383], [3.43, -4.728], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [52.447, -34.486], [-22.239, 44.873], [4.609, -42.824]], "o": [[-4.664, 6.132], [-69.64, 95.985], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-8.173, 5.374], [32.4, -65.377], [-3.48, 32.331]], "v": [[-19.15, 25.962], [-31.265, 42.224], [73.407, -41.16], [25.724, 58.861], [-23.954, 159.636], [-174.09, 329.491], [-107.796, 196.467], [-152.201, 218.323], [-84.621, 126.224]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[-11.537, 13.705], [-0.328, 0.203], [-0.008, -2.623], [-75.219, 83.34], [7.956, -14.774], [21.481, -35.437], [22.422, -15.33], [-4.505, 13.14], [58.552, -86.069]], "o": [[-0.165, 0.327], [6.654, -4.132], [0.008, 2.668], [-0.068, -0.053], [-57.274, 111.892], [-17.489, 28.852], [-7.875, 5.79], [-30.447, 3.993], [-14.642, 27.753]], "v": [[-41.984, 67.85], [-41.728, 68.022], [68.164, -41.591], [27.28, 47.423], [-25.025, 146.191], [-118.746, 297.328], [-169.048, 318.122], [-201.214, 246.73], [-101.872, 139.015]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [{"i": [[-27.719, 34.249], [-2.207, 2.669], [-0.008, -2.623], [-75.867, 74.548], [8.16, -14.666], [21.481, -35.437], [7.41, -5.752], [4.362, -2.727], [85.524, -107.691]], "o": [[2.085, -2.576], [44.8, -54.19], [0.008, 2.668], [-0.068, -0.053], [-14.592, 26.228], [-17.489, 28.851], [-7.727, 5.998], [-61.87, 38.678], [-20.223, 25.465]], "v": [[-53.402, 88.794], [-46.959, 80.921], [65.542, -41.806], [28.058, 41.704], [-25.561, 139.469], [-91.073, 281.247], [-160.796, 311.32], [-225.72, 260.934], [-110.497, 145.41]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [{"i": [[15.216, -52.56], [-0.237, -0.068], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [52.447, -34.486], [-22.239, 44.873], [4.609, -42.823]], "o": [[-0.205, 0.707], [4.819, 1.384], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-8.173, 5.374], [32.4, -65.377], [-3.48, 32.331]], "v": [[-64.666, 133.707], [-64.607, 134.855], [31.777, 14.704], [-54.177, 195.558], [-23.954, 159.636], [-141.52, 268.605], [-122.636, 216.48], [-152.201, 218.323], [-117.842, 173.448]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [{"i": [[102.954, -144.293], [-0.465, -1.702], [-0.007, -2.316], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [76.078, -97.66], [-32.883, 37.775], [-6.114, 10.126]], "o": [[-1.445, 2.025], [0.465, 1.702], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-24.513, 31.466], [25.814, -29.654], [6.829, -11.311]], "v": [[-119.096, 229.416], [-115.865, 224.79], [-25.682, 99.636], [-82.115, 224.447], [-111.125, 259.657], [-169.383, 307.102], [-150.499, 254.977], [-159.494, 245.488], [-125.045, 206.43]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [{"i": [[102.954, -144.293], [0.252, -0.366], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [76.078, -97.66], [-34.106, 36.674], [7.67, -9.005]], "o": [[-0.428, 0.599], [-5.112, 7.43], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-24.513, 31.466], [76.73, -82.507], [-21.085, 24.755]], "v": [[-160.217, 311.386], [-161.232, 312.829], [-136.761, 284.318], [-126.403, 286.597], [-121.09, 291.838], [-156.006, 296.647], [-155.635, 267.752], [-173.83, 272.74], [-149.398, 218.089]], "c": true}]}, {"t": 157, "s": [{"i": [[102.954, -144.293], [0.25, -0.385], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [76.078, -97.66], [-34.106, 36.674], [-2.219, 3.298]], "o": [[-0.428, 0.599], [-5.085, 7.817], [0.008, 2.668], [-0.068, -0.053], [-142.639, 283.219], [-17.489, 28.852], [-24.513, 31.466], [76.73, -82.507], [18.152, -26.98]], "v": [[-185.236, 358.649], [-186.249, 360.121], [-161.467, 336.124], [-151.421, 333.86], [-146.109, 339.101], [-181.025, 343.91], [-194.479, 334.43], [-198.849, 320.003], [-188.513, 291.293]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.403, 0.403, 0.5, 0.875, 0.202, 0.202, 1, 0.749, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 1, "nm": "jianbian22", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.940185546875, 0.339599609375, 0.155975341797, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 2, "bm": 0, "nm": "mianbian2323", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.906, 0.596, 0.5, 1, 0.873, 0.408, 1, 1, 0.839, 0.22], "ix": 9}}, "s": {"a": 0, "k": [107.019, 51.727], "ix": 5}, "e": {"a": 0, "k": [113.425, 164.788], "ix": 6}, "t": 1, "nm": "Gradient Fill 12", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-26.25, -15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 50, "op": 158, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "喷血", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.815, "ix": 10}, "p": {"a": 0, "k": [-7.421, 356.646, 0], "ix": 2, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [124.168, 176.138, 0], "ix": 1}, "s": {"a": 0, "k": [99.924, 84.736, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0.438, -0.381], [-0.156, 0.138], [-1.409, -2.386], [0.997, -2.048], [0.59, -0.717], [1.766, -0.856], [1.736, -0.499], [-3.478, 1.062], [-1.209, 0.646]], "o": [[0.156, -0.136], [3.174, -2.811], [1.357, 2.297], [-0.036, -0.028], [-0.964, 1.219], [-1.766, 0.856], [-4.274, 1.228], [1.286, -0.392], [1.209, -0.646]], "v": [[74.578, -43.048], [75.047, -43.46], [82.769, -47.107], [80.232, -40.552], [77.479, -38.137], [73.176, -35.429], [69.018, -33.66], [68.427, -39.409], [71.506, -41.151]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-1.126, 1.095], [-0.188, 0.167], [-1.124, -2.434], [1.541, -2.331], [1.151, -1.967], [-2.875, -2.224], [2.508, 0.147], [0.36, 2.788], [-1.863, 0.654]], "o": [[0.191, -0.171], [3.816, -3.385], [1.082, 2.373], [-0.036, -0.028], [-1.213, 2.073], [1.882, 1.456], [-4.35, 0.52], [-0.136, -3.242], [1.736, -0.634]], "v": [[74.033, -43.271], [74.601, -43.778], [82.746, -47.137], [76.981, -41.52], [72.109, -37.601], [76.156, -32.608], [73.776, -28.551], [66.573, -35.235], [69.04, -40.714]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0.438, -0.381], [-0.156, 0.138], [-1.409, -2.386], [0.997, -2.048], [0.59, -0.717], [1.766, -0.856], [1.736, -0.499], [-3.478, 1.062], [-1.209, 0.646]], "o": [[0.156, -0.136], [3.174, -2.811], [1.357, 2.297], [-0.036, -0.028], [-0.964, 1.219], [-1.766, 0.856], [-4.274, 1.228], [1.286, -0.392], [1.209, -0.646]], "v": [[74.578, -43.048], [75.047, -43.46], [82.769, -47.107], [80.232, -40.552], [77.479, -38.137], [73.176, -35.429], [69.018, -33.66], [68.427, -39.409], [71.506, -41.151]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [{"i": [[-1.126, 1.095], [-0.188, 0.167], [-1.124, -2.434], [1.541, -2.331], [1.151, -1.967], [-2.875, -2.224], [2.508, 0.147], [0.36, 2.788], [-1.863, 0.654]], "o": [[0.191, -0.171], [3.816, -3.385], [1.082, 2.373], [-0.036, -0.028], [-1.213, 2.073], [1.882, 1.456], [-4.35, 0.52], [-0.136, -3.242], [1.736, -0.634]], "v": [[74.033, -43.271], [74.601, -43.778], [82.746, -47.137], [76.981, -41.52], [72.109, -37.601], [76.156, -32.608], [73.776, -28.551], [66.573, -35.235], [69.04, -40.714]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [{"i": [[-7.241, 6.868], [-0.315, 0.298], [-0.008, -2.623], [3.671, -3.439], [0.699, -0.612], [8.398, -2.335], [5.527, 2.673], [-9.716, 0.381], [-4.418, 0.684]], "o": [[0.325, -0.309], [6.4, -6.054], [0.008, 2.668], [-0.036, -0.028], [-3.064, 2.684], [-8.163, 2.27], [-4.647, -2.247], [14.026, -0.55], [3.794, -0.587]], "v": [[71.045, -39.125], [72.006, -40.035], [82.658, -47.255], [75.696, -35.391], [70.379, -31.239], [57.825, -25.27], [36.123, -23.034], [40.462, -32.821], [57.722, -32.841]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [{"i": [[-7.455, 6.635], [-0.816, 0.697], [-0.008, -2.623], [10.867, -16.385], [0.699, -0.612], [10.389, -9.872], [6.25, -6.028], [-7.16, 6.578], [-3.351, 2.96]], "o": [[0.849, -0.755], [16.568, -14.157], [0.008, 2.668], [-0.036, -0.028], [-3.064, 2.684], [-6.142, 5.836], [-7.04, 6.791], [12.611, -11.586], [2.741, -2.422]], "v": [[54.284, -30.64], [56.781, -32.818], [83.131, -46.723], [73.468, -31.262], [72.34, -30.361], [49.955, -9.441], [30.912, 8.787], [21.985, -1.587], [41.444, -19.303]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [{"i": [[-7.455, 6.635], [-0.872, 0.79], [-0.008, -2.623], [10.867, -16.385], [2.608, -4.7], [27.699, -45.329], [7.02, -5.449], [4.362, -2.727], [-27.475, 27.186]], "o": [[0.849, -0.755], [17.71, -16.039], [0.008, 2.668], [-0.036, -0.028], [-11.439, 20.612], [-16.375, 26.798], [-7.727, 5.998], [-57.919, 36.208], [22.479, -22.243]], "v": [[34.497, -10.521], [37.08, -12.841], [76.731, -48.68], [43.871, 1.676], [39.744, 9.004], [-32.484, 134.877], [-70.369, 188.818], [-10.634, 56.168], [-26.645, 45.854]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [{"i": [[-20.964, 25.045], [-1.734, 2.049], [-0.008, -2.623], [-46.955, 44.237], [6.309, -11.344], [23.554, -38.735], [7.28, -5.651], [-185.57, 254.037], [-6.086, -19.487]], "o": [[1.673, -1.969], [35.203, -41.604], [0.008, 2.668], [-0.057, -0.045], [-13.541, 24.356], [-17.118, 28.167], [-7.727, 5.998], [42.124, -57.665], [5.173, 14.14]], "v": [[-17.445, 57.225], [-12.332, 51.193], [69.272, -44.098], [16.571, 64.086], [-2.722, 109.425], [-42.269, 138.752], [-149.187, 310.018], [-154.025, 192.678], [-29.26, 55.439]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [{"i": [[-27.719, 34.249], [-2.165, 2.679], [-0.008, -2.623], [-75.867, 74.548], [8.16, -14.666], [21.481, -35.437], [7.41, -5.752], [4.362, -2.727], [4.609, -42.824]], "o": [[2.085, -2.576], [43.949, -54.387], [0.008, 2.668], [-0.068, -0.053], [-14.592, 26.228], [-17.489, 28.851], [-7.727, 5.998], [-61.87, 38.678], [-3.48, 32.331]], "v": [[-43.416, 91.098], [-37.038, 83.211], [65.542, -41.806], [2.921, 95.291], [-23.954, 159.636], [-47.161, 140.689], [-160.796, 311.32], [-225.72, 260.934], [-30.567, 60.231]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[20.827, -27.383], [3.43, -4.728], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [-4.869, 19.841], [52.447, -34.486], [-22.239, 44.873], [4.609, -42.824]], "o": [[-4.664, 6.132], [-69.64, 95.985], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [8.041, -32.766], [-8.173, 5.374], [32.4, -65.377], [-3.48, 32.331]], "v": [[-19.15, 25.962], [-31.265, 42.224], [73.407, -41.16], [25.724, 58.861], [-23.954, 159.636], [-161.796, 294.692], [-133.609, 224.245], [-152.201, 218.323], [-84.621, 126.224]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [{"i": [[-14.21, 15.84], [-1.303, 1.42], [-0.008, -2.623], [-18.044, 13.926], [4.459, -8.022], [25.626, -42.032], [-4.972, 7.563], [16.212, -8.877], [-16.78, 3.85]], "o": [[1.261, -1.362], [26.456, -28.822], [0.008, 2.668], [-0.047, -0.037], [-12.49, 22.484], [-16.746, 27.482], [68.521, -104.229], [-61.274, 33.552], [13.826, -4.051]], "v": [[8.526, 23.352], [12.374, 19.176], [73.001, -46.389], [30.221, 32.881], [18.511, 59.214], [-37.376, 136.814], [-163.268, 300.082], [-166.201, 207.861], [-27.952, 50.647]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[-27.719, 34.249], [-2.165, 2.679], [-0.008, -2.623], [-75.867, 74.548], [8.16, -14.666], [21.481, -35.437], [7.41, -5.752], [4.362, -2.727], [4.609, -42.824]], "o": [[2.085, -2.576], [43.949, -54.387], [0.008, 2.668], [-0.068, -0.053], [-14.592, 26.228], [-17.489, 28.851], [-7.727, 5.998], [-61.87, 38.678], [-3.48, 32.331]], "v": [[-43.416, 91.098], [-37.038, 83.211], [65.542, -41.806], [2.921, 95.291], [-23.954, 159.636], [-47.161, 140.689], [-160.796, 311.32], [-225.72, 260.934], [-30.567, 60.231]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [{"i": [[8.691, -11.975], [2.031, -2.876], [-0.008, -2.623], [-74.41, 94.329], [7.702, -14.909], [10.219, 6.265], [-1.282, 15.789], [-15.589, 32.973], [4.609, -42.824]], "o": [[-2.977, 3.955], [-41.243, 58.392], [0.008, 2.668], [-0.068, -0.053], [-110.627, 218.971], [-38.66, -18.027], [-9.721, -5.446], [8.833, -39.363], [-3.48, 32.331]], "v": [[-25.216, 42.246], [-32.708, 52.47], [71.441, -41.321], [20.024, 67.969], [-23.954, 159.636], [-161.416, 330.11], [-200.402, 308.333], [-170.581, 228.976], [-71.108, 109.726]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [{"i": [[20.827, -27.383], [3.43, -4.728], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [52.447, -34.486], [-22.239, 44.873], [4.609, -42.824]], "o": [[-4.664, 6.132], [-69.64, 95.985], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-8.173, 5.374], [32.4, -65.377], [-3.48, 32.331]], "v": [[-19.15, 25.962], [-31.265, 42.224], [73.407, -41.16], [25.724, 58.861], [-23.954, 159.636], [-174.09, 329.491], [-107.796, 196.467], [-152.201, 218.323], [-84.621, 126.224]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[-11.537, 13.705], [-0.328, 0.203], [-0.008, -2.623], [-75.219, 83.34], [7.956, -14.774], [21.481, -35.437], [22.422, -15.33], [-4.505, 13.14], [58.552, -86.069]], "o": [[-0.165, 0.327], [6.654, -4.132], [0.008, 2.668], [-0.068, -0.053], [-57.274, 111.892], [-17.489, 28.852], [-7.875, 5.79], [-30.447, 3.993], [-14.642, 27.753]], "v": [[-41.984, 67.85], [-41.728, 68.022], [68.164, -41.591], [27.28, 47.423], [-25.025, 146.191], [-118.746, 297.328], [-169.048, 318.122], [-201.214, 246.73], [-101.872, 139.015]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [{"i": [[-27.719, 34.249], [-2.207, 2.669], [-0.008, -2.623], [-75.867, 74.548], [8.16, -14.666], [21.481, -35.437], [7.41, -5.752], [4.362, -2.727], [85.524, -107.691]], "o": [[2.085, -2.576], [44.8, -54.19], [0.008, 2.668], [-0.068, -0.053], [-14.592, 26.228], [-17.489, 28.851], [-7.727, 5.998], [-61.87, 38.678], [-20.223, 25.465]], "v": [[-53.402, 88.794], [-46.959, 80.921], [65.542, -41.806], [28.058, 41.704], [-25.561, 139.469], [-91.073, 281.247], [-160.796, 311.32], [-225.72, 260.934], [-110.497, 145.41]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [{"i": [[15.216, -52.56], [-0.237, -0.068], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [52.447, -34.486], [-22.239, 44.873], [4.609, -42.823]], "o": [[-0.205, 0.707], [4.819, 1.384], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-8.173, 5.374], [32.4, -65.377], [-3.48, 32.331]], "v": [[-64.666, 133.707], [-64.607, 134.855], [31.777, 14.704], [-54.177, 195.558], [-23.954, 159.636], [-141.52, 268.605], [-122.636, 216.48], [-152.201, 218.323], [-117.842, 173.448]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [{"i": [[102.954, -144.293], [-0.465, -1.702], [-0.007, -2.316], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [76.078, -97.66], [-32.883, 37.775], [-6.114, 10.126]], "o": [[-1.445, 2.025], [0.465, 1.702], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-24.513, 31.466], [25.814, -29.654], [6.829, -11.311]], "v": [[-119.096, 229.416], [-115.865, 224.79], [-25.682, 99.636], [-82.115, 224.447], [-111.125, 259.657], [-169.383, 307.102], [-150.499, 254.977], [-159.494, 245.488], [-125.045, 206.43]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [{"i": [[102.954, -144.293], [0.252, -0.366], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [76.078, -97.66], [-34.106, 36.674], [7.67, -9.005]], "o": [[-0.428, 0.599], [-5.112, 7.43], [0.008, 2.668], [-0.068, -0.053], [-142.638, 283.219], [-17.489, 28.852], [-24.513, 31.466], [76.73, -82.507], [-21.085, 24.755]], "v": [[-160.217, 311.386], [-161.232, 312.829], [-136.761, 284.318], [-126.403, 286.597], [-121.09, 291.838], [-156.006, 296.647], [-155.635, 267.752], [-173.83, 272.74], [-149.398, 218.089]], "c": true}]}, {"t": 157, "s": [{"i": [[102.954, -144.293], [0.25, -0.385], [-0.008, -2.623], [-73.924, 100.923], [7.549, -14.99], [21.481, -35.437], [76.078, -97.66], [-34.106, 36.674], [-2.219, 3.298]], "o": [[-0.428, 0.599], [-5.085, 7.817], [0.008, 2.668], [-0.068, -0.053], [-142.639, 283.219], [-17.489, 28.852], [-24.513, 31.466], [76.73, -82.507], [18.152, -26.98]], "v": [[-185.236, 358.649], [-186.249, 360.121], [-161.467, 336.124], [-151.421, 333.86], [-146.109, 339.101], [-181.025, 343.91], [-194.479, 334.43], [-198.849, 320.003], [-188.513, 291.293]], "c": true}]}], "ix": 2}, "nm": "lujin2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.403, 0.403, 0.5, 0.875, 0.202, 0.202, 1, 0.749, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 1, "nm": "jianbian00", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.940185546875, 0.339599609375, 0.155975341797, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 2, "bm": 0, "nm": "miaobian77", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.906, 0.596, 0.5, 1, 0.873, 0.408, 1, 1, 0.839, 0.22], "ix": 9}}, "s": {"a": 0, "k": [107.019, 51.727], "ix": 5}, "e": {"a": 0, "k": [113.425, 164.788], "ix": 6}, "t": 1, "nm": "Gradient Fill 12", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-26.25, -15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 50, "op": 158, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "3d控制器", "parent": 35, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-3.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 54, "s": [-4.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [-4.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 106, "s": [-4.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [-0.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [-4.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [-4.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [-0.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 134, "s": [-4.033]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 190, "s": [-4.033]}, {"t": 197, "s": [-3.033]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [32.945, -20.145, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [32.945, -27.145, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [32.945, -20.145, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [32.945, -20.145, 0], "to": [-2.669, -3.902, 0], "ti": [2.669, 3.902, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [16.931, -43.559, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [16.931, -43.559, 0], "to": [1.5, -2, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [25.931, -55.559, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [36.931, -66.559, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [25.931, -55.559, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [25.931, -55.559, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [36.931, -66.559, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [25.931, -55.559, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [16.931, -43.559, 0], "to": [2.669, 3.902, 0], "ti": [-2.669, -3.902, 0]}, {"t": 197, "s": [32.945, -20.145, 0]}], "ix": 2, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}, "a": {"a": 0, "k": [-17.51, 160.241, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 106, "s": [99.953, 113.506, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [98.953, 108.506, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [99.953, 113.506, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 119, "s": [99.953, 113.506, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 129, "s": [98.953, 108.506, 100]}, {"t": 134, "s": [99.953, 113.506, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.961, 7.88], [0, 0], [-2.984, 0.421], [-8.619, -4.654], [0, 0], [0, -14.298], [23.748, 0], [0, 23.748]], "o": [[0, 0], [4.204, -2.007], [17.752, -2.507], [0, 0], [9.228, 8.812], [0, 23.748], [-23.748, 0], [0, -12.871]], "v": [[77.715, 96.707], [85.056, 91.475], [96.566, 86.237], [129.942, 90.486], [133.369, 93.345], [149.092, 129], [106.092, 172], [63.092, 129]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274516582, 0.541176497936, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.906, 0.596, 0.5, 1, 0.873, 0.408, 1, 1, 0.839, 0.22], "ix": 9}}, "s": {"a": 0, "k": [107.019, 51.727], "ix": 5}, "e": {"a": 0, "k": [113.425, 164.788], "ix": 6}, "t": 1, "nm": "Gradient Fill 12", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-169.159, 20.867], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -110, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "koushui2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 202, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 212, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 229, "s": [100]}, {"t": 236, "s": [1]}], "ix": 11}, "r": {"a": 0, "k": -1, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [237.131, 307.727, 0], "to": [0, -3, 0], "ti": [0, -4, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [237.131, 289.727, 0], "to": [0, 4, 0], "ti": [0, -7, 0]}, {"t": 212, "s": [237.131, 331.727, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.196, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [{"i": [[1.981, -0.274], [0.013, 10.99], [0.153, 4.779], [0.19, 3.454], [0.021, 4.336], [0.83, 5.548], [-1.333, 0.191], [-1.072, -5.464], [-0.85, -3.518], [-0.29, -3.685], [-0.839, -6.878]], "o": [[-1.981, 0.274], [0.61, -7.915], [-0.235, -3.527], [-0.507, -3.038], [-0.446, -3.014], [-0.83, -5.548], [0.889, -0.056], [0.436, 2.224], [0.459, 5.027], [1.917, 12.87], [0.912, 6.9]], "v": [[62.876, 85.481], [57.026, 73.426], [59.394, 59.628], [59.171, 52.096], [58.553, 40.873], [57.622, 29.44], [56.902, 20.666], [59.074, 28.443], [60.534, 39.987], [62.027, 54.43], [66.005, 72.773]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [{"i": [[0, 0], [1.19, 3.133], [-0.022, 2.414], [-0.946, 3.535], [-1.439, 4.485], [-0.606, 3.452], [-0.966, -0.304], [-0.469, -7.241], [-0.141, -3.497], [0.321, -4.386], [1.078, -2.415]], "o": [[0, 0], [-1.097, -2.889], [0.024, -2.609], [0.799, -2.987], [0.317, -0.989], [0.999, -5.685], [0.966, 0.304], [0.196, 3.018], [0.123, 3.036], [-0.321, 4.386], [-0.979, 2.195]], "v": [[61.97, 99.764], [57.668, 96.456], [56.444, 89.05], [57.995, 80.935], [60.409, 73.607], [62.098, 66.487], [61.521, 53.365], [67.761, 68.398], [69.288, 80.289], [68.902, 88.934], [66.797, 96.852]], "c": true}]}, {"t": 212, "s": [{"i": [[0, 0], [1.684, 3.465], [-0.071, 2.685], [-2.545, 2.861], [-2.122, 2.154], [-2.85, 3.198], [-0.632, -0.095], [0.465, -8.467], [-0.149, -3.888], [0.54, -4.883], [2.185, -2.491]], "o": [[0, 0], [-1.553, -3.195], [0.077, -2.902], [2.443, -2.746], [2.122, -2.154], [2.793, -3.135], [0.632, 0.095], [-0.08, 1.463], [0.13, 3.374], [-0.54, 4.883], [-2.185, 2.491]], "v": [[60.23, 103.633], [52.123, 100.344], [50.625, 92.906], [53.623, 84.556], [59.027, 79.58], [64.258, 72.662], [69.097, 68.759], [70.043, 78.564], [69.966, 83.919], [70.234, 91.745], [67.397, 100.509]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 0.855, 0.973, 1, 0.501, 0.88, 0.976, 1, 1, 0.906, 0.98, 1], "ix": 9}}, "s": {"a": 0, "k": [-1, 49], "ix": 5}, "e": {"a": 0, "k": [-1, 8.072], "ix": 6}, "t": 1, "nm": "jian10086", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.828, 40.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [76.775, 76.775], "ix": 3}, "r": {"a": 0, "k": 2.688, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "zu", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.223541259766, 0.729400634766, 0.850982666016, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "miaobian2333", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 200, "op": 216, "st": -61, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "parent": 26, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 233, "s": [100]}, {"t": 240, "s": [1]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [0, 22.038, 0], "to": [0.386, -0.545, 0], "ti": [-0.386, 0.545, 0]}, {"t": 205, "s": [2.315, 18.766, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.196, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[3.684, -1.243], [3.758, -0.134], [8.481, 3.769], [-1.322, 0.285], [-11.627, -0.59], [-9.331, 2.75], [-4.982, 0.327], [2.481, -2.799], [1.242, -1.316], [0.348, -0.381], [1.528, -2.729]], "o": [[-3.791, 1.28], [-7.063, 0.251], [-17.95, -7.978], [3.955, -0.854], [12.396, 0.629], [3.589, -1.058], [1.106, -0.073], [-1.464, 1.651], [-0.759, 0.804], [-2.073, 2.269], [-2.525, 4.51]], "v": [[17.794, 42.763], [7.427, 44.921], [-8.895, 42.173], [-18.911, 29.717], [0.051, 32.701], [22.796, 23.98], [41.351, 11.118], [37.96, 16.414], [33.629, 21.112], [31.905, 22.946], [27.901, 35.521]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[3.888, -0.059], [4.647, -1.831], [22.708, 3.033], [-3.183, -0.921], [-13.746, 3.902], [-2.999, 9.254], [-1.281, 0.171], [0.425, -3.667], [-0.242, -3.544], [-0.123, -2.838], [4.483, 2.525]], "o": [[-3.933, 0.06], [-2.884, 1.291], [-19.47, -2.601], [5.461, 1.58], [11.941, -3.39], [1.327, -4.095], [2.115, -0.282], [-0.304, 2.625], [0.178, 2.602], [0.739, 16.995], [-4.956, -2.791]], "v": [[30.512, 31.982], [22.229, 35.36], [-6.637, 40.421], [-27.858, 30.696], [8.238, 31.397], [29.896, 19.012], [40.119, 10.131], [41.226, 15.66], [40.462, 27.758], [40.732, 35.554], [35.889, 56.431]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[11.442, 5.404], [-1.165, 37.056], [3.582, -0.215], [-2.542, 1.982], [-3.537, 0.391], [-4.85, 3.425], [-2.488, 0.608], [0.338, -2.154], [0.271, -3.004], [0.184, -2.234], [-0.284, -8.929]], "o": [[-10.528, -4.973], [1.505, -19.402], [-3.582, 0.215], [2.542, -1.982], [3.537, -0.391], [4.85, -3.425], [1.116, -0.273], [-0.189, 1.208], [-0.145, 1.608], [-1.824, 22.192], [0.227, 7.137]], "v": [[48.651, 121.791], [42.91, 44.197], [8.859, 34.159], [4.422, 32.053], [21.888, 28.756], [35.545, 24.591], [43.104, 16.721], [45.891, 19.062], [45.309, 24.354], [45.387, 29.26], [63.994, 112.869]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[7.546, -5.916], [7.785, 16.743], [10.187, 0.176], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-8.868, 1.077]], "o": [[-7.85, 6.155], [-12.441, -13.308], [-13.282, -0.23], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [8.218, -0.998]], "v": [[72.22, 74.343], [45.772, 60.522], [30.609, 34.044], [-4.441, 34.305], [18.728, 27.013], [35.293, 25.275], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [41.309, 38.58], [67.883, 60.177]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [3.339, -24.141], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[52.09, 102.931], [40.624, 79.312], [30.609, 34.044], [-9.507, 35.099], [18.573, 31.552], [31.96, 23.482], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [43.806, 40.009], [58.488, 84.578]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [5.343, -23.341], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[42.769, 108.154], [31.713, 82.851], [30.609, 34.044], [-9.507, 35.099], [18.635, 29.736], [33.293, 24.199], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [39.572, 43.724], [44.198, 86.869]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [3.339, -24.141], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[52.09, 102.931], [40.624, 79.312], [30.609, 34.044], [-9.507, 35.099], [18.573, 31.552], [31.96, 23.482], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [43.806, 40.009], [58.488, 84.578]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [8.349, -22.139], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[43.831, 101.312], [32.365, 77.693], [30.609, 34.044], [-9.507, 35.099], [18.728, 27.013], [35.292, 25.275], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [41.309, 38.58], [50.229, 82.958]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [3.339, -24.141], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[52.09, 102.931], [40.624, 79.312], [30.609, 34.044], [-9.507, 35.099], [18.573, 31.552], [31.96, 23.482], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [43.806, 40.009], [58.488, 84.578]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [8.349, -22.139], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[43.831, 101.312], [32.365, 77.693], [30.609, 34.044], [-9.507, 35.099], [18.728, 27.013], [35.292, 25.275], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [41.309, 38.58], [50.229, 82.958]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [3.339, -24.141], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[52.09, 102.931], [40.624, 79.312], [30.609, 34.044], [-9.507, 35.099], [18.573, 31.552], [31.96, 23.482], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [43.806, 40.009], [58.488, 84.578]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [8.349, -22.139], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[43.831, 101.312], [32.365, 77.693], [30.609, 34.044], [-9.507, 35.099], [18.728, 27.013], [35.292, 25.275], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [41.309, 38.58], [50.229, 82.958]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [3.339, -24.141], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[52.09, 102.931], [40.624, 79.312], [30.609, 34.044], [-9.507, 35.099], [18.573, 31.552], [31.96, 23.482], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [43.806, 40.009], [58.488, 84.578]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [8.349, -22.139], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[43.831, 101.312], [32.365, 77.693], [30.609, 34.044], [-9.507, 35.099], [18.728, 27.013], [35.292, 25.275], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [41.309, 38.58], [50.229, 82.958]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [{"i": [[9.892, -1.282], [-2.859, 16.712], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-2.708, -8.513]], "o": [[-9.892, 1.282], [3.339, -24.141], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.272], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.008, 9.457]], "v": [[52.09, 102.931], [40.624, 79.312], [30.609, 34.044], [-9.507, 35.099], [18.573, 31.552], [31.96, 23.482], [39.748, 17.444], [42.207, 22.414], [43.133, 29.445], [43.806, 40.009], [58.488, 84.578]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [{"i": [[6.105, -0.714], [-2.998, 22.123], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.316, 2.911], [-2.488, 0.608], [-0.579, -3.194], [0.089, -4.262], [-0.213, -2.645], [-3.487, -12.63]], "o": [[-6.105, 0.714], [4.602, -15.169], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [3.967, -2.676], [1.116, -0.273], [0.218, 1.204], [-0.034, 1.614], [1.787, 22.195], [3.607, 13.007]], "v": [[49.323, 110.604], [40.162, 85.756], [31.79, 32.654], [-4.546, 34.179], [21.082, 31.37], [33.959, 24.558], [40.085, 18.499], [42.207, 22.414], [42.52, 29.366], [42.308, 39.152], [53.101, 86.231]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [{"i": [[7.367, -0.903], [-2.952, 20.32], [9.716, -3.066], [3.786, 1.938], [-13.451, 5.742], [-4.915, 1.715], [0.056, -1.395], [-0.096, -3.25], [-0.316, -2.98], [-0.213, -2.645], [-3.228, -11.257]], "o": [[-7.368, 0.903], [4.181, -18.16], [-16.771, 5.292], [-3.786, -1.938], [3.273, -1.397], [1.821, -0.635], [-0.056, 1.404], [0.096, 3.25], [0.316, 2.98], [1.787, 22.195], [3.408, 11.824]], "v": [[51.782, 114.129], [44.876, 88.634], [27.054, 29.697], [-6.2, 34.486], [30.765, 28.54], [36.489, 26.121], [39.016, 25.997], [39.223, 30.566], [39.658, 37.627], [40.041, 43.47], [51.969, 89.96]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [{"i": [[1.361, -0.113], [2.462, 0.228], [1.659, 2.069], [1.194, 3.171], [-0.529, 2.545], [-5.043, 0.79], [-1.574, 0.193], [-1.199, -5.033], [3.193, -1.596], [2.623, -0.411], [1.366, -1.1]], "o": [[-1.361, 0.113], [-2.464, -0.291], [-2.288, -1.358], [-0.815, -3.559], [2.69, -2.041], [3.51, -0.55], [1.048, -0.062], [0.488, 2.048], [-3.191, 1.659], [-2.623, 0.411], [-0.39, 1.091]], "v": [[33.736, 24.351], [30.173, 28.675], [27.277, 26.772], [22.615, 24.102], [22.239, 18.899], [34.158, 17.225], [40.457, 16.414], [48.755, 16.288], [45.493, 19.271], [41.032, 21.63], [36.923, 23.357]], "c": true}]}, {"t": 211, "s": [{"i": [[0, 0], [2.67, 3.89], [0.555, 3.292], [-0.65, 5.106], [-1.206, 6.553], [-0.127, 4.888], [-1.62, -0.11], [-2.52, -9.706], [-1.674, -3.851], [-0.557, -6.069], [1.134, -3.624]], "o": [[0, 0], [-2.462, -3.587], [-0.6, -3.557], [0.549, -4.316], [0.266, -1.445], [0.209, -8.049], [1.62, 0.11], [1.051, 4.046], [1.674, 3.851], [0.557, 6.069], [-1.031, 3.294]], "v": [[62.589, 118.145], [54.155, 114.655], [50.386, 104.96], [50.886, 93.432], [52.958, 82.704], [54.459, 70.372], [55.612, 57.03], [59.526, 69.847], [66.634, 87.494], [70.296, 100.899], [68.862, 112.333]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 0.855, 0.973, 1, 0.501, 0.881, 0.976, 1, 1, 0.908, 0.98, 1], "ix": 9}}, "s": {"a": 0, "k": [-1, 49], "ix": 5}, "e": {"a": 0, "k": [-1, 8.072], "ix": 6}, "t": 1, "nm": "koushui11111", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.828, 40.225], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [76.775, 76.775], "ix": 3}, "r": {"a": 0, "k": 2.688, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "zu2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.223541259766, 0.729400634766, 0.850982666016, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "miaobian12", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 45, "op": 203, "st": -57, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "左边眉毛 2", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 16, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-62.422, -52.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [-62.422, -61.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [-62.422, -52.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [-62.422, -52.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [-62.422, -61.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [-62.422, -52.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [-62.422, -41.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [-62.422, -52.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 240, "s": [-62.422, -52.751, 0]}], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-32.217, -94.16], [-59.095, -99.385], [-81.259, -88.718], [-87.838, -93.719], [-60.824, -108.486], [-27.298, -101.234]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[3.955, 1.555], [11.967, 2.548], [6.144, -2.968], [-1.192, 3.259], [-16.689, -1.5], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-13.258, -2.823], [-3.495, 1.688], [1.517, -4.15], [14.524, 1.305], [3.129, 3.847]], "v": [[-32.919, -96.526], [-51.865, -106.686], [-78.781, -100.897], [-85.36, -105.898], [-52.627, -115.923], [-27.313, -102.218]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-32.217, -94.16], [-59.095, -99.385], [-81.259, -88.718], [-87.838, -93.719], [-60.824, -108.486], [-27.298, -101.234]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[3.955, 1.555], [11.967, 2.548], [6.144, -2.968], [-1.192, 3.259], [-16.689, -1.5], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-13.258, -2.823], [-3.495, 1.688], [1.517, -4.15], [14.524, 1.305], [3.129, 3.847]], "v": [[-32.919, -96.526], [-51.865, -106.686], [-78.781, -100.897], [-85.36, -105.898], [-52.627, -115.923], [-27.313, -102.218]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-32.217, -94.16], [-59.095, -99.385], [-81.259, -88.718], [-87.838, -93.719], [-60.824, -108.486], [-27.298, -101.234]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-32.217, -94.16], [-59.095, -99.385], [-81.259, -88.718], [-87.838, -93.719], [-60.824, -108.486], [-27.298, -101.234]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[3.955, 1.555], [11.967, 2.548], [6.144, -2.968], [-1.192, 3.259], [-16.689, -1.5], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-13.258, -2.823], [-3.495, 1.688], [1.517, -4.15], [14.524, 1.305], [3.129, 3.847]], "v": [[-32.919, -96.526], [-51.865, -106.686], [-78.781, -100.897], [-85.36, -105.898], [-52.627, -115.923], [-27.313, -102.218]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-32.217, -94.16], [-59.095, -99.385], [-81.259, -88.718], [-87.838, -93.719], [-60.824, -108.486], [-27.298, -101.234]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [{"i": [[4.602, -0.586], [17.406, -1.66], [6.932, -0.901], [-2.823, 2.019], [-19.08, 2.276], [-7.138, 0.415]], "o": [[-4.216, 0.537], [-12.193, 1.163], [-3.849, 0.5], [2.998, -2.144], [14.718, -1.756], [3.963, -0.23]], "v": [[-31.142, -95.773], [-57.756, -92.94], [-84.23, -90.343], [-86.853, -97.597], [-59.449, -101.665], [-31.747, -104.24]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-32.217, -94.16], [-59.095, -99.385], [-81.259, -88.718], [-87.838, -93.719], [-60.824, -108.486], [-27.298, -101.234]], "c": true}]}, {"t": 240, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-32.217, -94.16], [-59.095, -99.385], [-81.259, -88.718], [-87.838, -93.719], [-60.824, -108.486], [-27.298, -101.234]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.411743164063, 0.203918457031, 0.039215087891, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "眉毛右边", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-74.815, -50.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [-74.815, -67.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [-74.815, -50.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [-74.815, -50.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [-74.815, -67.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [-74.815, -50.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [-74.815, -39.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [-74.815, -50.751, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 240, "s": [-74.815, -50.751, 0]}], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-28.871, -93.151], [-59.095, -99.385], [-86.826, -84.485], [-93.405, -89.486], [-60.824, -108.486], [-23.952, -100.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[4.076, 1.202], [17.178, -3.982], [5.86, -3.495], [-0.902, 3.351], [-18.593, 4.852], [-5.51, -5.676]], "o": [[-4.076, -1.202], [-11.932, 2.766], [-3.333, 1.988], [1.148, -4.267], [14.11, -3.683], [3.454, 3.558]], "v": [[-42.253, -105.394], [-69.734, -108.529], [-93.795, -90.28], [-100.177, -94.319], [-71.643, -117.931], [-37.972, -112.872]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-28.871, -93.151], [-59.095, -99.385], [-86.826, -84.485], [-93.405, -89.486], [-60.824, -108.486], [-23.952, -100.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[4.076, 1.202], [17.178, -3.982], [5.86, -3.495], [-0.902, 3.351], [-18.593, 4.852], [-5.51, -5.676]], "o": [[-4.076, -1.202], [-11.932, 2.766], [-3.333, 1.988], [1.148, -4.267], [14.11, -3.683], [3.454, 3.558]], "v": [[-42.253, -105.394], [-69.734, -108.529], [-93.795, -90.28], [-100.177, -94.319], [-71.643, -117.931], [-37.972, -112.872]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-28.871, -93.151], [-59.095, -99.385], [-86.826, -84.485], [-93.405, -89.486], [-60.824, -108.486], [-23.952, -100.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-28.871, -93.151], [-59.095, -99.385], [-86.826, -84.485], [-93.405, -89.486], [-60.824, -108.486], [-23.952, -100.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[4.076, 1.202], [17.178, -3.982], [5.86, -3.495], [-0.902, 3.351], [-18.593, 4.852], [-5.51, -5.676]], "o": [[-4.076, -1.202], [-11.932, 2.766], [-3.333, 1.988], [1.148, -4.267], [14.11, -3.683], [3.454, 3.558]], "v": [[-42.253, -105.394], [-69.734, -108.529], [-93.795, -90.28], [-100.177, -94.319], [-71.643, -117.931], [-37.972, -112.872]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-28.871, -93.151], [-59.095, -99.385], [-86.826, -84.485], [-93.405, -89.486], [-60.824, -108.486], [-23.952, -100.226]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [{"i": [[4.426, -0.267], [17.461, -2.462], [6.965, -2.084], [-2.076, 2.781], [-18.946, 3.205], [-7.197, -4.1]], "o": [[-4.242, 0.256], [-12.129, 1.71], [-3.718, 1.113], [2.731, -3.658], [14.379, -2.432], [4.308, 2.454]], "v": [[-27.623, -93.72], [-56.652, -91.458], [-88.225, -85.939], [-93.91, -91.778], [-58.38, -100.559], [-24.358, -102.008]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-28.871, -93.151], [-59.095, -99.385], [-86.826, -84.485], [-93.405, -89.486], [-60.824, -108.486], [-23.952, -100.226]], "c": true}]}, {"t": 240, "s": [{"i": [[3.955, 1.555], [17.461, -2.462], [6.144, -2.968], [-1.192, 3.259], [-18.946, 3.205], [-4.991, -6.137]], "o": [[-3.955, -1.555], [-12.129, 1.71], [-3.495, 1.688], [1.517, -4.15], [14.379, -2.432], [3.129, 3.847]], "v": [[-28.871, -93.151], [-59.095, -99.385], [-86.826, -84.485], [-93.405, -89.486], [-60.824, -108.486], [-23.952, -100.226]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.411743164063, 0.203918457031, 0.039215087891, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "右眼讶框遮罩", "parent": 3, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 2.281, "ix": 10}, "p": {"a": 0, "k": [13.48, 131.791, 0], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [207.222, 188.328, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-0.045, -9.29], [13.872, 0.767], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.766, -0.761], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.761, -49.426], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[-0.045, -9.29], [13.799, 0.061], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.784, -0.06], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.984, -46.16], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [{"i": [[-0.113, -0.071], [13.797, 0.039], [0.25, 0.73], [-9.039, -0.325]], "o": [[0.113, 0.071], [-13.785, -0.038], [-0.033, -0.224], [9.316, 0.335]], "v": [[-38.013, -47.65], [-64.702, -48.231], [-88.309, -49.592], [-64.546, -49.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [{"i": [[-0.045, -9.29], [13.795, 0.026], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.785, -0.025], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.995, -45.995], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"t": 240, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.992218017578, 0.942474365234, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "右眼惊讶眼 2", "parent": 10, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-71.095, -49.287, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [-71.095, -49.287, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 240, "s": [-71.095, -49.287, 0]}], "ix": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.1;\n    frequency = 2;\n    decay = 4;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [49.144, 49.144, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, -10.991], [11.025, -0.539], [0, 10.991], [-11.269, 0.397]], "o": [[0, 10.991], [-11.025, 0.539], [0, -10.991], [11.269, -0.397]], "v": [[-35.935, -53.589], [-54.126, -36.047], [-71.42, -53.589], [-54.126, -72.189]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [{"i": [[0, -10.991], [11.025, -0.539], [0, 10.991], [-11.269, 0.397]], "o": [[0, 10.991], [-11.025, 0.539], [0, -10.991], [11.269, -0.397]], "v": [[-35.559, -60.974], [-53.749, -43.432], [-71.044, -60.974], [-53.749, -79.574]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-1.368, -14.983], [11.472, -1.49], [1.877, 13.89], [-19.228, 3.551]], "o": [[1.287, 14.1], [-12.357, 1.605], [-1.457, -10.782], [16.848, -3.112]], "v": [[-53.579, -57.419], [-69.669, -44.307], [-87.527, -53.685], [-70.233, -72.284]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[-3.438, -15.834], [11.151, -3.079], [1.877, 13.89], [-19.447, 2.033]], "o": [[2.793, 12.861], [-11.472, 3.167], [-1.457, -10.782], [4.172, -0.436]], "v": [[-63.338, -60.037], [-80.475, -40.286], [-106.799, -45.814], [-82.822, -68.167]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [{"i": [[-3.438, -15.834], [11.151, -3.079], [1.877, 13.89], [-19.447, 2.033]], "o": [[2.793, 12.861], [-11.472, 3.167], [-1.457, -10.782], [4.172, -0.436]], "v": [[-63.338, -60.037], [-80.475, -40.286], [-106.799, -45.814], [-82.822, -68.167]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[-1.16, -12.626], [11.068, -1.396], [0.634, 11.97], [-14.029, 0.949]], "o": [[0.942, 11.622], [-11.176, 1.426], [-0.492, -10.921], [8.874, -0.41]], "v": [[-45.183, -55.765], [-63.019, -37.478], [-83.36, -50.965], [-63.811, -70.832]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [{"i": [[0, -3.331], [11.061, -1.269], [0.036, 10.745], [-13.62, 0.867]], "o": [[0.803, 11.529], [-11.154, 1.295], [0, -5.241], [9.229, -0.408]], "v": [[-43.442, -51.004], [-61.721, -43.652], [-81.468, -49.627], [-62.313, -54.265]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [{"i": [[-0.146, -15.036], [11.025, -0.539], [0.996, 12.431], [-11.269, 0.397]], "o": [[0.146, 15.036], [-11.025, 0.539], [-0.996, -12.431], [11.269, -0.397]], "v": [[-35.945, -53.555], [-54.469, -37.56], [-71.482, -54.295], [-54.334, -75.506]], "c": true}]}, {"t": 240, "s": [{"i": [[0, -10.991], [11.025, -0.539], [0, 10.991], [-11.269, 0.397]], "o": [[0, 10.991], [-11.025, 0.539], [0, -10.991], [11.269, -0.397]], "v": [[-35.935, -53.589], [-54.126, -36.047], [-71.42, -53.589], [-54.126, -72.189]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "右眼惊讶框 2", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 2.671, "ix": 10}, "p": {"a": 0, "k": [13.471, 132.042, 0], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [208.465, 183.687, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-0.045, -9.29], [13.872, 0.767], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.766, -0.761], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.761, -49.426], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[-0.045, -9.29], [13.799, 0.061], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.784, -0.06], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.984, -46.16], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [{"i": [[-0.113, -0.071], [13.797, 0.039], [0.25, 0.73], [-9.039, -0.325]], "o": [[0.113, 0.071], [-13.785, -0.038], [-0.033, -0.224], [9.316, 0.335]], "v": [[-38.013, -47.65], [-64.702, -48.231], [-88.309, -49.592], [-64.546, -49.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [{"i": [[-0.045, -9.29], [13.795, 0.026], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.785, -0.025], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.995, -45.995], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"t": 240, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.992218017578, 0.942474365234, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.690196096897, 0.376470595598, 0.137254908681, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "左眼框遮罩", "parent": 3, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.883, "ix": 10}, "p": {"a": 0, "k": [-136.454, 124.114, 0], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [207.183, 188.364, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-0.045, -9.29], [13.872, 0.767], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.766, -0.761], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-62.213, -49.245], [-85.176, -47.262], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[-0.045, -9.29], [13.799, 0.061], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.784, -0.06], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.984, -46.16], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [{"i": [[-0.113, -0.071], [13.797, 0.039], [0.25, 0.73], [-9.039, -0.325]], "o": [[0.113, 0.071], [-13.785, -0.038], [-0.033, -0.224], [9.316, 0.335]], "v": [[-38.013, -47.65], [-64.702, -48.231], [-88.309, -49.592], [-64.546, -49.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [{"i": [[-0.045, -9.29], [13.795, 0.026], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.785, -0.025], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.995, -45.995], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"t": 240, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.992218017578, 0.942474365234, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "右眼惊讶眼 4", "parent": 13, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-65.095, -52.287, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [-65.095, -52.287, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 240, "s": [-65.095, -52.287, 0]}], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [49.144, 49.144, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, -10.219], [8.758, 0], [0, 10.219], [-8.758, 0]], "o": [[0, 10.219], [-8.758, 0], [0, -10.219], [8.758, 0]], "v": [[-48.485, -53.706], [-64.343, -35.203], [-80.201, -53.706], [-64.27, -69.305]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [{"i": [[0.773, -11.895], [9.36, 0.242], [-1.118, 13.538], [-12.899, -0.164]], "o": [[-0.773, 11.895], [-9.36, -0.242], [1.118, -13.538], [12.64, 0.161]], "v": [[-46.288, -54.812], [-63.75, -38.389], [-80.182, -55.689], [-62.814, -73.785]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-0.148, -10.218], [14.185, -2.937], [-0.636, 18.202], [-15.793, 0.916]], "o": [[0.166, 11.461], [-8.576, 1.776], [0.357, -10.212], [16.504, -0.958]], "v": [[-59.47, -57.993], [-73.531, -43.779], [-91.332, -53.593], [-75.474, -72.095]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[-0.492, -7.197], [14.185, -2.937], [-2.305, 18.067], [-15.497, 3.177]], "o": [[0.465, 6.797], [-8.576, 1.776], [1.064, -8.341], [14.46, -2.964]], "v": [[-69.579, -58.264], [-85.663, -36.343], [-110.786, -44.417], [-86.293, -67.53]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [{"i": [[-0.492, -7.197], [14.185, -2.937], [-2.305, 18.067], [-15.497, 3.177]], "o": [[0.465, 6.797], [-8.576, 1.776], [1.064, -8.341], [14.46, -2.964]], "v": [[-69.579, -58.264], [-86.24, -36.787], [-115.056, -45.223], [-86.293, -67.53]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[-0.166, -9.199], [10.59, -0.991], [-0.778, 12.868], [-11.033, 1.072]], "o": [[0.157, 9.064], [-8.697, 0.599], [0.359, -9.585], [10.683, -1]], "v": [[-53.655, -52.375], [-69.513, -34.194], [-87.253, -49.304], [-69.802, -67.76]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [{"i": [[0.457, -5.676], [9.445, -0.372], [0.747, 7.65], [-9.611, 0.402]], "o": [[0.457, 9.264], [-12.431, -1.185], [0.747, -6.809], [9.48, -0.375]], "v": [[-51.093, -46.708], [-67.018, -41.451], [-83.515, -45.557], [-66.888, -49.863]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [{"i": [[0.453, -13.091], [10.646, -0.585], [0.009, 12.095], [-8.758, 0]], "o": [[-0.453, 13.091], [-10.646, 0.585], [-0.009, -12.095], [8.758, 0]], "v": [[-48.485, -53.706], [-64.346, -35.783], [-80.201, -53.706], [-64.194, -70.665]], "c": true}]}, {"t": 240, "s": [{"i": [[0, -10.219], [8.758, 0], [0, 10.219], [-8.758, 0]], "o": [[0, 10.219], [-8.758, 0], [0, -10.219], [8.758, 0]], "v": [[-48.485, -53.706], [-64.343, -35.203], [-80.201, -53.706], [-64.343, -72.208]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100.902, 100.902], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 3, "nm": "右眼惊讶框 4", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 2.602, "ix": 10}, "p": {"a": 0, "k": [-136.882, 126.551, 0], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [208.432, 183.716, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "新左眼框", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 2.273, "ix": 10}, "p": {"a": 0, "k": [-136.346, 124.341, 0], "ix": 2}, "a": {"a": 0, "k": [-67.737, -49.287, 0], "ix": 1}, "s": {"a": 0, "k": [208.448, 183.702, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-18.759, 0.534]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [18.759, -0.534]], "v": [[-38.013, -47.65], [-63.779, -46.845], [-88.766, -47.51], [-64.073, -65.887]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-0.045, -9.29], [13.872, 0.767], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.766, -0.761], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-62.213, -49.245], [-85.176, -47.262], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [{"i": [[-0.045, -9.29], [13.799, 0.061], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.784, -0.06], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.984, -46.16], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [{"i": [[-0.113, -0.071], [13.797, 0.039], [0.25, 0.73], [-9.039, -0.325]], "o": [[0.113, 0.071], [-13.785, -0.038], [-0.033, -0.224], [9.316, 0.335]], "v": [[-38.013, -47.65], [-64.702, -48.231], [-88.309, -49.592], [-64.546, -49.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [{"i": [[-0.045, -9.29], [13.795, 0.026], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.785, -0.025], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-63.995, -45.995], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}, {"t": 240, "s": [{"i": [[-0.045, -9.29], [13.787, -0.05], [0.175, 7.123], [-9.043, 0.184]], "o": [[0.036, 7.558], [-13.787, 0.05], [-0.208, -8.431], [9.043, -0.184]], "v": [[-38.013, -47.65], [-64.019, -45.646], [-88.766, -47.51], [-63.953, -60.13]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.992218017578, 0.942474365234, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.690196096897, 0.376470595598, 0.137254908681, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.412, 0.204, 0.039, 0.5, 0.371, 0.173, 0.024, 1, 0.329, 0.141, 0.008], "ix": 9}}, "s": {"a": 0, "k": [-68, -73], "ix": 5}, "e": {"a": 0, "k": [-68, -27.331], "ix": 6}, "t": 1, "nm": "Gradient Fill 20", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 264, "st": -110, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "右边sp红晕 2", "parent": 10, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 188, "s": [100]}, {"t": 198, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [-46.033, -27.08, 0], "to": [-0.667, 0, 0], "ti": [0.667, 0, 0]}, {"t": 112, "s": [-50.033, -27.08, 0]}], "ix": 2}, "a": {"a": 0, "k": [-196.048, -51.393, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 55, "s": [31.01, 10.32, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 63, "s": [31.01, 10.32, 100]}, {"t": 70, "s": [49.039, 16.32, 100]}], "ix": 6, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -36.364], [36.364, 0], [0, 36.364], [-36.364, 0]], "o": [[0, 36.364], [-36.364, 0], [0, -36.364], [36.364, 0]], "v": [[-130.205, -51.393], [-196.048, 14.45], [-261.892, -51.393], [-196.048, -117.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.314, 0.447, 0.494, 1, 0.292, 0.359, 0.988, 1, 0.271, 0.271, 0, 0.5, 0.494, 0.25, 0.988, 0], "ix": 9}}, "s": {"a": 0, "k": [-195, -50.998], "ix": 5}, "e": {"a": 0, "k": [-135.986, -53.998], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 30", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [0]}, {"t": 95, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 55, "op": 306, "st": -55, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "右边红点点", "parent": 15, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 188, "s": [100]}, {"t": 198, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-197.435, -49.626, 0], "ix": 2}, "a": {"a": 0, "k": [101.933, -2.748, 0], "ix": 1}, "s": {"a": 0, "k": [100, 300.482, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, 1.039], [1.039, -1.039], [0, 0], [-1.039, -1.039], [-1.039, 1.039], [0, 0]], "o": [[-1.039, -1.039], [0, 0], [-1.039, 1.039], [1.039, 1.039], [0, 0], [1.039, -1.039]], "v": [[124.06, -12.847], [120.297, -12.847], [103.861, 3.589], [103.861, 7.351], [107.623, 7.351], [124.06, -9.085]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, -1.039], [0, 0], [-1.039, -1.039], [-1.039, 1.039], [0, 0], [1.039, 1.039]], "o": [[0, 0], [-1.039, 1.039], [1.039, 1.039], [0, 0], [1.039, -1.039], [-1.039, -1.039]], "v": [[96.243, -12.847], [79.807, 3.589], [79.807, 7.351], [83.57, 7.351], [100.006, -9.085], [100.006, -12.847]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 55, "op": 306, "st": 25, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "红晕遮罩", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [-5.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [6.359]}, {"t": 215, "s": [1]}], "ix": 10}, "p": {"a": 0, "k": [256, 424, 0], "ix": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [97, 101, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 46, "s": [100, 88.704, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 53, "s": [99.962, 105.04, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 61, "s": [99.941, 100.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [100.006, 104.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 106, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 130, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 140, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 145, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [100.195, 99.805, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [100.209, 104.781, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [99.941, 100.059, 100]}, {"t": 240, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [-1, -4], "ix": 5}, "e": {"a": 0, "k": [-0.381, 160], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "<PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "左边sp红晕 3", "parent": 13, "tt": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 188, "s": [100]}, {"t": 198, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-88.176, -27.08, 0], "ix": 2}, "a": {"a": 0, "k": [-196.048, -51.393, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 55, "s": [31.01, 10.32, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 63, "s": [31.01, 10.32, 100]}, {"t": 70, "s": [49.039, 16.32, 100]}], "ix": 6, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -36.364], [36.364, 0], [0, 36.364], [-36.364, 0]], "o": [[0, 36.364], [-36.364, 0], [0, -36.364], [36.364, 0]], "v": [[-130.205, -51.393], [-196.048, 14.45], [-261.892, -51.393], [-196.048, -117.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.314, 0.447, 0.494, 1, 0.292, 0.359, 0.988, 1, 0.271, 0.271, 0, 0.5, 0.494, 0.25, 0.988, 0], "ix": 9}}, "s": {"a": 0, "k": [-195, -50.998], "ix": 5}, "e": {"a": 0, "k": [-135.986, -53.998], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 30", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 55, "op": 306, "st": -55, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "左边红点点", "parent": 18, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 188, "s": [100]}, {"t": 198, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-191.903, -40.979, 0], "ix": 2}, "a": {"a": 0, "k": [-93.933, -2.748, 0], "ix": 1}, "s": {"a": 0, "k": [100, 300, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, 1.039], [0, 0], [1.039, -1.039], [0, 0], [-1.039, -1.039], [0, 0], [-1.039, 1.039], [0, 0]], "o": [[0, 0], [-1.039, -1.039], [0, 0], [-1.039, 1.039], [0, 0], [1.039, 1.039], [0, 0], [1.039, -1.039]], "v": [[-71.807, -12.847], [-71.807, -12.847], [-75.569, -12.847], [-92.006, 3.589], [-92.006, 7.351], [-92.006, 7.351], [-88.243, 7.351], [-71.807, -9.085]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.039, -1.039], [0, 0], [-1.039, -1.039], [-1.039, 1.039], [0, 0], [1.039, 1.039]], "o": [[0, 0], [-1.039, 1.039], [1.039, 1.039], [0, 0], [1.039, -1.039], [-1.039, -1.039]], "v": [[-99.623, -12.847], [-116.059, 3.589], [-116.059, 7.351], [-112.297, 7.351], [-95.861, -9.085], [-95.861, -12.847]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 55, "op": 306, "st": -91, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "嘴 12", "parent": 3, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.204, "ix": 10}, "p": {"a": 0, "k": [-60.765, 185.487, 0], "ix": 2}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 28, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 38, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 80, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 88, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 93, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 98, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 105, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 122, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 127, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 132, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 139, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 144, "s": [110.956, 97.877, 100]}, {"t": 149, "s": [95.962, 84.651, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[23.063, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.105], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-2.458, 26.604], [-50.339, -6.691], [-45.967, -13.963], [-38.695, -9.591], [-2.458, 14.604], [33.814, -9.738], [41.06, -14.154], [45.476, -6.908]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"t": 204, "s": [{"i": [[23.063, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.105], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-2.458, 26.604], [-50.339, -6.691], [-45.967, -13.963], [-38.695, -9.591], [-2.458, 14.604], [33.814, -9.738], [41.06, -14.154], [45.476, -6.908]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 0.765, 0.188, 0.004, 0.501, 0.765, 0.188, 0.004, 1, 0.765, 0.188, 0.004], "ix": 9}}, "s": {"a": 0, "k": [-1, 49], "ix": 5}, "e": {"a": 0, "k": [-1, 8.072], "ix": 6}, "t": 1, "nm": "jianbian88", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.531, 34.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [76.775, 76.775], "ix": 3}, "r": {"a": 0, "k": 2.688, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "牙齿", "parent": 3, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 2.602, "ix": 10}, "p": {"a": 0, "k": [-59.011, 204.37, 0], "ix": 2}, "a": {"a": 0, "k": [27.448, 44.747, 0], "ix": 1}, "s": {"a": 0, "k": [141.898, 114.872, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -11.326], [15.159, 0], [0, 11.326], [-15.159, 0]], "o": [[0, 11.326], [-15.159, 0], [0, -11.326], [15.159, 0]], "v": [[51.65, 12.698], [26.487, 23.529], [-0.603, 14.196], [24.907, -17.599]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.9921875, 0.942443847656, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "嘴 10", "parent": 3, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.204, "ix": 10}, "p": {"a": 0, "k": [-60.765, 185.487, 0], "ix": 2}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 28, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 38, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 80, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 88, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 93, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 98, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 105, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 122, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 127, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 132, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 139, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 144, "s": [110.956, 97.877, 100]}, {"t": 149, "s": [95.962, 84.651, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[4.562, -0.013], [9.705, 6.688], [5.683, 5.217], [-4.62, 2.855], [-32.155, 0.387], [-8.533, -3.73], [9.189, -8.113], [10.069, -7.58]], "o": [[-4.562, 0.013], [-9.705, -6.688], [-6.396, -5.871], [13.457, -8.314], [32.155, -0.387], [3.544, 1.549], [-3.245, 2.865], [-10.596, 7.976]], "v": [[-0.051, 22.101], [-31.224, 14.578], [-43.462, 4.983], [-49.118, -11.048], [-0.407, 8.14], [48.473, -13.767], [45.532, 1.948], [31.104, 13.244]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"t": 204, "s": [{"i": [[4.562, -0.013], [9.705, 6.688], [5.683, 5.217], [-4.62, 2.855], [-32.155, 0.387], [-8.533, -3.73], [9.189, -8.113], [10.069, -7.58]], "o": [[-4.562, 0.013], [-9.705, -6.688], [-6.396, -5.871], [13.457, -8.314], [32.155, -0.387], [3.544, 1.549], [-3.245, 2.865], [-10.596, 7.976]], "v": [[-0.051, 22.101], [-31.224, 14.578], [-43.462, 4.983], [-49.118, -11.048], [-0.407, 8.14], [48.473, -13.767], [45.532, 1.948], [31.104, 13.244]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 0.765, 0.188, 0.004, 0.501, 0.765, 0.188, 0.004, 1, 0.765, 0.188, 0.004], "ix": 9}}, "s": {"a": 0, "k": [-1, 49], "ix": 5}, "e": {"a": 0, "k": [-1, 8.072], "ix": 6}, "t": 1, "nm": "98989444", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.531, 34.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [76.775, 76.775], "ix": 3}, "r": {"a": 0, "k": 2.688, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "舌头", "parent": 3, "tt": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 197, "s": [100]}, {"t": 204, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 2.602, "ix": 10}, "p": {"a": 0, "k": [-59.499, 212.053, 0], "ix": 2}, "a": {"a": 0, "k": [27.448, 44.747, 0], "ix": 1}, "s": {"a": 0, "k": [141.898, 117.781, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -11.326], [46.513, 2.504], [0, 11.326], [-15.159, 0]], "o": [[0, 11.326], [-28.115, -1.514], [0, -11.326], [15.159, 0]], "v": [[54.895, 44.747], [21.42, 99.086], [0, 44.747], [27.448, 24.24]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.270588248968, 0.270588248968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 245, "st": -121, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "嘴", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.602, "ix": 10}, "p": {"a": 0, "k": [-60.765, 185.487, 0], "ix": 2}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 28, "s": [99.997, 88.145, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33, "s": [110.997, 97.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 38, "s": [95.997, 84.619, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [99.997, 88.145, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 80, "s": [95.997, 84.619, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 88, "s": [99.997, 88.145, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 93, "s": [110.997, 97.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 98, "s": [95.997, 84.619, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 105, "s": [99.997, 88.145, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [110.997, 97.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [95.997, 84.619, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 122, "s": [99.997, 88.145, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 127, "s": [110.997, 97.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 132, "s": [95.997, 84.619, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 139, "s": [99.997, 88.145, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 144, "s": [110.997, 97.841, 100]}, {"t": 149, "s": [95.997, 84.619, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[23.063, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.105], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-2.458, 26.604], [-50.339, -6.691], [-45.967, -13.963], [-38.695, -9.591], [-2.458, 14.604], [33.814, -9.738], [41.06, -14.154], [45.476, -6.908]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"t": 204, "s": [{"i": [[23.063, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.105], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-2.458, 26.604], [-50.339, -6.691], [-45.967, -13.963], [-38.695, -9.591], [-2.458, 14.604], [33.814, -9.738], [41.06, -14.154], [45.476, -6.908]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 0.765, 0.188, 0.004, 0.501, 0.765, 0.188, 0.004, 1, 0.765, 0.188, 0.004], "ix": 9}}, "s": {"a": 0, "k": [-1, 49], "ix": 5}, "e": {"a": 0, "k": [-1, 8.072], "ix": 6}, "t": 1, "nm": "jianbian88", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.531, 34.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [76.775, 76.775], "ix": 3}, "r": {"a": 0, "k": 2.688, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "嘴白高光", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 87, "ix": 11}, "r": {"a": 0, "k": 1.204, "ix": 10}, "p": {"a": 0, "k": [-60.765, 187.487, 0], "ix": 2}, "a": {"a": 0, "k": [0, 29.31, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 28, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 38, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 80, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 88, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 93, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 98, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 105, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 110, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 122, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 127, "s": [110.956, 97.877, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 132, "s": [95.962, 84.651, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 139, "s": [99.96, 88.178, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 144, "s": [110.956, 97.877, 100]}, {"t": 149, "s": [95.962, 84.651, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 位置", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "Jump", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "Gravity", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "Max Jumps", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[23.063, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.105], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-2.458, 26.604], [-50.339, -6.691], [-45.967, -13.963], [-38.695, -9.591], [-2.458, 14.604], [33.814, -9.738], [41.06, -14.154], [45.476, -6.908]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [{"i": [[13.905, -1.179], [9.705, 6.688], [3.609, 6.261], [0.199, 11.559], [-16.134, 0.429], [-6.259, -8.346], [0.902, -3.598], [9.455, -8.334]], "o": [[-13.905, 1.179], [-9.705, -6.688], [-3.609, -6.261], [-0.354, -20.626], [16.134, -0.429], [6.259, 8.346], [-0.902, 3.598], [-9.455, 8.334]], "v": [[-1.928, 34.983], [-38.867, 25.721], [-54.116, 10.685], [-58.984, -9.019], [-8.176, -31.181], [44.581, -24.617], [45.794, 2.216], [32.018, 21.813]], "c": true}]}, {"t": 204, "s": [{"i": [[23.063, 0], [4.806, 19.293], [-3.216, 0.801], [-0.801, -3.215], [-17.504, 0], [-3.423, 14.105], [-3.217, -0.779], [0.781, -3.22]], "o": [[-22.938, 0], [-0.801, -3.215], [3.218, -0.799], [3.493, 14.02], [17.595, 0], [0.782, -3.221], [3.221, 0.782], [-4.713, 19.418]], "v": [[-2.458, 26.604], [-50.339, -6.691], [-45.967, -13.963], [-38.695, -9.591], [-2.458, 14.604], [33.814, -9.738], [41.06, -14.154], [45.476, -6.908]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 1, 0.981, 0.868, 0.501, 1, 0.988, 0.914, 1, 1, 0.995, 0.961], "ix": 9}}, "s": {"a": 0, "k": [-1, 49], "ix": 5}, "e": {"a": 0, "k": [-1, 8.072], "ix": 6}, "t": 1, "nm": "jianbian88222", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.531, 34.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [76.775, 76.775], "ix": 3}, "r": {"a": 0, "k": 2.688, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "红晕遮罩 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [-5.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [6.359]}, {"t": 215, "s": [1]}], "ix": 10}, "p": {"a": 0, "k": [256, 424, 0], "ix": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [97, 101, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 46, "s": [100, 88.704, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 53, "s": [99.962, 105.04, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 61, "s": [99.941, 100.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [100.006, 104.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 106, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 130, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 140, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 145, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [100.195, 99.805, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [100.209, 104.781, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [99.941, 100.059, 100]}, {"t": 240, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [-1, -4], "ix": 5}, "e": {"a": 0, "k": [-0.381, 160], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "<PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "真正的红晕 2", "parent": 3, "tt": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [50]}, {"t": 240, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -0.699, "ix": 10}, "p": {"a": 0, "k": [-54.554, 162.005, 0], "ix": 2}, "a": {"a": 0, "k": [-196.048, -51.393, 0], "ix": 1}, "s": {"a": 0, "k": [217.186, 91.084, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -36.364], [36.364, 0], [0, 36.364], [-36.364, 0]], "o": [[0, 36.364], [-36.364, 0], [0, -36.364], [36.364, 0]], "v": [[-130.205, -51.393], [-196.048, 14.45], [-261.892, -51.393], [-196.048, -117.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.314, 0.447, 0.494, 1, 0.292, 0.359, 0.988, 1, 0.271, 0.271, 0, 0.5, 0.494, 0.25, 0.988, 0], "ix": 9}}, "s": {"a": 0, "k": [-195, -50.998], "ix": 5}, "e": {"a": 0, "k": [-135.986, -53.998], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 30", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -15, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "红晕遮罩 3", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [-5.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [6.359]}, {"t": 215, "s": [1]}], "ix": 10}, "p": {"a": 0, "k": [256, 424, 0], "ix": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [97, 101, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 46, "s": [100, 88.704, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 53, "s": [99.962, 105.04, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 61, "s": [99.941, 100.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [100.006, 104.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 106, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 130, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 140, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 145, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [100.195, 99.805, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [100.209, 104.781, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [99.941, 100.059, 100]}, {"t": 240, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [-1, -4], "ix": 5}, "e": {"a": 0, "k": [-0.381, 160], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "<PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "真正的红晕", "parent": 26, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 60, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-10, -11, 0], "ix": 2}, "a": {"a": 0, "k": [-196.048, -51.393, 0], "ix": 1}, "s": {"a": 0, "k": [288, 84, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -36.364], [36.364, 0], [0, 36.364], [-36.364, 0]], "o": [[0, 36.364], [-36.364, 0], [0, -36.364], [36.364, 0]], "v": [[-130.205, -51.393], [-196.048, 14.45], [-261.892, -51.393], [-196.048, -117.236]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.314, 0.447, 0.494, 1, 0.292, 0.359, 0.988, 1, 0.271, 0.271, 0, 0.5, 0.494, 0.25, 0.988, 0], "ix": 9}}, "s": {"a": 0, "k": [-195, -50.998], "ix": 5}, "e": {"a": 0, "k": [-135.986, -53.998], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 30", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -15, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "脸线", "parent": 35, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.43, "ix": 10}, "p": {"a": 0, "k": [-0.554, 168.011, 0], "ix": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1}, "s": {"a": 0, "k": [99.983, 100.017, 100], "ix": 6, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274516582, 0.541176497936, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "gaoguang", "parent": 35, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.43, "ix": 10}, "p": {"a": 0, "k": [-0.081, -66.923, 0], "ix": 2}, "a": {"a": 0, "k": [-2.215, -59.393, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [99.983, 100.017, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [99.983, 109.019, 100]}, {"t": 70, "s": [99.983, 96.016, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -4.405], [-8.247, 10.373], [-153.613, -4.405], [-2.215, -155.803]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.342, 1, 0.926, 0.57, 0.999, 1, 0.852, 0.14, 0, 1, 0.342, 0.5, 0.999, 0], "ix": 9}}, "s": {"a": 0, "k": [-3, -156], "ix": 5}, "e": {"a": 0, "k": [-3, 36.82], "ix": 6}, "t": 1, "nm": "Gradient Fill 4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "lianse", "parent": 35, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.43, "ix": 10}, "p": {"a": 0, "k": [-1.212, -22.101, 0], "ix": 2}, "a": {"a": 0, "k": [-2.215, -14.081, 0], "ix": 1}, "s": {"a": 0, "k": [99.983, 100.017, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -83.615], [83.615, 0], [0, 83.615], [-83.615, 0]], "o": [[0, 83.615], [-83.615, 0], [0, -83.615], [83.615, 0]], "v": [[149.183, -3.116], [-2.215, 126.353], [-153.613, -3.116], [-2.215, -154.514]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 0.64, 0.342, 1, 0.926, 0.371, 0.999, 1, 0.852, 0.102], "ix": 9}}, "s": {"a": 0, "k": [-3, -155], "ix": 5}, "e": {"a": 0, "k": [-3, 125.867], "ix": 6}, "t": 1, "nm": "Gradient Fill 50", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [-5.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [-0.308]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 207, "s": [6.359]}, {"t": 215, "s": [1]}], "ix": 10}, "p": {"a": 0, "k": [256, 424, 0], "ix": 2}, "a": {"a": 0, "k": [0, 168, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [97, 101, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 46, "s": [100, 88.704, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 53, "s": [99.962, 105.04, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 61, "s": [99.941, 100.059, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [100.006, 104.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 100, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 106, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 115, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 130, "s": [100.04, 104.058, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 140, "s": [100, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 145, "s": [100.063, 103.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 200, "s": [100.195, 99.805, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 207, "s": [100.209, 104.781, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 215, "s": [99.941, 100.059, 100]}, {"t": 240, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar freq, decay, n, n, t, amp, w;\nfreq = 3;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time)\n        n--;\n}\nif (n > 0) {\n    t = $bm_sub(time, key(n).time);\n    amp = velocityAtTime($bm_sub(key(n).time, 0.001));\n    w = $bm_mul($bm_mul(freq, Math.PI), 2);\n    $bm_rt = $bm_sum(value, $bm_mul(amp, $bm_div($bm_div(Math.sin($bm_mul(t, w)), Math.exp($bm_mul(decay, t))), w)));\n} else\n    $bm_rt = value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -93.888], [93.888, 0], [0, 93.888], [-93.888, 0]], "o": [[0, 93.888], [-93.888, 0], [0, -93.888], [93.888, 0]], "v": [[170, -2], [0, 168], [-170, -2], [0, -172]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.789, 1, 0.851, 0.008, 0.901, 1, 0.776, 0.09, 0.998, 1, 0.702, 0.173], "ix": 9}}, "s": {"a": 0, "k": [-1, -4], "ix": 5}, "e": {"a": 0, "k": [-0.381, 160], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "<PERSON><PERSON><PERSON><PERSON>", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": -115, "bm": 0}], "markers": []}