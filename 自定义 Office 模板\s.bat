@echo off
setlocal enabledelayedexpansion

:: 指定要处理的文件夹路径
set "folderPath=C:\Users\<USER>\Documents\自定义 Office 模板"

:: 指定要去除的前缀
set "prefix=[Airota&Nekomoe kissaten&LoliHouse]"

:: 进入指定的文件夹路径
cd /d "%folderPath%"

:: 遍历当前目录下的所有文件夹
for /d %%F in (*) do (
    :: 检查文件夹名称是否以指定前缀开头
    if /i "%%~nF" neq "%%~nF" (
        :: 去除前缀
        set "newName=%%~nF"
        set "newName=!newName:%prefix%=!"
        
        :: 重命名文件夹
        echo Renaming "%%F" to "!newName!"
        ren "%%F" "!newName!"
    )
)

echo Renaming complete.
pause